<ul class="breadcrumb">
  <li><a href="<?php echo base_url('avatars'); ?>">Dashboard</a></li>
  <li><a href="<?php echo base_url('academics/academics_menu'); ?>">Academics</a></li>
  <li class="active">Resources</li>
</ul>

<div class="col-md-12">
  <div class="card panel_new_style">
    <div class="card-header panel_heading_new_style_padding padding8px">
      <div class="row m-0">
        <div class="col-9 p-0">
          <h3 class="card-title panel_title_new_style m-0">
            <strong>Add & View Resources</strong>
          </h3>
        </div>
        <div class="col-3 p-0">
          <ul class="panel-controls">
            <li>
              <!-- <a href="" class="new_circleShape_res" style="background-color: #fe970a;" data-toggle="modal" data-target="#exampleModal">
                <span class="fa fa-plus" style="font-size: 19px;"></span>
              </a> -->
              <a href="" class="new_circleShape_res" style="background-color: #fe970a;" data-toggle="modal"
                data-target="#resource-uploader">
                <span class="fa fa-plus" style="font-size: 19px;"></span>
              </a>
            </li>
          </ul>
        </div>
      </div>
    </div>
    <div class="card-body padding8px">
      <div class="row m-0">
        <div class="col-xs-12 mb-2 p-0">
          <label style="font-size: 14px;">Created Date</label>
          <div id="reportrange" class="dtrange" style="width: 100%">
            <span></span>
            <input type="hidden" id="from_date">
            <input type="hidden" id="to_date">
          </div>
        </div>

        <div class="col-xs-12 mb-2 p-0">
          <label for="gradeView" style="font-size: 14px;">Class</label>
          <div>
            <select class="form-control" id="gradeView" onchange="getSubjects(); getStaff()" name="grade">
              <option value=''>Select Class</option>
              <?php
              foreach ($grades as $cs => $cl) { ?>
                <option value="<?= $cl->id ?>">
                  <?= $cl->class_name ?>
                </option>
              <?php }
              ?>
            </select>
          </div>
        </div>

        <?php if ($is_semester_scheme == '1'): ?>
          <div class="col-xs-12 mb-2 p-0">
            <label for="semester_id" style="font-size: 14px;">Semester</label>
            <select class="form-control" id="semester_id" name="semester_id" onchange="getSubjects();">
              <option value="all">All</option>
              <?php foreach ($semester_list as $sem) { ?>
                <option value="<?= $sem->semester_id ?>">
                  <?= $sem->semester_name ?>
                </option>
              <?php } ?>
            </select>
          </div>
        <?php endif ?>

        <div class="col-xs-12 mb-2 p-0">
          <label for="subjectView" style="font-size: 14px;">Subjects</label>
          <div>
            <select class="form-control" id="subjectView" name="subject">
              <?php
              echo '<option value="all">All</option>';

              ?>
            </select>
          </div>
        </div>

        <div class="col-xs-12 mb-2 p-0">
          <label for="created_by" style="font-size: 14px;">Created By</label>
          <div>
            <select class="form-control" id="created_by" name="created_by">
              <option value='all'>All</option>

            </select>
          </div>
        </div>

        <div class="col-xs-12 mb-4 p-0">
          <label for="resourceView" style="font-size: 14px;">Resource Types</label>
          <div>
            <select class="form-control" id="resourceView" name="resource_type">
              <option value='all'>All</option>
              <?php
              foreach ($resourceTypes as $row) { ?>
                <option value="<?= $row->resource_type ?>">
                  <?= $row->resource_type ?>
                </option>
              <?php }
              ?>
            </select>
          </div>
        </div>

        <div class="col-xs-12 mb-2 p-0">
          <label for="resource_status" style="font-size: 14px;">Status</label>
          <div>
            <select class="form-control" id="resource_status" name="resource_status">
              <option value='Active'>Active</option>
              <option value='Inactive'>Inactive</option>
              <option value='all'>All</option>
            </select>
          </div>
        </div>

        <div class="col-xs-12 mb-2 p-0 mt-4">
          <button type="submit" id="get" style="width: 100%" class="btn btn-md btn-primary" onclick="getResources()">Get
            Resources
          </button>
        </div>
      </div>
      <div class="card-body p-0 mt-3" id="displayResourcePanel" style="overflow: auto;">
      </div>
    </div>
  </div>
</div>

<div id="video-data" data-backdrop="static" data-keyboard="false" class="modal fade" role="dialog" style="top:2%;">
  <div class="modal-dialog" role="document">
    <div class="modal-content" style="border-radius: 8px;">
      <div class="modal-header">
        <h4 class="modal-title" id="modalHeader">Video</h4>
        <button style="font-size: 32px;font-weight: bold;color: #e04b4a;opacity: 1;padding-top: .5rem;" type="button"
          class="close" onclick="pauseVideo()" data-dismiss="modal">&times;</button>
      </div>
      <div class="modal-body form-horizontal">
        <div class="row">
          <div class="col-12 col-md-12" id="uploaded">

          </div>
        </div>
      </div>
    </div>
  </div>
</div>

<div id="audio-data" data-backdrop="static" data-keyboard="false" class="modal fade" role="dialog" style="top:2%;">
  <div class="modal-dialog" role="document">
    <div class="modal-content" style="border-radius: 8px;">
      <div class="modal-header">
        <h4 class="modal-title" id="modalHeader">Audio</h4>
        <button style="font-size: 32px;font-weight: bold;color: #e04b4a;opacity: 1;padding-top: .5rem;" type="button"
          class="close" onclick="pauseAudio()" data-dismiss="modal">&times;</button>
      </div>
      <div class="modal-body form-horizontal">

        <div class="row">
          <div class="col-12 col-md-12" id="audio1">

          </div>
        </div>
      </div>
    </div>
  </div>
</div>

<div id="youtube-data" data-backdrop="static" data-keyboard="false" class="modal fade" role="dialog" style="top:2%;">
  <div class="modal-dialog" role="document">
    <div class="modal-content" style="border-radius: 8px;">
      <div class="modal-header">
        <h4 class="modal-title" id="modalHeader">Video</h4>
        <button style="font-size: 32px;font-weight: bold;color: #e04b4a;opacity: 1;padding-top: .5rem;" type="button"
          class="close" onclick="pauseYouTubeVideo()" data-dismiss="modal">&times;</button>
      </div>
      <div class="modal-body form-horizontal">

        <div class="row">
          <div class="col-12 col-md-12" id="uploadedYouTube">
            <iframe id="resourceVideo" width="100%" height="300" frameborder="0" allowfullscreen></iframe>

          </div>
        </div>
      </div>
    </div>
  </div>
</div>

<div id="iframe_files" data-backdrop="static" data-keyboard="false" class="modal fade" role="dialog" style="top:2%;">
  <div class="modal-dialog" role="document">
    <div class="modal-content" style="margin:auto;margin-top:1%;border-radius: .75rem;">
      <div class="modal-header" style="border-top-right-radius: .75rem;border-top-left-radius: .75rem;">
        <h4 class="modal-title" id="modalHeader">Resources</h4>
        <button style="font-size: 32px;font-weight: bold;color: #e04b4a;opacity: 1;padding-top: .5rem;" type="button"
          class="close" data-dismiss="modal">&times;</button>
      </div>
      <div class="modal-body form-horizontal">
        <div class="row">
          <div class="col-12 col-md-12" id="iframe_content">
          </div>
        </div>
      </div>
    </div>
  </div>
</div>


<div class="modal fade" id="exampleModal" tabindex="-1" role="dialog" aria-labelledby="exampleModalLabel"
  aria-hidden="true">
  <div class="modal-dialog" role="document">
    <div class="modal-content" style="width: 100%;margin: auto;margin-top: 8%;border-radius: .75rem;">
      <div class="modal-header" style="border-top-left-radius: .75rem; border-top-right-radius: .75rem;">
        <h4 class="modal-title" id="exampleModalLabel">Add Resources</h4>
        <button type="button" class="close" data-dismiss="modal" aria-label="Close">
          <i class="fa fa-times" aria-hidden="true" style="color: #d80403;font-size: 21px;"></i>
        </button>
      </div>
      <div class="modal-body">
        <form enctype="multipart/form-data" method="post" id="FileSaver" data-parsley-validate=""
          class="form-horizontal">
          <div class="card-body px-0 py-2" id="recording-data">
            <div class="col-md-12 p-0">
              <div class="form-group">
                <label for="name"> Name<font color="red">*</font></label>
                <div>
                  <input class="form-control" placeholder="Enter Resource Name" name="name" class="form-control"
                    id="name1" type="text" required="" maxlength="750"
                    data-parsley-error-message="Name cannot be empty" />
                </div>
              </div>

              <div class="form-group">
                <label>Description</label>
                <div>
                  <textarea name="description" id="description" class="form-control"></textarea>
                </div>
              </div>

              <div class="form-group">
                <label>Class<font color="red">*</font></label>
                <div>
                  <select id="grade" name="grade" required="" class="form-control input-md"
                    onchange="getSubjectsToAdd()" data-parsley-error-message="Class cannot be empty">
                    <option value="">Select Class</option>
                    <?php
                    foreach ($grades as $cs => $cl) {
                      echo "<option value='$cl->class_name'>$cl->class_name</option>";
                    }
                    ?>
                  </select>
                </div>
              </div>

              <div class="form-group">
                <label>Subject<font color="red">*</font></label>
                <div>
                  <select id="subject" name="subject" required="" class="form-control input-md"
                    data-parsley-error-message="Subject cannot be empty">
                    <option value="">Select Subject</option>
                  </select>
                </div>
              </div>

              <div class="form-group">
                <label for="resource_type" data-parsley-error-message="Resource type cannot be empty.">Resource Type
                  <font color="red">*</font>
                </label>
                <div>
                  <select type="text" data-type="" name="resource_type" onchange="showFields()"
                    class="form-control input-md" data-parsley-error-message="Cannot be empty." required=""
                    id="resource_type1">
                    <option value=''>Select Resource Type</option>
                    <option value='Image'>Image</option>
                    <option value='Video'>Video</option>
                    <option value='Audio'>Audio</option>
                    <option value='PDF'>PDF</option>
                    <option value='PPT'>PPT</option>
                    <option value='Text File'>Text File</option>
                    <option value='Video Link'>Video Link</option>
                    <option value='Hyper Link'>Hyper Link</option>
                    <option value='Other'>Other</option>
                  </select>
                </div>
              </div>
              <div id="upload">

                <div class="form-group">
                  <label>Upload Files</label>
                  <div>
                    <div id="uploader"></div>
                    <input id="fileName" type="text" class="form-control" readonly>
                    <label class="input-group-btn" style="width: 17%;">
                      <span class="btn btn-primary"
                        style="width: 7rem; margin-left: 1rem; border-radius: 0.45rem; margin-top:2px;">
                        <input type="file" name="selectFiles" id="selectFiles" style="display: none;"
                          data-parsley-id="32">
                        Browse
                      </span>
                    </label>
                    <span id="fileuploadError" style="color: red;"></span>
                  </div>
                  <span id="file-size">Max file size allowed is
                    <?php echo $resource_size ?>.
                  </span>

                </div>
                <div class="loader-background">
                  <div style="color:white;text-align:center;height: 100%;">
                    <i style="color:black;font-size: 50px; margin-top: 100px;" class="fa fa-spinner fa-spin"></i>
                    <br>
                    <span id="percent-span" style="color:black;font-size: 25px; margin-top: 100px;">0</span><span
                      style="color:black;font-size: 25px; margin-top: 100px;">&nbsp;of 100%</span>
                    <br>
                    <button id="cancel-btn" class="btn btn-sm btn-danger">Cancel</button>

                    <br>
                  </div>
                </div>
                <input type="hidden" name="location" id="location">
                <center>
                  <button type="button" id="start-upload" style="width: 9rem;" class="btn btn-primary"
                    disabled>Submit</button>
                  <button type="button" class="btn btn-danger" style="width: 9rem;" data-dismiss="modal">Close</button>
                </center>
              </div>
              <div id="append_field">
              </div>


            </div>
          </div>
        </form>
      </div>
    </div>
  </div>
</div>

<div class="visible-xs">
  <a href="<?php echo site_url('academics/academics_menu'); ?>" id="backBtn" onclick="loader()"><span
      class="fa fa-mail-reply"></span></a>
</div>




<style type="text/css">
  .new_circleShape_res {
    padding: 8px;
    border-radius: 50% !important;
    color: white !important;
    font-size: 22px;
    height: 3.2rem !important;
    width: 3.2rem !important;
    text-align: center;
    vertical-align: middle;
    border: none !important;
    box-shadow: 0px 3px 7px #ccc;
    line-height: 1.7rem !important;

  }

  .new_circleRes {
    padding: .4rem;
    border-radius: 50% !important;
    /* color: white !important; */
    font-size: 17px;
    height: 3.2rem !important;
    width: 3.2rem !important;
    text-align: center;
    vertical-align: middle;
    border: none !important;
    box-shadow: 0px 3px 7px #ccc;
    line-height: 1.7rem !important;
    background-color: white;
  }

  .new_circleResVidAud {
    padding: .4rem .7rem;
    border-radius: 50% !important;
    /* color: white !important; */
    font-size: 17px;
    height: 3.2rem !important;
    width: 3.2rem !important;
    text-align: center;
    vertical-align: middle;
    border: none !important;
    box-shadow: 0px 3px 7px #ccc;
    line-height: 1.7rem !important;
    background-color: white;
  }

  .new_circleShape_res1 {
    padding: 5px 8px;
    border-radius: 50% !important;
    font-size: 16px;
    height: 3.2rem !important;
    width: 3.2rem !important;
    text-align: center;
    vertical-align: middle;
    border: none !important;
    box-shadow: 0px 3px 7px #ccc;
    line-height: 0rem !important;
    background-color: white;
  }

  .loader-background {
    width: 100%;
    height: 100%;
    position: absolute;
    display: none;
    top: 0;
    left: 0;
    opacity: 0.8;
    z-index: 10;
    background-color: #fff;
    border-radius: 8px;
  }

  .editable,
  .select-editable,
  .select-editable1 {
    cursor: pointer;
  }

  .editable:hover,
  .select-editable:hover,
  .select-editable1:hover {
    font-weight: 500;
  }

  .editable::before,
  .select-editable::before,
  .select-editable1::before {
    font-family: "FontAwesome";
    content: "\f040";
    display: inline-block;
    padding-right: 5px;
    vertical-align: middle;
    font-weight: 900;
    position: absolute;
    right: 5px;
    color: #e04b4a;
  }

  #video-player {
    object-fit: cover;
    width: 100%;
    /* height: 500px; */
  }

  .switch_activate {
    position: relative;
    display: inline-block;
    width: 60px;
    height: 34px;
  }

  .switch_activate input {
    opacity: 0;
    width: 0;
    height: 0;
  }

  .slider_activate {
    position: absolute;
    cursor: pointer;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    background-color: #ccc;
    -webkit-transition: .4s;
    transition: .4s;
  }

  .slider_activate:before {
    position: absolute;
    content: "";
    height: 26px;
    width: 26px;
    left: 4px;
    bottom: 4px;
    background-color: white;
    -webkit-transition: .4s;
    transition: .4s;
  }

  input:checked+.slider_activate {
    background-color: #2196F3;
  }

  input:focus+.slider_activate {
    box-shadow: 0 0 1px #2196F3;
  }

  input:checked+.slider_activate:before {
    -webkit-transform: translateX(26px);
    -ms-transform: translateX(26px);
    transform: translateX(26px);
  }

  /* Rounded sliders */
  .slider_activate.round {
    border-radius: 34px;
  }

  .slider_activate.round:before {
    border-radius: 50%;
  }
</style>

<?php $this->load->view('academics/resources/__script.php'); ?>
<script type="text/javascript" src="<?php echo base_url(); ?>assets/js/plugins/jeditor/editable.js"></script>

<script type="text/javascript" src="<?php echo base_url('assets/js/plugins/moment.min.js') ?>"></script>
<script type="text/javascript"
  src="<?php echo base_url('assets/js/plugins/daterangepicker/daterangepicker.js') ?>"></script>
<script type="text/javascript">
  $("#reportrange").daterangepicker({
    ranges: {
      'Today': [moment(), moment()],
      'Yesterday': [moment().subtract(1, 'days'), moment().subtract(1, 'days')],
      'Last 7 Days': [moment().subtract(6, 'days'), moment()],
      'Last 30 Days': [moment().subtract(29, 'days'), moment()],
      'This Month': [moment().startOf('month'), moment().endOf('month')],
    },
    opens: 'right',
    buttonClasses: ['btn btn-default'],
    applyClass: 'btn-small btn-primary',
    cancelClass: 'btn-small',
    format: 'DD-MM-YYYY',
    separator: ' to ',
    startDate: moment().subtract(29, 'days'),
    endDate: moment()
  }, function (start, end) {
    $('#reportrange span').html(start.format('MMM D, YYYY') + ' - ' + end.format('MMM D, YYYY'));
    $('#from_date').val(start.format('DD-MM-YYYY'));
    $('#to_date').val(end.format('DD-MM-YYYY'));
  });

  $("#reportrange span").html(moment().subtract(29, 'days').format('MMM D, YYYY') + ' - ' + moment().format('MMM D, YYYY'));
  $('#from_date').val(moment().subtract(29, 'days').format('DD-MM-YYYY'));
  $('#to_date').val(moment().format('DD-MM-YYYY'));
</script>

<script>
  var is_resource_admin = 0;
  $(document).ready(function () {
    is_resource_admin = <?php echo $is_resource_admin; ?>;
  });

  function enableButton() {
    $("#get").prop('disabled', false);
  }

  function showFields() {
    var resource_type = $('#resource_type1').val();
    var resource_size = '<?php echo $resource_size ?>';
    var html = '';
    if (resource_type == 'Video Link') {
      $('#upload').hide();

      html += `<div class="form-group">
                <label for="resource_file"> Link<font color="red">*</font></label>
                  <div>
                      <input class="form-control" placeholder="Enter Video Link" name="resource_file" class="form-control" id="resource_file" type="text" required="" maxlength="750" data-parsley-error-message="Field cannot be empty"/>
                  </div>
                </div>
                  <center>
                    <button type="button" id="submit_link" onclick="submitResource()" style="width: 9rem; " class="btn btn-primary">Submit</button>     
                    <button type="button" class="btn btn-danger" style="width: 9rem; " data-dismiss="modal">Close</button>
                  </center>`;

    } else if (resource_type == 'Hyper Link') {
      $('#upload').hide();

      html += `<div class="form-group">
                      <label for="resource_file"> Link<font color="red">*</font></label>
                        <div>
                            <input class="form-control" placeholder="Enter Hyper Link" name="resource_file" class="form-control" id="resource_file" type="text" required="" maxlength="750" data-parsley-error-message="Field cannot be empty"/>
                        </div>
                      </div>
                        <center>
                          <button type="button" id="submit_link" onclick="submitResource()" style="width: 9rem; " class="btn btn-primary">Submit</button>     
                          <button type="button" class="btn btn-danger" style="width: 9rem; " data-dismiss="modal">Close</button>
                        </center>`;

    } else {
      $('#upload').show();
    }
    $('#append_field').html(html);
  }

  function submitResource() {
    var $form = $('#FileSaver');
    if ($form.parsley().validate()) {
      $("#exampleModal").modal('hide');
      var form = $('#FileSaver')[0];
      var formData = new FormData(form);
      console.log(formData);
      $.ajax({
        url: '<?php echo site_url('academics/resources/submit_resource'); ?>',
        type: 'post',
        data: formData,
        processData: false,
        contentType: false,
        success: function (data) {
          if (data) {
            Swal.fire({
              icon: "success",
              title: "Added resource",
              text: "Resources added successfully!",
            });

            $("#name1").val('');
            $("#grade").val('');
            $("#subject").val('');
            $("#resource_type1").val('');
            $("#resource_file").val('');
            $("#description").val('');
            $("#submit_link").hide();
          }
          else {
            Swal.fire({
              icon: "error",
              title: "Oops...",
              text: "Something went wrong!",
            });
          }
        }
      });
    }
  }



  function getSubjects() {
    var grade = $("#gradeView").val();
    var semester_field = $("#semester_id");

    var semester_id = 'all';
    if (semester_field.length)
      semester_id = semester_field.val();

    $.ajax({
      url: '<?php echo site_url('academics/resources/getSubjects'); ?>',
      data: { 'grade': grade, 'semester_id': semester_id },
      type: "post",
      success: function (data) {
        var data = $.parseJSON(data);
        var subjectsList = data.subjectsList;
        // var subjects_options='<option value="">Select Subject</option>';
        var subjects_options = '<option value="all">All</option>';
        for (var i = 0; i < subjectsList.length; i++) {
          subjects_options += '<option value="' + subjectsList[i].subject_id + '">' + subjectsList[i].subject_name + '</option>';
        }
        $("#subjectView").html(subjects_options);
      },
      error: function (err) {
        console.log(err);
      }
    });
  }

  function getStaff() {
    var grade = $("#gradeView").val();
    $.ajax({
      url: '<?php echo site_url('academics/resources/getStaff'); ?>',
      data: { 'grade': grade },
      type: "post",
      success: function (data) {
        var data = $.parseJSON(data);
        var staff = data.staff;
        // var staff_options='<option value="">Select Created By</option>';
        staff_options = '<option value="all">All</option>';
        for (var i = 0; i < staff.length; i++) {
          staff_options += '<option value="' + staff[i].created_by + '">' + staff[i].friendly_name + '</option>';
        }
        $("#created_by").html(staff_options);
      },
      error: function (err) {
        console.log(err);
      }
    });
  }

  function getSubjectsToAdd() {
    var grade = $("#grade").val();
    $.ajax({
      url: '<?php echo site_url('academics/resources/getSubjects'); ?>',
      data: { 'grade': grade },
      type: "post",
      success: function (data) {
        var data = $.parseJSON(data);
        var subjectsList = data.subjectsList;
        var subjects_options = '<option value="">Select Subject</option>';
        for (var i = 0; i < subjectsList.length; i++) {
          subjects_options += '<option value="' + subjectsList[i].subject_id + '">' + subjectsList[i].subject_name + '</option>';
        }
        $("#subject").html(subjects_options);
      },
      error: function (err) {
        console.log(err);
      }
    });
  }


  function showRecording(resource_id) {
    $.ajax({
      url: '<?php echo site_url('academics/resources/getResourceToPlay'); ?>',
      type: 'post',
      data: { 'resource_id': resource_id },
      beforeSend: function () {
        $('#opacity').css('opacity', '0.5');
        $('#loader').show();
      },
      success: function (data) {
        var data = $.parseJSON(data);
        var resource = data.resource;
        if (resource != '' && resource != undefined && resource != null) {
          var video = '<video id="video-player" controls controlsList="nodownload">';
          video += '<source src="' + resource + '" type="video/mpeg">';
          video += '<source src="' + resource + '" type="video/mp4">';
          video += 'Your browser does not support the video tag.';
          video += '</video>';
          $("#uploaded").html(video);
        }
      },
      complete: function () {
        $('#loader').hide();
        $('#opacity').css('opacity', '');
      }
    });
    $("#video-data").modal('show');
  }


  function showAudio(resource_id) {
    $.ajax({
      url: '<?php echo site_url('academics/resources/getResourceToPlay'); ?>',
      type: 'post',
      data: { 'resource_id': resource_id },
      beforeSend: function () {
        $('#opacity').css('opacity', '0.5');
        $('#loader').show();
      },
      success: function (data) {
        var data = $.parseJSON(data);
        var resources = data.resources;
        if (resources[0].path != '' && resources[0].path != undefined && resources[0].path != null) {
          var audio = '<audio id="audio-player" controls controlsList="nodownload">';
          audio += '<source src="' + resources[0].path + '" type="audio/ogg">';
          audio += '<source src="' + resources[0].path + '" type="audio/mpeg">';
          audio += '<source src="' + resources[0].path + '" type="audio/mp3">';
          audio += 'Your browser does not support the audio tag.';
          audio += '</audio>';
          $("#audio1").html(audio);
        }
      },
      complete: function () {
        $('#loader').hide();
        $('#opacity').css('opacity', '');
      }
    });
    $("#audio-data").modal('show');

  }

  function showVimeoVideo(vimeo_id) {
    document.getElementById('resourceVideo').src = "https://player.vimeo.com/video/" + vimeo_id;
    $("#youtube-data").modal('show');
  }

  function showYouTubeVideo(resource_id) {
    $.ajax({
      url: '<?php echo site_url('academics/resources/getYouTubeVideo'); ?>',
      type: 'post',
      data: { 'resource_id': resource_id },
      beforeSend: function () {
        $('#opacity').css('opacity', '0.5');
        $('#loader').show();
      },
      success: function (data) {
        var data = $.parseJSON(data);
        var resource = data.resource;
        var path = resource.trim();
        if (path != '' && path != undefined && path != null) {
          var video_id = getVideoId(path);
          var embed_path = "https://www.youtube.com/embed/" + video_id;
          document.getElementById('resourceVideo').src = embed_path;

        }
        /*if(resources[0].path != '' && resources[0].path != undefined && resources[0].path != null) {
          
            document.getElementById('resourceVideo').src= (resources[0].path).trim();
           
        }*/
      },
      complete: function () {
        $('#loader').hide();
        $('#opacity').css('opacity', '');
      }
    });
    $("#youtube-data").modal('show');
  }

  function getVideoId(url) {
    const regExp = /^.*(youtu.be\/|v\/|u\/\w\/|embed\/|watch\?v=|&v=)([^#&?]*).*/;
    const match = url?.match(regExp);

    return (match && match[2].length === 11)
      ? match[2]
      : null;
  }

  function pauseVideo() {
    var vid = document.getElementById("video-player");
    if (vid != null || vid != undefined)
      vid.pause();
    $("#video-data").modal('hide');
  }

  function pauseAudio() {
    var audio = document.getElementById("audio-player");
    if (audio != null || audio != undefined)
      audio.pause();
    $("#audio-data").modal('hide');
  }


  function pauseYouTubeVideo() {
    $('#resourceVideo').attr('src', '');
  }



  function fillSubjects() {
    var grade = $("#grade option:selected").val();
    $.ajax({
      url: '<?php echo site_url('academics/resources/getSubjectsByGrade'); ?>',
      data: { 'grade': grade },
      type: "post",
      success: function (data) {
        data = $.parseJSON(data);
        subjectData = data.subjectData;

        var output = '';
        output += '<option value="">Select Subject</option>';

        output += '<option value="all">All</option>';
        for (var i = 0; i < subjectData.length; i++) {
          output += '<option   value="' + subjectData[i].subject + '">' + subjectData[i].subject + '</option>';
        };
        $('#subject').html(output);
      },
      error: function (err) {
        console.log(err);
      }
    });
  };

  function getResources() {
    var grade = $('#gradeView').val();
    if(!grade) {
      Swal.fire({
        icon: 'error',
        title: 'Oops...',
        text: 'Please select Class!',
      });
      return false;
    };
    
    $("#get").text("Please wait...").prop("disabled", true);

    $('#displayResourcePanel').html('');
    if (grade == '') {
      return false;
    }
    var subject = $('#subjectView').val();
    var resourceType = $('#resourceView').val();
    var created_by = $('#created_by').val();
    var status = $('#resource_status').val();
    var from_date = $('#from_date').val();
    var to_date = $('#to_date').val();
    $.ajax({
      url: '<?php echo site_url('academics/resources/getResources') ?>',
      type: 'post',
      data: {
        'from_date': from_date,
        'to_date': to_date,
        'grade': grade,
        'subject': subject,
        'created_by': created_by,
        'resource_type': resourceType,
        'resource_status': status
      },
      success: function (data) {
        $("#get").text("Get Resources").prop("disabled", false);

        data = $.parseJSON(data);
        viewResources = data.viewResources;
        base_url = data.base_url;
        $('#displayResourcePanel').html(makeResourceTable(viewResources, base_url));
        after_call(viewResources);
      },
      error: function (err) {
        console.log(err);
      }
    });
  }


  function getData() {
    var grade = $('#gradeView').val();
    var subject = $('#subjectView').val();
    var resourceType = $('#resourceView').val();
    var created_by = $('#created_by').val();

    if (subject == 'all' && resourceType == 'all' && created_by == 'all') {
      viewAllResourcesByGrade(grade);
    }
    else if (subject == 'all' && resourceType == 'all') {
      viewResourcesByGradeCreatedBy(grade, created_by);
    } else if (subject == 'all' && created_by == 'all') {
      viewResourcesByGradeResourceType(grade, resourceType);
    }
    else if (resourceType == 'all' && created_by == 'all') {
      viewResourcesByGradeSubject(grade, subject);
    }
    else if (subject == 'all') {
      viewResourcesByGradeResourceTypeCreatedBy(grade, resourceType, created_by);
    } else if (resourceType == 'all') {
      viewResourcesByGradeSubjectCreatedBy(grade, subject, created_by);
    } else if (created_by == 'all') {
      viewResourcesByGradeSubjectResourceType(grade, subject, resourceType);
    } else {
      viewResourcesByGradeSubjectTypeCreatedBy(grade, subject, created_by, resourceType);
    }
  }

  function viewAllResourcesByGrade(grade) {
    $.ajax({
      url: '<?php echo site_url('academics/resources/viewAllResourcesByGrade') ?>',
      type: 'post',
      data: {
        'grade': grade,
      },
      success: function (data) {
        data = $.parseJSON(data);
        viewResources = data.viewResources;
        // console.log(viewResources);
        base_url = data.base_url;
        $('#displayResourcePanel').html(makeResourceTable(viewResources, base_url));
        after_call(viewResources);

      },
      error: function (err) {
        console.log(err);
      }
    });
  }

  function viewResourcesByGradeCreatedBy(grade, created_by) {
    $.ajax({
      url: '<?php echo site_url('academics/resources/viewResourcesByGradeCreatedBy') ?>',
      type: 'post',
      data: {
        'grade': grade,
        'created_by': created_by
      },
      success: function (data) {
        data = $.parseJSON(data);
        viewResources = data.viewResources;
        base_url = data.base_url;
        $('#displayResourcePanel').html(makeResourceTable(viewResources, base_url));
        after_call(viewResources);

      },
      error: function (err) {
        console.log(err);
      }
    });
  }

  function viewResourcesByGradeResourceType(grade, resourceType) {
    $.ajax({
      url: '<?php echo site_url('academics/resources/viewResourcesByGradeResourceType') ?>',
      type: 'post',
      data: {
        'grade': grade,
        'resourceType': resourceType
      },
      success: function (data) {
        data = $.parseJSON(data);
        viewResources = data.viewResources;
        base_url = data.base_url;
        $('#displayResourcePanel').html(makeResourceTable(viewResources, base_url));
        after_call(viewResources);

      },
      error: function (err) {
        console.log(err);
      }
    });
  }

  function viewResourcesByGradeSubject(grade, subject) {
    $.ajax({
      url: '<?php echo site_url('academics/resources/viewResourcesByGradeSubject') ?>',
      type: 'post',
      data: {
        'grade': grade,
        'subject': subject
      },
      success: function (data) {
        data = $.parseJSON(data);
        viewResources = data.viewResources;
        base_url = data.base_url;
        $('#displayResourcePanel').html(makeResourceTable(viewResources, base_url));
        after_call(viewResources);

      },
      error: function (err) {
        console.log(err);
      }
    });
  }

  function viewResourcesByGradeResourceTypeCreatedBy(grade, resourceType, created_by) {
    $.ajax({
      url: '<?php echo site_url('academics/resources/viewResourcesByGradeResourceTypeCreatedBy') ?>',
      type: 'post',
      data: {
        'grade': grade,
        'resourceType': resourceType,
        'created_by': created_by
      },
      success: function (data) {
        data = $.parseJSON(data);
        viewResources = data.viewResources;
        base_url = data.base_url;
        $('#displayResourcePanel').html(makeResourceTable(viewResources, base_url));
        after_call(viewResources);

      },
      error: function (err) {
        console.log(err);
      }
    });
  }

  function viewResourcesByGradeSubjectCreatedBy(grade, subject, created_by) {
    $.ajax({
      url: '<?php echo site_url('academics/resources/viewResourcesByGradeSubjectCreatedBy') ?>',
      type: 'post',
      data: {
        'grade': grade,
        'subject': subject,
        'created_by': created_by
      },
      success: function (data) {
        data = $.parseJSON(data);
        viewResources = data.viewResources;
        base_url = data.base_url;
        $('#displayResourcePanel').html(makeResourceTable(viewResources, base_url));
        after_call(viewResources);

      },
      error: function (err) {
        console.log(err);
      }
    });
  }

  function viewResourcesByGradeSubjectResourceType(grade, subject, resourceType) {
    $.ajax({
      url: '<?php echo site_url('academics/resources/viewResourcesByGradeSubjectResourceType') ?>',
      type: 'post',
      data: {
        'grade': grade,
        'subject': subject,
        'resourceType': resourceType
      },
      success: function (data) {
        data = $.parseJSON(data);
        viewResources = data.viewResources;
        base_url = data.base_url;
        $('#displayResourcePanel').html(makeResourceTable(viewResources, base_url));
        after_call(viewResources);

      },
      error: function (err) {
        console.log(err);
      }
    });
  }

  function viewResourcesByGradeSubjectTypeCreatedBy(grade, subject, created_by, resourceType) {
    $.ajax({
      url: '<?php echo site_url('academics/resources/viewResourcesByGradeSubjectTypeCreatedBy') ?>',
      type: 'post',
      data: {
        'grade': grade,
        'subject': subject,
        'created_by': created_by,
        'resourceType': resourceType
      },
      success: function (data) {
        data = $.parseJSON(data);
        viewResources = data.viewResources;
        // console.log(viewResources);
        base_url = data.base_url;
        $('#displayResourcePanel').html(makeResourceTable(viewResources, base_url));
        after_call(viewResources);

      },
      error: function (err) {
        console.log(err);
      }
    });
  }

  function makeResourceTable(resourceData, base_url) {
    // console.log(resourceData);
    var output = '';
    var checked = '';
    var fntClr = '#dc3545';

    if (resourceData.length == 0) {
      let msg = `
          <div style="color:red;text-align:center;
            color: black;
            border: 2px solid #fffafa;
            text-align: center;
            border-radius: 6px;
            position: relative;
            padding: 10px;
            font-size: 14px;
            margin-top: 14px;
            background: #ebf3ff;">
              No Resources Added
            </div>
          `;
      output += msg;
    }
    else {


      for (i = 0; i < resourceData.length; i++) {
        fntClr = '#dc3545';
        if (resourceData[i].status == 'Active') {
          checked = 'checked';
          fntClr = '#6893ca';
        }

        output += `
        <div class="col-xs-12 p-0 mb-3">
          <div class="unread_box_no_style">
            <div class="row mx-0 mb-2">
              <div class="col-xs-2 pl-0">
                <div class="new_circleShape" style="background-color: #fe970a;">
                  <span style="vertical-align: middle;">${resourceData[i].grade}</span>
                </div>
              </div>
              <div class="col-xs-10 pr-0 d-flex align-items-center">
                <h4 >${resourceData[i].subject_name}</h4>
              </div>
            </div>
            <h4 id="${resourceData[i].id}" class="editable">${resourceData[i].name}</h4>
            <div style="max-height:6rem;overflow-y:auto;">
              <h5>
                ${resourceData[i].description}
              </h5>
            </div>

            <div class="row mx-0 mt-4">
              <div class="col-xs-5 pl-0 d-flex align-items-center"><h5 class="select-editable" id="${resourceData[i].id}" >${resourceData[i].resource_type}</h5></div>
              <div class="col-xs-7 pl-0 pr-0 d-flex align-items-center justify-content-end">
                <span style="font-size:20px;color:${fntClr};font-weight:700;">${resourceData[i].status} &nbsp;&nbsp;&nbsp;</span>`;
        if (is_resource_admin) {
          output += `<label class="switch_activate pull-right mb-0">
                  <input type="checkbox" id="status_${resourceData[i].id}" ${checked} onchange="updateResourceStatusChange(${resourceData[i].id})">
                  <span class="slider_activate round"></span>
                </label>`;
        }
        output += `</div>
            </div>
            <div class="row mx-0 mt-2">
              <div class="col-xs-6 pl-0">
                <h5><b>Created by:</b> <br><span style="color:#EC8100;">${resourceData[i].created_by}</span></h5>
              </div>
              <div class="col-xs-6">
                <h5><b>Created on:</b> <br><span style="color:#EC8100;">${resourceData[i].created_on}</span></h5>                
              </div>
            </div>
            <div class="row mx-0 mt-3">
              <div class="col-xs-3 pl-0"><h5>Action: </h5></div>`;


        var file_name = '';
        file_name = resourceData[i].resource_file;
        var newString = '';
        var onClk = '';
        newString = file_name.slice(-3);
        downloadUrl = "<?php echo site_url('academics/resources/downloadSignedURLResource/') ?>" + resourceData[i].id;

        if (resourceData[i].status == 'Active') {
          if (resourceData[i].resource_type == 'Audio') {
            output += `
                <div class="col-xs-9 p-0">
                  <a  href="${downloadUrl}" class="new_circleShape_res1" data-placement="top" data-toggle="tooltip" data-original-title="Download Resource">
                    <span class="fa fa-download" style="color:#fe970a;"></span>
                  </a>
                </div>
          `;
          }
          else if (resourceData[i].resource_type == 'Video') {

            output += `
                <div class="col-xs-9 p-0">
                  <a  href="${downloadUrl}" class="new_circleShape_res1" data-placement="top" data-toggle="tooltip" data-original-title="Download Resource">
                    <span class="fa fa-download" style="color:#fe970a;"></span>
                  </a>
                </div>
          `;
          }
          else if (resourceData[i].resource_type == 'Vimeo') {
            output += `<div class="col-xs-9 p-0">
                  <a onclick="showVimeoVideo(${resourceData[i].resource_file})" class="new_circleRes" data-placement="top" data-toggle="tooltip" data-original-title="View Resource">
                    <span class="fa fa-eye ml-1" style="color:#428bca;"></span>
                  </a>&nbsp; 
                </div>`;

          }
          else if (resourceData[i].resource_type == 'PDF' || resourceData[i].resource_type == 'PPT' || resourceData[i].resource_type == 'Text File') {
            output += `
                <div class="col-xs-9 p-0">
                  <a  href="${downloadUrl}" class="new_circleShape_res1" data-placement="top" data-toggle="tooltip" data-original-title="Download Resource">
                    <span class="fa fa-download" style="color:#fe970a;"></span>
                  </a>
                </div>
          `;
          }
          else if (resourceData[i].resource_type == 'Video Link') {
            // url = resourceData[i].signed_url;
            output += `<div class="col-xs-9 p-0">
                  <a onclick="showYouTubeVideo(${resourceData[i].id})" class="new_circleRes" data-placement="top" data-toggle="tooltip" data-original-title="View Resource">
                    <span class="fa fa-eye ml-1" style="color:#428bca;"></span>
                  </a>&nbsp; 
                </div>`;

          }
          else if (resourceData[i].resource_type == 'Hyper Link') {
            // url = resourceData[i].resource_file;
            output += `<div class="col-xs-9 p-0">
                  <a href= "` + resourceData[i].resource_file + `" target="new" class="new_circleRes" data-placement="top" data-toggle="tooltip" data-original-title="View Resource">
                    <span class="fa fa-external-link ml-1" style="color:#428bca;"></span>
                  </a>&nbsp; 
                </div>`;
          } else {

            output += `
                <div class="col-xs-9 p-0">
                  <a  href="${downloadUrl}" class="new_circleShape_res1" data-placement="top" data-toggle="tooltip" data-original-title="Download Resource">
                    <span class="fa fa-download" style="color:#fe970a;"></span>
                  </a>
                </div>
          `;
          }

        }

        else {
          output += `
                <div class="col-xs-9 p-0">
                  <a  href="${downloadUrl}" class="new_circleShape_res1" data-placement="top" data-toggle="tooltip" data-original-title="Download Resource" disabled>
                    <span class="fa fa-download" style="color:#ccb79b;"></span>
                  </a>
                </div>`;
        }

        output += `</div>
            </div>
          </div>`;

      }

    }
    return output;
  }


  function after_call(classes) {
    $(".editable").editable("<?php echo site_url('academics/resources/updateResourceData'); ?>", {
      tooltip: "Click to edit...",
      indicator: '<i class="fa fa-spinner fa-spin" style="font-size:20px"></i>',
      inputcssclass: 'form-control',
      // submitdata : {'resource_id': resource_id},
      width: '50%'
    });
    $(".select-editable").editable("<?php echo site_url('academics/resources/updateResourceTypeData'); ?>", {
      tooltip: "Click to edit...",
      type: "select",
      data: {
        'default': 'Select Type',
        'Image': 'Image',
        'Video': 'Video',
        'Audio': 'Audio',
        'PDF': 'PDF',
        'PPT': 'PPT',
        'Text File': 'Text File',
        'Other': 'Other'
      },
      indicator: '<i class="fa fa-spinner fa-spin" style="font-size:20px"></i>',
      inputcssclass: 'form-control',

      // submitdata : {'resource_id': resource_id},
      width: '100%'
    });

  }

  function updateResourceStatusToInactive(resourceId) {
    $.ajax({
      url: '<?php echo site_url('academics/resources/updateResourceStatusToInactive') ?>',
      type: 'post',
      data: {
        'resourceId': resourceId
      },
      success: function (data) {
        // var url = '<?php // echo site_url('academics/resources/viewResourcesPage') ?>';
        getResources();
      }
    });
  }

  function updateResourceStatusToActive(resourceId) {
    $.ajax({
      url: '<?php echo site_url('academics/resources/updateResourceStatusToActive') ?>',
      type: 'post',
      data: {
        'resourceId': resourceId
      },
      success: function (data) {
        // var url = '<?php // echo site_url('academics/resources/viewResourcesPage') ?>';
        getResources();
      }
    });
  }

  function updateResourceStatusChange(resourceId) {
    if ($("#status_" + resourceId).is(':checked')) {
      updateResourceStatusToActive(resourceId);
    } else {
      updateResourceStatusToInactive(resourceId);
    }
  }
</script>