<script type="text/javascript" src="<?php echo site_url('assets/js/plugins/morris/raphael-min.js') ?>"></script>
<script type="text/javascript" src="<?php echo site_url('assets/js/plugins/morris/morris.min.js') ?>"></script>

<script type="text/javascript" src="<?php echo site_url('assets/js/morris.js') ?>"></script>
<script type="text/javascript" src="https://www.gstatic.com/charts/loader.js"></script>
<?php $this->load->view("msm_v3/scripts/color_theme") ?>

<script>
    var json_school_list = '<?php echo $json_school_list ?>';
    var school_list = JSON.parse(json_school_list);
    var default_acad_year = sessionStorage.getItem("msm_acad_year") || '<?= $acad_year ?>';
    const selectElement = document.getElementById('acad_select');
    selectElement.value = default_acad_year;
    const overall_admission_array = [];
    const overall_leads_array = [];
    const overall_statistics = [];

    function waitOneSecondSync() {
        return new Promise(resolve => {
            setTimeout(() => {
                resolve();
            }, 400);
        });
    }

    $(document).ready(async function () {
        google.charts.load("current", {packages: ["corechart", "bar"]});
        $(".boarding_tb_btn").css("display", "block");
        $(".overview_tb_dropdown").css("display", "block");
        $(".overview_tb_btn").css("display", "none");

        load_theme();
        display_student_boarding_count_data(school_list, default_acad_year);
        await waitOneSecondSync();
        display_boarding_statistics_widget(school_list, default_acad_year, type = "Bar");
        await waitOneSecondSync();
        display_schools_dropdown_boarding(school_list, default_acad_year);
        display_boarding_nationalitywise_widget(school_list, default_acad_year);
    });

    selectElement.addEventListener('change', async function() {
        default_acad_year = selectElement.value;
        sessionStorage.setItem("msm_acad_year", selectElement.value);
        display_student_boarding_count_data(school_list, default_acad_year);
        await waitOneSecondSync();
        display_boarding_statistics_widget(school_list, default_acad_year, type = "Bar");
        await waitOneSecondSync();
        display_schools_dropdown_boarding(school_list, default_acad_year);
        display_boarding_nationalitywise_widget(school_list, default_acad_year);
    });

    function flip_boarding_view(type, id, button){
        const buttonGroup = button.closest('.btn-group-sm');
        const buttons = buttonGroup.querySelectorAll('.btn');
        buttons.forEach(btn => btn.classList.remove('highlight'));
        button.classList.add('highlight');
        switch(id){
            case 'boarding_type_card':
                switch (type) {
                    case 'column_chart':
                        $('#boarding_type_pie_card').css('display', 'block');
                        $('#boarding_type_table_card').css('display', 'none');
                        break;
                    case 'table':
                        $('#boarding_type_table_card').css('display', 'block');
                        $('#boarding_type_pie_card').css('display', 'none');
                        break;
                    default:
                        break;
                }
                break;
            case 'boarding_statistics_card':
                switch (type) {
                    case 'column_chart':
                        $('#boarding_statistics_pie_card').css('display', 'block');
                        $('#boarding_statistics_table_card').css('display', 'none');
                        break;
                    case 'table':
                        $('#boarding_statistics_table_card').css('display', 'block');
                        $('#boarding_statistics_pie_card').css('display', 'none');
                        break;
                    default:
                        break;
                }
                break;
        }
    }

    /* Display Student Boarding Count*/
    function display_student_boarding_count_data(school_list, acad_year) {
        disableTableButtons();
        $('#boarding_type_graph').html('<center>Loading Boarding Count...</center>');
        $('#boarding_table').html('<tr><td colspan="4">Loading Boarding Count...</td></tr>');
        var data_array = [];
        var total_count = 0;
        const mapLoop = async () => {
            const promises = await school_list.map(async school => {
            const num_promise = await get_student_boarding_count_data(school.school_code, school.school_domain, acad_year)
                .then(response => {
                if (!response) return true;
                data = response.data;
                var female_count = 0, male_count = 0;
                data.forEach(function(item) {
                    var gender = item.gender;
                    var count = parseInt(item.boarding_count);
                    if (gender === 'F') {
                        female_count += count; 
                    } else if (gender === 'M') {
                        male_count += count; 
                    }
                })
                total_count += female_count + male_count;
                data_array.push([school.school_code, male_count, female_count, total_count])
                })
            })
            await Promise.all(promises);
            var temp_array = [];
            school_list.forEach(sl => {
            data_array.forEach(ola => {
                if (sl.school_code == ola[0]) {
                temp_array.push(ola);
                return false;
                }
            });
            });
            $("#boarding_count").html(total_count);
        _construct_student_boarding_count_view(temp_array.slice());
        _construct_individual_boarding_table(temp_array)
        }
        mapLoop();
    }

    function get_student_boarding_count_data(school_code, school_domain, acad_year) {
        return new Promise(function(resolve, reject) {
            $.ajax({
                url: '<?php echo base_url("msm_v3/dashboard/bridge") ?>',
                type: 'post',
                data: {
                    'school_code': school_code,
                    'school_domain': school_domain,
                    'acad_year': acad_year,
                    'api': 'get_student_boarding_count_data'
                },
                success: function(data) {
                    data = JSON.parse(data);
                    if (data.status == 0) reject(data.message);
                    resolve(JSON.parse(data.response));
                },
                error: function(err) {
                    reject(err);
                }
            });
        });
    }

    function _construct_student_boarding_count_view(input_array, sc) {
        var chartData = [['Institution', 'Male Count', 'Female Count', 'Total Count']];

        input_array.forEach(item => {
            chartData.push([item[0].toUpperCase(), parseInt(item[1]), parseInt(item[2]), parseInt(item[3])]);
        });
        google.charts.setOnLoadCallback(function()  {
            var data = google.visualization.arrayToDataTable(chartData);

            var options = {
            height: 450,
            chartArea: {left: '15%', top: '15%', width: '70%', bottom: '10%', height: '100%'},
            isStacked: "true",
            orientation: 'vertical',
            hAxis: {
                title: 'Gender Count',
                titleTextStyle: {
                color: '#1450A3',
                fontName: 'Arial',
                fontSize: '16',
                bold: true,
                italic: false
                },
                bar: { groupWidth: "90%" },
                axisTitlesPosition: 'in',
                curveType: 'function',
                legend: {
                position: 'none'
                },
            },
            vAxis: {
                title: 'Instituition',
                titleTextStyle: {
                color: '#1450A3',
                fontName: 'Arial',
                fontSize: '16',
                bold: true,
                italic: false
                },
            },
            annotations: {
                alwaysOutside: false,
                textStyle: {
                fontSize: 10,
                bold: true
                },
            },
            series: {
                0: { 
                color: boys_color,
                areaOpacity: 0.85,
                visibleInLegend: true,
                annotations: {
                        stem: {
                        color: 'transparent',
                        length: 16
                        }
                    },
                },
                1: { 
                color: girls_color,
                areaOpacity: 0.85,
                visibleInLegend: true,
                annotations: {
                        stem: {
                        color: 'transparent',
                        length: 16
                        }
                    },
                }
            },
            legend: {
                position: "top",
                alignment: "center",
            },
            };

            var view = new google.visualization.DataView(data);
            view.setColumns([
            0,
            1,{
                sourceColumn: 1,
                type: "number",
                role: "annotation" 
            },
            2,
            { 
                sourceColumn: 2,
                type: "number",
                role: "annotation" 
            }
            ]);

            var chart = new google.visualization.ColumnChart(document.getElementById('boarding_type_graph'));
            chart.draw(view, options);
        });
        var html = `<div style="display: flex; margin-right: 10px; align-items: center">
                    <div style="width: 15px; height: 15px; background-color: ${boys_color}; display: inline-block; border-radius: 50%;"></div>
                    <span style="margin-left: 5px;">Male</span>
                    <div style="width: 15px; height: 15px; background-color: ${girls_color}; display: inline-block; border-radius: 50%; margin-left: 5%"></div>
                    <span style="margin-left: 5px;">Female</span>
                    </div>`;
        $("#boarding_type_legend_div").html(html);
    }

    function _construct_individual_boarding_table(input_array, sc) {
        enableTableButtons();
        var html = '';
        var i = 1;
        input_array.forEach(obj => {
            html += `
                <tr>
                    <td class="text-uppercase align-middle text-center text-sm">${i++}</td>
                    <td class="text-uppercase align-middle text-center text-sm">${obj[0]}</td>
                    <td class="text-uppercase align-middle text-center text-sm">${obj[1]}</td>
                    <td class="text-uppercase align-middle text-center text-sm">${obj[2]}</td>
                </tr>
            `;
        });
        $(`#boarding_table`).html(html);
    }

    /* Boarding Statistics*/
    function display_boarding_statistics_widget(school_list, acad_year, type = "Bar") {
        disableTableButtons();
        $("#boarding_statistics_graph").html("<center>Loading Boarding Statistics...</center>");
        //Call each school's data
        const mapLoop = async () => {
        const overall_boarding_array = []; // Define overall_boarding_array outside the mapLoop function scope
        const promises =await (school_list.map(async (school) => {
            const response = await get_boarding_statistics_data(school.school_code, school.school_domain, acad_year);
            const boardingCounts = {}; // Use an object to store boarding counts for each school
            response.forEach((data) => {
                const boardingType = data.boarding;
                const count = parseInt(data.boarding_count);
                boardingCounts[boardingType] = (boardingCounts[boardingType] || 0) + count;
            });
            const schoolData = [school.school_code, ...Object.values(boardingCounts)];
            overall_boarding_array.push(schoolData);
        }));

        await Promise.all(promises);

        // Arrange in a sequence
        const temp_array = [];
        school_list.forEach((sl) => {
            overall_boarding_array.forEach((ola) => {
                if (sl.school_code === ola[0]) {
                    temp_array.push(ola);
                    return false;
                }
            });
        });
        _construct_boarding_statistics_view(temp_array, type);
        _construct_boarding_statistics_table(temp_array);
        };

        mapLoop();

    }

    function get_boarding_statistics_data(school_code, school_domain, acad_year) {
        return new Promise(function (resolve, reject) {
        $.ajax({
            url: '<?php echo base_url("msm_v3/dashboard/bridge") ?>',
            type: "post",
            data: {
            school_code: school_code,
            school_domain: school_domain,
            acad_year: acad_year,
            api: "get_boarding_statistics_data",
            },
            success: function (data) {
            data = JSON.parse(data);
            if (data.status == 0) reject(data.message);
            resolve(JSON.parse(data.response));
            },
            error: function (err) {
            reject(err);
            },
        });
        });
    }

    function _construct_boarding_statistics_view(input_array, type) {
        const display_array = [];
        display_array.push(["School Code", "Day Scholar", "Regular Boarder", "Weekly Boarder"]);

        input_array.forEach((obj) => {
        display_array.push([obj[0].toUpperCase(), parseInt(obj[1]), parseInt(obj[2]) ? parseInt(obj[2]) : 0, parseInt(obj[3]) ? parseInt(obj[3]) : 0]);
        });
        console.log(display_array);

        google.charts.setOnLoadCallback(function()  {
            var data = google.visualization.arrayToDataTable(display_array);

            var options = {
            height: 300,
            chartArea: {left: '15%', top: '15%', width: '70%', bottom: '20%', height: '80%'},
            hAxis: {
                title: 'Institutions',
                titleTextStyle: {
                color: '#1450A3',
                fontName: 'Arial',
                fontSize: '16',
                bold: true,
                italic: false
                },
                bar: { groupWidth: "90%" },
                axisTitlesPosition: 'in',
                curveType: 'function',
                legend: {
                position: 'none'
                },
            },
            vAxis: {
                title: 'Boarding Count',
                titleTextStyle: {
                color: '#1450A3',
                fontName: 'Arial',
                fontSize: '16',
                bold: true,
                italic: false
                },
            },
            annotations: {
                alwaysOutside: true,
                textStyle: {
                fontSize: 11,
                bold: true
                },
            },
            series: {
                0: { 
                color: boys_color,
                areaOpacity: 0.25,
                visibleInLegend: true
                },
                1: { 
                color: girls_color,
                areaOpacity: 0.25,
                visibleInLegend: true
                },
                2: { 
                color: color1,
                areaOpacity: 0.25,
                visibleInLegend: true,
                }
            },
            legend: {
                position: "top",
                alignment: "center",
            },
            };

            var view = new google.visualization.DataView(data);
            view.setColumns([
            0,
            1,{
                sourceColumn: 1,
                type: "number",
                role: "annotation" 
            },
            2,
            { 
                sourceColumn: 2,
                type: "number",
                role: "annotation" 
            },
            3,
            { 
                sourceColumn: 3,
                type: "number",
                role: "annotation" 
            }
            ]);

            var chart = new google.visualization.ColumnChart(document.getElementById('boarding_statistics_graph'));
            chart.draw(view, options);
        });
        var html = `<div style="display: flex; margin-right: 10px; align-items: center">
                    <div style="width: 15px; height: 15px; background-color: ${boys_color}; display: inline-block; border-radius: 50%;"></div>
                    <span style="margin-left: 5px;">Day Scholar</span>
                    <div style="width: 15px; height: 15px; background-color: ${girls_color}; display: inline-block; border-radius: 50%; margin-left: 5%"></div>
                    <span style="margin-left: 5px;">Regular Boarder</span>
                    <div style="width: 15px; height: 15px; background-color: ${color1}; display: inline-block; border-radius: 50%; margin-left: 5%"></div>
                    <span style="margin-left: 5px;">Weekly Boarder</span>
                    </div>`;
        $("#boarding_statistics_legend_div").html(html);
    }

    function _construct_boarding_statistics_table(input_array){
        enableTableButtons();
        var html = '';
        let i = 1;
        input_array.forEach(function (item) {
            html += `
                <tr>
                    <td>${i}</td>
                    <td class="text-uppercase">${item[0]}</td>
                    <td>${item[1] ? item[1] : 0}</td>
                    <td>${item[2] ? item[2] : 0}</td>
                    <td>${item[3] ? item[3] : 0}</td>
                </tr>
            `;
            i++;
        });
        $("#boarding_statistics_table").html(html);
    }

    /* Nationality-Wise Table*/
    function display_schools_dropdown_boarding(school_list, acad_year) {
        let html = '';
        school_list.forEach((school) => {
        html += `<option value="${school.school_code}-${school.school_domain}">${school.school_name}</option>`;
        });
        const dropdowns = ['dropdownSchool_boarding'];
        dropdowns.forEach((dropdownId) => {
        const dropdown = document.getElementById(dropdownId);
        //If relevant permission is not there, then the dropdown object is not available. hence adding a check.
        if (dropdown){
            dropdown.innerHTML = html;
            dropdown.addEventListener('change', (event) => change_graph_boarding(event, acad_year));
            dropdown.addEventListener('change', change_graph_boarding);
        }
        });
    }

    function change_graph_boarding(event, acad_year) {
        const dropdown = event.target;
        const selectedOptions = Array.from(dropdown.selectedOptions);
        const selectedSchools = selectedOptions.map(option => {
        const [schoolCode, schoolDomain] = option.value.split('-');
        return { school_code: schoolCode, school_domain: schoolDomain };
        });
        display_boarding_nationalitywise_widget(selectedSchools, acad_year); // Pass acad_year as a parameter
    }

    function display_boarding_nationalitywise_widget(school_list, acad_year){
        $("#boarding_nationalitywise_table").html("<center>Loading Boarding Nationalitywise Statistics...</center>");
        // $("#boarding_nationalitywise_table").html('<td colspan="5">Loading Boarding Nationalitywise Statistics...</td>');
        var data_array =[]
        const mapLoop = async () => {
            const promises = [school_list[0]].map(async school => {
                try {
                    const response = await get_boarding_nationalitywise_data(school.school_code, school.school_domain, acad_year);
                    if (response) {
                    console.log(response);
                    data_array = response.map(obj => [obj.boarding, parseInt(obj.boarding_count), obj.nationality]);
                    }
                } catch (err) {
                    console.log(err);
                }
            });
            await Promise.all(promises);
            console.log(data_array);
            // _construct_boarding_nationalitywise_overall_view(final_array.slice(), [school_list[0]].length);
            _construct_boarding_nationalitywise_table(data_array);
        };
        mapLoop();
    }

    function get_boarding_nationalitywise_data(school_code, school_domain,acad_year) {
        return new Promise(function(resolve, reject) {
            $.ajax({
                url: '<?php echo base_url("msm_v3/dashboard/bridge") ?>',
                type: 'post',
                data: {
                    'school_code': school_code,
                    'school_domain': school_domain,
                    'acad_year': acad_year,
                    'api': 'get_boarding_nationalitywise_data'
                },
                success: function(data) {
                    data = JSON.parse(data);
                    if (data.status == 0) reject(data.message);
                    resolve(JSON.parse(data.response));
                },
                error: function(err) {
                    reject(err);
                }
            });
        });
    }

    function _construct_boarding_nationalitywise_overall_view(input_array, no_of_schools) {
        google.charts.setOnLoadCallback(function()  {
            var data = google.visualization.arrayToDataTable(input_array);
            var options = {
                height: 330,
                curveType: 'function',
                // width: 1400,
                chartArea: {left: '15%', top: '15%', width: '75%', height: '80%', bottom: '10%'},
                hAxis: {
                    titleTextStyle: {
                        color: '#1450A3',
                        fontName: 'Arial',
                        fontSize: '16',
                        bold: true,
                        italic: false
                    },
                    bar: { groupWidth: "90%" },
                    axisTitlesPosition: 'out',
                    curveType: 'function',
                    legend: {
                    position: 'labeled'
                    }
                },
                vAxis: {
                    title: '# TICKETS',
                    titleTextStyle: {
                        color: '#1450A3',
                        fontName: 'Arial',
                        fontSize: '16',
                        bold: true,
                        italic: false
                    },
                },
                legend: {
                    position: 'top', alignment: 'center'
                }
            };

            var view = new google.visualization.DataView(data);
            var columns = [0, 1];
            for (var i = 0; i < no_of_schools; i++) {
                columns.push({
                    sourceColumn: i + 1,
                    type: "number",
                    role: "annotation",
                    color: "#000000"
                });
                if (i < (no_of_schools-1)) {
                    columns.push(i + 2);
                }
            }
            view.setColumns(columns);
            var chart = new google.visualization.LineChart(document.getElementById('pt_column_chart'));
            chart.draw(view, options);
        });
    }

    function _construct_boarding_nationalitywise_table(input_array) {
        var html = `<table class="table-sm table-bordered mb-0" width="100%">`;
        html += `<thead style="color: #0F256E">`;
        html += `<th class="text-left font-weight-bolder text-m text-uppercase opacity-7">#</th>`;
        html += `<th class="text-left font-weight-bolder text-m text-uppercase opacity-7">Nationality</th>`;
        html += `<th class="text-left font-weight-bolder text-m text-uppercase opacity-7">Day Boarders</th>`;
        html += `<th class="text-left font-weight-bolder text-m text-uppercase opacity-7">Regular</th>`;
        html += `<th class="text-left font-weight-bolder text-m text-uppercase opacity-7">Weekly</th>`;
        html += `<th class="text-left font-weight-bolder text-m text-uppercase opacity-7">Total</th>`;
        html += `</thead>`;
        html += `<tbody>`;

        var nationalityData = {};

        // Organize data by nationality and calculate totals
        input_array.forEach((obj) => {
        var nationality = obj[2];
        if (!nationalityData[nationality]) {
            nationalityData[nationality] = {
                day_boarders: 0,
                regular: 0,
                weekly: 0
            };
        }
        switch (obj[0]) {
            case "Day Scholar":
                nationalityData[nationality].day_boarders += parseInt(obj[1]);
                break;
            case "Regular Boarder":
                nationalityData[nationality].regular += parseInt(obj[1]);
                break;
            case "Weekly Boarder":
                nationalityData[nationality].weekly += parseInt(obj[1]);
                break;
        }
        nationalityData[nationality].total = nationalityData[nationality].day_boarders + nationalityData[nationality].regular + nationalityData[nationality].weekly;
        });

        // Generate table rows
        var i = 1;
        var totalDayBoarders = 0;
        var totalRegular = 0;
        var totalWeekly = 0;
        var totalOverall = 0;
        for (var nationality in nationalityData) {
            html += `<tr>`;
            html += `<td>${i++}</td>`; // Row number
            html += `<td>${nationality}</td>`; // Nationality
            html += `<td>${nationalityData[nationality].day_boarders}</td>`; // Day Boarders
            html += `<td>${nationalityData[nationality].regular}</td>`; // Regular
            html += `<td>${nationalityData[nationality].weekly}</td>`; // Weekly
            html += `<td>${nationalityData[nationality].total}</td>`; // Total
            html += `</tr>`;
            totalDayBoarders += nationalityData[nationality].day_boarders;
            totalRegular += nationalityData[nationality].regular;
            totalWeekly += nationalityData[nationality].weekly;
            totalOverall += nationalityData[nationality].total;
        }

        // Add total row
        html += `<tr>`;
        html += `<td colspan="2" class="font-weight-bolder">Total</td>`;
        html += `<td>${totalDayBoarders}</td>`;
        html += `<td>${totalRegular}</td>`;
        html += `<td>${totalWeekly}</td>`;
        html += `<td>${totalOverall}</td>`;
        html += `</tr>`;

        html += `</tbody>`;
        html += `</table>`;

        $('#boarding_nationalitywise_table').html(html);
    }

    function disableTableButtons() {
        const buttons = document.getElementsByClassName('btn btn-outline');
        for (let button of buttons) {
            button.setAttribute('disabled', true);
        }
    }

    // Function to enable table buttons
    function enableTableButtons() {
        const buttons = document.getElementsByClassName('btn btn-outline');
        for (let button of buttons) {
            button.removeAttribute('disabled');
        }
    }
</script>
