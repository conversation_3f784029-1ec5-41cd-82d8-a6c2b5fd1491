<div class="col-md-12">
    <div class="card panel_new_style">
        <div class="card-header panel_heading_new_style_padding" style="padding-top: 10px;">
            <h3 class="card-title panel_title_new_style">
                <strong>Month wise summary report</strong>
            </h3>
        </div>
        <div class="card-body px-2 py-1">
	      	<div class="row mb-5">
		        <div class="col-md-12 form-group">
		          	<label for="fromdateId" class="control-label">From Month</label>
		          	<div class="input-group date" id="start_date_picker"> 
		                <input value="<?php echo date('m-Y'); ?>" input placeholder="From Month" required="" type="text"  class="form-control" id="fromdateId" name="from_date" >
		                <span class="input-group-addon">
		                    <span class="glyphicon glyphicon-calendar"></span>
		                </span>
		            </div>
		        </div>
		        <div class="col-md-12 form-group">
		          	<label for="todateId" class="control-label">To Month</label>
		          	<div class="input-group date" id="end_date_picker"> 
		               	<input required=""  autocomplete="off" type="text" input placeholder="From Month" value="<?php echo date('m-Y'); ?>" class="form-control " id="todateId" name="to_date">
		                <span class="input-group-addon">
		                	<span class="glyphicon glyphicon-calendar"></span>
		                </span>
		            </div>
		        </div>

				<div class="col-md-12 form-group">
                    <label class="control-label">Staff Status Type</label>
                    <select class="form-control" name="staff_status_type" id="staff_status_type">
							<option value="all">All</option>
                            <option value='2'>Approved</option>
                            <option value='4'>Resigned</option>
                    </select>
                </div>

				<div class="col-md-12 form-group">
                    <label class="control-label">Staff Type</label>
                    <select title="All" class="form-control select" name="staff_type[]" id="staff_type" onChange="hideDataArea()" multiple>
                        <!-- <option value="all">All</option> -->
						<?php if (!empty($staff_types)) {
							foreach ($staff_types as $key => $val) {
								echo "<option value='$key'>$val</option>";
							}
						} else {
							echo "<option value='2'>found no staff types</option>";
						} ?>
					</select>
				</div>

				<div class="col-md-12 form-group">
					  <button class="btn btn-primary mt-3 w-100" onclick="getAttendanceData()">Get Report</button>
				</div>
			  </div>
			
			<div id="attendance-data">
				<div style="color:red;text-align:center;
					color: black;
					border: 2px solid #fffafa;
					text-align: center;
					border-radius: 6px;
					position: relative;
					margin-left: 14px;
					padding: 10px;
					font-size: 14px;
					margin-top: 14px;
					background: #ebf3ff;
					">
						Click Get Report to fetch details!
				</div>
			</div>
		</div>  
	</div>
</div>

<a href="<?php echo site_url('staff/attendance');?>" id="backBtn" onclick="loader()"><span class="fa fa-mail-reply"></span></a>


<script src="https://cdnjs.cloudflare.com/ajax/libs/xlsx/0.16.9/xlsx.full.min.js" integrity="sha512-wBcFatf7yQavHQWtf4ZEjvtVz4XkYISO96hzvejfh18tn3OrJ3sPBppH0B6q/1SHB4OKHaNNUKqOmsiTGlOM/g==" crossorigin="anonymous"></script>
<script src="https://cdnjs.cloudflare.com/ajax/libs/FileSaver.js/2.0.0/FileSaver.min.js" integrity="sha512-csNcFYJniKjJxRWRV1R7fvnXrycHP6qDR21mgz1ZP55xY5d+aHLfo9/FcGDQLfn2IfngbAHd8LdfsagcCqgTcQ==" crossorigin="anonymous"></script>
<script type="text/javascript">
var headers = [];
var json_data = [];
$(document).ready(function() {

	var maxAttendenceDate = new Date();
  maxAttendenceDate.setDate(maxAttendenceDate.getDate());
  var minAttendenceDate = new Date();
  minAttendenceDate.setFullYear( minAttendenceDate.getFullYear() - 2);

	$('#start_date_picker,#end_date_picker').datetimepicker({
      viewMode: 'months',
      format: 'MM-YYYY',
      maxDate: maxAttendenceDate,
      minDate: minAttendenceDate
  });

});

function hideDataArea(){
	$("#attendance-data").html('');
}

function constructHeader(dates) {
	headers = ['Sl', 'Staff', 'Joining', 'Exit','Status'];
	var html = '';
	html += '<thead>';
	html += '<tr>';
	html += '<th rowspan="2">#</th>';
	html += '<th rowspan="2">Staff</th>';
	html += '<th rowspan="2" width="10%">Joining Date</th>';
	html += '<th rowspan="2" width="10%">Exit date</th>';
	html += '<th rowspan="2" width="7%">Status</th>';
	html += '<th rowspan="2" width="7%">Staff Type</th>';
	html += '<th rowspan="2" width="7%">Department</th>';
	var count = 0
	for(var i in dates) {
		count += 1;
		headers.push(dates[i].format);
		html += '<th colspan="5" style="text-align: center;">'+dates[i].format+'</th>';
	}
	html += '</tr>';
	html += '<tr>';
	for(j=1;j<=count;j++){
		
		html += '<th>Working Days</th>';
		html += '<th>Present FD</th>';
		html += '<th>Present HD</th>';
		html += '<th>Absent</th>';
		html += '<th>Percentage</th>';
		
	}
	html += '</tr>';
	
	html += '</thead>';
	return html;
}

function constructReport(dates, attendance) {
	json_data = [];
	var html = '<tbody>';
	var j = 1;

	
	for(var i in attendance) {
	
		html += '<tr>';
		html += '<td>'+(j++)+'</td>';
		html += '<td>'+attendance[i].staff_name+'</td>';
		html += '<td>'+attendance[i].joining_date+'</td>';
		html += '<td>'+attendance[i].last_date_of_work+'</td>';
		html += '<td>'+attendance[i].staff_status+'</td>';
		html += `<td>${attendance[i].staff_type || "NA"}</td>`;
		html += `<td>${attendance[i].staff_department || "NA"}</td>`;
		var month_attendance = attendance[i];
		for(var k in dates) {
			// Check if month data exists and has shift_exists property
			if(month_attendance[dates[k].format] && month_attendance[dates[k].format]['shift_exists'] == 1) {
				// var working_days = dates[k].days - month_attendance[dates[k].format]['WO'] - month_attendance[dates[k].format]['H'];
				var working_days=month_attendance[dates[k].format]['total_working_days'] || 0;
				var presents = +(month_attendance[dates[k].format]['P'] || 0) + (+(month_attendance[dates[k].format]['HD'] || 0) * 0.5);
				var percentage = working_days > 0 ? ((presents/working_days*100)).toFixed(2) : 0;
				html += '<td>'+(working_days)+'</td>';
				html += '<td>'+(month_attendance[dates[k].format]['P'] || 0)+'</td>';
				html += '<td>'+(month_attendance[dates[k].format]['HD'] || 0)+'</td>';
				html += '<td>'+(month_attendance[dates[k].format]['AB'] || 0)+'</td>';
				if(isNaN(percentage) || percentage == 0){
					html += '<td>0.00 %</td>';
				}else{
					html += '<td>'+percentage+' %</td>';
				}
			} else {
				html += '<td>-</td>';
				html += '<td>-</td>';
				html += '<td>-</td>';
				html += '<td>-</td>';
				html += '<td>-</td>';
			}
		}
		html += '</tr>';
	}
	
	html += '</tbody>';
	return html;
}

function printProfile(){
      var restorepage = document.body.innerHTML;
      var printcontent = document.getElementById('month_wise_report').innerHTML;
      document.body.innerHTML = printcontent;
      window.print();
      document.body.innerHTML = restorepage;
  }

function getAttendanceData() {
	const msg = `
            <div style="color:red;text-align:center;
              color: black;
              border: 2px solid #fffafa;
              text-align: center;
              border-radius: 6px;
              position: relative;
              margin-left: 14px;
              padding: 10px;
              font-size: 14px;
              margin-top: 14px;
			  background: #ebf3ff;
			  ">
                Loading...
            </div>`;

	$("#attendance-data").html(msg);

	var from_date = $("#fromdateId").val();
	var to_date = $("#todateId").val();
	var staff_type = $("#staff_type").val();

	var staff_status_type = $("#staff_status_type").val();

	$.ajax({
        url: '<?php echo site_url('staff/attendance/getMonthWiseAttendance'); ?>',
        type: 'post',
        data: {'from_date':from_date, 'to_date': to_date,"staff_type":staff_type,'staff_status_type':staff_status_type},
        success: function(data) {
            if(data != 0) {
                var response = JSON.parse(data);

                // Check if chunked processing is needed
                if(response.chunked_processing) {
                    processChunkedMonthWiseDataMobile(response);
                } else {
                    // Original processing for smaller date ranges
                    if(!Object.entries(response)?.length){
                        let msg = `
                        <div style="color:red;text-align:center;
                            color: black;
                            border: 2px solid #fffafa;
                            text-align: center;
                            border-radius: 6px;
                            position: relative;
                            margin-left: 14px;
                            padding: 10px;
                            font-size: 14px;
                            margin-top: 14px;
                            background: #ebf3ff;">
                            No Data to show
                            </div>
                        `;
                        return $("#attendance-data").html(msg);
                    }

                    displayMonthWiseReportMobile(response.dates, response.attendance);
                }
            } else {
                let msg = `
                <div style="color:red;text-align:center;
                    color: black;
                    border: 2px solid #fffafa;
                    text-align: center;
                    border-radius: 6px;
                    position: relative;
                    margin-left: 14px;
                    padding: 10px;
                    font-size: 14px;
                    margin-top: 14px;
                    background: #ebf3ff;">
                    No Data to show
                    </div>
                `;
                $("#attendance-data").html(msg);
            }
        },
        error: function (err) {
		    console.log(err);
		}
    });
}

function exportToExcel()
    {
        var htmls = "";
        var uri = 'data:application/vnd.ms-excel;base64,';
        var template = '<html xmlns:o="urn:schemas-microsoft-com:office:office" xmlns:x="urn:schemas-microsoft-com:office:excel" xmlns="http://www.w3.org/TR/REC-html40"><head><!--[if gte mso 9]><xml><x:ExcelWorkbook><x:ExcelWorksheets><x:ExcelWorksheet><x:Name>{worksheet}</x:Name><x:WorksheetOptions><x:DisplayGridlines/></x:WorksheetOptions></x:ExcelWorksheet></x:ExcelWorksheets></x:ExcelWorkbook></xml><![endif]--><meta http-equiv="content-type" content="text/plain; charset=UTF-8"/></head><body><table>{table}</table></body></html>';
        var base64 = function(s)
        {
            return window.btoa(unescape(encodeURIComponent(s)))
        };

        var format = function(s, c)
        {
            return s.replace(/{(\w+)}/g, function(m, p)
            {
                return c[p];
            })
        };

        var mainTable = $("#monthly_report").html();
        htmls = mainTable;
        var ctx = {
            worksheet : 'Spreadsheet',
            table : htmls
        }
        var link = document.createElement("a");
        link.download = "Staff_Attendance_Monthly_Report.xls";
        link.href = uri + base64(format(template, ctx));
        link.click();
    }

// Mobile-specific chunked processing functions
var chunkedMonthWiseDataMobile = [];
var chunkedMonthWiseFiltersMobile = {};

function processChunkedMonthWiseDataMobile(response) {
	chunkedMonthWiseFiltersMobile = response.filters;
	chunkedMonthWiseDataMobile = [];

	var totalChunks = response.total_date_chunks * response.total_staff_chunks;
	var processedChunks = 0;

	// Show progress indicator
	showChunkedMonthWiseProgressMobile(0, totalChunks);

	// Process each date chunk with each staff chunk
	for(var dateChunkIndex = 0; dateChunkIndex < response.date_chunks.length; dateChunkIndex++) {
		for(var staffChunkIndex = 0; staffChunkIndex < response.staff_chunks.length; staffChunkIndex++) {
			var dateChunk = response.date_chunks[dateChunkIndex];
			var staffChunk = response.staff_chunks[staffChunkIndex];

			processMonthWiseAttendanceChunkMobile(
				dateChunk.from_date,
				dateChunk.to_date,
				staffChunk,
				chunkedMonthWiseFiltersMobile.staff_status_type,
				chunkedMonthWiseFiltersMobile.staff_type,
				processedChunks,
				totalChunks
			).then(function(chunkData) {
				processedChunks++;
				updateChunkedMonthWiseProgressMobile(processedChunks, totalChunks);

				if(chunkData && chunkData.attendance_data) {
					chunkedMonthWiseDataMobile.push({
						attendance: chunkData.attendance_data,
						staff_names: chunkData.staff_names
					});
				}

				// If all chunks processed, display final result
				if(processedChunks >= totalChunks) {
					finalizeChunkedMonthWiseReportMobile(response.from_date, response.to_date, response.month_count);
				}
			});
		}
	}
}

function processMonthWiseAttendanceChunkMobile(chunk_from_date, chunk_to_date, staff_ids, staff_status_type, staff_type, chunk_index, total_chunks) {
	return new Promise(function(resolve, reject) {
		$.ajax({
			url: '<?php echo site_url('staff/attendance/processMonthWiseAttendanceChunk'); ?>',
			type: 'post',
			data: {
				'chunk_from_date': chunk_from_date,
				'chunk_to_date': chunk_to_date,
				'staff_ids': staff_ids,
				'staff_status_type': staff_status_type,
				'staff_type': staff_type,
				'chunk_index': chunk_index,
				'total_chunks': total_chunks
			},
			success: function(data) {
				try {
					var chunkResponse = JSON.parse(data);
					if(chunkResponse.success) {
						resolve(chunkResponse);
					} else {
						console.error('Month-wise chunk processing failed:', chunkResponse.error);
						resolve(null);
					}
				} catch(e) {
					console.error('Error parsing month-wise chunk response:', e);
					resolve(null);
				}
			},
			error: function(err) {
				console.error('Month-wise chunk request failed:', err);
				resolve(null);
			}
		});
	});
}

function showChunkedMonthWiseProgressMobile(processed, total) {
	var progressHtml = `
		<div class="text-center" id="chunked-monthwise-progress-mobile">
			<div class="mb-3">
				<i class="fa fa-spinner fa-spin" style="font-size: 2rem;"></i>
			</div>
			<div class="progress" style="height: 25px;">
				<div class="progress-bar progress-bar-striped progress-bar-animated"
					 role="progressbar" style="width: 0%" id="chunk-monthwise-progress-bar-mobile">
					0%
				</div>
			</div>
			<p class="mt-2">
				<small>Processed <span id="processed-monthwise-chunks-mobile">0</span> of <span id="total-monthwise-chunks-mobile">${total}</span> chunks</small>
			</p>
		</div>
	`;
	$("#attendance-data").html(progressHtml);
}

function updateChunkedMonthWiseProgressMobile(processed, total) {
	var percentage = Math.round((processed / total) * 100);
	$("#chunk-monthwise-progress-bar-mobile").css('width', percentage + '%').text(percentage + '%');
	$("#processed-monthwise-chunks-mobile").text(processed);
}

function finalizeChunkedMonthWiseReportMobile(from_date, to_date, month_count) {
	// Process all collected chunk data to create final month-wise report
	var finalAttendanceData = processChunkedMonthWiseAttendanceMobile(chunkedMonthWiseDataMobile, from_date, to_date, month_count);

	if(finalAttendanceData.attendance.length == 0) {
		let msg = `
		<div style="color:red;text-align:center;
			color: black;
			border: 2px solid #fffafa;
			text-align: center;
			border-radius: 6px;
			position: relative;
			margin-left: 14px;
			padding: 10px;
			font-size: 14px;
			margin-top: 14px;
			background: #ebf3ff;">
			No Data to show
			</div>
		`;
		$("#attendance-data").html(msg);
	} else {
		displayMonthWiseReportMobile(finalAttendanceData.dates, finalAttendanceData.attendance);
	}
}

function displayMonthWiseReportMobile(dates, attendance) {
	// Sort attendance data by staff name for consistent ordering
	attendance.sort(function(a, b) {
		var nameA = (a.staff_name || '').toLowerCase();
		var nameB = (b.staff_name || '').toLowerCase();
		return nameA.localeCompare(nameB);
	});

	var html = '<div class="pull-right">';
	html+='<a style="margin-bottom:10px" href="javascript:void(0)" class="btn btn-primary" onclick="printProfile()" > <i class="fa fa-print"></i></a>&nbsp;';
	html += '<a style="margin-bottom:10px" onclick="exportToExcel()" class="btn btn-primary">Export</a>&nbsp; ';
	html += '</div>';
	html +='<div id="month_wise_report" class="table-responsive">';
	html +='<table id="monthly_report" class="table table-bordered" style="width: 100%;white-space: nowrap;">';
	html += constructHeader(dates);
	html += constructReport(dates, attendance);
	html +='</table>';
	html +='</div>';

	$("#attendance-data").html(html);
}

function processChunkedMonthWiseAttendanceMobile(chunkedData, from_date, to_date, month_count) {
	// Same simplified processing as tablet version
	var allAttendanceData = [];
	var allStaffNames = {};

	chunkedData.forEach(function(chunk) {
		allAttendanceData = allAttendanceData.concat(chunk.attendance);
		Object.assign(allStaffNames, chunk.staff_names);
	});

	var staff_attendance = [];
	var dates = [];
	var currentDate = new Date(from_date);

	// Create dates array
	for(var j = 0; j < month_count; j++){
		var monthStr = currentDate.toLocaleDateString('en-US', { month: 'short', year: 'numeric' }).replace(' ', '-');
		var daysInMonth = new Date(currentDate.getFullYear(), currentDate.getMonth() + 1, 0).getDate();

		dates[j] = {
			'format': monthStr,
			'days': daysInMonth
		};

		currentDate.setMonth(currentDate.getMonth() + 1);
	}

	// Process attendance data
	allAttendanceData.forEach(function(att) {
		if(!staff_attendance[att.staff_id]) {
			staff_attendance[att.staff_id] = {
				'employee_code': att.employee_code,
				'staff_name': allStaffNames[att.staff_id] || att.staff_name,
				'staff_id': att.staff_id,
				'staff_type': att.staff_type,
				'staff_department': att.staff_department_name,
				'joining_date': att.joining_date,
				'last_date_of_work': att.last_date_of_work,
				'staff_status': att.staff_status
			};
		}

		var attDate = new Date(att.date);
		var monthKey = attDate.toLocaleDateString('en-US', { month: 'short', year: 'numeric' }).replace(' ', '-');

		if(!staff_attendance[att.staff_id][monthKey]){
			staff_attendance[att.staff_id][monthKey] = {
				'shift_exists': 0, 'P': 0, 'AB': 0, 'HD': 0, 'WO': 0, 'H': 0, 'total_working_days': 0
			};
		}

		// Add null checks for att properties
		if((att.status == 'P' || att.status == 'FD') && (att.type == 1 || att.type === undefined)){
			staff_attendance[att.staff_id][monthKey]['P'] += 1;
		}
		else if(att.status && att.status.indexOf('HD') !== -1){
			staff_attendance[att.staff_id][monthKey]['HD'] += 1;
		}
		else if(att.status == 'H'){
			staff_attendance[att.staff_id][monthKey]['H'] += 1;
		}
		else if(att.status == 'WO'){
			staff_attendance[att.staff_id][monthKey]['WO'] += 1;
		}
		else if(att.status && att.status.indexOf('AB') !== -1){
			staff_attendance[att.staff_id][monthKey]['AB'] += 1;
		}

		if(att.shift_exists == 1 || att.shift_exists === undefined){
			staff_attendance[att.staff_id][monthKey]['shift_exists'] = 1;
		}

		if ((att.shift_exists == 1 || att.shift_exists === undefined) && (att.type == 1 || att.type === undefined)) {
			staff_attendance[att.staff_id][monthKey]['total_working_days'] += 1;
		}
	});

	var attendanceArray = [];
	Object.keys(staff_attendance).forEach(function(key) {
		attendanceArray.push(staff_attendance[key]);
	});

	// Sort attendance data by staff name for consistent ordering
	attendanceArray.sort(function(a, b) {
		var nameA = (a.staff_name || '').toLowerCase();
		var nameB = (b.staff_name || '').toLowerCase();
		return nameA.localeCompare(nameB);
	});

	return { dates: dates, attendance: attendanceArray };
}

</script>