<ul class="breadcrumb">
    <li><a href="<?php echo site_url('dashboard') ?>">Dashboard</a></li>
    <li><a href="<?php echo site_url('admission_process') ?>">Admisson Dashboard</a></li>
    <li class="active">Admission Status</li>
</ul>

<div class="col-md-12 col_new_padding">
    <div class="card cd_border">
        <div class="card-header panel_heading_new_style_staff_border">
            <div class="row" style="margin: 0px">
                <div class="col-md-9 pl-0">
                    <h3 class="card-title panel_title_new_style_staff">
                        <a class="back_anchor" href="<?php echo site_url('admission_process') ?>"
                            class="control-primary">
                            <span class="fa fa-arrow-left"></span>
                        </a>
                        Manage Admission Status
                    </h3>
                </div>
                <div class="col-md-3 pr-0">
                    <ul class="panel-controls">
                        <li>
                            <a href="" class="new_circleShape_res" style="background-color: #fe970a;"
                                data-toggle="modal" data-target="#status_uploader" onclick="add_status()">
                                <span class="fa fa-plus" style="font-size: 19px;"></span>
                            </a>
                        </li>
                    </ul>
                </div>
            </div>
        </div>
        <div class="card-body pt-1">
            <div id="status_table" class="panel-body table-responsive hidden-xs">
            </div>
        </div>
    </div>
</div>

<div class="modal fade" id="status_uploader" tabindex="-1" role="dialog" style="width:40%;margin:auto;top:25%"
    data-backdrop="static" aria-labelledby="status_uploader-label" aria-hidden="true">
    <div class="modal-content modal-dialog" style="border-radius: 8px;">

        <div class="modal-header" style="border-bottom: 2px solid #ccc;">
            <h4 class="modal-title" id="modalHeader">Add Admission status</h4>
            <button style="font-size: 32px;font-weight: bold;color: #e04b4a;opacity: 1;padding-top: .5rem;"
                type="button" class="close" data-dismiss="modal">&times;</button>
        </div>

        <div class="modal-body">
            <form id="admission_status_form">
                <div class="col-md-12">
                    <div class="form-group">
                        <label for="name" class="col-md-4">User status<font color="red">*</font></label>
                        <div class="col-md-8">
                            <input id="user_status" class="form-control" name="user_status"
                                placeholder="Enter user status" type="text" required />
                        </div>
                    </div>
                </div>

                <div class="col-md-12" style="margin-top: 10px;">
                    <div class="form-group">
                        <label for="name" class="col-md-4">Internal status<font color="red">*</font></label>
                        <div class="col-md-8">
                            <select id="internal_status" class="select_dropdown" name="internal_status" required>
                                <option value="Draft">Draft</option>
                                <option value="Submitted">Submitted</option>
                                <option value="Application Amount Paid">Application Amount Paid</option>
                                <option value="Follow up">Follow up</option>
                                <option value="Admit">Admit</option>
                                <option value="Offer Released">Offer Released</option>
                                <option value="Rejected">Rejected</option>
                                <option value="Duplicate">Duplicate</option>
                                <option value="Offer Rejected">Offer Rejected</option>
                                <option value="Student added to ERP">Student added to ERP</option>
                                <option value="Withdrawn">Withdrawn</option>
                            </select>
                        </div>
                    </div>
                </div>

                <div class="col-md-12" style="margin-top: 10px;">
                    <div class="form-group">
                        <label for="name" class="col-md-4">Reporting status<font color="red">*</font></label>
                        <div class="col-md-8">
                            <select id="reporting_status" class="select_dropdown" name="reporting_status" required>
                                <option value="Not Applicable">Not Applicable</option>
                                <option value="wip">In Progress</option>
                                <option value="convert">Converted</option>
                                <option value="closed">Closed</option>
                            </select>
                        </div>
                    </div>
                </div>

                <div class="col-md-12" style="margin-top: 10px;">
                    <div class="form-group">
                        <label for="name" class="col-md-4">Color code</label>
                        <div class="col-md-8">
                            <input id="color_code" class="form-control" name="color_code" type="color" />
                        </div>
                    </div>
                </div>

                <div class="col-md-12" style="margin-top: 10px;">
                    <div class="form-group">
                        <label for="name" class="col-md-4">Next Status</label>
                        <div class="col-md-8">
                            <select id="next_status" class="form-control selectpicker" name="next_status[]" multiple title="Select Status">
                            <?php foreach ($admission_status_list as $key => $value) { ?>
                              <option value="<?= $value->user_status ?>"><?= $value->user_status ?></option>
                            <?php } ?>
                            </select>
                        </div>
                    </div>
                </div>

                <div class="col-md-12" style="margin-top: 10px;">
                    <div class="form-group">
                        <label for="name" class="col-md-4">Email Notification Send to</label>
                        <div class="col-md-8">
                            <select id="emai_staff_id" class="form-control selectpicker" name="emai_staff_id[]" multiple title="Select Staff">
                            <?php foreach ($staff_names as $key => $value) { ?>
                              <option value="<?= $value->smId ?>"><?= $value->s_name  ?></option>
                            <?php } ?>
                            </select>
                        </div>
                    </div>
                </div>

                <div class="col-md-12" style="margin-top: 10px;">
                    <div class="form-group">
                        <label for="name" class="col-md-4">Status Permission</label>
                        <div class="col-md-8">
                            <select id="status_permission" class="form-control selectpicker" name="status_permission[]" multiple title="Select Staff">
                            <?php foreach ($staff_names as $key => $value) { ?>
                              <option value="<?= $value->smId ?>"><?= $value->s_name  ?></option>
                            <?php } ?>
                            </select>
                        </div>
                    </div>
                </div>

            </form>
        </div>

        <div class="modal-footer">
            <button class="btn btn-primary" type="button" onclick="submit_admission_status()">Submit </button>
        </div>

    </div>
</div>

<script>
function add_status() {
    $('#status_uploader form').trigger('reset');
}

function show_alert(which_alert, text) {
    switch (which_alert) {
        case 'success':
            $("#successAlertText").html(text);
            $("#successAlert").removeClass('d-none').addClass('show');
            break;
        case 'error':
            $("#errorAlertText").html(text);
            $("#errorAlert").removeClass('d-none').addClass('show');
            break;
    }
    $(function() {
        var alert = $('div.alert[auto-close]');
        alert.each(function() {
            var that = $(this);
            var time_period = that.attr('auto-close');
            setTimeout(function() {
                that.addClass('d-none').removeClass('show');
            }, time_period);
        });
    });
}

function submit_admission_status() {
    var form = $('#admission_status_form');
    if (!form.parsley().validate()) {
        return 0;
    }
    var form = $('#admission_status_form')[0];
    var formData = new FormData(form);

    $.ajax({
        url: '<?php echo site_url('Admission_process/submit_admission_status'); ?>',
        type: 'post',
        data: formData,
        processData: false,
        contentType: false,
        // async: false,
        success: function(data) {
            if (data == 1) {
                $("#status_uploader").modal('hide');
                Swal.fire({
                    // position: "top-end",
                    icon: "success",
                    title: "Status is Added",
                    showConfirmButton: false,
                    timer: 1500
                });
                get_admission_status_table();
            } else {
                show_alert('error', 'Adding status failed');
                // console.log(err);
                return;
            }
        },
        error: function(err) {
            console.log(err);
        }
    });
}

$(document).ready(function() {
    get_admission_status_table();
    ['#next_status', '#edit_next_status', '#emai_staff_id', '#status_permission','#edit_emai_staff_id','#edit_status_permission'].forEach(id => {
    $(id).selectpicker({
        liveSearch: true,
        liveSearchPlaceholder: 'Search fields...'
    });
    });
    $(document).on('click', function(event) {
        var $target = $(event.target);
        if (!$target.closest('.bootstrap-select').length && $('.bootstrap-select').hasClass('open')) {
            $('.bootstrap-select').removeClass('open show'); 
            $('.dropdown-menu').removeClass('show'); 
        }
    });
});

function get_admission_status_table() {
    $("#status_table").html(
        '<div class="text-center"><i class="fa fa-spinner fa-spin" style="font-size: 3rem;"></i></div>');
    $.ajax({
        url: '<?php echo site_url('Admission_process/get_admission_status_data'); ?>',
        type: 'post',
        success: function(data) {
            parsed_data = $.parseJSON(data);
            console.log(data);
            var status_obj = parsed_data;
            html = construct_admission_status_table(status_obj);
            $("#status_table").html(html);
        },
        error: function(err) {
            console.log(err);
        }
    });
}

function construct_admission_status_table(status_obj) {

    console.log(status_obj);

    var html = '';

    if (status_obj.length == 0) {
        html += '<h4 class="no-data-display">No Status Available</h4>';
    } else {

        html += `
  <div class="col-md-12">
  <table id="" class="table table-bordered">
          <thead>
            <tr>
              <th width="5%">#</th>
              <th width="20%">User status</th>
              <th width="20%">Internal status</th>
              <th width="20%">Reporting status</th>
               <th width="20%">Status Next</th>
              <th width="10%">Color code</th>
              <th width="15%">Action</th>
            </tr>
          </thead>
  `;
        html += `<tbody>`;
        for (var i = 0; i < status_obj.length; i++) {
            var data = status_obj[i];

            if (data.color_code) {
                color_code = `<td style="background-color:${data.color_code}">${data.color_code}</td>`;
            } else {
                color_code = '<td>Not Assigned</td>';
            }
            var status_next = [];
            if(data['status_next']){
               status_next = JSON.parse(data['status_next']);
            }
            html += `
            <tr>
            <td>${i+1}</td>
            <td>${data['user_status']}</td>
            <td>${data['internal_status']}</td>
            <td>${data['reporting_status']}</td>
            <td>${status_next}</td>
            ${color_code}
            <td>
                <button class="btn btn-warning" style="margin-top:5px;" 
                onclick="edit_status('${data['id']}','${data['user_status']}','${data['internal_status']}','${data['reporting_status']}','${data['color_code']}')" data-toggle="modal"  data-target="#edit_status_modal">Edit</button>
                <input type="hidden" id="${data['id']}_next_status" value='${data['status_next']}'>
                <input type="hidden" id="${data['id']}_email_send_to" value='${data['email_send_to']}'>
                <input type="hidden" id="${data['id']}_status_permission" value='${data['status_permission']}'>
            </td>
            </tr>`;
        }
        html += `</tbody>
      </table>
      </div>`;
    }
    return html;
}

function edit_status(id, user_status, internal_status, reporting_status, color_code) {
    var status_next = $('#'+id+'_next_status').val();
    var email_send_to = $('#'+id+'_email_send_to').val();
    var status_permission = $('#'+id+'_status_permission').val();
    if(status_next){
        try {
            status_next = JSON.parse(status_next);
            if (!Array.isArray(status_next)) status_next = [];
        } catch (e) {
            status_next = [];
        }
    }else{
        status_next = [];
    }

    if(email_send_to){
        try {
            email_send_to = JSON.parse(email_send_to);
            if (!Array.isArray(email_send_to)) email_send_to = [];
        } catch (e) {
            email_send_to = [];
        }
    }else{
        email_send_to = [];
    }

    if(status_permission){
        try {
            status_permission = JSON.parse(status_permission);
            if (!Array.isArray(status_permission)) status_permission = [];
        } catch (e) {
            status_permission = [];
        }
    }else{
        status_permission = [];
    }
  
    $("#edit_status_modal form").trigger('reset');
    $('#status_id').val(id);
    $('#edit_user_status').val(user_status);
    $('#edit_internal_status').val(internal_status);
    $('#edit_reporting_status').val(reporting_status);
    $('#edit_color_code').val(color_code);
    $("#edit_next_status option:selected").prop("selected", false);
    status_next.forEach(status => {
        $("#edit_next_status").find(`option[value="${status}"]`).prop("selected", true);
    });
    $('#edit_next_status').selectpicker('refresh');

    $("#edit_emai_staff_id option:selected").prop("selected", false);
    email_send_to.forEach(staff_id => {
        $("#edit_emai_staff_id").find(`option[value="${staff_id}"]`).prop("selected", true);
    });
    $('#edit_emai_staff_id').selectpicker('refresh');

    $("#edit_status_permission option:selected").prop("selected", false);
    status_permission.forEach(staff_id => {
        $("#edit_status_permission").find(`option[value="${staff_id}"]`).prop("selected", true);
    });
    $('#edit_status_permission').selectpicker('refresh');
}

function update_admission_status() {
    var form = $('#edit_status_form');
    if (!form.parsley().validate()) {
        return 0;
    }
    var form = $('#edit_status_form')[0];
    var formData = new FormData(form);

    $.ajax({
        url: '<?php echo site_url('Admission_process/update_admission_status'); ?>',
        type: 'post',
        data: formData,
        processData: false,
        contentType: false,
        async: false,
        success: function(data) {
            if (data == 1) {
                $("#edit_status_modal").modal('hide');
                Swal.fire({
                    // position: "top-end",
                    icon: "success",
                    title: "Status is Edited",
                    showConfirmButton: false,
                    timer: 1500
                });
                get_admission_status_table();
            } else {
                show_alert('error', 'Adding status failed');
                // console.log(err);
                return;
            }
        },
        error: function(err) {
            console.log(err);
        }
    });
}
</script>
<script src="https://cdn.jsdelivr.net/npm/sweetalert2@11"></script>
<style type="text/css">
.new_circleShape_res {
    padding: 8px;
    border-radius: 50% !important;
    color: white !important;
    font-size: 22px;
    height: 3.2rem !important;
    width: 3.2rem !important;
    text-align: center;
    vertical-align: middle;
    border: none !important;
    box-shadow: 0px 3px 7px #ccc;
    line-height: 1.7rem !important;
}
</style>

<div class="modal fade" id="edit_status_modal" tabindex="-1" role="dialog" style="width:40%;margin:auto;top:25%"
    data-backdrop="static" aria-labelledby="edit-label" aria-hidden="true">
    <div class="modal-content modal-dialog" style="border-radius: 8px;">

        <div class="modal-header" style="border-bottom: 2px solid #ccc;">
            <h4 class="modal-title" id="modalHeader">Edit</h4>
            <button style="font-size: 32px;font-weight: bold;color: #e04b4a;opacity: 1;padding-top: .5rem;"
                type="button" class="close" data-dismiss="modal">&times;
            </button>
        </div>


        <div class="modal-body">
            <form id="edit_status_form">
                <input type="hidden" id="status_id" name="status_id">
                <div class="col-md-12">
                    <div class="form-group">
                        <label class="col-md-4">Edit User status</label>
                        <div class="col-md-8">
                            <input id="edit_user_status" class="form-control" name="edit_user_status" type="text"
                                maxlength="100" required />
                        </div>
                    </div>
                </div>

                <div class="col-md-12" style="margin-top: 10px;">
                    <div class="form-group">
                        <label class="col-md-4">Edit Internal Status</label>
                        <div class="col-md-8">
                            <select class="select_dropdown" id="edit_internal_status" name="edit_internal_status"
                                required>
                                <option value="Draft">Draft</option>
                                <option value="Submitted">Submitted</option>
                                <option value="Application Amount Paid">Application Amount Paid</option>
                                <option value="Follow up">Follow up</option>
                                <option value="Admit">Admit</option>
                                <option value="Offer Released">Offer Released</option>
                                <option value="Rejected">Rejected</option>
                                <option value="Duplicate">Duplicate</option>
                                <option value="Offer Rejected">Offer Rejected</option>
                                <option value="Student added to ERP">Student added to ERP</option>
                                <option value="Withdrawn">Withdrawn</option>
                            </select>
                        </div>
                    </div>
                </div>

                <div class="col-md-12" style="margin-top: 10px;">
                    <div class="form-group">
                        <label class="col-md-4">Edit Reporting Status</label>
                        <div class="col-md-8">
                            <select class="select_dropdown" id="edit_reporting_status" name="edit_reporting_status"
                                required>
                                <option value="Not Applicable">Not Applicable</option>
                                <option value="wip">In Progress</option>
                                <option value="convert">Converted</option>
                                <option value="closed">Closed</option>
                            </select>
                        </div>
                    </div>
                </div>

                <div class="col-md-12" style="margin-top: 10px;">
                    <div class="form-group">
                        <label class="col-md-4">Edit color code</label>
                        <div class="col-md-8">
                            <input class="form-control" name="edit_color_code" id="edit_color_code" type="color" />
                        </div>
                    </div>
                </div>

                <div class="col-md-12" style="margin-top: 10px;">
                    <div class="form-group">
                        <label for="name" class="col-md-4">Edit Next Status</label>
                        <div class="col-md-8">
                            <select id="edit_next_status" class="form-control selectpicker" name="edit_next_status[]" multiple title="Select Status">
                            <?php foreach ($admission_status_list as $key => $value) { ?>
                              <option value="<?= $value->user_status ?>"><?= $value->user_status ?></option>
                            <?php } ?>
                            </select>
                        </div>
                    </div>
                </div>

                <div class="col-md-12" style="margin-top: 10px;">
                    <div class="form-group">
                        <label for="name" class="col-md-4">Email Notification Send to</label>
                        <div class="col-md-8">
                            <select id="edit_emai_staff_id" class="form-control selectpicker" name="edit_emai_staff_id[]" multiple title="Select Staff">
                            <?php foreach ($staff_names as $key => $value) { ?>
                              <option value="<?= $value->smId ?>"><?= $value->s_name  ?></option>
                            <?php } ?>
                            </select>
                        </div>
                    </div>
                </div>

                <div class="col-md-12" style="margin-top: 10px;">
                    <div class="form-group">
                        <label for="name" class="col-md-4">Status Permission</label>
                        <div class="col-md-8">
                            <select id="edit_status_permission" class="form-control selectpicker" name="edit_status_permission[]" multiple title="Select Staff">
                            <?php foreach ($staff_names as $key => $value) { ?>
                              <option value="<?= $value->smId ?>"><?= $value->s_name  ?></option>
                            <?php } ?>
                            </select>
                        </div>
                    </div>
                </div>

            </form>
        </div>
        <div class="modal-footer">
            <button class="btn btn-primary" type="button" onclick="update_admission_status()">Update</button>
        </div>
    </div>
</div>

<style>
.select_dropdown {
    border: 1px solid #bbb;
    padding: 7px 20px;
    border-radius: 5px;
    width: 100%;
}
</style>