<ul class="breadcrumb">
    <li><a href="<?php echo site_url('dashboard') ?>">Dashboard</a></li>
    <li><a href="<?php echo site_url('examination/Assessments/index');?>">Examination</a></li>
    <li><a href="<?php echo site_url('examination/assessment_marks/marksCards/'.$class->id);?>">Report Card</a></li>
    <li><a href="<?php echo site_url('examination/assessment_marks/marksCardDetails/'.$tempId);?>">More</a></li>
    <li> Students </li>
</ul>

<div class="col-md-12">
	<div class="card cd_border">
		<div id="loader1" style="display: none;">
                <!-- ajax loader -->
            <img src="<?php echo base_url('assets/img/loader_img.gif');?>" width="200" height="200" style="position:fixed; top:20%;left:50%;z-index: 100;">
        </div>
		<div class="card-header panel_heading_new_style_staff_border">
			<div class="row" style="margin: 0px">
				<div class="col-md-10">
					<h3 class="card-title panel_title_new_style_staff">
						<a class="back_anchor" href="<?php echo site_url('examination/assessment_marks/marksCardDetails/'.$tempId) ?>">
							<span class="fa fa-arrow-left"></span>
						</a> 
						Students of <strong>Grade <?= $class->class_name ?>/<?= $class->section_name ?></strong> for marks card <strong><?= $marksCard->template_name ?></strong>
					</h3>
				</div>

				<div class="col-md-2">
					<!-- <div class="circleButton_noBackColor" style="background-color:#fe970a;float:right;">
						<a onclick='window.location.reload()'>
							<span class="fa fa-refresh backgroundColor_organge" style="font-size:19px"></span>
						</a>          
					</div> -->
				</div>
			</div>
		</div>		

	    <div class="card-body" id="students">
	    	<?php
			$storeAllUrls= []; // Store all the downloadable urls
			$storeAllIds= []; // Store all the downloadable urls
			$storeAllNames= []; // Store all the downloadable urls
			 if(empty($students)) {
	            echo "<h4 class='no-data-display'>No Assessments for selected class.</h4>";
	        } else { ?>
	        <table id="customers2" class="table" style="border-left:1px solid #000;border-right:1px solid #000;">
	            <thead>
	                <tr>
	                    <th>#</th>
	                    <th>Admission No.</th>
	                    <th style="text-align:left;">Student Name</th>
	                    <th colspan="3" style="text-align:center;border-left:1px solid #000;">Remarks</th>
	                    <th colspan="4" style="text-align:center;border-left:1px solid #000;">Marks Card</th>
	                    
	                </tr>
	                <tr>
	                	<th></th>
	                	<th></th>
	                	<th></th>
	                    <th style="border-left:1px solid #000;">Status</th>
	                    <th>Action</th>
	                    <th>Publish Status</th>
	                    <th style="border-left:1px solid #000;"><a id="generate_all_anchor" onclick='callGeneration(<?= $tempId ?>)' class='btn  btn-primary'>Generate</a> <input style="width:15px;height:15px;" type="checkbox" name="genAll" id="genAll"></th>
	                    <th><a id="regenerate_all_anchor" onclick='callReGeneration(<?= $tempId ?>)' class='btn  btn-primary'>Regenerate</a> <input style="width:15px;height:15px;" type="checkbox" name="regenAll" id="regenAll"></th>
	                    <th>Last Generated On</th>
						<!-- <th>View/Download</th> -->
	                    <th>


						<?php if($this->authorization->isSuperAdmin()) {
							echo '<button onclick="downloadAllOneByOne(`unzip`)" data-placement="top" data-toggle="tooltip" data-original-title="Download All In One Click" id="direct_downloader_unzip" class="btn btn-info" type="button">
									Download All
								</button>';
						} ?>
						<button onclick="downloadAllOneByOne(`zip`)" data-placement="top" data-toggle="tooltip" data-original-title="Download All In One Click" id="direct_downloader_zip" class="btn btn-info" type="button">
							Zip All
						</button>

						

							
							<button data-toggle="modal" data-target="#range-modal" data-placement="top" id="zipper" class="btn btn-primary" type="button"><i class="fa fa-cloud-download"></i></button>
							<?php if ($is_super_admin) : ?>
								<button class="btn btn-primary" type="button" data-placement="top" data-toggle="tooltip" data-original-title="Reset Generation Status" onclick="reset_regen_status(<?= $tempId ?>)"><i class="fa fa-refresh"></i></button>
							<?php endif ?>
						</th>
	                    <!-- <th><button data-toggle="modal" data-target="#range-modal" id="zipper" class="btn btn-primary" type="button" onclick="downloadZip()"><i class="fa fa-cloud-download"></i></button></th> -->
	                </tr>
	            </thead>
	            <tbody>
	            <?php
	                $i=1;
	                $published = array();
	                $not_published = array();
	                foreach ($students as $student) { ?>
	                <tr>
	                		<input type="hidden" id="rep-id-<?php echo $i; ?>" value="<?php echo $student->amId; ?>">


	                		<input type="hidden" id="downloadabe-file-name-<?php echo $student->id; ?>" value="<?php echo $student->stdName; ?>-<?php echo $class->class_name ?>-<?php echo $class->section_name ?>">


	                    <td><?= $i++;?></td>
	                    <td><?= $student->admission_no ?></td>
	                    <td style="text-align:left;"><?= $student->stdName ?></td>
	                    <?php $state = ($student->remark_status)?$student->remark_status:'Not Added';
	                    	echo '<td style="border-left:1px solid #000;">'. $state .'</td>';
	                    ?>
	                    <td>
	                    	<?php 
	                    		if($add_remarks) {
	                    			echo "<a class='btn  btn-primary' href='".site_url("examination/assessment_marks/studentRemarks/".$tempId."/".$student->id."/add")."' data-placement='top' data-toggle='tooltip' data-original-title='Add Remarks'><i class='fa fa-plus'></i></a>";
	                    		} else {
	                    			echo "<a style='cursor: default;' class='btn  btn-secondary' data-placement='top' data-toggle='tooltip' data-original-title='Add Remarks'><i class='fa fa-plus'></i></a>";
	                    		}
	                    		if($verify_remarks) {
	                    			echo "<a class='btn  btn-primary' href='".site_url("examination/assessment_marks/studentRemarks/".$tempId."/".$student->id."/verify")."' data-placement='top' data-toggle='tooltip' data-original-title='Verify Remarks'><i class='fa fa-check'></i></a>";
	                    		} else {
	                    			echo "<a style='cursor: default;' class='btn  btn-secondary' data-placement='top' data-toggle='tooltip' data-original-title='Verify Remarks'><i class='fa fa-check'></i></a>";
	                    		}
	                    		if($student->status == 'Generated' || $state == 'Verified') {
	                    			echo "<a class='btn  btn-primary' onclick='unlockRemarks(".$tempId.",".$student->id.")' data-placement='top' data-toggle='tooltip' data-original-title='Unlock Remarks'><i class='fa fa-lock'></i></a>";
	                    		} else {
	                    			echo "<a style='cursor: default;' class='btn  btn-secondary' data-placement='top' data-toggle='tooltip' data-original-title='Unlock Remarks'><i class='fa fa-lock'></i></a>";
	                    		}
	                    	?>
	                    </td>
	                    <td>
	                    	<?php 
	                    	if($marksCard->template_content) {
	                    		if($student->published) {
	                    			array_push($published, $student->id);
	                    			echo "<span id='publish_".$student->id."'><a class='btn btn-primary' onclick='un_publish(".$student->id.", ".$tempId.")' href='javascript:void(0);'><i class='fa fa-check'></i></a></span>";
	                    		} else {
	                    			array_push($not_published, $student->id);
	                    			echo "<span id='publish_".$student->id."'><a class='btn btn-warning' onclick='publish(".$student->id.", ".$tempId.")' href='javascript:void(0);'><i class='fa fa-times'></i></a></span>";
	                    		}
	                    	} ?>
	                    </td>
	                    <td style="border-left:1px solid #000;">
	                    	<?php 
	                    	if($marksCard->template_content) {
	                    		if($generate_marksCard) {
	                    			$verify = ' disabled';
			                    	if($student->remark_status == 'Verified') {
			                    		$verify = '';
			                    	}
			                    	$view = ' disabled';
			                    	if($student->status == 'Generated') {
			                    		$verify = ' disabled';
			                    	}
	                    			$genMessage = '';
	                    			if($student->genStatus == '' || $student->status == '') {
										$name = $student->stdName.'_'.$class->class_name.$class->section_name.'_'.$marksCard->template_name;
										$name = str_replace(' ', '-', $name);
										$name = urlencode($name);
										$download_url = site_url('examination/assessment_marks/downloadMarksCard/').'cardidplaceholder/'.$name;
										echo "<input type='checkbox' data-downloadurl='$download_url' class='genClass' name='generateIds[]' id='genCheck_".$student->id."' value='".$student->id."'>";
	                    				echo "<span id='genMsg_".$student->id."'></span>";
	                    			} else if($student->genStatus == 0)
	                    				echo '<span style="color:#fff;background-color:#1caf9a;padding:5px 10px;border-radius:4px;line-height:2rem;">Generating</span>';
	                    			/*else if($student->genStatus == 2)
	                    				echo '<span style="color:#fff;background-color:#1caf9a;padding:5px;">Re generating</span>';*/
	                    			else if($student->genStatus == 1 || $student->genStatus == 2)
	                    				echo '<span style="color:#fff;background-color:#00701a;padding:5px 10px;border-radius:4px;line-height:2rem;">Generated</span>';
	                    				
	                    		} else {
	                    			echo "<a style='cursor: default;' class='btn  btn-secondary' data-placement='top' data-toggle='tooltip' data-original-title='Generate Marks card'><i class='fa fa-file'></i></a>";
	                    		}
	                    	} else {
	                    		echo "Marks card template not added.";
	                    	}
	                    	?>
	                    </td>
	                    <td>
	                    	<?php 
	                    	if($marksCard->template_content) {
	                    		if($regenerate_marksCrad) {
	                    			$view = ' disabled';
	                    			if($student->status == 'Generated') {
	                    				$view = '';
	                    				if($student->genStatus == 2) {
	                    					echo '<span style="color:#fff;background-color:#1caf9a;padding:5px 10px;border-radius:4px;line-height:2rem;">Re generating</span>';
	                    				} else {
											$name = $student->stdName.'_'.$class->class_name.$class->section_name.'_'.$marksCard->template_name;
											$name = str_replace(' ', '-', $name);
											$name = urlencode($name);
											$download_url = site_url('examination/assessment_marks/downloadMarksCard/').$student->cardId.'/'.$name;
											echo "<input ".$view." type='checkbox' data-downloadurl='$download_url' class='reGenClass' name='reGenerateIds[]' id='regenCheck_".$student->id."' value='".$student->id."'>";
		                    				echo "<span id='reGenMsg_".$student->id."'></span>";
	                    				}
	                    				
	                    			}
	                    		}
	                    	} ?>
	                    </td>
						<td id="viewdate_<?= $student->id ?>">
						<?php
							//Make date color red if there is an error
							if ($student->is_error == '1') {
								echo "<span data-toggle='tooltip' data-original-title='Error in Markscard' data-placement='top'><font color='#ff0000'>$student->date</font></span>";
							} else {
								echo "<font color='#000000'>$student->date</font>";
							}
						?>
						</td>
	                    <td id="view_<?= $student->id ?>">
	                    	<?php 
							
	                    	if($marksCard->template_content) {
	                    		if($view_marksCard) {
	                    			$view = ' disabled';
	                    			if($student->status == 'Generated') {
	                    				$view = '';
	                    			}
	                    			if($student->genStatus == 0) {
                    					echo '<span style="color:#fff;background-color:#1caf9a;padding:5px 10px;border-radius:4px;line-height:2rem;">Generating</span>';
                    				}
	                    			else if($student->genStatus == 2) {
                    					echo '<span style="color:#fff;background-color:#1caf9a;padding:5px 10px;border-radius:4px;line-height:2rem;">Re generating</span>';
                    				} else if($student->genStatus == 1) {
										$absolute_path= $this->filemanager->getFilePath($student->pdf_link);
                    					echo "<a target='_blank' ".$view." href='". $absolute_path . '?a=' . time() . "'  class='btn  btn-primary' data-placement='top'  data-toggle='tooltip' data-original-title='View Marks card'><i class='fa fa-eye'></i></a>";
                    					$name = $student->stdName.'_'.$class->class_name.$class->section_name.'_'.$marksCard->template_name;
                    					$name = str_replace(' ', '-', $name);
                    					$name = urlencode($name);
										$download_url = site_url('examination/assessment_marks/downloadMarksCard/').$student->cardId.'/'.$name;
										$storeAllUrls[]= $download_url;
										$storeAllIds[]= $student->cardId;
										$storeAllNames[]= $name;
                    					echo "<button onclick='downloadWithFetch(`$absolute_path`, `$student->id`)' class='btn  btn-primary' data-placement='top'  data-toggle='tooltip' data-original-title='Download Marks card'> <i class='fa fa-download'></i></button>";
                    				}
	                    		} else {
	                    			echo "<a style='cursor: default;' class='btn  btn-secondary' data-placement='top' data-toggle='tooltip' data-original-title='View Marks card'><i class='fa fa-eye'></i></a>";
	                    		}
	                    	} 
							if ($is_super_admin) {
								echo "<a data-toggle='modal' data-target='#show_html_modal' id='view_html_file' data-student_name='$student->stdName' data-student_card_id=$student->cardId style='cursor: default;' class='btn btn-info' data-placement='top' data-toggle='tooltip' data-original-title='View HTML Code'><i class='fa fa-file'></i></a>";
							}						
							?>

<?php if($this->authorization->isSuperAdmin()) { ?>	<button onclick="view_edit_delete_marks('<?php echo $student->stdName; ?>', '<?php echo $class->class_name; ?>', '<?php echo $class->section_name; ?>', '<?php echo $student->id; ?>', '<?php echo $sectionId; ?>', '<?php echo $class->id; ?>', '<?php echo $student->status; ?>')" style='cursor: pointer;' class='btn  btn-secondary' data-placement='top' data-toggle='tooltip' data-original-title='View, Edit and Delete Marks'><i class='fa fa-bug'></i></button>	<?php } ?>
	                    
						</td>
	                </tr>
	            <?php  } ?>
	            </tbody>
	        </table> 
	        <?php } ?>
	    </div>
	</div>
</div>

<div id="range-modal" class="modal fade" tabindex="-1" role="dialog" aria-labelledby="myModalLabel" aria-hidden="true" style="display: none;">
  <div class="modal-dialog" style="width:30%;margin: 15% auto"> 
     <div class="modal-content">  
   
        <div class="modal-header"> 
           <h4 class="modal-title"> Choose Report Cards</h4> 
           <button type="button" class="close" data-dismiss="modal" aria-hidden="true">×</button> 
        </div> 
            
        <div class="modal-body mobile">
        	<div class="row">
        		<div class="col-md-6">
        			<div class="form-group">
		            <label class="control-label">From</label>                               
		            <input placeholder="From" type="number" name="from" min="1" value="1" id="from" class="form-control"/>
		        	</div>
        		</div>
        		<div class="col-md-6">
        			<div class="form-group">
		            <label class="control-label">To</label>                 
		            <input placeholder="To" type="number" name="to" value="15" min="1" id="to" class="form-control"/>
		        	</div>
        		</div>
        	</div>
           
        </div> 
        <div class="modal-footer">
            <input type="hidden" name="publishId" id="publishId">
            <button type="button" onclick="zip_marks_cards()" id="confirmP" class="btn btn-primary" data-dismiss="modal">Confirm</button>  
            <button type="button" class="btn btn-danger" data-dismiss="modal" style="margin-bottom: 3px">Close</button>  
        </div> 
                        
    </div> 
  </div>
</div>

<div id="show_html_modal" class="modal fade" tabindex="-1" role="dialog">
	<div class="modal-dialog modal-dialog-scrollable modal-x1" style="width:60%;margin:auto;">
	<div class="modal-content" style="border-radius: 8px;">

		<div class="modal-header"> 
			<span id="gen_modal_title"><h4 class="modal-title">Generated HTML</h4></span> 
			<button type="button" class="close" data-dismiss="modal" aria-hidden="true">×</button>
		</div> 
			
		<div class="modal-body" style="overflow:auto">
			<div id="htmlpreview">
				<div class="col-12 text-center loading-icon">
					<i class="fa fa-spinner fa-spin" style="font-size: 40px;"></i>
				</div>
			</div>
		</div>

	</div> 
	</div>
</div>

<form id="downloadForm" method="post" action="<?php echo site_url('examination/assessment_marks/marckCardZip') ?>">
	<input type="hidden" name="tempId" value="<?php echo $tempId; ?>">
	<input type="hidden" name="sectionId" value="<?php echo $sectionId; ?>">
	<input type="hidden" name="class_section" value="<?php echo $class->class_name.'_'.$class->section_name; ?>">
</form>

<form id="downloadZipFile" method="post" action="<?php echo site_url('examination/assessment_marks/downloadZipFile') ?>">
	<input type="hidden" name="class_section" value="<?php echo $class->class_name.'_'.$class->section_name; ?>">
</form>

<form id="zipReportCards" method="post" action="<?php echo site_url('examination/assessment_marks/zipReportCards') ?>">
	<input type="hidden" name="report_card_ids" id="report_card_ids" value="">
	<input type="hidden" name="sectionId" value="<?php echo $sectionId; ?>">
	<input type="hidden" name="class_section" value="<?php echo $class->class_name.'_'.$class->section_name; ?>">
</form>

<!-- Re-generation fields -->
<div class="modal fade" id="update_for_regenerate_fields" tabindex="-1" role="dialog" style="width:70%;margin:auto;top:0%;" data-backdrop="static" aria-labelledby="resource-uploader-label" aria-hidden="true">
    <div class="modal-content modal-dialog" style="border-radius: 8px;">
      <div class="modal-header" style="border-bottom: 2px solid #ccc;">
        <h4 class="modal-title" id="modalHeader_h4">Add Category</h4>
        <button style="font-size: 32px;font-weight: bold;color: #e04b4a;opacity: 1;padding-top: .5rem;" type="button" class="close" data-dismiss="modal">&times;</button>
      </div>
       <form id="myform_h4">
        <div class="modal-body">
            <div class="col-md-12">
                <div class="form-group">
                    <label for="obtained_marks_h4" class="col-md-4 col-xs-12 control-label">Obtained Marks <font color="red">*</font></label>
                    <div class="col-md-8 col-xs-12">
                        <div class="input-group">
                            <span class="input-group-addon">
                                <span class="fa fa-pencil"></span>
                            </span>
                            <input type="text" class="form-control" id="obtained_marks_h4" placeholder="Enter Marks" name="obtained_marks_h4" required="true">
                        </div>
                        <div class="help-block">Write Here</div>
                    </div>
                </div>
            </div>
        </div>
        <div class="modal-footer" id="footer_h4">
            
        </div>
        </form>
    </div>
  </div>
  <!-- end -->

  <!-- Progress Bar -->
<div class="modal fade" id="progress_bar_modal" tabindex="3" role="dialog" style="width:70%;margin:auto;top:0%;" data-backdrop="static" aria-labelledby="resource-uploader-label" aria-hidden="true">
    <div class="modal-content modal-dialog" style="border-radius: 8px;">
      <div class="modal-header" style="border-bottom: 2px solid #ccc;">
        <h4 class="modal-title" id="modalHeader_h5">Add Category</h4>
        <!-- <button style="font-size: 32px;font-weight: bold;color: #e04b4a;opacity: 1;padding-top: .5rem;" type="button" class="close" data-dismiss="modal">&times;</button> -->
      </div>
        <div class="modal-body">
			<div style="height: 430px; overflow: auto;" id="table_div_tag">
								<div class=""><b>Generating Derived Subjects</b></div>
								<div id="d_sub_details" class="">Derived Subjects Not found</div> <br><br>
								<div class=""><b>Generating Derived assessments</b></div>
								<div id="d_ass_details" class="">Derived assessment not found</div> <br> <br>
								<div class=""><b>Generating Computed fields</b></div>
								<div id="c_field_details" class="">Computed fields not found</div> <br><br>
								<div class=""><b>Generating Average, highest and ranks</b></div>
								<div id="avg_high_rank_details" class="">	</div><br><br>
								<div class=""><b>Generating Report Card</b></div>
								<div id="r_card_details" class="">	</div><br><br>
			</div>
            <div id="body_name_of_generation_div" style="text-align: center;">	</div>
            <div id="body_progress_div" style="text-align: center;">	</div>
        </div>
    </div>
  </div>
  <!-- end -->

<script type="text/javascript">
	var published = [];
	var not_published = [];
	$(document).ready(function(){
		var published = JSON.parse('<?php echo json_encode($published); ?>');
		var not_published = JSON.parse('<?php echo json_encode($not_published); ?>');
	});

	function un_publish(std_id, temp_id) {
		$.ajax({
          url: "<?php echo site_url('examination/assessment_marks/changePublish');?>",
          data: {'tempId': temp_id, 'stdId':std_id, 'status':0},
          type: 'post',
          success: function(data) {
          	if(data == 1) {
          		$("#publish_"+std_id).html("<a class='btn btn-warning' onclick='publish("+std_id+", "+temp_id+")' href='javascript:void(0);'><i class='fa fa-times'></i></a>");
          		not_published.push(std_id);
          		const index = published.indexOf(std_id);
          		if (index > -1) {
				  published.splice(index, 1);
				}
          	}
          		
          },
          error: function(err) {
          	alert('Something went wrong');
            console.log(err);
          }
        });
	}

	function publish(std_id, temp_id) {
		$.ajax({
          url: "<?php echo site_url('examination/assessment_marks/changePublish');?>",
          data: {'tempId': temp_id, 'stdId':std_id, 'status':1},
          type: 'post',
          success: function(data) {
          	if(data == 1) {
          		$("#publish_"+std_id).html("<a class='btn btn-primary' onclick='un_publish("+std_id+", "+temp_id+")' href='javascript:void(0);'><i class='fa fa-check'></i></a>");
          		published.push(std_id);
          		const index = not_published.indexOf(std_id);
          		if (index > -1) {
				  not_published.splice(index, 1);
				}
          	}
          },
          error: function(err) {
          	alert('Something went wrong');
            console.log(err);
          }
        });
	}

	function un_publish_all() {
		var temp_id = <?php echo $tempId; ?>;
		$.ajax({
          url: "<?php echo site_url('examination/assessment_marks/changeMultiPublish');?>",
          data: {'tempId': temp_id, 'stdIds':published, 'status':0},
          type: 'post',
          success: function(data) {
          	if(data == 1) {
          		location.reload();
          	}
          },
          error: function(err) {
          	alert('Something went wrong');
            console.log(err);
          }
        });
	}

	function publish_all() {
		var temp_id = <?php echo $tempId; ?>;
		$.ajax({
          url: "<?php echo site_url('examination/assessment_marks/changeMultiPublish');?>",
          data: {'tempId': temp_id, 'stdIds':not_published, 'status':1},
          type: 'post',
          success: function(data) {
          	if(data == 1) {
          		location.reload();
          	}
          },
          error: function(err) {
          	alert('Something went wrong');
            console.log(err);
          }
        });
	}

	//old
	// function downloadZip() {
	// 	$("#zipper").html('Zipping').prop('disabled', true);
	// 	$("#downloadForm").submit();
	// }

	function zip_marks_cards() {
		$("#zipper").html('Zipping &nbsp;&nbsp;<i class="fa fa-spinner fa-spin"></i>').prop('disabled', true);
		var from = parseInt($("#from").val());
		var to = parseInt($("#to").val());
		if(from > to) return false;
		var report_card_ids = [];
		while(from <= to) {
			var id = $("#rep-id-"+from).val();
			from++;
			if(id) report_card_ids.push(id);
		}
		if(report_card_ids.length == 0) return false;
		downloadReportAsZip(report_card_ids);
	}

	function downloadReportAsZip(report_card_ids) {
		$("#report_card_ids").val(report_card_ids.join(','));
		$("#zipReportCards").submit();
		// $("#zipper").html('<i class="fa fa-cloud-download"></i>').prop('disabled', false);
	}

	function downloadZip() {
		//new code
		$("#zipper").html('Zipping &nbsp;&nbsp;<i class="fa fa-spinner fa-spin"></i>').prop('disabled', true);
		var class_section = '<?php echo $class->class_name.'_'.$class->section_name; ?>';
		var sectionId = '<?php echo $sectionId; ?>';
		var tempId = '<?php echo $tempId; ?>';
		$.ajax({
      url: "<?php echo site_url('examination/assessment_marks/getMarksCardLinks');?>",
      data: {'tempId': tempId, 'sectionId':sectionId, 'class_section': class_section},
      type: 'post',
      success: function(data) {
      	var cards = JSON.parse(data);
      	if(cards.length) {
      		zipFiles(cards, class_section);
      	} else {
      		alert('No data to download');
      	}
      },
      error: function(err) {
      	// alert('Something went wrong');
        console.log(err);
      }
    });
	}

	function zipFiles(cards, class_section) {
		if(cards.length) {
			var card = cards.shift();
			$.ajax({
	      url: "<?php echo site_url('examination/assessment_marks/addToZip');?>",
	      data: {'card': card, 'cards_length':cards.length, 'class_section': class_section},
	      type: 'post',
	      success: function(data) {
	      	zipFiles(cards, class_section);
	      },
	      error: function(err) {
	      	// alert('Something went wrong');
	        console.log(err);
	      }
	    });
		} else {
			$("#zipper").html('Downloading &nbsp;&nbsp;<i class="fa fa-spinner fa-spin"></i>');
			$("#downloadZipFile").submit();
			$("#zipper").html('<i class="fa fa-cloud-download"></i>').prop('disabled', false);
		}
	}

	function load(id){
		$("#V_"+id).attr('disabled', true);
		$("#loader1").show();
	}

	function unlockRemarks(tempId, stdId){
		$.ajax({
          url: "<?php echo site_url('examination/assessment_marks/unlockRemarks');?>",
          data: {'tempId': tempId, 'stdId':stdId},
          type: 'post',
          success: function(data) {
          	if(data == 1)
          		location.reload();
          },
          error: function(err) {
          	alert('Something went wrong');
            console.log(err);
          }
        });
	}

	function downloadMarksCard(stdName,url){
		alert(stdName);
		alert(url);
		$.ajax({
          url: "<?php echo site_url('examination/assessment_marks/downloadMarksCard');?>",
          data: {'url': url, 'stdName':stdName},
          type: 'post',
          success: function(data) {
          },
          error: function(err) {
          	alert('Something went wrong');
            console.log(err);
          }
        });
	}

	$("#regenAll").change(function(){
		if($(this).is(":checked")){
			$("input:checkbox[class=reGenClass]").prop('checked', true);
		} else {
			$("input:checkbox[class=reGenClass]").prop('checked', false);
		}
	});

	$("#genAll").change(function(){
		if($(this).is(":checked")){
			$("input:checkbox[class=genClass]").prop('checked', true);
		} else {
			$("input:checkbox[class=genClass]").prop('checked', false);
		}
	});

	// function callReGeneration(tempId){
	// 	$("input[name='reGenerateIds[]']:checked").each( function () {
	// 		generateReportCard(tempId, $(this).val(), 'reGen');
	// 	});
	// }

	// function callGeneration(tempId){
	// 	$("input[name='generateIds[]']:checked").each( function () {
	// 		generateReportCard(tempId, $(this).val(), 'gen');
	// 	});
	// }

	function reset_regen_status(template_id){
		$.ajax({
          url: "<?php echo site_url('examination/assessment_marks/reset_regen_status');?>",
          data: {'template_id': template_id},
          type: 'post',
          success: function(data) {
			location.reload();
          },
          error: function(err) {
          	alert('Something went wrong');
            console.log(err);
          }
        });
	}

// 	function generateReportCard(tempId, stdId, id){
// 		var type = 0;
// 		if(id == 'reGen') {
// 			$("#r_card_details").html('<span style="color:#fff;background-color:#1caf9a;padding:5px;">Re generating</span>');
// 			$("#reGenMsg_"+stdId).html('<span style="color:#fff;background-color:#1caf9a;padding:5px;">Re generating</span>');
// 			$("#regenCheck_"+stdId).hide();
// 			$("#view_"+stdId).html('<span style="color:#fff;background-color:#1caf9a;padding:5px;">Re generating</span>');
// 			type = 2;
// 		}
// 		else {
// 			$("#r_card_details").html('<span style="color:#fff;background-color:#1caf9a;padding:5px;">Generating</span>');
// 			$("#genMsg_"+stdId).html('<span style="color:#fff;background-color:#1caf9a;padding:5px;">Generating</span>');
// 			$("#genCheck_"+stdId).hide();
// 			$("#view_"+stdId).html('<span style="color:#fff;background-color:#1caf9a;padding:5px;">Generating</span>');
// 			type = 0;
// 		}

		
		
// 		$.ajax({
//             url: "<?php // echo site_url('examination/assessment_marks/testingMarksCard');?>",
//             data: {'tempId':tempId, 'stdId':stdId, 'type':type},
//             type: 'post',
//             success: function(data) {
// 				var data = JSON.parse(data);
// 				window['wait_timer_' + stdId] = setInterval(function() { 
// // #6 => if pdf path not defined
// 					if(Object.keys(data)?.length !== 0 && data.pdf_path) {
// 						check_pdf_generated(data.pdf_path, data.pdf_relative_path, stdId, id);
// 					}
// 				 }, 5000);
//             },
//             error: function(err) {
//                 console.log(err);
//             }
//         });
// 	}

	// function check_pdf_generated(pdf_path, pdf_check_path, stdId, id) {
	// 	$.ajax({
	// 		url: '<?php // echo site_url('examination/assessment_marks/check_pdf_generated'); ?>',
	// 		type: "POST",
	// 		data: { pdf_path: pdf_check_path },
	// 		success: response => {
	// 			var response = JSON.parse(response);				
	// 			var display_date_html = `<font style="color:#00701a;font-weight:700;">${response.date}</font>`;
	// 			if(response.status == 1) {
	// 				//Stop the timer
	// 				clearInterval(window['wait_timer_' + stdId]);

	// 				//Reset the UI
	// 				var type = 0;
	// 				let xnumber = Math.random() * 1000;
	// 				if(id == 'reGen') {
	// 					var download_url = $("#regenCheck_"+stdId).attr('data-downloadurl');
	// 					var html = `
	// 						<a target='_blank' href='${pdf_path}?a=${xnumber}' class='btn  btn-primary' data-placement='top' data-toggle='tooltip' data-original-title='View Marks card' style="background:#00701a"><i class='fa fa-eye'></i></a>
	// 						<a href="${download_url}" class='btn  btn-primary' data-placement='top'  data-toggle='tooltip' data-original-title='Download Marks card' style="background:#00701a"><i class='fa fa-download'></i></a>
	// 					`;
	// 					$("#reGenMsg_"+stdId).html('');
	// 					$("#regenCheck_"+stdId).show();
	// 					$("#regenCheck_"+stdId).removeAttr('checked');
	// 					$("#view_"+stdId).html(html);
	// 					$("#viewdate_"+stdId).html(display_date_html);
	// 					type = 2;					}
	// 				else {
	// 					var download_url = $("#genCheck_"+stdId).attr('data-downloadurl');
	// 					download_url = download_url.replace('cardidplaceholder', response.id);
	// 					var html = `
	// 						<a target='_blank' href='${pdf_path}?a=${xnumber}' class='btn  btn-primary' data-placement='top' data-toggle='tooltip' data-original-title='View Marks card' style="background:#00701a"><i class='fa fa-eye'></i></a>
	// 						<a href="${download_url}" class='btn  btn-primary' data-placement='top'  data-toggle='tooltip' data-original-title='Download Marks card' style="background:#00701a"><i class='fa fa-download'></i></a>
	// 					`;
	// 					$("#genMsg_"+stdId).html('<span style="color:#fff;background-color:#00701a;padding:5px 10px;border-radius:4px;line-height:2rem;">Generated</span>');
	// 					$("#genCheck_"+stdId).removeAttr('checked');
	// 					$("#view_"+stdId).html(html);
	// 					$("#viewdate_"+stdId).html(display_date_html);
	// 					type = 0;					}
	// 			}
	// 		}
	// 	});
	// }

	$('#show_html_modal').on('show.bs.modal', function (event) {
		var student_name = $(event.relatedTarget).data('student_name');
		$('#show_html_modal').find('.modal-header #gen_modal_title').html(`<h4>Generated HTML of ${student_name}</h4>`);
		var student_card_id = $(event.relatedTarget).data('student_card_id');
		$.ajax({
			url : '<?php echo site_url('examination/assessment_marks/get_generated_html'); ?>',
			type: 'post',
			data: {'student_card_id': student_card_id},
			dataType: 'JSON',
			success: function(data) {
				$('#show_html_modal').find('.modal-body #htmlpreview').html(`${data.gen_html || 'HTML Not Generated'}`);
			},
			error: function(err) {
				console.log(err);
			}
		});
	});
	
	$('#show_html_modal').on('hide.bs.modal', function (event) {
		location.reload();
	});

	let generation_status= 'Generated';
	function view_edit_delete_marks(std_name, class_name, section_name, student_id, class_section_id, class_id, generation_status) {
		generation_status= generation_status;
		$("#view_edit_delete_marks_modal #view_edit_delete_marks_form").trigger('reset');
		$("#view_edit_delete_marks_modal #view_edit_delete_marks_div").html(``);
		$("#view_edit_delete_marks_modal #view_edit_delete_marks_ass_name").html(``);
		$("#view_edit_delete_marks_modal #view_edit_delete_marks_generation_type").html(``);
		$("#view_edit_delete_marks_modal #view_edit_delete_marks_ass_id").html(``);

		$("#view_edit_delete_marks_modal #view_edit_delete_marks_modalHeader").html(`Marks of student - <b>${std_name}</b> of class - <b>${class_name} ${section_name}</b>`);

		$("#view_edit_delete_marks_modal #view_edit_delete_marks_student_id").val(student_id);
		$("#view_edit_delete_marks_modal #view_edit_delete_marks_class_section_id").val(class_section_id);
		$("#view_edit_delete_marks_modal #view_edit_delete_marks_class_id").val(class_id);

		$("#view_edit_delete_marks_modal").modal('show');
	}

	async function get_student_marks_details(current) {
		var generation_type= $("#view_edit_delete_marks_modal #filter_title_input").find(':selected').data('generation_type')
		await $("#view_edit_delete_marks_modal #view_edit_delete_marks_div").html(`<img src="<?php echo base_url('assets/img/ajax-loader.gif');?>">`);
		let assessment_id= $("#view_edit_delete_marks_modal #filter_title_input").val();
		let student_id= $("#view_edit_delete_marks_modal #view_edit_delete_marks_student_id").val();
		let class_section_id= $("#view_edit_delete_marks_modal #view_edit_delete_marks_class_section_id").val();
		let class_id= $("#view_edit_delete_marks_modal #view_edit_delete_marks_class_id").val();

		$("#view_edit_delete_marks_modal #view_edit_delete_marks_ass_name").html(`<b>Assessment: </b>${$("#view_edit_delete_marks_modal #filter_title_input").find(':selected').text()}`);
		$("#view_edit_delete_marks_modal #view_edit_delete_marks_generation_type").html(`<b>Generation Type: </b>${$("#view_edit_delete_marks_modal #filter_title_input").find(':selected').data('generation_type')}`);
		$("#view_edit_delete_marks_modal #view_edit_delete_marks_ass_id").html(`<b>Assessment Id: </b>${assessment_id}`);
		if(assessment_id) {
			$.ajax({
				url: '<?php echo site_url('examination/assessment_marks/get_student_marks_details'); ?>',
				type: "post",
				data: {assessment_id, student_id, class_section_id, class_id},
				success(data) {
					var p_data = JSON.parse(data);
					let marks_table= `
									<table class="table table-bordered">
										<thead>
											<tr>
												<th>#</th>
												<th>AEMS Id</th>
												<th>Entity Id</th>
												<th>Entity Name</th>
												<th>Entity Mapping String</th>
												<th>Group Id</th>
												<th>Group Name</th>
												<th>Group Mapping String</th>

												<th>Is Elective?</th>
												<th>Elective Group Id</th>
												<th>Elective Mapping String</th>

												<th>Total Marks</th>
												<th>Obtained (Click to Update)</th>
												<th>Action</th>
											</tr>
										</thead>
										<tbody>
								`;
					
					var sn= 1;			
					for(var v of p_data) {
						if(v.is_derived_subject && (v.is_derived_subject == '1' || v.is_derived_subject == 1) || generation_type == 'Auto') {
							var editable= `
											<td>
												${v.evaluation_type == 'marks' ? v.marks : v.grade}
											</td>
											<td>
												<button type="button" class="btn btn-danger" onclick="delete_marks_of_a_student('${v.is_editable}', '${v.aems_id}', '${v.aem_name}')"><span class="fa fa-trash-o"></span></button>
												<button disabled type="button" class="btn btn-warning" ><span class="fa fa-edit"></span></button>		
											</td>
											`;
						} else {
							var editable= `
											<td>
												<input onclick="make_active_inactive('btn_1_${v.aems_id}', 'btn_2_${v.aems_id}', 'view_edit_delete_marks_input_id_${v.aems_id}')" style="cursor: pointer;" readonly class="form-control" value="${v.evaluation_type == 'marks' ? v.marks : v.grade}" id="view_edit_delete_marks_input_id_${v.aems_id}" />
												<button id="btn_1_${v.aems_id}" style="display: none;" type="button" class="btn btn-success" onclick="edit_marks_of_a_student('${v.is_editable}', '${v.aems_id}', '${v.aem_name}', '${v.total_marks}', '${v.evaluation_type}')">Save</button>
												<button id="btn_2_${v.aems_id}" style="display: none;" type="button" class="btn btn-secondary" onclick="cancel_editing('btn_1_${v.aems_id}', 'btn_2_${v.aems_id}', 'view_edit_delete_marks_input_id_${v.aems_id}')">Cancel</button>
											</td>
											<td>
												<button type="button" class="btn btn-danger" onclick="delete_marks_of_a_student('${v.is_editable}', '${v.aems_id}', '${v.aem_name}')"><span class="fa fa-trash-o"></span></button>
												<button type="button" class="btn btn-warning" onclick="open_modal_update_marks_and_generate_fields('${v.is_editable}', '${v.aems_id}', '${v.aem_name}', '${v.total_marks}', '${v.evaluation_type}', '${v.aem_id}', '${v.entities_group_id}', '${v.elective_group_id}', '${v.evaluation_type == 'marks' ? v.marks : v.grade}', ${assessment_id}, ${student_id}, ${class_section_id}, ${class_id})"><span class="fa fa-edit"></span></button>		
											</td>
											`;
						}
						marks_table += `
										<tr>
											<td>${sn++}</td>
											<td>${v.aems_id}</td>
											<td>${v.aem_id}</td>
											<td>${v.aem_name}</td>
											<td>${v.aem_mapping_string}</td>
											<td>${v.entities_group_id}</td>
											<td>${v.entities_group_group_name || v.entities_group_group_name_2}</td>
											<td>${v.entities_group_mapping_string}</td>

											<td>${v.is_elective == '1' ? 'Yes' : 'No'}</td>
											<td>${v.elective_group_id}</td>
											<td>${v.elective_mapping_string}</td>

											<td>${v.total_marks}</td>
											
											${editable}
										</tr>
										`;
					}
					marks_table += `
								</tbody>
								</table>
								`;

								
					$("#view_edit_delete_marks_modal #view_edit_delete_marks_div").html(marks_table);
				}
			});
		}
	}

	function open_modal_update_marks_and_generate_fields(is_editable, aems_id, aem_name, total_marks, evaluation_type, aem_id, entities_group_id, elective_group_id, marks_grade_value, assessment_id, student_id, class_section_id, class_id) {
		if(is_editable != '1') {
			return alert(`Marks for subject - ${aem_name} is not editable. Contact to your exam administrator`);
		}
		$("#update_for_regenerate_fields #modalHeader_h4").html(`Update Marks for - ${aem_name}`);
		$("#update_for_regenerate_fields #obtained_marks_h4").val(marks_grade_value);
		$("#update_for_regenerate_fields #footer_h4").html(`
															<button type="button" class="btn btn-warning" onclick="update_marks_and_generate_fields('${is_editable}', '${aems_id}', '${aem_name}', '${total_marks}', '${evaluation_type}', '${aem_id}', '${entities_group_id}', '${elective_group_id}', ${assessment_id}, ${student_id}, ${class_section_id}, ${class_id})"><span class="fa fa-edit"></span> Update Marks and Start Re-generation Fields</button>
															`);
		$("#view_edit_delete_marks_modal").modal('hide');
		$("#update_for_regenerate_fields").modal('show');
	}

	function update_marks_and_generate_fields(is_editable, aems_id, aem_name, total_marks, evaluation_type, aem_id, entities_group_id, elective_group_id, assessment_id, student_id, class_section_id, class_id) {
		var updated_marks= $(`#update_for_regenerate_fields input#obtained_marks_h4`).val();
		if((evaluation_type == 'marks' && +updated_marks) > (+total_marks)) {
			return alert(`Marks can not be greather than total marks`);
		}
		var confirm_once= confirm(`You are upating marks for subject - ${aem_name} by ${+updated_marks ? updated_marks : 0.00}. If you proceed it, it will regenerate all the computed fields as well in sorting orer wise. To avoid wrong calculation of computed fields, Please... make sure that all the COMPUTED FIELDS are in correct sorting order. If you sure about it, click on 'OK' button to update the marks?`);

		if(confirm_once) {
			$.ajax({
				url: '<?php echo site_url('examination/assessment_marks/edit_marks_of_a_student'); ?>',
				type: "post",
				data: {aems_id, updated_marks, evaluation_type},
				success(data) {
					var p_data = JSON.parse(data);
					// $("#view_edit_delete_marks_modal").modal('show');
					$("#update_for_regenerate_fields").modal('hide');
					// get_student_marks_details();
					re_generate_all_derived_fields(is_editable, aems_id, aem_name, total_marks, evaluation_type, aem_id, entities_group_id, elective_group_id, assessment_id, student_id, class_section_id, class_id, updated_marks);
				}
			});
		}

	}

	function start_counting() {
        const counters = document.querySelectorAll(".counter");
        counters.forEach((counter) => {
            const target = parseInt(counter.getAttribute("data-target"));
            let count = 0;

            const updateCounter = () => {
                const increment = target / 100; // Adjust the increment speed as needed
                count += increment;

                if (count < target) {
                    counter.innerText = Math.floor(count);
                    requestAnimationFrame(updateCounter);
                } else {
                    counter.innerText = target;
                }
            };

            updateCounter();
        });
    }

	
	async function fetch_and_regenerate_derived_subject(is_editable, aems_id, aem_name, total_marks, evaluation_type, aem_id, entities_group_id, elective_group_id, assessment_id, student_id, class_section_id, class_id) {
		await $("#view_edit_delete_marks_modal").modal('hide');
		await $("#update_for_regenerate_fields").modal('hide');
		await $("#progress_bar_modal").modal('show');
		await $("#progress_bar_modal .modal-body #body_name_of_generation_div").html(`<h2>Generating Derived Subjects <span class="counter" data-target="100"></span>%</h2> 
																		</div>
																	`);
		await $("#progress_bar_modal .modal-body #body_progress_div").html(`
																		<span class="spinner-border spinner-border-sm" style="height: 80px; width: 80px;">
																		</span>
																	`);
		start_counting();
		await $.ajax({
				url: '<?php echo site_url('examination/assessment_marks/fetch_and_regenerate_derived_subject'); ?>',
				type: "post",
				data: {elective_group_id, entities_group_id, aem_id, assessment_id, student_id, class_section_id, class_id},
				success(data) {
					var p_data = JSON.parse(data);
					$("#progress_bar_modal .modal-body #body_progress_div").html(``);
					$("#d_sub_status").html((p_data.found).toUpperCase());
					if(p_data.status && ( p_data.status == true || p_data.status == 'true' || p_data.status == 1)) {
						$("#d_sub_success").html(p_data.found == 'yes' ? 'YES' : 'NA');
					} else {
						$("#d_sub_success").html(p_data.found == 'yes' ? 'NO' : 'NA');
					}

					if(p_data.status && ( p_data.status == true || p_data.status == 'true' || p_data.status == 1) && p_data.found == 'yes') {
						status_html_details = `<div class=""><table class="table table-bordered"> <tr>
										<td><b>Derived Subject's Name</b></td>
										<td><b>Old Marks</b></td>
										<td><b>New Marks</b></td>
										</tr>`;
						for(var v of p_data.comparioson_marks_arr) {
							status_html_details+= `<tr>
								<td>${v.name}</td>
								<td>${v.marks}</td>
								<td>${v.derived_marks}</td>
							</tr>
							`;
						}
						status_html_details += `</table></div>`;
						$("#d_sub_details").html(status_html_details);
					}
					
				}
			});
		return 1;
	}

	async function fetch_and_regenerate_derived_subjects_in_derived_assessments(is_editable, aems_id, aem_name, total_marks, evaluation_type, aem_id, entities_group_id, elective_group_id, assessment_id, student_id, class_section_id, class_id) {
		await $("#progress_bar_modal .modal-body #body_name_of_generation_div").html(`<h2>Generating Derived Assessments for derived Subjects <span class="counter" data-target="100"></span>%</h2> 
																		</div>
																	`);
		await $("#progress_bar_modal .modal-body #body_progress_div").html(`
																			<span class="spinner-border spinner-border-sm" style="height: 80px; width: 80px;">
																			</span>
																	`);
		start_counting();
		await $.ajax({
				url: '<?php echo site_url('examination/assessment_marks/fetch_and_regenerate_derived_subjects_in_derived_assessments'); ?>',
				type: "post",
				data: {elective_group_id, entities_group_id, aem_id, assessment_id, student_id, class_section_id, class_id},
				success(data) {
					var p_data = JSON.parse(data);
					$("#progress_bar_modal .modal-body #body_name_of_generation_div").html('');
					// var status_html= `<div class="col-md-12">`;
					$("#d_ass_status").html((p_data.found).toUpperCase());
					if(p_data.status && ( p_data.status == true || p_data.status == 'true' || p_data.status == 1)) {
						$("#d_ass_success").html(p_data.found == 'yes' ? 'YES' : 'NA');
					} else {
						$("#d_ass_success").html(p_data.found == 'yes' ? 'YES' : 'NA');
					}
					// status_html += `</div>`;
					// var prev= $("#body_status_div").html();
					// $("#body_status_div").html(prev + status_html + `<br>`);
					
					
					if(p_data.status && ( p_data.status == true || p_data.status == 'true' || p_data.status == 1) && p_data.found == 'yes') {
						status_html_details = `<div class=""><table class="table table-bordered"> <tr>
										<td><b>Derived Assessment's Name</b></td>
										<td><b>Old Marks</b></td>
										<td><b>New Marks</b></td>
										</tr>`;
						for(var v of p_data.comparioson_marks_arr) {
							if(!v.name || v.name == undefined || v.name == 'undefined') {
								continue;
							}
							status_html_details+= `<tr>
								<td>${v.name}</td>
								<td>${v.marks}</td>
								<td>${v.derived_marks}</td>
							</tr>
							`;
						}
						status_html_details += `</table></div>`;
						// var prev_details= $("#body_status_details_div").html();
						$("#d_ass_details").html(status_html_details);
					}
					
				}
			});
		// await $("#progress_bar_modal .modal-body #body_progress_div").html(`<button style="width: 2in;" onclick="finish_regeneration()" class="btn btn-success">Finish</button>`);
		return 1;
	}

	async function fetch_and_regenerate_ccomputed_fields(is_editable, aems_id, aem_name, total_marks, evaluation_type, aem_id, entities_group_id, elective_group_id, assessment_id, student_id, class_section_id, class_id) {
		await $("#progress_bar_modal .modal-body #body_name_of_generation_div").html(`<h2>Generating Computed Fields <span class="counter" data-target="100"></span>%</h2> 
																		</div>
																	`);
		await $("#progress_bar_modal .modal-body #body_progress_div").html(`
																			<span class="spinner-border" style="height: 80px; width: 80px;">
																			</span>
																	`);
		start_counting();
		await $.ajax({
				url: '<?php echo site_url('examination/assessment_marks/fetch_and_regenerate_ccomputed_fields'); ?>',
				type: "post",
				data: {elective_group_id, entities_group_id, aem_id, assessment_id, student_id, class_section_id, class_id},
				success(data) {
					var p_data = JSON.parse(data);
					$("#progress_bar_modal .modal-body #body_name_of_generation_div").html('');
					$("#c_field_status").html((p_data.found).toUpperCase());
					if(p_data.status && ( p_data.status == true || p_data.status == 'true' || p_data.status == 1)) {
						$("#c_field_success").html(p_data.found == 'yes' ? 'YES' : 'NA');
					} else {
						$("#c_field_success").html(p_data.found == 'yes' ? 'YES' : 'NA');
					}

					if(p_data.status && ( p_data.status == true || p_data.status == 'true' || p_data.status == 1) && p_data.found == 'yes') {
						status_html_details = `<div class=""><table class="table table-bordered"> <tr>
										<td><b>Computed Field Name</b></td>
										<td><b>Old Result</b></td>
										<td><b>New Result</b></td>
										</tr>`;
						for(var v of p_data.comparioson_marks_arr) {
							status_html_details+= `<tr>
								<td>${v.name}</td>
								<td>${v.marks}</td>
								<td>${v.derived_marks}</td>
							</tr>
							`;
						}
						status_html_details += `</table></div>`;
						// var prev_details= $("#body_status_details_div").html();
						$("#c_field_details").html(status_html_details);
					}
					
				}
			});
		
		return 1;
	}

	async function regenerate_average_high_and_rank(student_id, class_section_id, class_id, aem_id, entities_group_id, elective_group_id, assessment_id, aems_id, updated_marks) {
		await $("#progress_bar_modal .modal-body #body_name_of_generation_div").html(`<h2>Generating Average, Highest and Rank <span class="counter" data-target="100"></span>%</h2> 
																		</div>
																	`);
		await $("#progress_bar_modal .modal-body #body_progress_div").html(`
																			<span class="spinner-border" style="height: 80px; width: 80px;">
																			</span>
																	`);
		start_counting();
		await $.ajax({
				url: '<?php echo site_url('examination/assessment_marks/regenerate_average_high_and_rank'); ?>',
				type: "post",
				data: {student_id, class_section_id, class_id, aem_id, assessment_id, aems_id, updated_marks, entities_group_id, elective_group_id},
				success(data) {
					var p_data = JSON.parse(data);
					$("#progress_bar_modal .modal-body #body_name_of_generation_div").html('');
					if(p_data.section_status == '1' || p_data.class_status == '1' || p_data.sec_rank_status == '1' || p_data.class_rank_status == '1') {
						$("#avg_high_rank_status").html('YES');
					} else {
						$("#avg_high_rank_status").html('NO');
					}

					if(p_data.section_status == '1' || p_data.class_status == '1' || p_data.sec_rank_status == '1' || p_data.class_rank_status == '1') {
						status_html_details = `<div class=""><table class="table table-bordered"> <tr>
										<td><b>Regeneration Name</b></td>
										<td><b>Old Result</b></td>
										<td><b>New Result</b></td>
										</tr>`;
						// if(p_data.section_status == '1') {
							status_html_details+= `<tr>
														<td>Section Average</td>
														<td>${p_data.section_old_avg}</td>
														<td>${p_data.section_new_avg}</td>
													</tr>
													<tr>
														<td>Section Highest</td>
														<td>${p_data.section_old_max}</td>
														<td>${p_data.section_new_max}</td>
													</tr>
													`;
						// }
						// if(p_data.class_status == '1') {
							status_html_details+= `<tr>
														<td>Class Average</td>
														<td>${p_data.old_class_avg}</td>
														<td>${p_data.new_class_avg}</td>
													</tr>
													<tr>
														<td>Class Highest</td>
														<td>${p_data.old_class_max}</td>
														<td>${p_data.new_class_max}</td>
													</tr>
													`;
						// }
						// if(p_data.sec_rank_status == '1') {
							status_html_details+= `<tr>
														<td>Section Rank</td>
														<td>${p_data.old_section_rank}</td>
														<td>${p_data.new_section_rank}</td>
													</tr>
													`;
						// }
						// if(p_data.class_rank_status == '1') {
							status_html_details+= `<tr>
														<td>Class Rank</td>
														<td>${p_data.old_class_rank}</td>
														<td>${p_data.new_class_rank}</td>
													</tr>
													`;
						// }
						status_html_details += `</table></div>`;
						// var prev_details= $("#body_status_details_div").html();
						$("#avg_high_rank_details").html(status_html_details);
					}
					
				}
			});
	}

	async function fetch_and_regenerate_report_card(student_id) {
		await $("#progress_bar_modal .modal-body #body_name_of_generation_div").html(`<h2><b>Generating report card and it may take some time, Please Wait...</b></h2> 
																		</div>
																	`);
		await $("#progress_bar_modal .modal-body #body_progress_div").html(`
																			<span class="spinner-border" style="height: 80px; width: 80px;">
																			</span>
																	`);
		start_counting();
		if(generation_status.trim() == 'Generated') {
			await $("#r_card_status").html('YES');
			await generateReportCard_of_this_student('<?php echo $tempId; ?>', student_id, 'reGen');
		}else{
			await $("#r_card_status").html('NO');
			await generateReportCard_of_this_student('<?php echo $tempId; ?>', student_id, 'gen');
		}
		return 1;
	}

	function finish_regeneration() {
		$("#body_status_div").html('');
		$("#progress_bar_modal").modal('hide');
	}

	async function re_generate_all_derived_fields(is_editable, aems_id, aem_name, total_marks, evaluation_type, aem_id, entities_group_id, elective_group_id, assessment_id, student_id, class_section_id, class_id, updated_marks) {
		await $("#progress_bar_modal #modalHeader_h5").html(`Re-generating Derived Marks and Report Card...`);
		await fetch_and_regenerate_derived_subject(is_editable, aems_id, aem_name, total_marks, evaluation_type, aem_id, entities_group_id, elective_group_id, assessment_id, student_id, class_section_id, class_id);
		await fetch_and_regenerate_derived_subjects_in_derived_assessments(is_editable, aems_id, aem_name, total_marks, evaluation_type, aem_id, entities_group_id, elective_group_id, assessment_id, student_id, class_section_id, class_id);
		await fetch_and_regenerate_ccomputed_fields(is_editable, aems_id, aem_name, total_marks, evaluation_type, aem_id, entities_group_id, elective_group_id, assessment_id, student_id, class_section_id, class_id);
		await regenerate_average_high_and_rank(student_id, class_section_id, class_id, aem_id, entities_group_id, elective_group_id, assessment_id, aems_id, updated_marks);
		await fetch_and_regenerate_report_card(student_id);
		
	}

	function cancel_editing(button_id, cancel_id, input_id) {
		$(`#${input_id}`).prop('readonly', true)
		$(`#${button_id}, #${cancel_id}`).hide();
	}

	function make_active_inactive(button_id, cancel_id, input_id) {
		if ($(`#${input_id}`).is('[readonly]')) {
			$(`#${input_id}`).prop('readonly', false)
			$(`#${button_id}, #${cancel_id}`).show();
		}
	}

	function edit_marks_of_a_student(is_editable, aems_id, aem_name, total_marks, evaluation_type) {
		if(is_editable != '1') {
			return alert(`Marks for subject - ${aem_name} is not editable. Contact to your exam administrator`);
		}
		var updated_marks= $(`#view_edit_delete_marks_modal #view_edit_delete_marks_input_id_${aems_id}`).val();
		if((evaluation_type != 'marks' && +updated_marks) > (+total_marks)) {
			return alert(`Marks can not be greather than total marks`);
		}
		var confirm_once= confirm(`You are upating marks for subject - ${aem_name} by ${+updated_marks ? updated_marks : 0.00}. Are you sure?`);
		if(confirm_once) {
			$.ajax({
				url: '<?php echo site_url('examination/assessment_marks/edit_marks_of_a_student'); ?>',
				type: "post",
				data: {aems_id, updated_marks, evaluation_type},
				success(data) {
					var p_data = JSON.parse(data);
					get_student_marks_details();
					
				}
			});
		}
	}

	function delete_marks_of_a_student(is_editable, aems_id, aem_name) {
		if(is_editable != '1') {
			return alert(`Marks for subject - ${aem_name} is not deletable. Contact to your exam administrator`);
		}
		var confirm_once= confirm(`You are deleting subject - ${aem_name}'s marks. You can not revert it back. Are you sure?`);
		if(confirm_once) {
			$.ajax({
				url: '<?php echo site_url('examination/assessment_marks/delete_marks_of_a_student'); ?>',
				type: "post",
				data: {aems_id},
				success(data) {
					var p_data = JSON.parse(data);
					get_student_marks_details();
					
				}
			});
		}
	}

	function get_assessments(ass_type) {
		var class_id= '<?php echo $class->id; ?>';
		$("#view_edit_delete_marks_ass_name, #view_edit_delete_marks_generation_type, #view_edit_delete_marks_ass_id, #view_edit_delete_marks_div").html('');
		$.ajax({
			url: '<?php echo site_url('examination/assessment_marks/get_assessments_by_type'); ?>',
			type: "post",
			data: {ass_type, class_id},
			success(data) {
				var p_data = JSON.parse(data);
				if(p_data.length) {
					var ass= `<option>Select Assessment</option>`;
					for(var v of p_data) {
						ass += `<option data-generation_type="${v.generation_type}" value="${v.id}">${v.long_name} (${v.short_name})</option>`;
					}
					$("#filter_title_input").html(ass);
				}
				
			}
		});
	}

	async function generateReportCard_of_this_student(tempId, stdId, id){
		var type = 0;
		if(id == 'reGen') {
			$("#r_card_details").html('<span style="color:#fff;background-color:#1caf9a;padding:5px;">Re generating</span>');
			$("#reGenMsg_"+stdId).html('<span style="color:#fff;background-color:#1caf9a;padding:5px;">Re generating</span>');
			$("#regenCheck_"+stdId).hide();
			$("#view_"+stdId).html('<span style="color:#fff;background-color:#1caf9a;padding:5px;">Re generating</span>');
			type = 2;
		}
		else {
			$("#r_card_details").html('<span style="color:#fff;background-color:#1caf9a;padding:5px;">Generating</span>');
			$("#genMsg_"+stdId).html('<span style="color:#fff;background-color:#1caf9a;padding:5px;">Generating</span>');
			$("#genCheck_"+stdId).hide();
			$("#view_"+stdId).html('<span style="color:#fff;background-color:#1caf9a;padding:5px;">Generating</span>');
			type = 0;
		}

		
		
		await $.ajax({
            url: "<?php echo site_url('examination/assessment_marks/testingMarksCard');?>",
            data: {'tempId':tempId, 'stdId':stdId, 'type':type},
            type: 'post',
            success: function(data) {
				var data = JSON.parse(data);
				setTimeout(() => {
					check_pdf_generated_for_this_student(data.pdf_path, data.pdf_relative_path, stdId, id)
				}, 9000);
            },
            error: function(err) {
                console.log(err);
            }
        });
		return 1;
	}

	async function check_pdf_generated_for_this_student(pdf_path, pdf_check_path, stdId, id) {
		await $.ajax({
			url: '<?php echo site_url('examination/assessment_marks/check_pdf_generated'); ?>',
			type: "POST",
			data: { pdf_path: pdf_check_path },
			success: response => {
				var response = JSON.parse(response);				
				var display_date_html = `<font style="color:#00701a;font-weight:700;">${response.date}</font>`;
				if(response.status == 1) {
					//Stop the timer

					//Reset the UI
					var type = 0;
					let xnumber = Math.random() * 1000;
					if(id == 'reGen') {
						var download_url = $("#regenCheck_"+stdId).attr('data-downloadurl');
						var html = `
							<a target='_blank' href='${pdf_path}?a=${xnumber}' class='btn  btn-primary' data-placement='top' data-toggle='tooltip' data-original-title='View Marks card' style="background:#00701a"><i class='fa fa-eye'></i></a>
							<a href="${download_url}" class='btn  btn-primary' data-placement='top'  data-toggle='tooltip' data-original-title='Download Marks card' style="background:#00701a"><i class='fa fa-download'></i></a>
						`;
						$("#reGenMsg_"+stdId).html('');
						$("#regenCheck_"+stdId).show();
						$("#regenCheck_"+stdId).removeAttr('checked');
						$("#view_"+stdId).html(html);
						$("#r_card_details").html(html);
						$("#viewdate_"+stdId).html(display_date_html);
						type = 2;
						$("r_card_success").html('YES');
						$("#progress_bar_modal .modal-body #body_name_of_generation_div").html('');
						$("#progress_bar_modal .modal-body #body_progress_div").html(`<button style="width: 2in;" onclick="finish_regeneration()" class="btn btn-success">Finish</button>`);
					}
					else {
						var download_url = $("#genCheck_"+stdId).attr('data-downloadurl');
						download_url = download_url.replace('cardidplaceholder', response.id);
						var html = `
							<a target='_blank' href='${pdf_path}?a=${xnumber}' class='btn  btn-primary' data-placement='top' data-toggle='tooltip' data-original-title='View Marks card' style="background:#00701a"><i class='fa fa-eye'></i></a>
							<a href="${download_url}" class='btn  btn-primary' data-placement='top'  data-toggle='tooltip' data-original-title='Download Marks card' style="background:#00701a"><i class='fa fa-download'></i></a>
						`;
						$("#genMsg_"+stdId).html('<span style="color:#fff;background-color:#00701a;padding:5px 10px;border-radius:4px;line-height:2rem;">Generated</span>');
						$("#genCheck_"+stdId).removeAttr('checked');
						$("#view_"+stdId).html(html);
						$("#r_card_details").html(html);
						$("#viewdate_"+stdId).html(display_date_html);
						type = 0;
						$("r_card_success").html('YES');
						$("#progress_bar_modal .modal-body #body_name_of_generation_div").html('');
						$("#progress_bar_modal .modal-body #body_progress_div").html(`<button style="width: 2in;" onclick="finish_regeneration()" class="btn btn-success">Finish</button>`);
					}
				} else if(response.status == 0 || response.status == '0'){
					$("r_card_success").html('NO');
				}
			}
		});
		return 1;
	}


	function downloadAllOneByOne(type) {

		var ids = JSON.parse('<?php echo json_encode($storeAllIds); ?>');
		var names = JSON.parse('<?php echo json_encode($storeAllNames); ?>');
		if(ids.length) {
			if(type == 'zip'){
				$("#direct_downloader_zip").prop('disabled', true).html('Plase Wait...');
			}else{
				$("#direct_downloader_unzip").prop('disabled', true).html('Plase Wait...');
			}
			$.ajax({
				url: '<?php  echo site_url('examination/assessment_marks/get_all_ids_path'); ?>',
				type: 'post',
				data: {ids, names},
				async: true,
				success: function(data) {
					var p_data= JSON.parse(data);
					// console.log(p_data);
					if(type == 'zip'){
						zipAllOneByOne(p_data);
					}else{
						downloadFiles(p_data);
					}

					// p_data.forEach(function(file) {
					// 	downloadFile(file.url, file.filename);
					// });


				},
				error: function(err) {
					console.log(err);
					// reject();
				}
			});
		}
	}

	async function downloadFiles(p_data) {
		for (var i = 0; i < p_data.length; i++) {
        try {
            const result = await asyncOperation(p_data[i].url, p_data[i].filename);
            await delay(500); // Adding a 1-second delay between downloads
			if(i == p_data.length - 1) {
				$("#direct_downloader_unzip").prop('disabled', false).html('Download All');
			}
        } catch (error) {

        }
    }

	}

function asyncOperation(url, filename) {
    return new Promise((resolve, reject) => {
        var xhr = new XMLHttpRequest();
        xhr.open('GET', url, true);
        xhr.responseType = 'blob';
        xhr.onload = function() {
            if (xhr.status === 200) {
                var blob = new Blob([xhr.response], { type: 'application/octet-stream' });
                var a = document.createElement('a');
                a.href = window.URL.createObjectURL(blob);
                a.download = filename;
                document.body.appendChild(a);
                a.click();
                document.body.removeChild(a);
                window.URL.revokeObjectURL(a.href);  // Clean up the URL object
                resolve();
            } else {
                reject(`Failed to download: ${filename}`);
            }
        };
        xhr.onerror = function() {
            reject('Network error');
        };
        xhr.send();
    });

}
function delay(ms) {
    return new Promise(resolve => setTimeout(resolve, ms));
}

var class_section ='<?php echo $class->class_name.'-'.$class->section_name ?>';
async function zipAllOneByOne(p_data) {
	var zip = new JSZip();
  var folder = zip.folder(class_section+" progress_cards");

  for (var i = 0; i < p_data.length; i++) {
      try {
          const blob = await fetchFile(p_data[i].url);
          folder.file(p_data[i].filename, blob);
      } catch (error) {
      }
  }

  zip.generateAsync({type:"blob"})
  .then(function(content) {
      saveAs(content,  class_section+" progress_cards.zip");
		$("#direct_downloader_zip").prop('disabled', false).html('Zip All');
  });
}
function fetchFile(url) {
  return fetch(url)
    .then(response => {
      if (!response.ok) throw new Error('Network response was not ok.');
      return response.blob();
	});
}


</script>


		
	


<div class="modal fade" id="view_edit_delete_marks_modal" tabindex="-1" role="dialog" style="width:90%;margin:auto;top:0%" data-backdrop="static" aria-labelledby="resource-uploader-label" aria-hidden="true">
    <div class="modal-content modal-dialog" style="border-radius: 8px;">
      <div class="modal-header" style="border-bottom: 2px solid #ccc;">
        <h4 class="modal-title" id="view_edit_delete_marks_modalHeader"></h4>
        <button style="font-size: 32px;font-weight: bold;color: #e04b4a;opacity: 1;padding-top: .5rem;" type="button" class="close" data-dismiss="modal">&times;</button>
      </div>
       <form id="view_edit_delete_marks_form">
        <div class="modal-body" style="height: 440px; overflow: auto;">
			<div class="col-md-12" id="filter_and_title" style="margin: 0 0 15px 0;">
			<span class="col-md-6">
				<label for="enternal_ass" class="">Enternal Assessment</label>
				<span style="opacity: 0;" class="">P</span>
				<input onclick="get_assessments('Manual')" type="radio" class="radio-inline " id="enternal_ass" name="ass_type" value="enternal" checked>
				<span style="opacity: 0;" class="">PPP</span>
				<label for="derived_ass" class="">Derived Assessment</label>
				<span style="opacity: 0;" class="">P</span>
				<input onclick="get_assessments('Auto')" type="radio" class="radio-inline " id="derived_ass" name="ass_type" value="derived">
			</span>
				<select name="" id="filter_title_input" class="form-control col-md-6" onchange="get_student_marks_details(this)">
					<option data-generation_type="" value="">Select Assessment</option>
					<?php if(!empty($assessments)) { foreach($assessments as $key =>$val) { ?>
						<option data-generation_type="<?php echo ucwords($val->generation_type); ?>" value="<?php echo $val->id; ?>"><?php echo ucwords($val->long_name. ' (' .$val->short_name. ')'); ?></option>
					<?php }} ?>
				</select>
				<!-- <span class="col-md-6">AEMS - assessments_entities_marks_students (Table)</span> -->
				<input type="hidden" id="view_edit_delete_marks_student_id">
				<input type="hidden" id="view_edit_delete_marks_class_section_id">
				<input type="hidden" id="view_edit_delete_marks_class_id">
			</div>
			<div class="col-md-12">
				<span id="view_edit_delete_marks_ass_name" class="col-md-4"></span>
				<span id="view_edit_delete_marks_generation_type" class="col-md-4"></span> 
				<span id="view_edit_delete_marks_ass_id" class="col-md-4"></span>
			</div>
            <div class="col-md-12" id="view_edit_delete_marks_div">
                
            </div>
        </div>
        <!-- <div class="modal-footer">
            <button type="button" class="btn btn-primary" onclick="add_category()"></button>
        </div> -->
        </form>
    </div>
</div>

<script src="https://cdnjs.cloudflare.com/ajax/libs/jszip/3.7.1/jszip.min.js"></script>
<script src="https://cdnjs.cloudflare.com/ajax/libs/FileSaver.js/2.0.5/FileSaver.min.js"></script>

<style type="text/css">
	.btn{
		margin: 2px; 
	}
	input[type="checkbox"]{
	  width: 20px; 
	  height: 20px;
	}
	tr td{
		text-align: center;
	}
	tr th{
		text-align: center;
	}
	ul.panel-controls>li>a {
		border-radius: 50%;
	}
	.btn .fa, .btn .glyphicon {
		margin-right: 0px; 
	}


div#table_div_tag::-webkit-scrollbar {
  width: 12px; /* Adjust as needed */
}

/* Style the scrollbar track */
div#table_div_tag::-webkit-scrollbar-track {
  -webkit-box-shadow: inset 0 0 6px rgba(0, 0, 0, 0.3);
  border-radius: 10px;
}

/* Customize the scrollbar thumb appearance */
div#table_div_tag::-webkit-scrollbar-thumb {
  background: #eee; /* Adjust the color as desired */
}

div#table_div_tag {
  scrollbar-width: thin;
}

</style>




<!-- Regeneration promises added -->
 <script>
	async function generateReportCard(tempId, stdId, id) {
		try {
			// Update UI for processing state
			const processingText = id === 'reGen' ? 'Re generating' : 'Generating';
			const processingSpan = `<span style="color:#fff;background-color:#1caf9a;padding:5px;">${processingText}</span>`;
			
			$("#r_card_details").html(processingSpan);
			$(`#${id}Msg_${stdId}`).html(processingSpan);
			$(`#${id}Check_${stdId}`).hide();
			$("#view_"+stdId).html(processingSpan);

			var type = 0;
			if(id == 'reGen') {
				$("#r_card_details").html('<span style="color:#fff;background-color:#1caf9a;padding:5px;">Re generating</span>');
				$("#reGenMsg_"+stdId).html('<span style="color:#fff;background-color:#1caf9a;padding:5px;">Re generating</span>');
				$("#regenCheck_"+stdId).hide();
				$("#view_"+stdId).html('<span style="color:#fff;background-color:#1caf9a;padding:5px;">Re generating</span>');
				type = 2;
			}
			else {
				$("#r_card_details").html('<span style="color:#fff;background-color:#1caf9a;padding:5px;">Generating</span>');
				$("#genMsg_"+stdId).html('<span style="color:#fff;background-color:#1caf9a;padding:5px;">Generating</span>');
				$("#genCheck_"+stdId).hide();
				$("#view_"+stdId).html('<span style="color:#fff;background-color:#1caf9a;padding:5px;">Generating</span>');
				type = 0;
			}

			// Make the AJAX call with Promise
			const data = await $.ajax({
				url: "<?php echo site_url('examination/assessment_marks/testingMarksCard');?>",
				data: {'tempId': tempId, 'stdId': stdId, 'type': type},
				type: 'post'
			});

			const parsedData = JSON.parse(data);
			
			if (Object.keys(parsedData)?.length !== 0 && parsedData.pdf_path) {
				await checkPdfGenerated(parsedData.pdf_path, parsedData.pdf_relative_path, stdId, id);
			}
		} catch (error) {
			console.error('Error in generateReportCard:', error);
		}
	}

	async function downloadWithFetch(url, stdId) {
    try {
        let studentDetails = $("#downloadabe-file-name-"+stdId).val();
        const response = await fetch(url);
        const blob = await response.blob();
        const blobUrl = window.URL.createObjectURL(blob);
        
        const a = document.createElement('a');
        a.href = blobUrl;
        a.download = studentDetails;
        
        // Make the anchor invisible and non-focusable
        a.style.display = 'none';
        a.style.position = 'fixed';
        a.style.top = '-100px';
        a.setAttribute('tabindex', '-1');
        
        document.body.appendChild(a);
        
        // Create and dispatch a click event manually
        const clickEvent = new MouseEvent('click', {
            view: window,
            bubbles: true,
            cancelable: true
        });
        a.dispatchEvent(clickEvent);
        
        document.body.removeChild(a);
        
        // Clean up
        window.URL.revokeObjectURL(blobUrl);
    } catch (error) {
        console.error('Download failed:', error);
        // Fallback to regular download
        window.open(url, '_blank');
    }
}

	function checkPdfGenerated(pdf_path, pdf_check_path, stdId, id) {
		return new Promise((resolve, reject) => {
			const checkInterval = setInterval(async () => {
				try {
					const response = await $.ajax({
						url: '<?php echo site_url('examination/assessment_marks/check_pdf_generated'); ?>',
						type: "POST",
						data: { pdf_path: pdf_check_path }
					});

					const parsedResponse = JSON.parse(response);
					
					if (parsedResponse && Object.keys(parsedResponse)?.length && parsedResponse.status == 1) {
						const display_date_html = `<font style="color:#00701a;font-weight:700;">${parsedResponse.date}</font>`;
						clearInterval(checkInterval);
						
						// Reset UI
						const xnumber = Math.random() * 1000;
						const downloadUrl = $(`#${id}Check_${stdId}`).attr('data-downloadurl');
						const adjustedDownloadUrl = id === 'gen' 
							? downloadUrl.replace('cardidplaceholder', parsedResponse.id) 
							: downloadUrl;
						
						const html = `
							<a target='_blank' href='${pdf_path}?a=${xnumber}' class='btn btn-primary' data-placement='top' data-toggle='tooltip' data-original-title='View Marks card' style="background:#00701a">
								<i class='fa fa-eye'></i>
							</a>
							<button href="#" onclick="downloadWithFetch('${pdf_path}', '${stdId}')" class='btn btn-primary' data-placement='top' data-toggle='tooltip' data-original-title='Download Marks card' style="background:#00701a">
								<i class='fa fa-download'></i>
							</button>
						`;

						$(`#${id}Msg_${stdId}`).html(id === 'gen' ? '<span style="color:#fff;background-color:#00701a;padding:5px 10px;border-radius:4px;line-height:2rem;">Generated</span>' : '');
						$(`#${id}Check_${stdId}`).show().removeAttr('checked');
						$("#view_" + stdId).html(html);
						$("#viewdate_" + stdId).html(display_date_html);

						

						resolve();
					}
				} catch (error) {
					clearInterval(checkInterval);
					console.error('Error in checkPdfGenerated:', error);
					reject(error);
				}
			}, 5000);
		});
	}

	function callReGeneration(tempId) {

		Swal.fire({
			title: 'Are you sure?',
			text: "If you refresh or go back, regeneration will stop working!",
			icon: 'warning',
			showCancelButton: true,
			confirmButtonColor: '#3085d6',
			cancelButtonColor: '#d33',
			confirmButtonText: 'Okay, got it!',
			cancelButtonText: 'Cancel'
		}).then((result) => {
			if (result.isConfirmed) {
				_start_re_gen(tempId);
			}
		});
		
	}

	async function _start_re_gen(tempId) {
		const studentIds = [];
				$("input[name='reGenerateIds[]']:checked").each(function() {
					studentIds.push($(this).val());
				});

				if(studentIds.length) {
					$("#regenerate_all_anchor, #regenAll").prop('readonly', true).css({
						'background-color': '#ccc',
						'pointer-events': 'none',
						'border': '1px solid #ccc',
						'cursor': 'not-allowed'
					});
				}
				
				let i = 0;
				for (const stdId of studentIds) {
					await generateReportCard(tempId, stdId, 'reGen');
					if(i == studentIds.length - 1) {
						setTimeout(() => {
							$("input[name='reGenerateIds[]']:checked").each(function() {
								$(this).prop('checked', false);
							});
							$("#regenerate_all_anchor, #regenAll").prop('readonly', false).css({
								'background-color': '#0062cc',
								'pointer-events': 'auto',
								'border': '1px solid #0062cc',
								'cursor': 'auto'
							});
						}, 4000);
					}
					i++;
				}
	}

	function callGeneration(tempId) {

		Swal.fire({
			title: 'Are you sure?',
			text: "If you refresh or go back, generation will stop working!",
			icon: 'warning',
			showCancelButton: true,
			confirmButtonColor: '#3085d6',
			cancelButtonColor: '#d33',
			confirmButtonText: 'Okay, got it',
			cancelButtonText: 'Cancel'
		}).then((result) => {
			if (result.isConfirmed) {
				_start_gen(tempId);
			}
		});
		
	}

	async function _start_gen(tempId) {
		const studentIds = [];
				$("input[name='generateIds[]']:checked").each(function() {
					studentIds.push($(this).val());
				});

				if(studentIds.length) {
					$("#generate_all_anchor, #genAll").prop('readonly', true).css({
						'background-color': '#ccc',
						'pointer-events': 'none',
						'border': '1px solid #ccc',
						'cursor': 'not-allowed'
					});
				}
				
				let i = 0;
				for (const stdId of studentIds) {
					await generateReportCard(tempId, stdId, 'gen');
					if(i == studentIds.length - 1) {
						setTimeout(() => {
							$("input[name='generateIds[]']:checked").each(function() {
								$(this).prop('checked', false);
							});
							$("#generate_all_anchor, #genAll").prop('readonly', false).css({
								'background-color': '#0062cc',
								'pointer-events': 'auto',
								'border': '1px solid #0062cc',
								'cursor': 'auto'
							});
						}, 4000);
					}
					i++;
				}
	}
 </script>

   <script src="//cdn.jsdelivr.net/npm/sweetalert2@11"></script>
