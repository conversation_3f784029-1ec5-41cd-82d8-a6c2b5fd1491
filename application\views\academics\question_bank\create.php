<ul class="breadcrumb">
    <li><a href="<?php echo site_url('dashboard');?>">Dashboard</a></li>
    <li><a href="<?php echo site_url('academics/academics_menu/index');?>">Academics</a></li>
    <li>Add Questions</li>
</ul>

<div class="col-md-12">
    <div class="panel panel-default new-panel-style_3">
        <div class="card-header panel_heading_new_style_staff_border">
            <div class="row" style="margin: 0px;">
                <div class="col-md-8 pl-0">
                    <h3 class="card-title panel_title_new_style_staff">
                        <a class="back_anchor" href="<?php echo site_url('academics/academics_menu/index'); ?>">
                            <span class="fa fa-arrow-left"></span>
                        </a>
                        Add Questions
                    </h3>
                </div>
            </div>
        </div>

        <div class="card-body pt-0">
            <!-- Step Progress Bar (inside panel-body, flex only) -->
            <div class="mb-4">
                <div class="d-flex align-items-center" style="gap: 8px;">
                    <div class="flex-grow-1">
                        <div class="progress" style="height: 12px; border-radius: 6px;">
                            <div class="progress-bar bg-success progress-bar-striped progress-bar-animated" id="stepProgressBar" role="progressbar" style="width: 16.6%; transition: width 0.3s ease;"></div>
                        </div>
                    </div>
                    <div class="step-counter mb-4">
                        <span id="currentStepNumber">1</span> / <span id="totalStepNumber">6</span>
                    </div>
                </div>
                <div class="d-flex justify-content-between mt-2" role="tablist" aria-label="Question creation steps">
                    <div class="text-center flex-fill step-label" id="label-step-1" data-step="1" style="cursor:pointer;" role="tab" aria-controls="step-1" aria-selected="true" tabindex="0" aria-label="Step 1: Class and Subject Selection">
                        <div class="step-circle" id="circle-step-1" aria-hidden="true">1</div>
                        <small>Class & Subject</small>
                    </div>
                    <div class="text-center flex-fill step-label" id="label-step-2" data-step="2" style="cursor:pointer;" role="tab" aria-controls="step-2" aria-selected="false" tabindex="-1" aria-label="Step 2: Question Entry">
                        <div class="step-circle" id="circle-step-2" aria-hidden="true">2</div>
                        <small>Question</small>
                    </div>
                    <div class="text-center flex-fill step-label" id="label-step-3" data-step="3" style="cursor:pointer;" role="tab" aria-controls="step-3" aria-selected="false" tabindex="-1" aria-label="Step 3: Answer Options">
                        <div class="step-circle" id="circle-step-3" aria-hidden="true">3</div>
                        <small>Answers</small>
                    </div>
                    <div class="text-center flex-fill step-label" id="label-step-4" data-step="4" style="cursor:pointer;" role="tab" aria-controls="step-4" aria-selected="false" tabindex="-1" aria-label="Step 4: Additional Mapping">
                        <div class="step-circle" id="circle-step-4" aria-hidden="true">4</div>
                        <small>Mapping</small>
                    </div>
                    <div class="text-center flex-fill step-label" id="label-step-5" data-step="5" style="cursor:pointer;" role="tab" aria-controls="step-5" aria-selected="false" tabindex="-1" aria-label="Step 5: Hints and Attachments">
                        <div class="step-circle" id="circle-step-5" aria-hidden="true">5</div>
                        <small>Hints</small>
                    </div>
                    <div class="text-center flex-fill step-label" id="label-step-6" data-step="6" style="cursor:pointer;" role="tab" aria-controls="step-6" aria-selected="false" tabindex="-1" aria-label="Step 6: Preview and Submit">
                        <div class="step-circle" id="circle-step-6" aria-hidden="true">6</div>
                        <small>Preview</small>
                    </div>
                </div>
            </div>
            <form id="questionForm" autocomplete="off" role="form" aria-label="Question Bank Form" novalidate>
                <!-- Hidden field for question_id (draft) -->
                <input type="hidden" name="question_id" id="question_id" value="<?= isset($draft) ? $draft->id : '' ?>" aria-hidden="true">
                <!-- Step 1: Select Class, Subject, Question Type, and Learning Outcome -->
                <div class="step fade-step" id="step-1" role="tabpanel" aria-labelledby="label-step-1">
                    <h4 class="mb-2" id="step-1-heading">1. Select Class, Subject, Question Type, and Learning Outcome</h4>
                    <p class="text-muted mb-4">Choose the class, subject, question type, and learning outcome for your question.</p>
                    <div class="row g-3 mb-3 pl-2">
                        <div class="col-md-3">
                            <label for="class_id" class="fw-bold mb-1">Class <span style="color:red;" aria-label="required">*</span></label>
                            <select class="form-control select-enhanced" name="class_id" id="class_id" required onchange="getSubjects()" aria-describedby="class-help" aria-required="true">
                                <?php if(!empty($classes)) { ?>
                                    <option value="">Select Class</option>
                                    <?php foreach($classes as $cs){ ?>
                                        <option value="<?php echo $cs->class_master_id; ?>"><?php echo $cs->class_name; ?></option>
                                    <?php } ?>
                                <?php } else { ?>
                                    <option value="" disabled selected>No classes found</option>
                                <?php } ?>
                            </select>
                            <div id="class-help" class="sr-only">Select the class for which you want to create a question</div>
                        </div>
                        <div class="col-md-3">
                            <label for="subject_master_id" class="fw-bold mb-1">Subject <span style="color:red;" aria-label="required">*</span></label>
                            <select class="form-control select-enhanced" name="subject_master_id" id="subject_master_id" required onchange="getLessons()" aria-describedby="subject-help" aria-required="true">
                                <option value="">Select Subject</option>
                            </select>
                            <div id="subject-help" class="sr-only">Select the subject for the question</div>
                        </div>
                        <div class="col-md-3">
                            <label for="question_type" class="fw-bold mb-1">Question Type <span style="color:red;" aria-label="required">*</span></label>
                            <select class="form-control select-enhanced" name="question_type" id="question_type" required aria-describedby="question-type-help" aria-required="true" onchange="handleQuestionTypeChange()">
                                <option value="MCQ">Multiple Choice</option>
                                <option value="Short Answer">Short Answer</option>
                                <option value="Long Answer">Long Answer</option>
                                <option value="Fill in the Blanks">Fill in the Blanks</option>
                                <option value="True/False">True / False</option>
                                <option value="Match">Match</option>
                                <option value="Comprehension">Comprehension</option>
                                <option value="Custom">Custom</option>
                            </select>
                            <div id="question-type-help" class="sr-only">Choose the type of question you want to create</div>
                        </div>
                        <div class="col-md-3">
                            <label for="learning_outcome" class="fw-bold mb-1">Learning Outcome</label>
                            <select class="form-control select-enhanced" name="learning_outcome" id="learning_outcome" aria-describedby="learning-outcome-help">
                                <?php if(!empty($learningOutcomes)) { ?>
                                    <option value="">Select Learning Outcome</option>
                                    <?php foreach($learningOutcomes as $lo){ ?>
                                        <option value="<?php echo $lo->id; ?>" <?php echo $lo->objective_description ? 'data-description="'.$lo->objective_description.'"' : '' ?>><?php echo $lo->objective_name; ?></option>
                                    <?php } ?>
                                <?php } else { ?>
                                    <option value="" disabled selected>No learning outcomes found</option>
                                <?php }?>
                            </select>
                            <div id="learning-outcome-help" class="sr-only">Optional: Select a learning outcome for this question</div>
                        </div>
                    </div>
                </div>

                <!-- Step 2: Enter Question + Media -->
                <div class="step fade-step" id="step-2" style="display:none;">
                    <h4 class="mb-2">2. Enter Question + Media</h4>
                    <p class="text-muted mb-4">Write your question and optionally add an image.</p>
                    <div class="mb-3">
                        <label class="fw-bold mb-1">Question Text <font style="color:red;">*</font></label>
                        <textarea class="form-control" name="question_text" id="question_text" rows="3" required onblur="checkForDuplicates()" placeholder="Question Text"></textarea>
                        <div id="duplicateAlert" class="alert alert-warning mt-2" style="display:none;">
                            <i class="fa fa-exclamation-triangle mr-0"></i>
                            <strong>Possible Duplicate Found!</strong>
                            <p id="duplicateMessage"></p>
                            <button type="button" class="btn btn-sm btn-outline-primary" onclick="viewDuplicateQuestion()">
                                <i class="fa fa-eye mr-0"></i> View Similar Question
                            </button>
                        </div>
                    </div>
                    <div class="mb-3 d-none">
                        <label class="fw-bold mb-1">Question Image (Optional)</label>
                        <input type="file" class="form-control" name="question_image">
                    </div>
                </div>

                <!-- Step 3: Add Answers (MCQ/TF/etc.) -->
                <div class="step fade-step" id="step-3" style="display:none;">
                    <h4 class="mb-2">3. Add Answers</h4>
                    <p class="text-muted mb-4">Provide answer options or correct answer as per the question type.</p>
                    <!-- MCQ Options -->
                    <div id="mcqAnswerSection" class="answer-type-section mb-2">
                        <div id="mcqOptions"></div>
                        <button type="button" class="btn btn-outline-primary btn-sm mt-2" id="addOptionBtn" onclick="addMcqOption()">
                            <i class="fa fa-plus mr-0"></i> Add Option
                        </button>
                    </div>
                    <!-- Short Answer -->
                    <div id="shortAnswerSection" class="answer-type-section mb-2" style="display:none;">
                        <label class="fw-bold mb-1">Short Answer <font style="color:red;">*</font></label>
                        <input type="text" class="form-control mb-2" name="short_answer[]">
                        <button type="button" class="btn btn-outline-primary btn-sm" onclick="addShortAnswer()"><i class="fa fa-plus mr-0"></i> Add Another Answer</button>
                        <div id="shortAnswerList"></div>
                    </div>
                    <!-- Long Answer -->
                    <div id="longAnswerSection" class="answer-type-section mb-2" style="display:none;">
                        <label class="fw-bold mb-1">Long Answer <font style="color:red;">*</font></label>
                        <textarea class="form-control mb-2" name="long_answer[]"></textarea>
                        <button type="button" class="btn btn-outline-primary btn-sm" onclick="addLongAnswer()"><i class="fa fa-plus mr-0"></i> Add Another Answer</button>
                        <div id="longAnswerList"></div>
                    </div>
                    <!-- Fill in the Blanks -->
                    <div id="fillBlanksSection" class="answer-type-section mb-2" style="display:none;">
                        <label class="fw-bold mb-1">Blanks & Answers <font style="color:red;">*</font></label>
                        <div id="fillBlanksList"></div>
                        <button type="button" class="btn btn-outline-primary btn-sm" onclick="addBlank()"><i class="fa fa-plus mr-0"></i> Add Blank</button>
                    </div>
                    <!-- True/False -->
                    <div id="tfSection" class="answer-type-section mb-2" style="display:none;">
                        <label class="fw-bold mb-3">Select Correct Answer <span style="color:red;">*</span></label>
                        <div class="tf-options-container">
                            <div class="tf-option">
                                <input class="form-check-input" type="radio" name="tf_answer" value="True" id="tfTrue" required>
                                <label class="tf-label tf-true" for="tfTrue">
                                    <div class="tf-icon">
                                        <i class="fa fa-check mr-0"></i>
                                    </div>
                                    <div class="tf-text">
                                        <span class="tf-title">True</span>
                                        <small class="tf-description">This statement is correct</small>
                                    </div>
                                </label>
                            </div>
                            <div class="tf-option">
                                <input class="form-check-input" type="radio" name="tf_answer" value="False" id="tfFalse" required>
                                <label class="tf-label tf-false" for="tfFalse">
                                    <div class="tf-icon">
                                        <i class="fa fa-times mr-0"></i>
                                    </div>
                                    <div class="tf-text">
                                        <span class="tf-title">False</span>
                                        <small class="tf-description">This statement is incorrect</small>
                                    </div>
                                </label>
                            </div>
                        </div>
                        <small class="text-muted mt-2 d-block">Choose the correct answer for this True/False question</small>
                    </div>
                    <!-- Match -->
                    <div id="matchSection" class="answer-type-section mb-2" style="display:none;">
                        <label class="fw-bold mb-1">Match Pairs <font style="color:red;">*</font></label>
                        <div id="matchList"></div>
                        <button type="button" class="btn btn-outline-primary btn-sm" onclick="addMatchPair()"><i class="fa fa-plus mr-0"></i> Add Pair</button>
                    </div>
                    <!-- Comprehension -->
                    <div id="comprehensionSection" class="answer-type-section mb-2" style="display:none;">
                        <label class="fw-bold mb-1">Passage <font style="color:red;">*</font></label>
                        <textarea class="form-control mb-2" name="passage_text"></textarea>
                        <label class="fw-bold mb-1">Sub-Questions <font style="color:red;">*</font></label>
                        <div id="comprehensionQuestions"></div>
                        <button type="button" class="btn btn-outline-primary btn-sm" onclick="addComprehensionQuestion()"><i class="fa fa-plus mr-0"></i> Add Sub-Question</button>
                    </div>
                    <!-- Custom -->
                    <div id="customSection" class="answer-type-section mb-2" style="display:none;">
                        <label class="fw-bold mb-1">Custom Answer <font style="color:red;">*</font></label>
                        <input type="text" class="form-control" name="custom_answer">
                    </div>
                </div>

                <!-- Step 4: Additional Mapping to Bloom's, Skills, Lesson, Topic -->
                <div class="step fade-step" id="step-4" style="display:none;">
                    <h4 class="mb-2">4. Additional Mapping</h4>
                    <p class="text-muted mb-4">Tag this question for easy search and reuse.</p>
                    <div class="row g-3 mb-3">
                        <div class="col-md-4">
                            <label class="fw-bold mb-1">Lesson</label>
                            <input type="hidden" name='lesson_name' id="lesson_name">
                            <select class="form-control select-enhanced" name="lesson_id" id="lesson_id_step4" onchange="handleLessonChange()">
                                <option value="">Select Lesson</option>
                            </select>
                        </div>
                        <div class="col-md-4">
                            <label class="fw-bold mb-1">Topic</label>
                            <input type="hidden" name="topic_name" id="topic_name">
                            <select class="form-control select-enhanced" name="topic_id" id="topic_id" onchange="handleTopicChange()">
                                <option value="">Select Topic</option>
                            </select>
                        </div>
                        <div class="col-md-4">
                            <label class="fw-bold mb-1">Difficulty Level  <span style="color:red;">*</span></label>
                            <select class="form-control select-enhanced" name="difficulty_level" id="difficulty_level" required>
                                <option value="">Select Difficulty</option>
                                <option value="Easy">Easy</option>
                                <option value="Medium">Medium</option>
                                <option value="Hard">Hard</option>
                            </select>
                        </div>
                    </div>
                    <div class="row g-3 mb-3">
                        <div class="col-md-4">
                            <label class="fw-bold mb-1">Max Marks <span style="color:red;">*</span></label>
                            <input type="number" class="form-control" name="max_marks" id="max_marks" min="0" step="0.5" required placeholder="Enter marks">
                        </div>
                        <div class="col-md-4">
                            <label class="fw-bold mb-1">Negative Marks <span style="color:red;">*</span></label>
                            <input type="number" class="form-control" name="negative_marks" id="negative_marks" min="0" step="0.5" required placeholder="Enter negative marks">
                        </div>
                        <div class="col-md-4">
                            <label class="fw-bold mb-1">Bloom's Taxonomy</label>
                            <select class="form-control select-enhanced" name="blooms[]" id="blooms" multiple>
                                <option value="Remember">Remember</option>
                                <option value="Understand">Understand</option>
                                <option value="Apply">Apply</option>
                                <option value="Analyze">Analyze</option>
                                <option value="Evaluate">Evaluate</option>
                                <option value="Create">Create</option>
                            </select>
                        </div>
                    </div>
                    <div class="row g-3 mb-3">
                        <div class="col-md-6">
                            <label class="fw-bold mb-1">Skills</label>
                            <select class="form-control select-enhanced" name="skills[]" id="skills" multiple>
                                <option value="Critical Thinking">Critical Thinking</option>
                                <option value="Problem Solving">Problem Solving</option>
                                <option value="Communication">Communication</option>
                                <option value="Collaboration">Collaboration</option>
                                <option value="Creativity">Creativity</option>
                                <option value="Research">Research</option>
                            </select>
                        </div>
                        <div class="col-md-6">
                            <label class="fw-bold mb-1">Custom Tags</label>
                            <input type="text" class="form-control" name="tags" id="tags" placeholder="Comma separated tags">
                        </div>
                    </div>
                    <!-- <div class="mb-3">
                        <label class="fw-bold mb-1">Reusable for Classes</label><br>
                        <div id="classReuseCheckboxes" class="d-flex flex-wrap gap-2"></div>
                    </div> -->
                </div>

                <!-- Step 5: Add Hints + Attachments -->
                <div class="step fade-step" id="step-5" style="display:none;">
                    <h4 class="mb-2">5. Add Hints + Attachments</h4>
                    <p class="text-muted mb-4">Optionally provide hints or attach supporting files.</p>
                    <div class="mb-3">
                        <label class="fw-bold mb-1">Hint (Optional)</label>
                        <textarea class="form-control" name="hints" rows="2"></textarea>
                    </div>
                    <div class="mb-3">
                        <label class="fw-bold mb-1">Attachment (Optional)</label>
                        <input type="file" class="form-control" name="attachment">
                    </div>
                </div>

                <!-- Step 6: Preview with Close, Draft, Submit for Approval -->
                <div class="step fade-step" id="step-6" style="display:none;">
                    <h4 class="mb-2">6. Preview</h4>
                    <p class="text-muted mb-4">Review your question and save as draft or submit for approval.</p>
                    <div class="d-flex justify-content-center">
                        <button type="reset" class="btn btn-secondary mr-2" onclick="window.location.reload();"><i class="fa fa-times mr-0"></i> Cancel</button>
                        <button type="submit" name="status" value="draft" class="btn btn-primary mr-2"><i class="fa fa-save mr-0"></i> Save as Draft</button>
                        <button type="submit" name="status" value="submit" class="btn btn-success"><i class="fa fa-paper-plane mr-0"></i> Submit for Approval</button>
                    </div>
                </div>

                <!-- Step Navigation (at the end of the page) -->
                <div id="stepNavWrapper" class="mt-4 mb-2">
                    <div id="stepNavBtns" class="d-flex justify-content-between align-items-center">
                        <button type="button" class="btn btn-secondary btn-lg px-4" id="prevBtn" onclick="nextPrev(-1)" style="display:none;">
                            <i class="fa fa-arrow-left mr-0"></i>
                            Previous
                        </button>
                        <button type="button" class="btn btn-primary btn-lg px-4" id="nextBtn" onclick="nextPrev(1)">
                            <span class="btn-text">Next</span>
                            <i class="fa fa-arrow-right btn-icon mr-0"></i>
                            <i class="fa fa-spinner fa-spin btn-spinner" style="display: none;"></i>
                        </button>
                    </div>

                    <!-- Loading overlay for step transitions -->
                    <div id="stepLoadingOverlay" class="step-loading-overlay" style="display: none;">
                        <div class="loading-content">
                            <i class="fa fa-spinner fa-spin fa-2x"></i>
                            <p class="mt-2">Loading...</p>
                        </div>
                    </div>
                </div>
            </form>
        </div>
    </div>
</div>

<style>
.fade-step {
    transition: opacity 0.3s;
    opacity: 1;
}
.step[style*="display: none"] {
    opacity: 0;
    pointer-events: none;
    height: 0;
    overflow: hidden;
}
.step-label {
    font-weight: 600;
    color: #888;
    font-size: 13px;
    line-height: 1.1;
    min-width: 60px;
    transition: all 0.3s ease;
}

.step-circle {
    width: 32px;
    height: 32px;
    border-radius: 50%;
    background-color: #e9ecef;
    color: #6c757d;
    display: flex;
    align-items: center;
    justify-content: center;
    font-weight: bold;
    font-size: 14px;
    margin: 0 auto 8px;
    border: 2px solid #e9ecef;
    transition: all 0.3s ease;
}

.step-label.active .step-circle {
    background-color: #198754;
    color: white;
    border-color: #198754;
    transform: scale(1.1);
}

.step-label.completed .step-circle {
    background-color: #28a745;
    color: white;
    border-color: #28a745;
}

.step-label.completed .step-circle::before {
    content: '✓';
    font-size: 16px;
}

.step-label.active {
    color: #198754;
    font-weight: bold;
}

.step-label.completed {
    color: #28a745;
    font-weight: bold;
}

.step-label.disabled {
    color: #ccc !important;
    cursor: not-allowed !important;
}

.step-label.disabled .step-circle {
    background-color: #f8f9fa;
    color: #ccc;
    border-color: #f8f9fa;
}

.step-counter {
    background-color: #f8f9fa;
    padding: 4px 12px;
    border-radius: 20px;
    font-size: 12px;
    font-weight: bold;
    color: #495057;
    white-space: nowrap;
}
/* Validation styles */
.is-invalid {
    border-color: #dc3545 !important;
    box-shadow: 0 0 0 0.2rem rgba(220, 53, 69, 0.25) !important;
}

.is-valid {
    border-color: #28a745 !important;
    box-shadow: 0 0 0 0.2rem rgba(40, 167, 69, 0.25) !important;
}

/* Loading states */
.loading {
    opacity: 0.6;
    pointer-events: none;
}

.btn-loading {
    position: relative;
}

.btn-loading .btn-text,
.btn-loading .btn-icon {
    opacity: 0.5;
}

.btn-loading .btn-spinner {
    display: inline-block !important;
}

.step-loading-overlay {
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    background-color: rgba(255, 255, 255, 0.9);
    z-index: 1000;
    display: flex;
    align-items: center;
    justify-content: center;
}

.loading-content {
    text-align: center;
    color: #6c757d;
}

.form-loading {
    position: relative;
}

.form-loading::before {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    background-color: rgba(255, 255, 255, 0.8);
    z-index: 999;
    display: flex;
    align-items: center;
    justify-content: center;
}

.form-loading::after {
    content: '';
    position: absolute;
    top: 50%;
    left: 50%;
    width: 32px;
    height: 32px;
    margin: -16px 0 0 -16px;
    border: 3px solid #f3f3f3;
    border-top: 3px solid #007bff;
    border-radius: 50%;
    animation: spin 1s linear infinite;
    z-index: 1000;
}

@keyframes spin {
    0% { transform: rotate(0deg); }
    100% { transform: rotate(360deg); }
}

/* Screen reader only content */
.sr-only {
    position: absolute;
    width: 1px;
    height: 1px;
    padding: 0;
    margin: -1px;
    overflow: hidden;
    clip: rect(0, 0, 0, 0);
    white-space: nowrap;
    border: 0;
}

/* Focus styles for accessibility */
.step-label:focus {
    outline: 2px solid #007bff;
    outline-offset: 2px;
}

.form-control:focus {
    border-color: #007bff;
    box-shadow: 0 0 0 0.2rem rgba(0, 123, 255, 0.25);
}

.btn:focus {
    outline: 2px solid #007bff;
    outline-offset: 2px;
}

/* Comprehension sub-question styles */
.sub-question {
    border: 1px solid #e9ecef;
    border-radius: 8px;
    background-color: #f8f9fa;
    transition: all 0.3s ease;
}

.sub-question:hover {
    border-color: #007bff;
    box-shadow: 0 2px 4px rgba(0, 123, 255, 0.1);
}

.sub-question h6 {
    color: #007bff;
    font-weight: 600;
}

.sub-question-answer-section {
    background-color: #ffffff;
    border-radius: 6px;
    padding: 15px;
    border: 1px solid #e9ecef;
}

.sub-question .input-group-text {
    background-color: #e9ecef;
    border-color: #ced4da;
    font-weight: 500;
}

.sub-question .form-control:focus {
    border-color: #007bff;
    box-shadow: 0 0 0 0.2rem rgba(0, 123, 255, 0.25);
}

/* True/False enhanced UI styles */
.tf-options-container {
    display: flex;
    gap: 15px;
    margin-bottom: 15px;
}

.tf-option {
    flex: 1;
    position: relative;
}

.tf-option input[type="radio"] {
    position: absolute;
    opacity: 0;
    width: 0;
    height: 0;
}

.tf-label {
    display: flex;
    align-items: center;
    padding: 20px 15px;
    border: 2px solid #e9ecef;
    border-radius: 12px;
    cursor: pointer;
    transition: all 0.3s ease;
    background-color: #ffffff;
    min-height: 80px;
}

.tf-label:hover {
    border-color: #007bff;
    box-shadow: 0 2px 8px rgba(0, 123, 255, 0.15);
    transform: translateY(-2px);
}

.tf-option input[type="radio"]:checked + .tf-label {
    border-color: #28a745;
    background-color: #f8fff9;
    box-shadow: 0 4px 12px rgba(40, 167, 69, 0.2);
}

.tf-option input[type="radio"]:checked + .tf-label.tf-false {
    border-color: #dc3545;
    background-color: #fff8f8;
    box-shadow: 0 4px 12px rgba(220, 53, 69, 0.2);
}

.tf-icon {
    width: 40px;
    height: 40px;
    border-radius: 50%;
    display: flex;
    align-items: center;
    justify-content: center;
    margin-right: 15px;
    font-size: 18px;
    transition: all 0.3s ease;
}

.tf-true .tf-icon {
    background-color: #e8f5e8;
    color: #28a745;
    border: 2px solid #c3e6cb;
}

.tf-false .tf-icon {
    background-color: #f8e8e8;
    color: #dc3545;
    border: 2px solid #f5c6cb;
}

.tf-option input[type="radio"]:checked + .tf-label .tf-icon {
    transform: scale(1.1);
}

.tf-option input[type="radio"]:checked + .tf-label.tf-true .tf-icon {
    background-color: #28a745;
    color: white;
    border-color: #28a745;
}

.tf-option input[type="radio"]:checked + .tf-label.tf-false .tf-icon {
    background-color: #dc3545;
    color: white;
    border-color: #dc3545;
}

.tf-text {
    flex: 1;
}

.tf-title {
    display: block;
    font-weight: 600;
    font-size: 16px;
    color: #495057;
    margin-bottom: 4px;
}

.tf-description {
    display: block;
    color: #6c757d;
    font-size: 13px;
    line-height: 1.3;
}

.tf-option input[type="radio"]:checked + .tf-label .tf-title {
    color: #212529;
}

/* Responsive design for True/False */
@media (max-width: 576px) {
    .tf-options-container {
        flex-direction: column;
        gap: 10px;
    }

    .tf-label {
        min-height: 70px;
        padding: 15px 12px;
    }

    .tf-icon {
        width: 35px;
        height: 35px;
        margin-right: 12px;
        font-size: 16px;
    }

    .tf-title {
        font-size: 15px;
    }

    .tf-description {
        font-size: 12px;
    }
}

/* Simple dropdown arrow for select fields */
.select-enhanced,
select.form-control {
    background-image: url("data:image/svg+xml,%3csvg xmlns='http://www.w3.org/2000/svg' viewBox='0 0 16 16'%3e%3cpath fill='none' stroke='%23495057' stroke-linecap='round' stroke-linejoin='round' stroke-width='2' d='m1 6 7 7 7-7'/%3e%3c/svg%3e");
    background-repeat: no-repeat;
    background-position: right 0.75rem center;
    background-size: 16px 12px;
    padding-right: 2.5rem;
    -webkit-appearance: none;
    -moz-appearance: none;
    appearance: none;
}

/* Hide dropdown arrow for multiple select */
.select-enhanced[multiple],
select.form-control[multiple] {
    background-image: none;
    padding-right: 0.75rem;
}

@media (max-width: 600px) {
    .step-label { font-size: 11px; min-width: 40px; }
    .card-body { padding: 1rem !important; }
}
</style>

<script src="https://cdn.jsdelivr.net/npm/sweetalert2@11"></script>
<script>
// Global state management
const QuestionBankApp = {
    state: {
        mcqCount: 0,
        currentStep: 1,
        totalSteps: 6,
        furthestStep: 1,
        isLoading: false
    },

    // Configuration
    config: {
        stepTransitionDelay: 300,
        ajaxTimeout: 10000,
        validationMessages: {
            required: 'This field is required',
            mcqMinOptions: 'Please provide at least two answer options for MCQ',
            mcqCorrectAnswer: 'Please mark at least one option as correct',
            shortAnswer: 'Please provide at least one short answer',
            longAnswer: 'Please provide at least one long answer',
            fillBlanks: 'Please provide at least one blank answer',
            trueFalse: 'Please select the correct answer for True/False',
            matchPairs: 'Please provide at least two match pairs',
            comprehensionPassage: 'Please provide a passage for comprehension',
            comprehensionQuestion: 'Please provide at least one sub-question',
            customAnswer: 'Please provide a custom answer'
        }
    }
};

// Utility functions
const Utils = {
    // DOM helpers
    getElement: (id) => document.getElementById(id),
    getElements: (selector) => document.querySelectorAll(selector),

    // Validation helpers
    isEmpty: (value) => !value || value.trim() === '',
    isValidEmail: (email) => /^[^\s@]+@[^\s@]+\.[^\s@]+$/.test(email),

    // AJAX helpers
    makeAjaxRequest: (options) => {
        const defaults = {
            type: 'POST',
            timeout: QuestionBankApp.config.ajaxTimeout,
            dataType: 'json'
        };
        return $.ajax({...defaults, ...options});
    },

    // UI helpers
    showAlert: (type, title, text, options = {}) => {
        return Swal.fire({
            icon: type,
            title: title,
            text: text,
            confirmButtonText: 'OK',
            ...options
        });
    },

    // Form helpers
    getFormData: (formId) => new FormData(Utils.getElement(formId)),
    resetForm: (formId) => Utils.getElement(formId)?.reset(),

    // Loading helpers
    setLoading: (elementId, isLoading) => {
        const element = Utils.getElement(elementId);
        if (!element) return;

        if (isLoading) {
            element.classList.add('loading');
            element.disabled = true;
        } else {
            element.classList.remove('loading');
            element.disabled = false;
        }
    }
};

// Use global variables for backward compatibility
let mcqCount = QuestionBankApp.state.mcqCount;
let currentStep = QuestionBankApp.state.currentStep;
const totalSteps = QuestionBankApp.state.totalSteps;
let furthestStep = QuestionBankApp.state.furthestStep;

// Helper function to get current step
function getCurrentStep() {
    return currentStep;
}

// Helper function to check if a field should be validated based on current question type
function isFieldVisible(element) {
    // Check if element itself is hidden
    var elementStyle = window.getComputedStyle(element);
    if (elementStyle.display === 'none' || elementStyle.visibility === 'hidden') {
        return false;
    }

    // Check if any parent is hidden
    var parent = element.parentElement;
    while (parent && parent !== document.body) {
        var parentStyle = window.getComputedStyle(parent);
        if (parentStyle.display === 'none' || parentStyle.visibility === 'hidden') {
            return false;
        }
        parent = parent.parentElement;
    }

    // Always validate fields that are not in answer sections
    var answerSection = element.closest('.answer-type-section');
    if (!answerSection) {
        return true;
    }

    // For answer sections, check if they are currently visible
    var computedStyle = window.getComputedStyle(answerSection);
    if (computedStyle.display === 'none') {
        return false;
    }

    // Additional check: validate based on current question type
    var currentQuestionType = document.getElementById('question_type').value;
    var sectionId = answerSection.id;

    // Map question types to their corresponding section IDs
    var questionTypeMapping = {
        'MCQ': 'mcqAnswerSection',
        'Short Answer': 'shortAnswerSection',
        'Long Answer': 'longAnswerSection',
        'Fill in the Blanks': 'fillBlanksSection',
        'True/False': 'tfSection',
        'Match': 'matchSection',
        'Comprehension': 'comprehensionSection',
        'Custom': 'customSection'
    };

    // Only validate if this section matches the current question type
    return questionTypeMapping[currentQuestionType] === sectionId;
}

function showStep(n) {
    for (let i = 1; i <= totalSteps; i++) {
        const stepDiv = document.getElementById('step-' + i);
        if (stepDiv) stepDiv.style.display = (i === n) ? '' : 'none';

        const label = document.getElementById('label-step-' + i);
        const circle = document.getElementById('circle-step-' + i);

        if (label && circle) {
            // Remove all state classes
            label.classList.remove('active', 'completed', 'disabled');

            if (i < n) {
                // Completed steps
                label.classList.add('completed');
                label.style.cursor = 'pointer';
                circle.textContent = ''; // Will show checkmark via CSS
            } else if (i === n) {
                // Current step
                label.classList.add('active');
                label.style.cursor = 'default';
                circle.textContent = i;
            } else {
                // Future steps
                label.classList.add('disabled');
                label.style.cursor = 'not-allowed';
                circle.textContent = i;
            }
        }
    }

    // Update step counter
    document.getElementById('currentStepNumber').textContent = n;
    document.getElementById('totalStepNumber').textContent = totalSteps;

    // Update navigation buttons
    document.getElementById('prevBtn').style.display = n === 1 ? 'none' : '';
    document.getElementById('nextBtn').style.display = n === totalSteps ? 'none' : '';

    // Update progress bar
    const progressPercentage = (n * 100 / totalSteps);
    document.getElementById('stepProgressBar').style.width = progressPercentage + '%';

    // Update navigation button layout
    const navBtns = document.getElementById('stepNavBtns');
    if (n === 1) {
        navBtns.className = 'd-flex justify-content-end align-items-center';
    } else {
        navBtns.className = 'd-flex justify-content-between align-items-center';
    }

    // Update tab index for accessibility
    updateStepTabIndex();
}

function nextPrev(n) {
    // Show loading state for navigation
    if (n === 1) {
        showButtonLoading('nextBtn', true);
    } else if (n === -1) {
        showButtonLoading('prevBtn', true);
    }

    // Validate required fields in the current step before moving forward
    if (n === 1) {
        // Clear previous validation errors
        clearValidationErrors();

        var currentStepDiv = document.getElementById('step-' + currentStep);
        var invalidFields = [];
        var validationErrors = [];

        currentStepDiv.querySelectorAll('[required]').forEach(function(el) {
            // Skip validation for hidden fields or fields in hidden answer sections
            if (!isFieldVisible(el)) {
                return;
            }

            var fieldName = el.getAttribute('name') || el.id;
            var labelElement = currentStepDiv.querySelector('label[for="' + el.id + '"]');
            var labelText = '';

            if (labelElement) {
                labelText = labelElement.textContent.replace('*', '').trim();
            } else {
                // Special handling for radio buttons - get the group label instead of individual labels
                if (el.type === 'radio') {
                    var radioContainer = el.closest('.answer-type-section') || el.closest('.tf-options-container');
                    if (radioContainer) {
                        var groupLabel = radioContainer.querySelector('label:not(.tf-label)');
                        if (groupLabel) {
                            labelText = groupLabel.textContent.replace('*', '').trim();
                        } else {
                            labelText = fieldName.replace(/_/g, ' ').replace(/\b\w/g, function(l) { return l.toUpperCase(); });
                        }
                    }
                } else {
                    // Fallback: find the closest label or use a generic name
                    var parentContainer = el.closest('.mb-3') || el.closest('.col-md-3') || el.closest('.col-md-4') || el.closest('.col-md-6');
                    var closestLabel = parentContainer ? parentContainer.querySelector('label') : null;
                    if (closestLabel) {
                        labelText = closestLabel.textContent.replace('*', '').trim();
                    } else {
                        labelText = fieldName.replace(/_/g, ' ').replace(/\b\w/g, function(l) { return l.toUpperCase(); });
                    }
                }
            }

            if ((el.type === 'checkbox' || el.type === 'radio')) {
                var group = currentStepDiv.querySelectorAll('[name="' + el.name + '"]');
                var checked = Array.from(group).some(function(input) { return input.checked; });

                // For radio buttons, only validate once per group (check if this is the first element in the group)
                var isFirstInGroup = el === group[0];

                if (!checked && !invalidFields.includes(el) && (el.type === 'checkbox' || isFirstInGroup)) {
                    invalidFields.push(el);
                    validationErrors.push(labelText + ' is required');
                    highlightField(el, true);
                }
            } else if (!el.value || el.value === '') {
                invalidFields.push(el);
                validationErrors.push(labelText + ' is required');
                highlightField(el, true);
            } else {
                highlightField(el, false);
            }
        });

        // Custom validation for Step 3 (Add Answers)
        if (currentStep === 3) {
            const validationResult = validateAnswerStep();
            if (!validationResult.isValid) {
                Utils.showAlert('warning', validationResult.title, validationResult.message);
                showButtonLoading('nextBtn', false);
                showButtonLoading('prevBtn', false);
                return false;
            }
        }

        if (invalidFields.length > 0) {
            var errorMessage = validationErrors.length > 1 ?
                'Please fill the following required fields:\n• ' + validationErrors.join('\n• ') :
                validationErrors[0];

            Swal.fire({
                icon: 'warning',
                title: 'Validation Error',
                text: errorMessage,
                confirmButtonText: 'OK'
            });

            // Focus on first invalid field
            if (invalidFields[0] && invalidFields[0].focus) {
                setTimeout(() => invalidFields[0].focus(), 100);
            }

            // Clear loading states
            showButtonLoading('nextBtn', false);
            showButtonLoading('prevBtn', false);
            return false;
        }
    }

    // Show step loading overlay for transitions
    showStepLoading(true);

    setTimeout(() => {
        currentStep += n;
        if (currentStep < 1) currentStep = 1;
        if (currentStep > totalSteps) currentStep = totalSteps;
        if (currentStep > furthestStep) furthestStep = currentStep;

        // Sync data between steps when moving to step 4
        if (currentStep === 4) {
            syncStepData();
        }

        showStep(currentStep);
        window.scrollTo({top: 0, behavior: 'smooth'});

        // Clear all loading states
        showButtonLoading('nextBtn', false);
        showButtonLoading('prevBtn', false);
        showStepLoading(false);
    }, 300); // Small delay for better UX
}

function validateStep(step) {
    // Add per-step validation if needed
    return true;
}

function validateAnswerStep() {
    const qType = Utils.getElement('question_type').value;
    const config = QuestionBankApp.config.validationMessages;

    const validators = {
        'MCQ': () => {
            const options = Utils.getElements('input[name="option_text[]"]');
            const corrects = Utils.getElements('input[name="is_correct[]"]:checked');
            let filledOptions = 0;
            options.forEach(opt => { if (!Utils.isEmpty(opt.value)) filledOptions++; });

            if (filledOptions < 2) {
                return { isValid: false, title: 'Add at least 2 options', message: config.mcqMinOptions };
            }
            if (corrects.length < 1) {
                return { isValid: false, title: 'Select correct answer', message: config.mcqCorrectAnswer };
            }
            return { isValid: true };
        },

        'Short Answer': () => {
            const answers = Utils.getElements('input[name="short_answer[]"]');
            let filled = 0;
            answers.forEach(a => { if (!Utils.isEmpty(a.value)) filled++; });

            if (filled < 1) {
                return { isValid: false, title: 'Add Short Answer', message: config.shortAnswer };
            }
            return { isValid: true };
        },

        'Long Answer': () => {
            const answers = Utils.getElements('textarea[name="long_answer[]"]');
            let filled = 0;
            answers.forEach(a => { if (!Utils.isEmpty(a.value)) filled++; });

            if (filled < 1) {
                return { isValid: false, title: 'Add Long Answer', message: config.longAnswer };
            }
            return { isValid: true };
        },

        'Fill in the Blanks': () => {
            const blanks = Utils.getElements('input[name="blank_answer[]"]');
            let filled = 0;
            blanks.forEach(b => { if (!Utils.isEmpty(b.value)) filled++; });

            if (filled < 1) {
                return { isValid: false, title: 'Add Blank Answer', message: config.fillBlanks };
            }
            return { isValid: true };
        },

        'True/False': () => {
            const tfChecked = document.querySelector('input[name="tf_answer"]:checked');
            if (!tfChecked) {
                return { isValid: false, title: 'Select True or False', message: config.trueFalse };
            }
            return { isValid: true };
        },

        'Match': () => {
            const lefts = Utils.getElements('input[name="match_left[]"]');
            const rights = Utils.getElements('input[name="match_right[]"]');
            let filled = 0;
            for (let i = 0; i < lefts.length; i++) {
                if (!Utils.isEmpty(lefts[i].value) && !Utils.isEmpty(rights[i].value)) filled++;
            }

            if (filled < 2) {
                return { isValid: false, title: 'Add at least 2 pairs', message: config.matchPairs };
            }
            return { isValid: true };
        },

        'Comprehension': () => {
            const passage = document.querySelector('textarea[name="passage_text"]');
            const subQuestions = Utils.getElements('.sub-question');

            // Validate passage
            if (!passage || Utils.isEmpty(passage.value)) {
                return { isValid: false, title: 'Add Passage', message: config.comprehensionPassage };
            }

            // Validate sub-questions
            if (subQuestions.length < 1) {
                return { isValid: false, title: 'Add Sub-Question', message: config.comprehensionQuestion };
            }

            // Validate each sub-question
            for (let i = 0; i < subQuestions.length; i++) {
                const subQ = subQuestions[i];
                const questionText = subQ.querySelector('textarea[name="comprehension_question[]"]');
                const questionType = subQ.querySelector('select[name="comprehension_question_type[]"]');

                // Check if question text is filled
                if (!questionText || Utils.isEmpty(questionText.value)) {
                    return {
                        isValid: false,
                        title: `Sub-Question ${i + 1} Missing`,
                        message: `Please enter text for sub-question ${i + 1}`
                    };
                }

                // Validate based on question type
                const type = questionType ? questionType.value : 'MCQ';
                const subValidation = validateSubQuestion(subQ, type, i + 1);
                if (!subValidation.isValid) {
                    return subValidation;
                }
            }

            return { isValid: true };
        },

        'Custom': () => {
            const custom = document.querySelector('input[name="custom_answer"]');
            if (Utils.isEmpty(custom.value)) {
                return { isValid: false, title: 'Add Custom Answer', message: config.customAnswer };
            }
            return { isValid: true };
        }
    };

    const validator = validators[qType];
    return validator ? validator() : { isValid: true };
}

function validateSubQuestion(subQuestion, type, questionNumber) {
    const subQuestionId = subQuestion.id;

    switch(type) {
        case 'MCQ':
            const options = subQuestion.querySelectorAll(`input[name="mcq_option_${subQuestionId}[]"]`);
            const corrects = subQuestion.querySelectorAll(`input[name="mcq_correct_${subQuestionId}[]"]:checked`);
            let filledOptions = 0;

            options.forEach(opt => {
                if (!Utils.isEmpty(opt.value)) filledOptions++;
            });

            if (filledOptions < 2) {
                return {
                    isValid: false,
                    title: `Sub-Question ${questionNumber} - MCQ Error`,
                    message: `Please provide at least 2 options for sub-question ${questionNumber}`
                };
            }

            if (corrects.length < 1) {
                return {
                    isValid: false,
                    title: `Sub-Question ${questionNumber} - MCQ Error`,
                    message: `Please mark at least one correct answer for sub-question ${questionNumber}`
                };
            }
            break;

        case 'Short Answer':
            const shortAnswers = subQuestion.querySelectorAll(`input[name="short_answer_${subQuestionId}[]"]`);
            let filledShortAnswers = 0;

            shortAnswers.forEach(ans => {
                if (!Utils.isEmpty(ans.value)) filledShortAnswers++;
            });

            if (filledShortAnswers < 1) {
                return {
                    isValid: false,
                    title: `Sub-Question ${questionNumber} - Answer Missing`,
                    message: `Please provide at least one answer for sub-question ${questionNumber}`
                };
            }
            break;

        case 'Long Answer':
            const longAnswers = subQuestion.querySelectorAll(`textarea[name="long_answer_${subQuestionId}[]"]`);
            let filledLongAnswers = 0;

            longAnswers.forEach(ans => {
                if (!Utils.isEmpty(ans.value)) filledLongAnswers++;
            });

            if (filledLongAnswers < 1) {
                return {
                    isValid: false,
                    title: `Sub-Question ${questionNumber} - Answer Missing`,
                    message: `Please provide a sample answer for sub-question ${questionNumber}`
                };
            }
            break;

        case 'Fill in the Blanks':
            const blanks = subQuestion.querySelectorAll(`input[name="blank_answer_${subQuestionId}[]"]`);
            let filledBlanks = 0;

            blanks.forEach(blank => {
                if (!Utils.isEmpty(blank.value)) filledBlanks++;
            });

            if (filledBlanks < 1) {
                return {
                    isValid: false,
                    title: `Sub-Question ${questionNumber} - Blanks Missing`,
                    message: `Please provide answers for blanks in sub-question ${questionNumber}`
                };
            }
            break;

        case 'True/False':
            const tfChecked = subQuestion.querySelector(`input[name="tf_answer_${subQuestionId}"]:checked`);

            if (!tfChecked) {
                return {
                    isValid: false,
                    title: `Sub-Question ${questionNumber} - Answer Missing`,
                    message: `Please select True or False for sub-question ${questionNumber}`
                };
            }
            break;

        case 'Match':
            const lefts = subQuestion.querySelectorAll(`input[name="match_left_${subQuestionId}[]"]`);
            const rights = subQuestion.querySelectorAll(`input[name="match_right_${subQuestionId}[]"]`);
            let filledPairs = 0;

            for (let i = 0; i < lefts.length; i++) {
                if (lefts[i] && rights[i] && !Utils.isEmpty(lefts[i].value) && !Utils.isEmpty(rights[i].value)) {
                    filledPairs++;
                }
            }

            if (filledPairs < 2) {
                return {
                    isValid: false,
                    title: `Sub-Question ${questionNumber} - Match Pairs Missing`,
                    message: `Please provide at least 2 match pairs for sub-question ${questionNumber}`
                };
            }
            break;

        case 'Custom':
            const customAnswer = subQuestion.querySelector(`textarea[name="custom_answer_${subQuestionId}"]`);

            if (!customAnswer || Utils.isEmpty(customAnswer.value)) {
                return {
                    isValid: false,
                    title: `Sub-Question ${questionNumber} - Answer Missing`,
                    message: `Please provide answer/instructions for sub-question ${questionNumber}`
                };
            }
            break;
    }

    return { isValid: true };
}

function syncStepData() {
    // Sync class and subject data from step 1 to step 4
    var classId = $('#class_id').val();
    var subjectId = $('#subject_master_id').val();

    if (subjectId) {
        // Populate lessons in step 4 based on selected subject
        getLessons();
    }
}

function highlightField(field, isError) {
    if (isError) {
        field.classList.add('is-invalid');
        field.classList.remove('is-valid');
    } else {
        field.classList.remove('is-invalid');
        field.classList.add('is-valid');
    }
}

function clearValidationErrors() {
    document.querySelectorAll('.is-invalid, .is-valid').forEach(function(el) {
        el.classList.remove('is-invalid', 'is-valid');
    });
}

function showAjaxError(type, message) {
    var selector = '';
    var retryFunction = null;

    switch(type) {
        case 'subjects':
            selector = '#subject_master_id';
            retryFunction = getSubjects;
            break;
        case 'lessons':
            selector = '#lesson_id, #lesson_id_step4';
            retryFunction = getLessons;
            break;
        case 'topics':
            selector = '#topic_id';
            retryFunction = getTopics;
            break;
    }

    var errorHtml = '<option value="">Error loading ' + type + '</option>';
    if (retryFunction) {
        errorHtml += '<option value="" disabled>Click to retry</option>';
    }

    $(selector).html(errorHtml);

    // Show user-friendly error message
    Swal.fire({
        icon: 'error',
        title: 'Loading Error',
        text: message,
        showCancelButton: retryFunction ? true : false,
        confirmButtonText: retryFunction ? 'Retry' : 'OK',
        cancelButtonText: 'Cancel'
    }).then((result) => {
        if (result.isConfirmed && retryFunction) {
            retryFunction();
        }
    });
}

function showFormError(message) {
    Swal.fire({
        icon: 'error',
        title: 'Error',
        text: message,
        confirmButtonText: 'OK'
    });
}

function showButtonLoading(buttonId, isLoading) {
    const button = document.getElementById(buttonId);
    if (!button) return;

    const spinner = button.querySelector('.btn-spinner');
    const text = button.querySelector('.btn-text');
    const icon = button.querySelector('.btn-icon');

    if (isLoading) {
        button.classList.add('btn-loading');
        button.disabled = true;
        if (spinner) spinner.style.display = 'inline-block';
        if (text) text.style.opacity = '0.5';
        if (icon) icon.style.opacity = '0.5';
    } else {
        button.classList.remove('btn-loading');
        button.disabled = false;
        if (spinner) spinner.style.display = 'none';
        if (text) text.style.opacity = '1';
        if (icon) icon.style.opacity = '1';
    }
}

function showStepLoading(isLoading) {
    const overlay = document.getElementById('stepLoadingOverlay');
    if (overlay) {
        overlay.style.display = isLoading ? 'flex' : 'none';
    }
}

function showFormLoading(formId, isLoading) {
    const form = document.getElementById(formId);
    if (!form) return;

    if (isLoading) {
        form.classList.add('form-loading');
    } else {
        form.classList.remove('form-loading');
    }
}

function prepareFormData(form, action) {
    const formData = new FormData(form);
    const questionType = document.getElementById('question_type').value;

    // console.log('Preparing form data for question type:', questionType);

    // Add action to form data
    formData.append('action', action);
    formData.append('question_type', questionType);

    // Handle specific question type data
    switch(questionType) {
        case 'MCQ':
            prepareMCQData(formData);
            break;
        case 'Comprehension':
            prepareComprehensionData(formData);
            break;
        case 'Match':
            prepareMatchData(formData);
            break;
        case 'Fill in the Blanks':
            prepareFillBlanksData(formData);
            break;
        case 'True/False':
            prepareTrueFalseData(formData);
            break;
        case 'Short Answer':
            prepareShortAnswerData(formData);
            break;
        case 'Long Answer':
            prepareLongAnswerData(formData);
            break;
        case 'Custom':
            prepareCustomAnswerData(formData);
            break;
        default:
            console.log('No specific data preparation for question type:', questionType);
            break;
    }

    // Debug: Log all form data
    console.log('Final form data entries:');
    for (let pair of formData.entries()) {
        console.log(pair[0] + ':', pair[1]);
    }

    return formData;
}

function prepareMCQData(formData) {
    console.log('Preparing MCQ data...');

    // First, ensure MCQ section is visible
    const mcqSection = document.getElementById('mcqAnswerSection');
    if (mcqSection && mcqSection.style.display === 'none') {
        console.log('MCQ section is hidden, temporarily making it visible...');
        mcqSection.style.display = 'block';
    }

    // Use document.querySelectorAll with multiple selectors to be more robust
    let options = document.querySelectorAll('input[name="option_text[]"]');
    let corrects = document.querySelectorAll('input[name="is_correct[]"]');

    console.log('Found options:', options.length);
    console.log('Found corrects:', corrects.length);

    // If no options found, try alternative selectors
    if (options.length === 0) {
        console.log('No options found with standard selector, trying alternatives...');
        options = document.querySelectorAll('#mcqOptions input[type="text"]');
        corrects = document.querySelectorAll('#mcqOptions input[type="checkbox"]');
        console.log('Alternative search - Options:', options.length, 'Corrects:', corrects.length);
    }

    // Debug: Log all option values
    options.forEach((option, index) => {
        const correct = corrects[index];
        console.log(`Option ${index}:`, {
            value: option.value,
            visible: option.offsetParent !== null,
            inMCQSection: option.closest('#mcqAnswerSection') !== null,
            correctElement: correct ? 'Found' : 'Missing',
            correctChecked: correct ? correct.checked : false
        });
    });

    const mcqData = {
        type: 'MCQ',
        options: [],
        correct_answers: [],
        allow_multiple: false,
        randomize_options: true
    };

    let validOptionsCount = 0;
    let correctAnswersCount = 0;

    options.forEach((option, index) => {
        if (option.value && option.value.trim() !== '') {
            const isCorrect = corrects[index] && corrects[index].checked;
            mcqData.options.push({
                text: option.value.trim(),
                is_correct: isCorrect
            });

            validOptionsCount++;
            if (isCorrect) {
                mcqData.correct_answers.push(index);
                correctAnswersCount++;
            }
        }
    });

    console.log(`MCQ Summary: ${validOptionsCount} options, ${correctAnswersCount} correct answers`);

    // Validation
    if (validOptionsCount < 2) {
        console.error('MCQ Error: Need at least 2 options');
        alert('Please provide at least 2 options for MCQ question');
        return;
    }

    if (correctAnswersCount === 0) {
        console.error('MCQ Error: No correct answer selected');
        alert('Please mark at least one option as correct');
        return;
    }

    console.log('MCQ Data prepared:', mcqData);

    // Also add individual fields for backward compatibility
    options.forEach((option, index) => {
        if (option.value && option.value.trim() !== '') {
            formData.append('option_text[]', option.value.trim());
            formData.append('is_correct[]', corrects[index] && corrects[index].checked ? '1' : '0');
        }
    });

    formData.append('mcq_data', JSON.stringify(mcqData));
}

function prepareComprehensionData(formData) {
    console.log('Preparing Comprehension data...');

    // Check if comprehension section is visible
    const comprehensionSection = document.getElementById('comprehensionSection');
    console.log('Comprehension section found:', comprehensionSection ? 'Yes' : 'No');
    console.log('Comprehension section visible:', comprehensionSection ? (comprehensionSection.style.display !== 'none') : 'N/A');

    const passage = document.querySelector('textarea[name="passage_text"]');
    const subQuestions = document.querySelectorAll('.sub-question');

    console.log('Found passage element:', passage ? 'Yes' : 'No');
    console.log('Passage value:', passage ? `"${passage.value}"` : 'N/A');
    console.log('Passage visible:', passage ? (passage.offsetParent !== null) : 'N/A');
    console.log('Found sub-questions:', subQuestions.length);

    // Debug sub-questions
    subQuestions.forEach((subQ, index) => {
        console.log(`Sub-question ${index}:`, {
            id: subQ.id,
            visible: subQ.offsetParent !== null,
            hasQuestionText: !!subQ.querySelector('textarea[name="comprehension_question[]"]'),
            hasQuestionType: !!subQ.querySelector('select[name="comprehension_question_type[]"]')
        });
    });

    const comprehensionData = {
        type: 'Comprehension',
        passage: passage ? passage.value.trim() : '',
        sub_questions: []
    };

    subQuestions.forEach((subQ, index) => {
        const subQuestionId = subQ.id;
        const questionText = subQ.querySelector('textarea[name="comprehension_question[]"]');
        const questionType = subQ.querySelector('select[name="comprehension_question_type[]"]');
        const marks = subQ.querySelector('input[name="comprehension_marks[]"]');
        const negativeMarks = subQ.querySelector('input[name="comprehension_negative_marks[]"]');

        console.log(`Processing sub-question ${index}:`, {
            subQuestionId: subQuestionId,
            questionTextElement: questionText ? 'Found' : 'NOT FOUND',
            questionTextValue: questionText ? `"${questionText.value}"` : 'N/A',
            questionTypeElement: questionType ? 'Found' : 'NOT FOUND',
            questionTypeValue: questionType ? questionType.value : 'N/A',
            marksElement: marks ? 'Found' : 'NOT FOUND',
            marksValue: marks ? marks.value : 'N/A',
            negativeMarksElement: negativeMarks ? 'Found' : 'NOT FOUND',
            negativeMarksValue: negativeMarks ? negativeMarks.value : 'N/A'
        });

        if (questionText && questionText.value && questionText.value.trim() !== '') {
            const type = questionType ? questionType.value : 'MCQ';
            console.log(`✅ Adding sub-question ${index} with type: ${type}`);

            const subQuestionData = {
                question: questionText.value.trim(),
                type: type,
                marks: marks ? parseFloat(marks.value) || 0 : 0,
                negative_marks: negativeMarks ? parseFloat(negativeMarks.value) || 0 : 0,
                answer_data: prepareSubQuestionAnswerData(subQ, subQuestionId, type)
            };

            console.log(`Sub-question ${index} data:`, subQuestionData);
            comprehensionData.sub_questions.push(subQuestionData);
        } else {
            console.log(`❌ Skipping sub-question ${index} - missing or empty question text`);
        }
    });

    console.log('Comprehension Data prepared:', comprehensionData);

    // Also add passage for backward compatibility
    if (passage && passage.value) {
        formData.append('passage_text', passage.value.trim());
    }

    formData.append('comprehension_data', JSON.stringify(comprehensionData));
}

function prepareSubQuestionAnswerData(subQuestion, subQuestionId, type) {
    switch(type) {
        case 'MCQ':
            const options = subQuestion.querySelectorAll(`input[name="mcq_option_${subQuestionId}[]"]`);
            const corrects = subQuestion.querySelectorAll(`input[name="mcq_correct_${subQuestionId}[]"]`);

            const mcqData = {
                options: [],
                correct_answers: []
            };

            options.forEach((option, index) => {
                if (!Utils.isEmpty(option.value)) {
                    mcqData.options.push({
                        text: option.value.trim(),
                        is_correct: corrects[index] && corrects[index].checked
                    });

                    if (corrects[index] && corrects[index].checked) {
                        mcqData.correct_answers.push(index);
                    }
                }
            });

            return mcqData;

        case 'Short Answer':
            const shortAnswers = subQuestion.querySelectorAll(`input[name="short_answer_${subQuestionId}[]"]`);
            const shortAnswerData = {
                answers: []
            };

            shortAnswers.forEach(answer => {
                if (!Utils.isEmpty(answer.value)) {
                    shortAnswerData.answers.push(answer.value.trim());
                }
            });

            return shortAnswerData;

        case 'Long Answer':
            const longAnswers = subQuestion.querySelectorAll(`textarea[name="long_answer_${subQuestionId}[]"]`);
            const longAnswerData = {
                sample_answers: []
            };

            longAnswers.forEach(answer => {
                if (!Utils.isEmpty(answer.value)) {
                    longAnswerData.sample_answers.push(answer.value.trim());
                }
            });

            return longAnswerData;

        case 'Fill in the Blanks':
            const blanks = subQuestion.querySelectorAll(`input[name="blank_answer_${subQuestionId}[]"]`);
            const fillBlanksData = {
                answers: []
            };

            blanks.forEach(blank => {
                if (!Utils.isEmpty(blank.value)) {
                    fillBlanksData.answers.push(blank.value.trim());
                }
            });

            return fillBlanksData;

        case 'True/False':
            const tfAnswer = subQuestion.querySelector(`input[name="tf_answer_${subQuestionId}"]:checked`);
            return {
                correct_answer: tfAnswer ? tfAnswer.value : null
            };

        case 'Match':
            const lefts = subQuestion.querySelectorAll(`input[name="match_left_${subQuestionId}[]"]`);
            const rights = subQuestion.querySelectorAll(`input[name="match_right_${subQuestionId}[]"]`);
            const matchData = {
                pairs: []
            };

            lefts.forEach((left, index) => {
                const right = rights[index];
                if (left && right && !Utils.isEmpty(left.value) && !Utils.isEmpty(right.value)) {
                    matchData.pairs.push({
                        left: left.value.trim(),
                        right: right.value.trim()
                    });
                }
            });

            return matchData;

        case 'Custom':
            const customAnswer = subQuestion.querySelector(`textarea[name="custom_answer_${subQuestionId}"]`);
            return {
                instructions: customAnswer ? customAnswer.value.trim() : ''
            };

        default:
            return {};
    }
}

function prepareMatchData(formData) {
    console.log('Preparing Match data...');

    // Use document.querySelectorAll for more reliable selection
    const lefts = document.querySelectorAll('input[name="match_left[]"]');
    const rights = document.querySelectorAll('input[name="match_right[]"]');

    console.log('Found left options:', lefts.length);
    console.log('Found right options:', rights.length);

    // Debug: Check if match section is visible
    const matchSection = document.getElementById('matchSection');
    console.log('Match section found:', matchSection ? 'Yes' : 'No');
    console.log('Match section visible:', matchSection ? (matchSection.style.display !== 'none') : 'N/A');

    // Debug: Log all left and right values
    lefts.forEach((left, index) => {
        const right = rights[index];
        console.log(`Left ${index}:`, left ? left.value : 'NOT FOUND', 'Visible:', left ? (left.offsetParent !== null) : 'N/A');
        console.log(`Right ${index}:`, right ? right.value : 'NOT FOUND', 'Visible:', right ? (right.offsetParent !== null) : 'N/A');
    });

    const matchData = {
        type: 'Match',
        pairs: [],
        randomize: true
    };

    let validPairsCount = 0;

    lefts.forEach((left, index) => {
        const right = rights[index];
        console.log(`Processing pair ${index}:`, {
            leftExists: !!left,
            rightExists: !!right,
            leftValue: left ? left.value : 'N/A',
            rightValue: right ? right.value : 'N/A',
            leftTrimmed: left && left.value ? left.value.trim() : 'N/A',
            rightTrimmed: right && right.value ? right.value.trim() : 'N/A'
        });

        if (left && right && left.value && left.value.trim() !== '' && right.value && right.value.trim() !== '') {
            const pair = {
                left: left.value.trim(),
                right: right.value.trim()
            };
            matchData.pairs.push(pair);

            // Also add for backward compatibility
            formData.append('match_left[]', left.value.trim());
            formData.append('match_right[]', right.value.trim());

            validPairsCount++;
            console.log(`✅ Added Match pair ${index}:`, pair);
        } else {
            console.log(`❌ Skipped pair ${index} - missing or empty values`);
        }
    });

    console.log(`Match Summary: ${validPairsCount} valid pairs`);

    // Validation
    if (validPairsCount < 2) {
        console.error('Match Error: Need at least 2 pairs');
        alert('Please provide at least 2 match pairs');
        return;
    }

    console.log('Final Match Data prepared:', matchData);
    console.log('Total pairs added:', matchData.pairs.length);
    formData.append('match_data', JSON.stringify(matchData));
}

function prepareFillBlanksData(formData) {
    console.log('Preparing Fill in the Blanks data...');

    // Use document.querySelectorAll for more reliable selection
    const blanks = document.querySelectorAll('input[name="blank_answer[]"]');

    console.log('Found blanks:', blanks.length);

    // Debug: Log all blank values
    blanks.forEach((blank, index) => {
        console.log(`Blank ${index}:`, blank.value, 'Visible:', blank.offsetParent !== null, 'In Fill Blanks section:', blank.closest('#fillBlanksSection') !== null);
    });

    const fillBlanksData = {
        type: 'Fill in the Blanks',
        answers: [],
        blanks: [] // Add blanks array for compatibility
    };

    let validBlanksCount = 0;

    blanks.forEach((blank, index) => {
        if (blank.value && blank.value.trim() !== '') {
            const answer = blank.value.trim();
            fillBlanksData.answers.push(answer);
            fillBlanksData.blanks.push(answer); // Add to both arrays for compatibility

            // Also add for backward compatibility
            formData.append('blank_answer[]', answer);
            validBlanksCount++;

            console.log(`✅ Added blank ${index + 1}: "${answer}"`);
        } else {
            console.log(`❌ Skipped blank ${index + 1} - empty value`);
        }
    });

    console.log(`Fill Blanks Summary: ${validBlanksCount} valid blanks`);

    // Validation
    if (validBlanksCount === 0) {
        console.error('Fill Blanks Error: No blank answers provided');
        alert('Please provide at least one blank answer');
        return;
    }

    console.log('Fill in the Blanks Data prepared:', fillBlanksData);
    formData.append('fill_blanks_data', JSON.stringify(fillBlanksData));
}

function prepareTrueFalseData(formData) {
    console.log('Preparing True/False data...');

    const tfAnswer = document.querySelector('input[name="tf_answer"]:checked');

    const tfData = {
        type: 'True/False',
        correct_answer: tfAnswer ? tfAnswer.value : null,
        explanation: ''
    };

    console.log('True/False Data prepared:', tfData);
    console.log('TF Answer element found:', tfAnswer);
    console.log('TF Answer value:', tfAnswer ? tfAnswer.value : 'None selected');

    // Also add the field for backward compatibility
    if (tfAnswer) {
        formData.append('tf_answer', tfAnswer.value);
    }

    formData.append('tf_data', JSON.stringify(tfData));
}

function prepareShortAnswerData(formData) {
    console.log('Preparing Short Answer data...');

    const answers = document.querySelectorAll('input[name="short_answer[]"]');

    const shortAnswerData = {
        type: 'Short Answer',
        answers: [],
        case_sensitive: false,
        exact_match: false
    };

    answers.forEach(answer => {
        if (answer.value && answer.value.trim() !== '') {
            shortAnswerData.answers.push(answer.value.trim());
            // Also add for backward compatibility
            formData.append('short_answer[]', answer.value.trim());
        }
    });

    console.log('Short Answer Data prepared:', shortAnswerData);
    formData.append('short_answer_data', JSON.stringify(shortAnswerData));
}

function prepareLongAnswerData(formData) {
    console.log('Preparing Long Answer data...');

    const answers = document.querySelectorAll('textarea[name="long_answer[]"]');

    const longAnswerData = {
        type: 'Long Answer',
        sample_answers: [],
        rubric: '',
        max_words: null
    };

    answers.forEach(answer => {
        if (answer.value && answer.value.trim() !== '') {
            longAnswerData.sample_answers.push(answer.value.trim());
            // Also add for backward compatibility
            formData.append('long_answer[]', answer.value.trim());
        }
    });

    console.log('Long Answer Data prepared:', longAnswerData);
    formData.append('long_answer_data', JSON.stringify(longAnswerData));
}

function prepareCustomAnswerData(formData) {
    console.log('Preparing Custom Answer data...');

    const customAnswer = document.querySelector('input[name="custom_answer"]');

    const customData = {
        type: 'Custom',
        answer: customAnswer ? customAnswer.value.trim() : '',
        instructions: ''
    };

    console.log('Custom Answer Data prepared:', customData);
    console.log('Custom answer element found:', customAnswer);
    console.log('Custom answer value:', customAnswer ? customAnswer.value : 'Not found');

    // Also add for backward compatibility
    if (customAnswer && customAnswer.value) {
        formData.append('custom_answer', customAnswer.value.trim());
    }

    formData.append('custom_answer_data', JSON.stringify(customData));
}

// Accessibility and keyboard navigation functions
function navigateToStep(step) {
    currentStep = step;
    showStep(currentStep);
    updateStepTabIndex();
}

function navigateStepLabels(direction) {
    const labels = document.querySelectorAll('.step-label');
    const currentIndex = Array.from(labels).findIndex(label => label === document.activeElement);
    let newIndex = currentIndex + direction;

    if (newIndex < 0) newIndex = 0;
    if (newIndex >= labels.length) newIndex = labels.length - 1;

    focusStepLabel(newIndex + 1);
}

function focusStepLabel(step) {
    const label = document.getElementById('label-step-' + step);
    if (label) {
        label.focus();
    }
}

function updateStepTabIndex() {
    for (let i = 1; i <= totalSteps; i++) {
        const label = document.getElementById('label-step-' + i);
        if (label) {
            if (i === currentStep) {
                label.setAttribute('tabindex', '0');
                label.setAttribute('aria-selected', 'true');
            } else {
                label.setAttribute('tabindex', '-1');
                label.setAttribute('aria-selected', 'false');
            }
        }
    }
}

function resetDependentFields(changedField) {
    switch(changedField) {
        case 'class':
            // Reset subject and all dependent fields
            $('#subject_master_id').html('<option value="">Select Subject</option>');
            $('#lesson_id, #lesson_id_step4').html('<option value="">Select Lesson</option>');
            $('#topic_id').html('<option value="">Select Topic</option>');

            // DON'T reset form data when changing class/subject/lesson
            // Only reset the dropdown options, not the user's work
            // resetStep2Fields();
            // resetStep3Fields();
            // resetStep4Fields();
            // resetStep5Fields();
            break;

        case 'subject':
            // Reset lesson and all dependent fields
            $('#lesson_id, #lesson_id_step4').html('<option value="">Select Lesson</option>');
            $('#topic_id').html('<option value="">Select Topic</option>');

            // DON'T reset form data when changing subject
            // resetStep2Fields();
            // resetStep3Fields();
            // resetStep4Fields();
            // resetStep5Fields();
            break;

        case 'lesson':
            // Reset topic and dependent fields
            $('#topic_id').html('<option value="">Select Topic</option>');

            // DON'T reset form data when changing lesson
            // resetStep2Fields();
            // resetStep3Fields();
            // resetStep4Fields();
            // resetStep5Fields();
            break;

        case 'question_type':
            // DON'T reset fields when question type changes - this was causing data loss
            // resetStep3Fields();
            // resetStep4Fields();
            // resetStep5Fields();
            break;
    }
}

function resetStep2Fields() {
    // Reset question text and image
    $('textarea[name="question_text"]').val('');
    $('input[name="question_image"]').val('');
}

function resetStep3Fields() {
    // Reset all answer sections
    $('#mcqOptions').html('');
    $('#shortAnswerList').html('');
    $('#longAnswerList').html('');
    $('#fillBlanksList').html('');
    $('#matchList').html('');
    $('#comprehensionQuestions').html('');

    // Reset form inputs
    $('input[name="short_answer[]"]').val('');
    $('textarea[name="long_answer[]"]').val('');
    $('textarea[name="passage_text"]').val('');
    $('input[name="custom_answer"]').val('');

    // Reset radio buttons
    $('input[name="tf_answer"]').prop('checked', false);

    // Reset MCQ count
    mcqCount = 0;
}

function resetStep4Fields() {
    // Reset additional mapping fields
    $('#difficulty_level').val('');
    $('#max_marks').val('');
    $('#negative_marks').val('');
    $('#blooms').val([]);
    $('#skills').val([]);
    $('#tags').val('');

    // Reset reusable classes checkboxes
    // $('input[name="reusable_classes[]"]').prop('checked', false);
}

function resetStep5Fields() {
    // Reset hints and attachments
    $('textarea[name="hints"]').val('');
    $('input[name="attachment"]').val('');
}

function handleQuestionTypeChange() {
    // DON'T reset fields when question type changes - just update the answer section
    // resetDependentFields('question_type'); // ← REMOVED to prevent data loss

    // Also trigger the existing question type change handler
    if (typeof changeQuestionType === 'function') {
        changeQuestionType();
    }

    // Check for duplicates when question type changes
    checkForDuplicates();
}

function handleLessonChange() {
    // Get topics and reset dependent fields
    getTopics();
}

function handleTopicChange() {
    // Reset fields that depend on topic selection
    // Clear validation states
    clearValidationErrors();
    assignTopicName();
    // Could add topic-specific logic here if needed
    // console.log('Topic changed to:', $('#topic_id').val());
}

// Add change handlers for form fields that should trigger resets
$(document).ready(function() {
    // Add change handler for question text
    $('textarea[name="question_text"]').on('change input', function() {
        // Clear validation states when user starts typing
        clearValidationErrors();
    });

    // Add change handler for learning outcome
    $('#learning_outcome').on('change', function() {
        // Could add specific logic here if needed
    });

    // Add change handlers for Step 4 fields that might affect validation
    $('#difficulty_level, #max_marks, #negative_marks').on('change', function() {
        // Clear validation states
        clearValidationErrors();
    });

    // Add change handler for tags
    $('#tags').on('change input', function() {
        // Could add tag validation here if needed
    });

    // Add change handlers for hints and attachments
    $('textarea[name="hints"], input[name="attachment"]').on('change', function() {
        // Clear validation states
        clearValidationErrors();
    });

    // Add change handler for learning outcome that might affect further steps
    $('#learning_outcome').on('change', function() {
        // Clear validation states
        clearValidationErrors();
    });

    // Initialize form state
    initializeFormState();
});

function initializeFormState() {
    // Set initial state for the form
    currentStep = 1;
    furthestStep = 1;
    mcqCount = 0;

    // Show initial step
    showStep(1);

    // Clear any existing validation states
    clearValidationErrors();
}

function resetEntireForm() {
    // Reset all form fields to initial state
    $('#questionForm')[0].reset();

    // Reset all dependent dropdowns
    resetDependentFields('class');

    // Reset step navigation
    currentStep = 1;
    furthestStep = 1;
    showStep(1);

    // Clear validation states
    clearValidationErrors();

    // Reset counters
    mcqCount = 0;

    // Clear all dynamic content
    $('#mcqOptions').html('');
    $('#shortAnswerList').html('');
    $('#longAnswerList').html('');
    $('#fillBlanksList').html('');
    $('#matchList').html('');
    $('#comprehensionQuestions').html('');

    console.log('Form reset to initial state');
}

function addMcqOption(text = '', isCorrect = false) {
    mcqCount++;
    const container = document.getElementById('mcqOptions');
    const optionHTML = `
        <div class="input-group mb-2">
        <div class="input-group-text">
            <input class="mt-0" name="is_correct[]" type="checkbox" value="${mcqCount}">
        </div>
        <input type="text" name="option_text[]" class="form-control" value="${text}" placeholder="Option ${mcqCount}">
        <button type="button" class="btn btn-outline-danger" onclick="this.parentElement.remove()"><i class='fa fa-times mr-0'></i></button>
        </div>`;
    container.insertAdjacentHTML('beforeend', optionHTML);
}

function updateAnswerSection() {
    const qType = document.getElementById('question_type').value;
    // Hide all answer sections
    document.querySelectorAll('.answer-type-section').forEach(el => el.style.display = 'none');
    if (qType === 'MCQ') {
        document.getElementById('mcqAnswerSection').style.display = '';
        const addBtn = document.getElementById('addOptionBtn');
        addBtn.style.display = '';
        const mcqDiv = document.getElementById('mcqOptions');

        // Only clear and add default options if the container is empty
        if (mcqDiv.children.length === 0) {
            mcqDiv.innerHTML = '';
            mcqCount = 0;
            for (let i = 0; i < 4; i++) addMcqOption();
            console.log('Added default MCQ options');
        } else {
            console.log('MCQ options already exist, not clearing them');
        }
    } else if (qType === 'Short Answer') {
        document.getElementById('shortAnswerSection').style.display = '';
        document.getElementById('shortAnswerList').innerHTML = '';
    } else if (qType === 'Long Answer') {
        document.getElementById('longAnswerSection').style.display = '';
        document.getElementById('longAnswerList').innerHTML = '';
    } else if (qType === 'Fill in the Blanks') {
        document.getElementById('fillBlanksSection').style.display = '';
        document.getElementById('fillBlanksList').innerHTML = '';
        addBlank();
    } else if (qType === 'True/False') {
        document.getElementById('tfSection').style.display = '';
    } else if (qType === 'Match') {
        document.getElementById('matchSection').style.display = '';
        document.getElementById('matchList').innerHTML = '';
        addMatchPair();
        addMatchPair();
    } else if (qType === 'Comprehension') {
        document.getElementById('comprehensionSection').style.display = '';
        document.getElementById('comprehensionQuestions').innerHTML = '';
        // Add initial sub-question
        setTimeout(() => {
            addComprehensionQuestion();
        }, 100);
    } else if (qType === 'Custom') {
        document.getElementById('customSection').style.display = '';
    }
}

// Add dynamic field functions for each type
function addShortAnswer() {
    const list = document.getElementById('shortAnswerList');
    const input = document.createElement('input');
    input.type = 'text';
    input.className = 'form-control mb-2';
    input.name = 'short_answer[]';
    list.appendChild(input);
}
function addLongAnswer() {
    const list = document.getElementById('longAnswerList');
    const textarea = document.createElement('textarea');
    textarea.className = 'form-control mb-2';
    textarea.name = 'long_answer[]';
    list.appendChild(textarea);
}
function addBlank() {
    const list = document.getElementById('fillBlanksList');
    const div = document.createElement('div');
    div.className = 'input-group mb-2';
    div.innerHTML = `<span class="input-group-text">Blank</span><input type="text" class="form-control" name="blank_answer[]" placeholder="Correct Answer"><button type="button" class="btn btn-outline-danger" onclick="this.parentElement.remove()"><i class='fa fa-times mr-0'></i></button>`;
    list.appendChild(div);
}
function addMatchPair(left = '', right = '') {
    const list = document.getElementById('matchList');
    const div = document.createElement('div');
    div.className = 'input-group mb-2';
    div.innerHTML = `<input type="text" class="form-control" name="match_left[]" placeholder="Left"><span class="input-group-text">=</span><input type="text" class="form-control" name="match_right[]" placeholder="Right"><button type="button" class="btn btn-outline-danger" onclick="this.parentElement.remove()"><i class='fa fa-times mr-0'></i></button>`;
    list.appendChild(div);
}
function addComprehensionQuestion() {
    const list = document.getElementById('comprehensionQuestions');
    const subQuestionId = `subQuestion_${Date.now()}`;
    const subQuestionNumber = list.children.length + 1;

    const div = document.createElement('div');
    div.className = 'sub-question mb-4 border rounded p-3';
    div.id = subQuestionId;
    div.style.backgroundColor = '#f8f9fa';

    div.innerHTML = `
        <div class="d-flex justify-content-between align-items-center mb-3">
            <h6 class="mb-0 text-primary">Sub-Question ${subQuestionNumber}</h6>
            <button type="button" class="btn btn-outline-danger btn-sm" onclick="removeSubQuestion('${subQuestionId}')">
                <i class="fa fa-times mr-0"></i> Remove
            </button>
        </div>

        <div class="row mb-3">
            <div class="col-md-8">
                <label class="fw-bold mb-1">Question Text <span style="color:red;">*</span></label>
                <textarea class="form-control" name="comprehension_question[]" placeholder="Enter sub-question" rows="2" required></textarea>
            </div>
            <div class="col-md-4">
                <label class="fw-bold mb-1">Question Type <span style="color:red;">*</span></label>
                <select class="form-control select-enhanced" name="comprehension_question_type[]" onchange="updateSubQuestionType('${subQuestionId}', this.value)" required>
                    <option value="MCQ">Multiple Choice</option>
                    <option value="Short Answer">Short Answer</option>
                    <option value="Long Answer">Long Answer</option>
                    <option value="Fill in the Blanks">Fill in the Blanks</option>
                    <option value="True/False">True / False</option>
                    <option value="Match">Match</option>
                    <option value="Custom">Custom</option>
                </select>
            </div>
        </div>

        <div class="sub-question-answer-section mb-3">
            <!-- Answer options will be populated based on question type -->
        </div>

        <div class="row">
            <div class="col-md-6">
                <label class="fw-bold mb-1">Marks</label>
                <input type="number" class="form-control" name="comprehension_marks[]" placeholder="Enter marks" min="0" step="0.5">
            </div>
            <div class="col-md-6">
                <label class="fw-bold mb-1">Negative Marks</label>
                <input type="number" class="form-control" name="comprehension_negative_marks[]" placeholder="Enter negative marks" min="0" step="0.5">
            </div>
        </div>
    `;

    list.appendChild(div);

    // Initialize with MCQ type by default
    updateSubQuestionType(subQuestionId, 'MCQ');

    // Update sub-question numbers
    updateSubQuestionNumbers();
}

function updateSubQuestionType(subQuestionId, type) {
    const answerSection = document.querySelector(`#${subQuestionId} .sub-question-answer-section`);
    if (!answerSection) return;

    answerSection.innerHTML = ''; // Clear previous inputs

    switch(type) {
        case 'MCQ':
            const mcqContainerId = `mcqOptions_${subQuestionId}`;
            answerSection.innerHTML = `
                <label class="fw-bold mb-2">Answer Options <span style="color:red;">*</span></label>
                <div id="${mcqContainerId}" class="mb-2"></div>
                <button type="button" class="btn btn-outline-primary btn-sm" onclick="addSubQuestionMcqOption('${subQuestionId}')">
                    <i class="fa fa-plus mr-0"></i> Add Option
                </button>
                <small class="text-muted d-block mt-1">Add at least 2 options and mark the correct answer(s)</small>`;

            // Initialize with 2 options
            setTimeout(() => {
                addSubQuestionMcqOption(subQuestionId);
                addSubQuestionMcqOption(subQuestionId);
            }, 100);
            break;

        case 'Short Answer':
            answerSection.innerHTML = `
                <label class="fw-bold mb-2">Correct Answer(s) <span style="color:red;">*</span></label>
                <div id="shortAnswers_${subQuestionId}">
                    <input type="text" class="form-control mb-2" name="short_answer_${subQuestionId}[]" placeholder="Enter correct answer" required>
                </div>
                <button type="button" class="btn btn-outline-primary btn-sm" onclick="addSubQuestionShortAnswer('${subQuestionId}')">
                    <i class="fa fa-plus mr-0"></i> Add Another Answer
                </button>
                <small class="text-muted d-block mt-1">Add possible correct answers</small>`;
            break;

        case 'Long Answer':
            answerSection.innerHTML = `
                <label class="fw-bold mb-2">Sample Answer <span style="color:red;">*</span></label>
                <textarea class="form-control mb-2" name="long_answer_${subQuestionId}[]" placeholder="Enter sample answer or key points" rows="4" required></textarea>
                <button type="button" class="btn btn-outline-primary btn-sm" onclick="addSubQuestionLongAnswer('${subQuestionId}')">
                    <i class="fa fa-plus mr-0"></i> Add Alternative Answer
                </button>
                <small class="text-muted d-block mt-1">Provide sample answer or key points for evaluation</small>`;
            break;

        case 'Fill in the Blanks':
            answerSection.innerHTML = `
                <label class="fw-bold mb-2">Blank Answers <span style="color:red;">*</span></label>
                <div id="fillBlanks_${subQuestionId}">
                    <div class="input-group mb-2">
                        <span class="input-group-text">Blank 1</span>
                        <input type="text" class="form-control" name="blank_answer_${subQuestionId}[]" placeholder="Correct answer for blank" required>
                        <button type="button" class="btn btn-outline-danger" onclick="this.parentElement.remove()">
                            <i class="fa fa-times mr-0"></i>
                        </button>
                    </div>
                </div>
                <button type="button" class="btn btn-outline-primary btn-sm" onclick="addSubQuestionBlank('${subQuestionId}')">
                    <i class="fa fa-plus mr-0"></i> Add Blank
                </button>
                <small class="text-muted d-block mt-1">Add answers for each blank in the question</small>`;
            break;

        case 'True/False':
            answerSection.innerHTML = `
                <label class="fw-bold mb-3">Correct Answer <span style="color:red;">*</span></label>
                <div class="tf-options-container">
                    <div class="tf-option">
                        <input class="form-check-input" type="radio" name="tf_answer_${subQuestionId}" value="True" id="tf_true_${subQuestionId}" required>
                        <label class="tf-label tf-true" for="tf_true_${subQuestionId}">
                            <div class="tf-icon">
                                <i class="fa fa-check mr-0"></i>
                            </div>
                            <div class="tf-text">
                                <span class="tf-title">True</span>
                                <small class="tf-description">This statement is correct</small>
                            </div>
                        </label>
                    </div>
                    <div class="tf-option">
                        <input class="form-check-input" type="radio" name="tf_answer_${subQuestionId}" value="False" id="tf_false_${subQuestionId}" required>
                        <label class="tf-label tf-false" for="tf_false_${subQuestionId}">
                            <div class="tf-icon">
                                <i class="fa fa-times mr-0"></i>
                            </div>
                            <div class="tf-text">
                                <span class="tf-title">False</span>
                                <small class="tf-description">This statement is incorrect</small>
                            </div>
                        </label>
                    </div>
                </div>
                <small class="text-muted mt-2 d-block">Select the correct answer for this True/False question</small>`;
            break;

        case 'Match':
            const matchContainerId = `matchList_${subQuestionId}`;
            answerSection.innerHTML = `
                <label class="fw-bold mb-2">Match Pairs <span style="color:red;">*</span></label>
                <div id="${matchContainerId}" class="mb-2"></div>
                <button type="button" class="btn btn-outline-primary btn-sm" onclick="addSubQuestionMatchPair('${subQuestionId}')">
                    <i class="fa fa-plus mr-0"></i> Add Pair
                </button>
                <small class="text-muted d-block mt-1">Add at least 2 matching pairs</small>`;

            // Initialize with 2 pairs
            setTimeout(() => {
                addSubQuestionMatchPair(subQuestionId);
                addSubQuestionMatchPair(subQuestionId);
            }, 100);
            break;

        case 'Custom':
            answerSection.innerHTML = `
                <label class="fw-bold mb-2">Answer/Instructions <span style="color:red;">*</span></label>
                <textarea class="form-control mb-2" name="custom_answer_${subQuestionId}" placeholder="Enter answer or evaluation instructions" rows="3" required></textarea>
                <small class="text-muted d-block mt-1">Provide answer or instructions for manual evaluation</small>`;
            break;
    }
}

// Sub-question specific helper functions
function addSubQuestionMcqOption(subQuestionId) {
    const container = document.getElementById(`mcqOptions_${subQuestionId}`);
    if (!container) {
        console.error(`Container with ID mcqOptions_${subQuestionId} does not exist.`);
        return;
    }

    const optionId = `option_${Date.now()}`;
    const optionNumber = container.children.length + 1;
    const optionHTML = `
        <div class="input-group mb-2" id="${optionId}">
            <span class="input-group-text">${String.fromCharCode(64 + optionNumber)}</span>
            <input type="text" class="form-control" name="mcq_option_${subQuestionId}[]" placeholder="Enter option text" required>
            <div class="input-group-text">
                <input class="mt-0" type="checkbox" name="mcq_correct_${subQuestionId}[]" value="${optionId}" title="Mark as correct">
            </div>
            <button type="button" class="btn btn-outline-danger" onclick="removeSubQuestionOption('${optionId}', '${subQuestionId}')" title="Remove option">
                <i class="fa fa-times mr-0"></i>
            </button>
        </div>`;
    container.insertAdjacentHTML('beforeend', optionHTML);
}

function addSubQuestionShortAnswer(subQuestionId) {
    const container = document.getElementById(`shortAnswers_${subQuestionId}`);
    if (!container) return;

    const answerHTML = `
        <div class="input-group mb-2">
            <input type="text" class="form-control" name="short_answer_${subQuestionId}[]" placeholder="Enter another correct answer">
            <button type="button" class="btn btn-outline-danger" onclick="this.parentElement.remove()">
                <i class="fa fa-times mr-0"></i>
            </button>
        </div>`;
    container.insertAdjacentHTML('beforeend', answerHTML);
}

function addSubQuestionLongAnswer(subQuestionId) {
    const answerSection = document.querySelector(`#${subQuestionId} .sub-question-answer-section`);
    if (!answerSection) return;

    const answerHTML = `
        <div class="mb-2">
            <textarea class="form-control" name="long_answer_${subQuestionId}[]" placeholder="Enter alternative answer" rows="3"></textarea>
            <button type="button" class="btn btn-outline-danger btn-sm mt-1" onclick="this.parentElement.remove()">
                <i class="fa fa-times mr-0"></i> Remove
            </button>
        </div>`;

    // Insert before the "Add Alternative Answer" button
    const addButton = answerSection.querySelector('button');
    addButton.insertAdjacentHTML('beforebegin', answerHTML);
}

function addSubQuestionBlank(subQuestionId) {
    const container = document.getElementById(`fillBlanks_${subQuestionId}`);
    if (!container) return;

    const blankNumber = container.children.length + 1;
    const blankHTML = `
        <div class="input-group mb-2">
            <span class="input-group-text">Blank ${blankNumber}</span>
            <input type="text" class="form-control" name="blank_answer_${subQuestionId}[]" placeholder="Correct answer for blank" required>
            <button type="button" class="btn btn-outline-danger" onclick="removeSubQuestionBlank(this, '${subQuestionId}')">
                <i class="fa fa-times mr-0"></i>
            </button>
        </div>`;
    container.insertAdjacentHTML('beforeend', blankHTML);
}

function addSubQuestionMatchPair(subQuestionId) {
    const container = document.getElementById(`matchList_${subQuestionId}`);
    if (!container) return;

    const pairId = `pair_${Date.now()}`;
    const pairHTML = `
        <div class="input-group mb-2" id="${pairId}">
            <input type="text" class="form-control" name="match_left_${subQuestionId}[]" placeholder="Left item" required>
            <span class="input-group-text">=</span>
            <input type="text" class="form-control" name="match_right_${subQuestionId}[]" placeholder="Right item" required>
            <button type="button" class="btn btn-outline-danger" onclick="document.getElementById('${pairId}').remove()">
                <i class="fa fa-times mr-0"></i>
            </button>
        </div>`;
    container.insertAdjacentHTML('beforeend', pairHTML);
}

// Helper functions for removing elements
function removeSubQuestionOption(optionId, subQuestionId) {
    document.getElementById(optionId)?.remove();
    updateMcqOptionLabels(subQuestionId);
}

function removeSubQuestionBlank(button, subQuestionId) {
    button.parentElement.remove();
    updateBlankLabels(subQuestionId);
}

function updateMcqOptionLabels(subQuestionId) {
    const container = document.getElementById(`mcqOptions_${subQuestionId}`);
    if (!container) return;

    const options = container.querySelectorAll('.input-group');
    options.forEach((option, index) => {
        const label = option.querySelector('.input-group-text:first-child');
        if (label) {
            label.textContent = String.fromCharCode(65 + index); // A, B, C, D...
        }
    });
}

function updateBlankLabels(subQuestionId) {
    const container = document.getElementById(`fillBlanks_${subQuestionId}`);
    if (!container) return;

    const blanks = container.querySelectorAll('.input-group');
    blanks.forEach((blank, index) => {
        const label = blank.querySelector('.input-group-text');
        if (label) {
            label.textContent = `Blank ${index + 1}`;
        }
    });
}

// Legacy function for backward compatibility
function addMcqOption(subQuestionId) {
    if (subQuestionId) {
        addSubQuestionMcqOption(subQuestionId);
    } else {
        // Original MCQ function for main question
        mcqCount++;
        const container = document.getElementById('mcqOptions');
        const optionHTML = `
            <div class="input-group mb-2">
                <div class="input-group-text">
                    <input class="mt-0" name="is_correct[]" type="checkbox" value="${mcqCount}">
                </div>
                <input type="text" name="option_text[]" class="form-control" value="" placeholder="Option ${mcqCount}">
                <button type="button" class="btn btn-outline-danger" onclick="this.parentElement.remove()"><i class='fa fa-times mr-0'></i></button>
            </div>`;
        container.insertAdjacentHTML('beforeend', optionHTML);
    }
}

// Legacy function for backward compatibility - removed duplicate, using the one above

function updateSubQuestionNumbers() {
    const subQuestions = document.querySelectorAll('.sub-question');
    subQuestions.forEach((subQ, index) => {
        const header = subQ.querySelector('h6');
        if (header) {
            header.textContent = `Sub-Question ${index + 1}`;
        }
    });
}

function removeSubQuestion(subQuestionId) {
    const subQuestion = document.getElementById(subQuestionId);
    if (subQuestion) {
        // Show confirmation dialog
        Swal.fire({
            title: 'Remove Sub-Question?',
            text: 'Are you sure you want to remove this sub-question? This action cannot be undone.',
            icon: 'warning',
            showCancelButton: true,
            confirmButtonColor: '#d33',
            cancelButtonColor: '#3085d6',
            confirmButtonText: 'Yes, remove it!',
            cancelButtonText: 'Cancel'
        }).then((result) => {
            if (result.isConfirmed) {
                subQuestion.remove();
                updateSubQuestionNumbers();

                // Show success message
                Swal.fire({
                    title: 'Removed!',
                    text: 'Sub-question has been removed.',
                    icon: 'success',
                    timer: 1500,
                    showConfirmButton: false
                });
            }
        });
    }
}

document.addEventListener('DOMContentLoaded', function () {
    showStep(currentStep);
    updateAnswerSection();
    document.getElementById('question_type').addEventListener('change', updateAnswerSection);

    // Load draft data if available (PHP to JS)
    <?php if (isset($draft)) : ?>
        if (<?= json_encode($draft->question_text ? $draft->question_text : null) ?>) document.querySelector('[name=question_text]').value = <?= json_encode($draft->question_text) ?>;
        if (<?= json_encode($draft->question_type ? $draft->question_type : null) ?>) document.querySelector('[name=question_type]').value = <?= json_encode($draft->question_type) ?>;
        if (<?= json_encode($draft->difficulty_level ? $draft->difficulty_level : null) ?>) document.querySelector('[name=difficulty_level]').value = <?= json_encode($draft->difficulty_level) ?>;
        if (<?= json_encode($draft->max_marks ? $draft->max_marks : null) ?>) document.querySelector('[name=max_marks]').value = <?= json_encode($draft->max_marks) ?>;
        // ...repeat for other fields as needed...
    <?php endif; ?>

    // Populate dummy class checkboxes (replace with API in real case)
    // const reuseBox = document.getElementById('classReuseCheckboxes');
    // ['Class 6', 'Class 7', 'Class 8', 'Class 9', 'Class 10', 'Class 11', 'Class 12'].forEach((cls, i) => {
    //     reuseBox.innerHTML += `
    //     <label><input type="checkbox" name="reusable_classes[]" value="${i + 6}"> ${cls}</label>
    //     `;
    // });

    // Add click and keyboard event to step labels for tab navigation
    document.querySelectorAll('.step-label').forEach(function(label) {
        label.addEventListener('click', function() {
            var step = parseInt(this.getAttribute('data-step'));
            if (step < currentStep) {
                navigateToStep(step);
            }
        });

        // Keyboard navigation support
        label.addEventListener('keydown', function(e) {
            var step = parseInt(this.getAttribute('data-step'));

            switch(e.key) {
                case 'Enter':
                case ' ':
                    e.preventDefault();
                    if (step < currentStep) {
                        navigateToStep(step);
                    }
                    break;
                case 'ArrowLeft':
                    e.preventDefault();
                    navigateStepLabels(-1);
                    break;
                case 'ArrowRight':
                    e.preventDefault();
                    navigateStepLabels(1);
                    break;
                case 'Home':
                    e.preventDefault();
                    focusStepLabel(1);
                    break;
                case 'End':
                    e.preventDefault();
                    focusStepLabel(totalSteps);
                    break;
            }
        });
    });

    // Handle form submission for Save as Draft or Submit for Approval
    document.getElementById('questionForm').addEventListener('submit', function(e) {
        e.preventDefault();

        var form = this;
        var submitBtn = document.activeElement;
        var action = 'draft'; // default action

        // Get the action from the clicked button
        if (submitBtn && submitBtn.type === 'submit') {
            action = submitBtn.value || 'draft';
        }

        console.log('Form submission triggered with action:', action);

        // Get original button text for restoration
        var originalText = submitBtn.innerHTML;

        // Force collection of all form data FIRST and wait for DOM updates
        forceCollectAllFormData(form);

        // Wait a moment for DOM updates, then proceed
        setTimeout(() => {
            // Temporarily disable required attributes on hidden fields to prevent HTML5 validation errors
            var hiddenRequiredFields = disableHiddenRequiredFields(form);

            // Validate the form manually AFTER data collection
            if (!validateFormForSubmission(form)) {
                // Re-enable required attributes
                restoreRequiredFields(hiddenRequiredFields);
                return;
            }

            // Continue with form submission
            continueFormSubmission(form, action, submitBtn, originalText, hiddenRequiredFields);
        }, 300);
    });

    function continueFormSubmission(form, action, submitBtn, originalText, hiddenRequiredFields) {

        // Debug: Check key field values before creating FormData
        var questionTextField = form.querySelector('[name="question_text"]');
        var questionTypeField = form.querySelector('[name="question_type"]');
        var classField = form.querySelector('[name="class_id"]');
        var subjectField = form.querySelector('[name="subject_master_id"]');

        console.log('Field values before FormData creation:');
        console.log('question_text:', questionTextField ? questionTextField.value : 'FIELD NOT FOUND');
        console.log('question_text field visible?:', questionTextField ? isFieldVisible(questionTextField) : 'N/A');
        console.log('question_text field step:', questionTextField ? questionTextField.closest('.step')?.id : 'N/A');
        console.log('question_text field required?:', questionTextField ? questionTextField.hasAttribute('required') : 'N/A');
        console.log('question_text field disabled?:', questionTextField ? questionTextField.disabled : 'N/A');
        console.log('question_type:', questionTypeField ? questionTypeField.value : 'FIELD NOT FOUND');
        console.log('class_id:', classField ? classField.value : 'FIELD NOT FOUND');
        console.log('subject_master_id:', subjectField ? subjectField.value : 'FIELD NOT FOUND');

        // Test: Try to manually check if we can access the field from different steps
        var allQuestionTextFields = document.querySelectorAll('[name="question_text"]');
        console.log('Total question_text fields found:', allQuestionTextFields.length);
        allQuestionTextFields.forEach((field, index) => {
            console.log(`Field ${index}:`, field.value, 'in step:', field.closest('.step')?.id);
        });

        // Workaround: Ensure question_text is not empty
        if (questionTextField && !questionTextField.value) {
            console.warn('question_text field is empty, checking if we can recover it...');
            // Try to get the value from localStorage or other steps
            var savedQuestionText = localStorage.getItem('temp_question_text');
            if (savedQuestionText) {
                questionTextField.value = savedQuestionText;
                console.log('Restored question_text from localStorage:', savedQuestionText);
            }
        }

        // Ensure all required fields from hidden steps are included
        ensureHiddenFieldsIncluded(form);

        // Prepare form data with proper handling for complex question types
        var formData = prepareFormData(form, action);

        // Debug: Log form data contents
        console.log('Form data being sent:');
        for (var pair of formData.entries()) {
            console.log(pair[0] + ': ' + pair[1]);
        }
        // return;
        // Show loading state
        var originalText = submitBtn.innerHTML;
        submitBtn.disabled = true;
        submitBtn.classList.add('btn-loading');
        submitBtn.innerHTML = '<i class="fa fa-spinner fa-spin"></i> ' + (action === 'submit' ? 'Submitting...' : 'Saving...');

        // Disable all form buttons
        form.querySelectorAll('button').forEach(btn => btn.disabled = true);
        
        $.ajax({
            url: '<?= site_url('academics/question_bank/save'); ?>',
            type: 'POST',
            data: formData,
            processData: false,
            contentType: false,
            timeout: 30000, // 30 second timeout
            success: function(response) {
                // Re-enable required attributes
                restoreRequiredFields(hiddenRequiredFields);

                try {
                    // Parse response if it's a string
                    var result = typeof response === 'string' ? JSON.parse(response) : response;

                    if (result.success || result.status === 'success') {
                        // Clear saved form data since submission was successful
                        if (typeof clearSavedFormData === 'function') {
                            clearSavedFormData();
                        }

                        Swal.fire({
                            title: 'Success!',
                            text: 'Question ' + (action === 'submit' ? 'submitted for approval!' : 'saved as draft!'),
                            icon: 'success',
                            showCancelButton: true,
                            confirmButtonText: 'Continue adding question',
                            cancelButtonText: 'Go to Question Bank',
                        }).then((result) => {
                            if (result.isConfirmed) {
                                window.location.reload();
                            } else if (result.dismiss === Swal.DismissReason.cancel) {
                                window.location.href = '<?= site_url('academics/question_bank/index'); ?>';
                            }
                        });
                    } else {
                        throw new Error(result.message || 'Unknown error occurred');
                    }
                } catch(e) {
                    console.error('Error parsing response:', e);
                    showFormError(e.message);
                }
            },
            error: function(xhr, status, error) {
                // Re-enable required attributes
                restoreRequiredFields(hiddenRequiredFields);

                console.error('Form submission error:', {xhr, status, error});

                var errorMessage = 'There was an error saving the question. Please try again.';

                if (status === 'timeout') {
                    errorMessage = 'Request timed out. Please check your connection and try again.';
                } else if (xhr.status === 413) {
                    errorMessage = 'File size too large. Please reduce file sizes and try again.';
                } else if (xhr.status === 422) {
                    errorMessage = 'Validation error. Please check your input and try again.';
                } else if (xhr.status >= 500) {
                    errorMessage = 'Server error. Please try again later or contact support.';
                }

                showFormError(errorMessage);
            },
            complete: function() {
                // Restore button states
                submitBtn.disabled = false;
                submitBtn.classList.remove('btn-loading');
                submitBtn.innerHTML = originalText;
                form.querySelectorAll('button').forEach(btn => btn.disabled = false);
            }
        });
    }
})

function getSubjects() {
    var class_master_id = $('#class_id').val();
    if (!class_master_id) {
        resetDependentFields('class');
        return;
    }

    // Show loading state
    $('#subject_master_id').html('<option value="">Loading subjects...</option>');

    // DON'T reset form data when loading subjects
    // resetDependentFields('class');

    $.ajax({
        url: '<?php echo site_url('academics/Question_bank/get_subjects_list'); ?>',
        type: 'post',
        data: {'class_master_id':class_master_id},
        timeout: 10000, // 10 second timeout
        success: function(data) {
            try {
                var subjects = $.parseJSON(data);
                var output = '<option value="">Select Subject</option>';

                if(subjects.length == 0) {
                    output = '<option value="" disabled selected>No Subjects Found</option>';
                } else {
                    for (var i=0; i<subjects.length; i++) {
                        output += '<option value="' + subjects[i].subject_master_id + '">' + subjects[i].subject_name + '</option>';
                    }
                }

                $('#subject_master_id').html(output);
            } catch(e) {
                console.error('Error parsing subjects data:', e);
                showAjaxError('subjects', 'Error parsing subjects data');
            }
        },
        error: function(xhr, status, error) {
            console.error('Error fetching subjects:', {xhr, status, error});
            showAjaxError('subjects', 'Failed to load subjects. Please try again.');
        }
    });
}

function getLessons() {
    var class_master_id = $('#class_id').val();
    if (!class_master_id) {
        resetDependentFields('class');
        return;
    }
    var subject_master_id = $('#subject_master_id').val();
    if (!subject_master_id) {
        resetDependentFields('subject');
        return;
    }

    // Show loading state
    $('#lesson_id, #lesson_id_step4').html('<option value="">Loading lessons...</option>');

    // DON'T reset form data when loading lessons
    // resetDependentFields('subject');

    $.ajax({
        url: '<?php echo site_url('academics/Question_bank/get_subject_lessons'); ?>',
        type: 'post',
        data: {'subject_master_id':subject_master_id, 'class_master_id': class_master_id},
        success: function(data) {
            try {
                var lessons = $.parseJSON(data);
                var output = '<option value="">Select Lesson</option>';

                if(lessons.length == 0) {
                    output = '<option value="" disabled selected>No Lessons Found</option>';
                } else {
                    for (var i=0; i<lessons.length; i++) {
                        output += '<option value="' + lessons[i].id + '">' + lessons[i].lesson_name + '</option>';
                    }
                }

                $('#lesson_id, #lesson_id_step4').html(output);
            } catch(e) {
                console.error('Error parsing lessons data:', e);
                $('#lesson_id, #lesson_id_step4').html('<option value="">Error loading lessons</option>');
            }
        },
        error: function(xhr, status, error) {
            console.error('Error fetching lessons:', error);
            $('#lesson_id, #lesson_id_step4').html('<option value="">Error loading lessons</option>');
        }
    });
}

function assignTopicName(){
    console.log($('#topic_id option:selected').text());
    $('#topic_name').val($('#topic_id option:selected').text());
}

function getTopics() {
    var lesson_id = $('#lesson_id_step4').val() || $('#lesson_id').val();
    if (!lesson_id) {
        resetDependentFields('lesson');
        return;
    }
    console.log($('#lesson_id_step4 option:selected').text());
    $('#lesson_name').val($('#lesson_id_step4 option:selected').text());

    // Show loading state
    $('#topic_id').html('<option value="">Loading topics...</option>');

    // DON'T reset form data when loading topics
    // resetDependentFields('lesson');

    $.ajax({
        url: '<?php echo site_url('academics/Question_bank/get_lesson_topics'); ?>',
        type: 'post',
        data: {'lessonId':lesson_id},
        success: function(data) {
            try {
                var topics = $.parseJSON(data);
                var output = '<option value="">Select Topic</option>';

                if(topics.length == 0) {
                    output = '<option value="" disabled selected>No Topics Found</option>';
                } else {
                    for (var i=0; i<topics.length; i++) {
                        output += '<option value="' + topics[i].id + '">' + topics[i].sub_topic_name + '</option>';
                    }
                }

                $('#topic_id').html(output);
            } catch(e) {
                console.error('Error parsing topics data:', e);
                $('#topic_id').html('<option value="">Error loading topics</option>');
            }
        },
        error: function(xhr, status, error) {
            console.error('Error fetching topics:', error);
            $('#topic_id').html('<option value="">Error loading topics</option>');
        }
    });
}

// Global variable to store duplicate question info
let duplicateQuestionId = null;

function checkForDuplicates() {
    const questionText = $('#question_text').val();
    const questionType = $('#question_type').val();
    const classId = $('#class_id').val();
    const subject_master_id = $('#subject_master_id').val();

    // Only check if we have minimum required data
    if (!questionText || questionText.length < 10 || !questionType || !classId || !subject_master_id) {
        $('#duplicateAlert').hide();
        return;
    }

    // Debounce the check to avoid too many requests
    clearTimeout(window.duplicateCheckTimeout);
    window.duplicateCheckTimeout = setTimeout(function() {
        performDuplicateCheck(questionText, questionType, classId, subject_master_id);
    }, 1000);
}

function performDuplicateCheck(questionText, questionType, classId, subject_master_id) {
    $.ajax({
        url: '<?php echo site_url('academics/question_bank/check_duplicate'); ?>',
        type: 'POST',
        data: {
            question_text: questionText,
            question_type: questionType,
            class_master_id: classId,
            subject_master_id: subject_master_id
        },
        success: function(response) {
            try {
                const data = JSON.parse(response);

                if (data.exists) {
                    duplicateQuestionId = data.question_id;
                    $('#duplicateMessage').html(`
                        A similar ${data.similarity} question already exists:<br>
                        <em>"${data.question_text.substring(0, 100)}${data.question_text.length > 100 ? '...' : ''}"</em>
                    `);
                    $('#duplicateAlert').show();
                } else {
                    $('#duplicateAlert').hide();
                    duplicateQuestionId = null;
                }
            } catch(e) {
                console.error('Error parsing duplicate check response:', e);
            }
        },
        error: function() {
            console.error('Error checking for duplicates');
        }
    });
}

function viewDuplicateQuestion() {
    if (duplicateQuestionId) {
        window.open('<?php echo site_url('academics/question_bank/view/'); ?>' + duplicateQuestionId, '_blank');
    }
}

// Debug function to check match data (can be called from browser console)
function debugMatchData() {
    console.log('=== MATCH DATA DEBUG ===');

    const matchSection = document.getElementById('matchSection');
    console.log('Match section:', matchSection);
    console.log('Match section display:', matchSection ? matchSection.style.display : 'N/A');

    const matchList = document.getElementById('matchList');
    console.log('Match list:', matchList);
    console.log('Match list children:', matchList ? matchList.children.length : 'N/A');

    const lefts = document.querySelectorAll('input[name="match_left[]"]');
    const rights = document.querySelectorAll('input[name="match_right[]"]');

    console.log('Left inputs found:', lefts.length);
    console.log('Right inputs found:', rights.length);

    lefts.forEach((left, index) => {
        const right = rights[index];
        console.log(`Pair ${index}:`, {
            left: left ? left.value : 'NOT FOUND',
            right: right ? right.value : 'NOT FOUND'
        });
    });

    console.log('=== END DEBUG ===');
}

// Make it globally available
window.debugMatchData = debugMatchData;

// Debug function for MCQ data (call from console)
function debugMCQData() {
    console.log('=== MCQ DATA DEBUG ===');

    const questionType = document.getElementById('question_type').value;
    console.log('Current question type:', questionType);

    const mcqSection = document.getElementById('mcqAnswerSection');
    console.log('MCQ section found:', mcqSection ? 'Yes' : 'No');
    console.log('MCQ section display:', mcqSection ? mcqSection.style.display : 'N/A');
    console.log('MCQ section visible:', mcqSection ? (mcqSection.offsetParent !== null) : 'N/A');

    const mcqOptions = document.getElementById('mcqOptions');
    console.log('MCQ options container found:', mcqOptions ? 'Yes' : 'No');
    console.log('MCQ options container children:', mcqOptions ? mcqOptions.children.length : 'N/A');

    if (mcqOptions) {
        console.log('MCQ options container HTML:', mcqOptions.innerHTML.substring(0, 200) + '...');
    }

    const options = document.querySelectorAll('input[name="option_text[]"]');
    const corrects = document.querySelectorAll('input[name="is_correct[]"]');

    console.log('Options found by querySelectorAll:', options.length);
    console.log('Corrects found by querySelectorAll:', corrects.length);

    options.forEach((option, index) => {
        const correct = corrects[index];
        console.log(`Option ${index}:`, {
            element: option,
            value: option.value,
            visible: option.offsetParent !== null,
            parent: option.parentElement,
            correct: correct ? correct.checked : 'No correct element'
        });
    });

    console.log('=== END MCQ DEBUG ===');
}

// Make it globally available
window.debugMCQData = debugMCQData;

// Debug function for comprehension data
function debugComprehensionData() {
    console.log('=== COMPREHENSION DATA DEBUG ===');

    const comprehensionSection = document.getElementById('comprehensionSection');
    console.log('Comprehension section:', comprehensionSection);
    console.log('Comprehension section display:', comprehensionSection ? comprehensionSection.style.display : 'N/A');

    const passage = document.querySelector('textarea[name="passage_text"]');
    console.log('Passage element:', passage);
    console.log('Passage value:', passage ? `"${passage.value}"` : 'N/A');

    const comprehensionQuestions = document.getElementById('comprehensionQuestions');
    console.log('Comprehension questions container:', comprehensionQuestions);
    console.log('Container children:', comprehensionQuestions ? comprehensionQuestions.children.length : 'N/A');

    const subQuestions = document.querySelectorAll('.sub-question');
    console.log('Sub-questions found:', subQuestions.length);

    subQuestions.forEach((subQ, index) => {
        const questionText = subQ.querySelector('textarea[name="comprehension_question[]"]');
        const questionType = subQ.querySelector('select[name="comprehension_question_type[]"]');

        console.log(`Sub-question ${index}:`, {
            id: subQ.id,
            questionText: questionText ? questionText.value : 'NOT FOUND',
            questionType: questionType ? questionType.value : 'NOT FOUND'
        });
    });

    console.log('=== END DEBUG ===');
}

// Make it globally available
window.debugComprehensionData = debugComprehensionData;

// Simplified force collection function
function forceCollectAllFormData(form) {
    console.log('Force collecting form data...');

    const questionType = document.getElementById('question_type').value;
    console.log('Question type:', questionType);

    // For MCQ questions, ensure the section is visible
    if (questionType === 'MCQ') {
        const mcqSection = document.getElementById('mcqAnswerSection');
        if (mcqSection && mcqSection.style.display === 'none') {
            console.log('Making MCQ section visible for data collection');
            mcqSection.style.display = 'block';
        }
    }
}

// Removed comprehensive validation function - using existing validateFormForSubmission instead

// Removed individual validation functions - using existing form validation instead

// Helper function to ensure hidden fields are included in form submission
function ensureHiddenFieldsIncluded(form) {
    console.log('Ensuring hidden fields are included...');

    // Check if question_text is empty and try to populate it
    var questionTextField = form.querySelector('[name="question_text"]');
    if (questionTextField && !questionTextField.value) {
        var savedValue = localStorage.getItem('temp_question_text');
        if (savedValue) {
            questionTextField.value = savedValue;
            console.log('Restored question_text from localStorage');
        }
    }

    // Ensure MCQ options are properly set if this is an MCQ question
    var questionType = form.querySelector('[name="question_type"]');
    if (questionType && questionType.value === 'MCQ') {
        var options = form.querySelectorAll('input[name="option_text[]"]');
        var corrects = form.querySelectorAll('input[name="is_correct[]"]');

        console.log('MCQ validation - Options found:', options.length);
        console.log('MCQ validation - Corrects found:', corrects.length);

        var hasValidOptions = false;
        options.forEach((option, index) => {
            if (option.value && option.value.trim() !== '') {
                hasValidOptions = true;
                console.log(`Option ${index}: "${option.value}" - Correct: ${corrects[index] ? corrects[index].checked : false}`);
            }
        });

        if (!hasValidOptions) {
            console.warn('No valid MCQ options found!');
        }
    }
}

// Helper functions for form submission
function disableHiddenRequiredFields(form) {
    var hiddenRequiredFields = [];

    console.log('Disabling hidden required fields...');

    form.querySelectorAll('[required]').forEach(function(field) {
        var fieldStep = field.closest('.step');
        // Use the global currentStep variable

        // Only disable required for fields in answer sections that don't match current question type
        // Don't disable required for fields in previous steps (they should always be valid)
        var isAnswerField = field.closest('.answer-type-section');
        var shouldDisable = false;

        if (isAnswerField) {
            var isVisible = isFieldVisible(field);
            // console.log('Answer field:', field.name || field.id, 'Visible:', isVisible, 'Type:', field.type);

            if (!isVisible) {
                shouldDisable = true;
            }
        }

        if (shouldDisable) {
            hiddenRequiredFields.push({
                element: field,
                wasRequired: true
            });
            field.removeAttribute('required');
            // console.log('Removed required from:', field.name || field.id);
        }
    });

    // console.log('Disabled', hiddenRequiredFields.length, 'hidden required fields');
    return hiddenRequiredFields;
}

function restoreRequiredFields(hiddenRequiredFields) {
    hiddenRequiredFields.forEach(function(fieldInfo) {
        if (fieldInfo.wasRequired) {
            fieldInfo.element.setAttribute('required', 'required');
        }
    });
}

function validateFormForSubmission(form) {
    // Validate all visible steps
    var allValid = true;

    // Check each step
    for (var step = 1; step <= 6; step++) {
        var stepDiv = document.getElementById('step-' + step);
        if (!stepDiv) continue;

        var stepValid = true;
        stepDiv.querySelectorAll('[required]').forEach(function(field) {
            if (isFieldVisible(field)) {
                if (field.type === 'radio') {
                    var group = stepDiv.querySelectorAll('[name="' + field.name + '"]');
                    var checked = Array.from(group).some(function(input) { return input.checked; });
                    if (!checked && field === group[0]) {
                        stepValid = false;
                        allValid = false;
                    }
                } else if (!field.value || field.value === '') {
                    stepValid = false;
                    allValid = false;
                }
            }
        });

        if (!stepValid && step === 3) {
            // Special validation for answer step
            const validationResult = validateAnswerStep();
            if (!validationResult.isValid) {
                allValid = false;
            }
        }
    }

    if (!allValid) {
        Swal.fire({
            icon: 'warning',
            title: 'Validation Error',
            text: 'Please complete all required fields before submitting.',
            confirmButtonText: 'OK'
        });
    }

    return allValid;
}

// Add event listeners when document is ready
$(document).ready(function() {
    // Add event listeners for duplicate checking
    $('#class_id, #subject_master_id').on('change', function() {
        checkForDuplicates();
    });

    // Check for duplicates when typing in question text (with debounce)
    $('#question_text').on('input', function() {
        checkForDuplicates();
        // Save to localStorage as backup
        localStorage.setItem('temp_question_text', $(this).val());
    });

    // Initial cleanup of required attributes on hidden fields
    setTimeout(function() {
        var form = document.getElementById('questionForm');
        if (form) {
            disableHiddenRequiredFields(form);
        }
    }, 100);
});

// Removed complex form persistence functions to fix syntax errors

// Removed auto-save functionality that was causing form resets

// Simple test function to manually check MCQ data
function testMCQCollection() {
    console.log('=== MANUAL MCQ TEST ===');

    // Force MCQ section to be visible
    const mcqSection = document.getElementById('mcqAnswerSection');
    if (mcqSection) {
        mcqSection.style.display = 'block';
        console.log('Made MCQ section visible');
    }

    // Wait a moment, then check
    setTimeout(() => {
        const options = document.querySelectorAll('input[name="option_text[]"]');
        const corrects = document.querySelectorAll('input[name="is_correct[]"]');

        console.log('Options found:', options.length);
        console.log('Corrects found:', corrects.length);

        options.forEach((option, index) => {
            console.log(`Option ${index}: "${option.value}" (checked: ${corrects[index] ? corrects[index].checked : 'N/A'})`);
        });

        // Test the actual prepareMCQData function
        const testFormData = new FormData();
        prepareMCQData(testFormData);

        console.log('=== END MANUAL TEST ===');
    }, 100);
}

// Complete DOM inspection function
function inspectDOM() {
    console.log('=== COMPLETE DOM INSPECTION ===');

    // Check question type
    const questionType = document.getElementById('question_type');
    console.log('Question type element:', questionType);
    console.log('Question type value:', questionType ? questionType.value : 'N/A');

    // Check MCQ section
    const mcqSection = document.getElementById('mcqAnswerSection');
    console.log('MCQ section:', mcqSection);
    console.log('MCQ section display:', mcqSection ? mcqSection.style.display : 'N/A');

    // Check MCQ options container
    const mcqOptions = document.getElementById('mcqOptions');
    console.log('MCQ options container:', mcqOptions);
    if (mcqOptions) {
        console.log('MCQ options HTML:', mcqOptions.innerHTML);
        console.log('MCQ options children count:', mcqOptions.children.length);
    }

    // Check all inputs in the page
    const allInputs = document.querySelectorAll('input');
    console.log('Total inputs on page:', allInputs.length);

    // Check for any inputs with "option" in the name
    const optionInputs = document.querySelectorAll('input[name*="option"]');
    console.log('Inputs with "option" in name:', optionInputs.length);
    optionInputs.forEach((input, index) => {
        console.log(`Option input ${index}:`, input.name, input.value);
    });

    // Check for any text inputs in MCQ section
    if (mcqSection) {
        const textInputsInMCQ = mcqSection.querySelectorAll('input[type="text"]');
        const checkboxesInMCQ = mcqSection.querySelectorAll('input[type="checkbox"]');
        console.log('Text inputs in MCQ section:', textInputsInMCQ.length);
        console.log('Checkboxes in MCQ section:', checkboxesInMCQ.length);

        textInputsInMCQ.forEach((input, index) => {
            console.log(`MCQ text input ${index}:`, {
                name: input.name,
                value: input.value,
                placeholder: input.placeholder
            });
        });
    }

    console.log('=== END DOM INSPECTION ===');
}

// Make it globally available for console testing
window.testMCQCollection = testMCQCollection;
window.debugMCQData = debugMCQData;
window.inspectDOM = inspectDOM;

</script>