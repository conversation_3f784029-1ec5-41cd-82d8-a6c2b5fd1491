<ul class="breadcrumb">
    <li><a href="<?php echo site_url('dashboard');?>">Dashboard</a></li>
    <li><a href="<?php echo site_url('communication_dashboard');?>">Communication</a></li>
    <li>Flash News</li>
</ul>

<hr>
<div class="col-md-12">
    <div class="card cd_border">
        <div class="card-header panel_heading_new_style_staff_border">
            <div class="row" style="margin: 0px;">
                <div class="d-flex justify-content-between" style="width:100%;">
                    <h3 class="card-title panel_title_new_style_staff">
                    <a class="back_anchor" href="<?php echo site_url('communication_dashboard'); ?>">
                        <span class="fa fa-arrow-left"></span>
                    </a> 
                    Flash News
                    </h3>
                    <div>
                        <a data-toggle="tooltip" data-original-title="New Flash News" href="<?php echo site_url('flash/flash')?>" class="new_circleShape_res" style="background-color: #fe970a;">
                            <span class="fa fa-plus" style="font-size: 19px;"></span>
                        </a>
                    </div>
                </div>
            </div>
        </div>        
        <div class="card-body" style="overflow:auto;">
            <?php if(empty($flash_news)) {
                echo "<h4 class='no-data-display'>No Flash News Published.</h4>";
            } else { ?>
            <table id="flash_details_table" class="table table-bordered">
                <thead>
                    <tr>
                        <th style="min-width:50px">#</th>
                        <th style="min-width:125px">Start Date</th>
                        <th style="min-width:125px">End Date</th>
                        <th stle="min-width:200px">Flash News</th>         
                        <th style="min-width:125px">Published to</th>
                        <th stle="min-width:125px">Published By</th>
                        <th style="min-width:125px">Published on</th>
                        <th style="min-width:100px">Status</th>
                        <th style="min-width:200px">Operations</th>
                    </tr>
                </thead>
                <tbody>
                <?php
                    $i=1;
                    foreach ($flash_news as $flash_data) {  ?>
                    <tr>
                        <td><?= $i++;?></td>
                        <td><?php echo date('d-m-Y',strtotime($flash_data->start_date)); ?></td>
                        <td><?php echo date('d-m-Y',strtotime($flash_data->end_date)); ?></td>
                        <td><?= $flash_data->flash_content ?></td>
                        <?php 
                        $send_to_str = "";
                        if($flash_data->user_type == 2) {
                            if(!empty($flash_data->send_to) && $flash_data->send_to != 'null'){
                                $send_to_data = json_decode($flash_data->send_to);
                                if(is_array($send_to_data)){
                                    foreach ($send_to_data as $value) {
                                        if(isset($value->name)){
                                            $send_to_str .= $value->name . ", " ;
                                        } else {
                                            $send_to_str .= 'N/A' . ", " ;
                                        }
                                    }
                                } else {
                                    $send_to_str = 'N/A';
                                }
                            } else {
                                $send_to_str = 'N/A';
                            }
                        } else if($flash_data->user_type == 1) {
                            $send_to_str = "Staff All";
                        } else if($flash_data->user_type == 0) {
                            $send_to_str = "Student All";   
                        } else if($flash_data->user_type == 3) {
                            $send_to_str = "All Staff and Students";   
                        }
                        ?>

                        <td><?= $send_to_str ?></td>
                        <td><?= $flash_data->published_by ?></td>
                        <td><?php echo local_time($flash_data->published_on, 'd-M h:i a'); ?></td>
                        <?php 
                            $status = "TBD";
                            if($flash_data->status == 0)
                            { 
                                $status = "Published";
                            }
                            else if($flash_data->status == 1)
                            {
                                $status = "Not Published";
                            }
                            else if($flash_data->status == 2)
                            {
                                $status = "Draft";
                            }
                        ?>
                        <td><?= $status ?></td> 
                        <!-- <td><?= "TBD" ?></td> -->
                        <td>

                        <a  href="<?php echo site_url('flash/flash_edit/'.$flash_data->id);?>" class='btn btn-warning ' data-placement='top' data-toggle='tooltip' data-original-title='Edit'><i class='fa fa-edit'></i></a>
                        <a href="<?php echo site_url('flash/flash_delete/'.$flash_data->id);?>" class='btn btn-danger ' data-placement='top' data-toggle='tooltip' data-original-title='Delete'><i class='fa fa-trash-o'></i></a> 
                        </td>
                    </tr>
                <?php  } ?>
                </tbody>
            </table> 
            <?php } ?>
        </div>
    </div>
</div>
<style type="text/css">
    .publish{
        cursor: pointer;
    }
      ul.panel-controls>li>a {
    border-radius: 50%;
}
.btn{
  margin-bottom: 4px;
}
.new_circleShape_res {
    padding: 8px;
    border-radius: 50% !important;
    color: white !important;
    font-size: 22px;
    height: 3.2rem !important;
    width: 3.2rem !important;
    text-align: center;
    vertical-align: middle;
    float: left;
    border: none !important;
    box-shadow: 0px 3px 7px #ccc;
    line-height: 1.7rem !important;
}
th, td {
            min-width: 100px;
        }
.dataTables_wrapper .dt-buttons {
    float: right;
}
.dataTables_filter input {
    background-color: #f2f2f2;
    border: 1px solid #ccc;
    border-radius: 4px;
    margin-right: 5px;
}
.dataTables_wrapper .dataTables_filter {
    float: right;
    text-align: left;
    width: unset;
}
</style>

<script type="text/javascript">
    $("#classSectionId").change(function(){
        $("#selectStd").submit();
    });
    function checkClass(){
        var val = $("#classSectionId").val();
        if(val == 0) {
            bootbox.alert({
              title: "Assessments",
              message: "Select the class to add a new assessment.",
              buttons: {
                  ok: {
                      label: 'Got it',
                      className: 'btn-success'
                  }
              }
            });
            return false;
        }
        var site = '<?php echo site_url("flash/showflashnews");?>';
        window.location = site;
    }

    function checkIsFirst(sub,assId,classId){
        if(sub == ''){
            bootbox.confirm({
              title: "Info",
              message: "Edit assessment will be locked once you add subjects. Do you want to continue?",
              buttons: {
                  confirm: {
                      label: 'Yes',
                      className: 'btn-success'
                  },
                  cancel: {
                      label: 'No',
                      className: 'btn-danger'
                  }
              },
              callback: function (result) {
                if(result) {
                  window.location='<?php echo site_url('examination/assessments/addSubjects/') ?>'+assId+'/'+classId;
                }
              }
            });
        } else {
            window.location='<?php echo site_url('examination/assessments/addSubjects/') ?>'+assId+'/'+classId;
        }
    }

    $(document).ready(function(){
		$('#flash_details_table').DataTable({
			"language": {
				"search": "",
				"searchPlaceholder": "Enter Search..."
			},
			"lengthMenu": [ [10, 25, 50, -1], [10, 25, 50, "All"] ],
            "pageLength": 10,
			dom: 'lBfrtip',
			buttons: [
				{
					extend: 'excelHtml5',
					text: 'Excel',
					filename: 'daily_tracking',
					className: 'btn btn-info'
				},
				
				{
					extend: 'print',
					text: 'Print',
					filename: 'daily_tracking',
					className: 'btn btn-info'
				}
			]
		});



        $(document).on('click', '#publishBtn', function(e){
            e.preventDefault();  
            var uid = $(this).data('id'); // get id of clicked row
            var arr = uid.split("_");
            $("#publishId").val(arr[0]+'_'+arr[2]);
            $('#dynamic-content').html(''); // leave this div blank
            $('#modal-loader').show();      // load ajax loader on button click
         
            $.ajax({
              url: "<?php echo site_url('examination/assessments/publishAssessment');?>",
              data: {'assId': arr[0], 'classId': arr[1]},
              type: 'post',
            }).done(function(data){
                    var data = JSON.parse(data);
                    var empty = data.empty;
                    var data = data.html;
                    $('#dynamic-content').html(''); // blank before load.
                   $('#dynamic-content').html(data); // load here
                   $('#modal-loader').hide(); // hide loader
                   if(empty == 1) $("#confirmP").hide();
             })
             .fail(function(){
                  $('#dynamic-content').html('Something went wrong, Please try again...');
                  $('#modal-loader').hide();
             });
        });
    });

    function publish(){
        var ids = $("#publishId").val();
        var idStat = ids.split("_");
        $.ajax({
            url: "<?php echo site_url('examination/assessments/changePublishStatus');?>",
            data: {'id':idStat[0], 'status':idStat[1]},
            type: 'post',
            success: function(data) {
                if(data == -1){
                    bootbox.alert({
                        title: "Publishing Assessment",
                        message: "Date or Portions not added to one or more subjects. Please add before continuing.",
                        buttons: {
                            ok: {
                                label: 'Got it',
                                className: 'btn-success'
                            }
                        }
                    });
                } else if(data){
                    location.reload();
                }
            },
            error: function(err) {
                console.log(err);
            }
        }); 
    }

    function releaseMarks(changeTo, assId) {
        $msg = "You cannot make changes to subjects after releasing. Are you Sure?";
        if(changeTo == 0)
            $msg = "You are closing the marks entry. Are you Sure?";
        bootbox.confirm({
            title: 'Release Assessment Marks Entry',
          message: $msg,
          buttons: {
              confirm: {
                  label: 'Yes',
                  className: 'btn-success'
              },
              cancel: {
                  label: 'No',
                  className: 'btn-danger'
              }
          },
          callback: function (result) {
            if(result) {
                $.ajax({
                    url: "<?php echo site_url('examination/assessments/changeMarksReleaseStatus');?>",
                    data: {'assId':assId, 'status':changeTo},
                    type: 'post',
                    success: function(data) {
                        location.reload();
                    },
                    error: function(err) {
                        console.log(err);
                    }
                });
            }
          }
        });
    }
</script>

<div id="view-modal" class="modal fade" tabindex="-1" role="dialog" aria-labelledby="myModalLabel" aria-hidden="true" style="display: none;">
  <div class="modal-dialog" style="width:70%;"> 
     <div class="modal-content">  
   
        <div class="modal-header"> 
           <button type="button" class="close" data-dismiss="modal" aria-hidden="true">×</button> 
           <h4 class="modal-title"> Assessment timetable </h4> 
        </div> 
            
        <div class="modal-body mobile" style="height:500px;overflow-y: scroll;">
           <div id="modal-loader" style="display: none; text-align: center;">
           <!-- ajax loader -->
           <img src="<?php echo base_url('assets/img/ajax-loader.gif');?>">
           </div>
           <div id="dynamic-content"></div>
        </div> 
        <div class="modal-footer">
            <input type="hidden" name="publishId" id="publishId">
            <button type="button" onclick="publish()" id="confirmP" class="btn btn-secondary" data-dismiss="modal">Confirm</button>  
            <button type="button" class="btn btn-secondary" data-dismiss="modal">Close</button>  
        </div> 
                        
    </div> 
  </div>
</div>