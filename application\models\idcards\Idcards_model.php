<?php
	class Idcards_model extends CI_Model{

		private $yearId;
	    public function __construct() {
	        parent::__construct();
			$this->load->library('filemanager');
	        $this->yearId = $this->acad_year->getAcadYearId();

	    }

		public function Kolkata_datetime(){
			$timezone = new DateTimeZone("Asia/Kolkata" );
			$date = new DateTime();
			$date->setTimezone($timezone );
			$dtobj = $date->format('Y-m-d H:i:s');
			return $dtobj;
		}

		private function _getAvatarNameById($avatarId) {
			$collected = $this->db_readonly->select('CONCAT(ifnull(sm.first_name," "), " ", ifnull(sm.last_name," ")) as staffName')
				->from('staff_master sm')
				->where('sm.id',$avatarId)
				->get()->row();
			if (!empty($collected)) {
			  return $collected->staffName;
			}else{
			  return 'Admin';
			}
		}

		public function get_all_templates() {
			$query = $this->db->get('idcard_template');
			return $query->result();
		}

		public function get_all_templates_selected_idcards_for($id_card_for = null) {
			// If id_card_for is not provided, try to get it from POST
			if ($id_card_for === null) {
				$id_card_for = $this->input->post('idCardFor');
			}
			// If id_card_for is provided, filter templates by type
			if (!empty($id_card_for)) {
				$this->db->where('id_card_for', $id_card_for);
			}

			$this->db->where('id_card_for', $id_card_for);
			$query = $this->db->get('idcard_template');
			return $query->result();
		}

		public function get_template($id) {
			$query = $this->db->get_where('idcard_template', array('id' => $id));
			return $query->row();
		}

		public function save_template($data) {
			$this->db->insert('idcard_template', $data);
			return $this->db->insert_id();
		}

		public function update_template($id, $data) {
			$this->db->where('id', $id);
			return $this->db->update('idcard_template', $data);
		}

		public function delete_template($id) {
			$this->db->where('id', $id);
			return $this->db->delete('idcard_template');
		}

		public function get_count_of_templates(){
			$this->db->select('count(*) as count');
			$this->db->from('idcard_template');
			return $this->db->get()->row()->count;
		}

		public function getApprovedStaff(){
			$sql="select id, ifnull(concat(first_name, ' ',ifnull(last_name,'')), '') as head from staff_master
			where status=2
			order by first_name;";
			$result = $this->db_readonly->query($sql)->result();
			return $result;
		}

		public function getDataOfProfiletypes(){
			$profile_type = $this->input->post('profile_type');

			$prefix_student_name = $this->settings->getSetting('prefix_student_name');
			if ($prefix_student_name == "roll_number") {
			  $std_name = "CONCAT(if(sy.roll_no = 0, 'NA', sy.roll_no), ' - ', ifnull(std.first_name,''),' ', ifnull(std.last_name,'')) as std_name";
			} else if ($prefix_student_name == "enrollment_number") {
			  $std_name = "CONCAT(ifnull(std.enrollment_number, 'NA'), ' - ', ifnull(std.first_name,''),' ', ifnull(std.last_name,'')) as std_name";
			} else if ($prefix_student_name == "admission_number") {
			  $std_name = "CONCAT(ifnull(std.admission_no, 'NA'), ' - ', ifnull(std.first_name,''),' ', ifnull(std.last_name,'')) as std_name";
			} else if ($prefix_student_name == "registration_no") {
			  $std_name = "CONCAT(ifnull(std.registration_no, 'NA'), ' - ', ifnull(std.first_name,''),' ', ifnull(std.last_name,'')) as std_name";
			} else if ($prefix_student_name == "alpha_rollnum") {
				$std_name = "CONCAT(ifnull(sy.alpha_rollnum, 'NA'), ' - ', ifnull(std.first_name,''),' ', ifnull(std.last_name,'')) as std_name";
			}else {
			  $std_name = "CONCAT(ifnull(std.first_name,''), ' ', ifnull(std.last_name,'')) AS std_name";
			}
			$prefix_order_by = $this->settings->getSetting('prefix_order_by');
			$order_by = 'std.first_name';
			if ($prefix_order_by == "roll_number") {
			  $order_by = 'sy.roll_no';
			}else if($prefix_order_by == "enrollment_number"){
			  $order_by = 'std.enrollment_number';
			}else if($prefix_order_by == "admission_number"){
			  $order_by = 'std.admission_no';
			}else if($prefix_order_by == "alpha_rollnum"){
			  $order_by = 'sy.alpha_rollnum';
			}

			if($profile_type == 'Student' || $profile_type == 'Parent'){
				$this->db_readonly->select("std.id, $std_name,concat(ifnull(c.class_name,''), ' ' ,ifnull(cs.section_name,'')) as class_section,cs.id as class_section_id, c.id as class_id,std.admission_no");
				$this->db_readonly->from('student_admission std');
				$this->db_readonly->join('student_year sy', "std.id=sy.student_admission_id AND sy.acad_year_id = " . $this->yearId);
				$this->db_readonly->join('class_section cs','sy.class_section_id=cs.id');
				$this->db_readonly->join('class c','sy.class_id=c.id');
				$this->db_readonly->where('admission_status','2');
				$this->db_readonly->where('sy.promotion_status!=', '4');
				$this->db_readonly->where('sy.promotion_status!=', '5');
				$this->db_readonly->order_by("$order_by");
				return $this->db_readonly->get()->result();
			}else if($profile_type == 'Staff'){
				$this->db_readonly->select('sm.id, concat(sm.first_name, " ",ifnull(sm.last_name,"")) as name, sdes.designation, sd.department, sm.employee_code, sm.blood_group');
				$this->db_readonly->from('staff_master sm');
				$this->db_readonly->join('staff_departments sd','sd.id=sm.department','left');
				$this->db_readonly->join('staff_designations sdes','sdes.id=sm.designation','left');
				$this->db_readonly->where('sm.status', '2');
				$this->db_readonly->order_by('first_name');
				$result =  $this->db_readonly->get()->result();
				return $result;
			}else if($profile_type == 'Parent'){
				return 0;
			}else{
				return 0;
			}

		return 0;

		}

		public function insertOrderPage1() {
			$input = $this->input->post();

			// Start DB transaction
			$this->db->trans_begin();

			$verifier_json = isset($input['verifier']) ? json_encode($input['verifier']) : null;
			$order_data = [
				'order_name'       => $input['orderName'],
				'id_card_verifier' => $verifier_json,
				'id_card_type'     => $input['idCardType'],
				'id_card_for'      => $input['idCardFor'],
				'id_card_for_type' => $input['selectOptions'],
				'description'      => $input['description'],
				'status'           => 'draft',
				'created_by'       => $this->authorization->getAvatarStakeHolderId(),
				'created_on'       => $this->Kolkata_datetime(),
			];

			$is_insert = (empty($input['order_id']) || $input['order_id'] == '0');

			if ($is_insert) {
				if (!$this->db->insert('idcard_template_orders', $order_data)) {
					$this->db->trans_rollback();
					return false;
				}
				$order_id = $this->db->insert_id();
			} else {
				$order_id = $input['order_id'];
				$this->db->where('id', $order_id);
				if (!$this->db->update('idcard_template_orders', $order_data)) {
					$this->db->trans_rollback();
					return false;
				}

				// Delete existing entities
				$this->db->where('idcard_template_order_id', $order_id);
				$this->db->delete('idcard_template_order_entities');
			}

			if($input['idCardType']=='Digital Card'){
				$this->db->where('idcard_template_order_id', $order_id);
				$this->db->delete('idcard_template_order_entities');
				$this->db->trans_commit();
				return $order_id;
			}

			// Add entities
			$entities_data = [];

			if ($input['selectOptions'] === 'all_staff') {
				// Get all staff IDs
				$this->db->select('id');
				$this->db->from('staff_master');
				$this->db->where('status', '2');
				$staff_ids = $this->db->get()->result();

				$entities_data = array_map(function ($staff) use ($order_id) {
					return [
						'idcard_template_order_id' => $order_id,
						'avatar_type'              => 'staff',
						'avatar_id'                => $staff->id,
						'status'                   => 'in review',
					];
				}, $staff_ids);
			} elseif ($input['selectOptions'] === 'all_students') {
				// Get all student IDs
				$this->db->select('std.id');
				$this->db->from('student_admission std');
				$this->db->join('student_year sy', "std.id = sy.student_admission_id AND sy.acad_year_id = " . $this->yearId);
				$this->db->where('admission_status', '2');
				$this->db->where("sy.promotion_status != 'JOINED'");
				$this->db->where_not_in('sy.promotion_status', ['4', '5']);
				$student_ids = $this->db->get()->result();
				$entities_data = array_map(function ($student) use ($order_id) {
					return [
						'idcard_template_order_id' => $order_id,
						'avatar_type'              => 'student',
						'avatar_id'                => $student->id,
						'status'                   => 'in review',
					];
				}, $student_ids);
			} elseif ($input['selectOptions'] === 'staff_subset') {
				// Handle selected staff IDs
				if (isset($input['selected_ids']) && !empty($input['selected_ids'])) {
					$selected_ids = json_decode($input['selected_ids'], true);

					if (!empty($selected_ids)) {
						foreach ($selected_ids as $staff_id) {
							$entities_data[] = [
								'idcard_template_order_id' => $order_id,
								'avatar_type'              => 'staff',
								'avatar_id'                => $staff_id,
								'status'                   => 'in review',
							];
						}
					}
				}
			} elseif ($input['selectOptions'] === 'student_subset') {
				// Handle selected student IDs
				if (isset($input['selected_ids']) && !empty($input['selected_ids'])) {
					$selected_ids = json_decode($input['selected_ids'], true);

					if (!empty($selected_ids)) {
						foreach ($selected_ids as $student_id) {
							$entities_data[] = [
								'idcard_template_order_id' => $order_id,
								'avatar_type'              => 'student',
								'avatar_id'                => $student_id,
								'status'                   => 'in review',
							];
						}
					}
				}
			}elseif ($input['selectOptions'] === 'parent_subset') {
				// Handle selected student IDs
				if (isset($input['selected_ids']) && !empty($input['selected_ids'])) {
					$selected_ids = json_decode($input['selected_ids'], true);

					if (!empty($selected_ids)) {
						foreach ($selected_ids as $student_id) {
							$entities_data[] = [
								'idcard_template_order_id' => $order_id,
								'avatar_type'              => $input['parentType'],
								'avatar_id'                => $student_id,
								'status'                   => 'in review',
							];
						}
					}
				}
			}elseif ($input['selectOptions'] === 'all_parents') {
				// Get all student IDs
				$this->db->select('std.id');
				$this->db->from('student_admission std');
				$this->db->join('student_year sy', "std.id = sy.student_admission_id AND sy.acad_year_id = " . $this->yearId);
				$this->db->where('admission_status', '2');
				$this->db->where("sy.promotion_status != 'JOINED'");
				$this->db->where_not_in('sy.promotion_status', ['4', '5']);
				$student_ids = $this->db->get()->result();
				$entities_data = array_map(function ($student) use ($order_id, $input) {
					return [
						'idcard_template_order_id' => $order_id,
						'avatar_type'              => $input['parentType'],
						'avatar_id'                => $student->id,
						'status'                   => 'in review',
					];
				}, $student_ids);
			} 

			if (!empty($entities_data)) {
				if (!$this->db->insert_batch('idcard_template_order_entities', $entities_data)) {
					$this->db->trans_rollback();
					return false;
				}
			}

			// Update quantity - only count entities with status "in review" or "approved"
			$this->db->select('COUNT(*) as count_of_entities');
			$this->db->from('idcard_template_order_entities');
			$this->db->where('idcard_template_order_id', $order_id);
			$this->db->where_in('status', array('in review', 'approved'));
			$quantity_idcards = $this->db->get()->row()->count_of_entities;

			if (!$this->db->update('idcard_template_orders', ['quantity' => $quantity_idcards], ['id' => $order_id])) {
				$this->db->trans_rollback();
				return false;
			}

			// Final check
			if ($this->db->trans_status() === false) {
				$this->db->trans_rollback();
				return false;
			}

			// Commit all changes
			$this->db->trans_commit();

			return $order_id;
		}


		public function insertTemplateId(){
			$input=$this->input->post();
			$data = array(
				'idcard_template_id' =>$input['selectedTemplateId'],
			);
			$this->db->where('id', $input['orderID']);
			$this->db->update('idcard_template_orders', $data);

			$this->db->select("ito.id,ito.id_card_for,ito.id_card_type,ito.created_by,date_format(ito.created_on,'%d-%b-%Y') as created_on, it.unit_price");
			$this->db->from('idcard_template_orders ito');
			$this->db->join('idcard_template it','ito.idcard_template_id = it.id');
			$this->db->where('ito.id', $input['orderID']);
			$orderData = $this->db->get()->row();
			$orderData->created_by = $this->_getAvatarNameById($orderData->created_by);

			$this->db->select('count(*) as count_of_entities');
			$this->db->from('idcard_template_order_entities');
			$this->db->where('idcard_template_order_id', $input['orderID']);
			$this->db->where_in('status', array('in review', 'approved'));
			$orderData->count_of_entities = $this->db->get()->row()->count_of_entities;

			return $orderData;
		}

		public function insertPlaceOrderDetails(){

			$receipt_book_idcard_order_id = $this->settings->getSetting('idcard_order_number');
			$idcard_order_id =  0;
			$input=$this->input->post();
			$this->db->trans_start();
			if(!empty($receipt_book_idcard_order_id)){
				$this->load->library('fee_library');
				$receipt_book = $this->db->select('*')->from('feev2_receipt_book')->where('id',$receipt_book_idcard_order_id)->get()->row();
				if (!empty($receipt_book)) {
				  $idcard_order_id =  $this->fee_library->receipt_format_get_update($receipt_book);
				}else{
				  $idcard_order_id =  0;
				}
			}
			if(!empty($receipt_book_idcard_order_id)){
				$receipt_book = $this->db->select('*')->from('feev2_receipt_book')->where('id',$receipt_book_idcard_order_id)->get()->row();
				$this->db->where('id',$receipt_book_idcard_order_id);
				$this->db->update('feev2_receipt_book', array('running_number'=>$receipt_book->running_number+1));
			}
			$data = array(
				'status' => 'in review',
				'sub_total' => $input['orderData']['subtotal'],
				'grand_total' => $input['orderData']['grandtotal'],
				's_gst' => $input['orderData']['sgst'],
				'c_gst' => $input['orderData']['cgst'],
				'unit_price' => $input['orderData']['unitprice'],
				'order_id' => $idcard_order_id
			);
			$this->db->where('id', $input['orderData']['id']);
			$this->db->update('idcard_template_orders', $data);

			$this->db->trans_complete();

			return $this->db->trans_status();
		}

		public function getOrderDetails($order_id){
			$this->db->select("*,id,order_name,id_card_verifier,id_card_for,id_card_type,created_by,date_format(created_on,'%d-%b-%Y') as created_on,quantity,sub_total,grand_total,s_gst,c_gst, idcard_template_id,status,id_card_for_type, ifnull(unit_price,0) as unit_price,ifnull(order_id, '-') as order_id,description,date_format(estimated_time_of_completion_for_printing,'%d-%b-%Y') as estimated_time_of_completion_for_printing,date_format(delivered_on,'%d-%b-%Y') as delivered_on");
			$this->db->from('idcard_template_orders');
			$this->db->where('id', $order_id);
			$orderData = $this->db->get()->row();
			$orderData->created_by = $this->_getAvatarNameById($orderData->created_by);

			return $orderData;
		}
		public function getAllOrderDetails() {
			// Fetch all order data
			$this->db_readonly->select("id, id_card_for, id_card_type, created_by, DATE_FORMAT(created_on, '%d-%b-%Y') as created_on, quantity, sub_total, grand_total, s_gst, c_gst, status, order_name,ifnull(order_id, '-') as order_id");
			$this->db_readonly->from('idcard_template_orders');
			$this->db_readonly->order_by('id', 'desc');
			$orderData = $this->db_readonly->get()->result();

			if (!$orderData) {
				return array();
			}

			// Collect order IDs and created_by IDs
			$orderIds = array();
			$createdByIds = array();
			foreach ($orderData as $order) {
				$orderIds[] = $order->id;
				if (!in_array($order->created_by, $createdByIds)) {
					$createdByIds[] = $order->created_by;
				}
			}

			// Fetch approved counts in a single query
			$approvedCounts = array();
			if (!empty($orderIds)) {
				$this->db_readonly->select('idcard_template_order_id, COUNT(*) as approved_count');
				$this->db_readonly->from('idcard_template_order_entities');
				$this->db_readonly->where_in('idcard_template_order_id', $orderIds);
				$this->db_readonly->where('status', 'approved');
				$this->db_readonly->group_by('idcard_template_order_id');
				$approvedCountsResult = $this->db_readonly->get()->result();

				foreach ($approvedCountsResult as $row) {
					$approvedCounts[$row->idcard_template_order_id] = $row->approved_count;
				}
			}

			// Fetch counts of entities with status "in review" or "approved" for each order
			$validEntityCounts = array();
			if (!empty($orderIds)) {
				$this->db_readonly->select('idcard_template_order_id, COUNT(*) as valid_count');
				$this->db_readonly->from('idcard_template_order_entities');
				$this->db_readonly->where_in('idcard_template_order_id', $orderIds);
				$this->db_readonly->where_in('status', array('in review', 'approved'));
				$this->db_readonly->group_by('idcard_template_order_id');
				$validCountsResult = $this->db_readonly->get()->result();

				foreach ($validCountsResult as $row) {
					$validEntityCounts[$row->idcard_template_order_id] = $row->valid_count;
				}
			}

			// Map created_by IDs to names (assumes _getAvatarNameById() returns names)
			$createdByNames = array();
			foreach ($createdByIds as $uid) {
				$createdByNames[$uid] = $this->_getAvatarNameById($uid);
			}

			// Attach approved count, valid entity count, and creator name to each order
			foreach ($orderData as &$order) {
				$order->approved_count = isset($approvedCounts[$order->id]) ? $approvedCounts[$order->id] : 0;

				// Update quantity to only count entities with status "in review" or "approved"
				$order->quantity = isset($validEntityCounts[$order->id]) ? $validEntityCounts[$order->id] : 0;

				$order->created_by = isset($createdByNames[$order->created_by]) ? $createdByNames[$order->created_by] : $order->created_by;
			}

			return $orderData;
		}


		public function get_all_staffs(){
			$this->db_readonly->select('sm.id, concat(sm.first_name, " ",ifnull(sm.last_name,"")) as name, sdes.designation, sd.department, sm.picture_url, sm.employee_code, sm.blood_group');
			$this->db_readonly->from('staff_master sm');
			$this->db_readonly->join('staff_departments sd','sd.id=sm.department','left');
			$this->db_readonly->join('staff_designations sdes','sdes.id=sm.designation','left');
			$this->db_readonly->where('sm.status', '2');
			$this->db_readonly->order_by('first_name');
			$result =  $this->db_readonly->get()->result();
			foreach ($result as $key => $val) {
				$val->photo = $this->filemanager->getFilePath($val->picture_url);
			}
			return $result;
		}

		public function get_total_id_card_orders(){
			$this->db_readonly->select('count(*) as total_count_id_Card_orders');
			$this->db_readonly->from('idcard_template_orders');
			return $this->db_readonly->get()->row()->total_count_id_Card_orders;
		}

		public function total_id_card_order_in_review(){
			$this->db_readonly->select('count(*) as total_id_card_order_in_review');
			$this->db_readonly->from('idcard_template_orders');
			$this->db_readonly->where('status','in review');
			return $this->db_readonly->get()->row()->total_id_card_order_in_review;
		}

		public function total_id_card_orders_completed(){
			$this->db_readonly->select('count(*) as total_count_id_Card_orders_completed');
			$this->db_readonly->from('idcard_template_order_entities');
			$this->db_readonly->where('status','delivered');
			return $this->db_readonly->get()->row()->total_count_id_Card_orders_completed;
		}

		public function total_id_card_order_in_printing(){
			$this->db_readonly->select('COUNT(id) as in_print_count');
			$this->db_readonly->from('idcard_template_orders');
			$this->db_readonly->where_not_in('status', ['draft', 'in review','delivered']);
			return $this->db_readonly->get()->row()->in_print_count;
		}

		public function update_entity_status() {
			$sm_id = $this->input->post('sm_id');
			$order_id = $this->input->post('order_id');
			$status = trim($this->input->post('status'));
			$type = trim($this->input->post('type'));
			$remarks = trim($this->input->post('remarks'));
			$col_name = '';
			if($status == 'removed'){
				$col_name='removed_remarks';
			}else if($status == 'modify'){
				$col_name='modified_remarks';

			}

			// Update the status in the database
			$this->db->where('avatar_id', $sm_id);
			$this->db->where('idcard_template_order_id', $order_id);
			$this->db->where('avatar_type', $type);
			$this->db->update('idcard_template_order_entities', ['status' => $status, $col_name =>$remarks]);

			// Return true if the update was successful
			return ($this->db->affected_rows() > 0);
		}

		public function update_order_status($order_id, $status) {
			// Update the status in the database
			$this->db->where('id', $order_id);
			$this->db->update('idcard_template_orders', ['status' => $status]);

			// Return true if the update was successful
			return ($this->db->affected_rows() > 0);
		}


		public function clone_order_for_unapproved($order_id) {
			// Start a database transaction
			$this->db->trans_begin();

			try {
				// Get the original order details
				$original_order = $this->getOrderDetails($order_id);

				if (!$original_order) {
					return ['success' => false, 'message' => 'Original order not found'];
				}

				// Get unapproved entities for this order
				$unapproved_entities = $this->get_unapproved_entities($order_id);

				if (empty($unapproved_entities)) {
					return ['success' => false, 'message' => 'No unapproved entities found for this order'];
				}

				if ($original_order->id_card_for == 'Staff') {
					$id_card_for_type = 'staff_subset';
				} elseif ($original_order->id_card_for == 'Student') {
					$id_card_for_type = 'student_subset';
				} else {
					$id_card_for_type = $original_order->id_card_for_type; // fallback to original if needed
				}

				// Create a new order with the same details
				$new_order_data = [
					'order_name'       => $original_order->order_name,
					'id_card_verifier' => $original_order->id_card_verifier,
					'id_card_type'     => $original_order->id_card_type,
					'id_card_for'      => $original_order->id_card_for,
					'id_card_for_type' => $id_card_for_type,
					'description'      => $original_order->description . ' (Re-ordered from Order ' . $original_order->order_name . ')',
					'status'           => 'draft',
					'created_by'       => $this->authorization->getAvatarStakeHolderId(),
					'created_on'       => $this->Kolkata_datetime(),
					'idcard_template_id' => $original_order->idcard_template_id
				];

				// Insert the new order
				$this->db->insert('idcard_template_orders', $new_order_data);
				$new_order_id = $this->db->insert_id();

				if (!$new_order_id) {
					$this->db->trans_rollback();
					return false;
				}

				// Add the unapproved entities to the new order
				$entities_data = [];
				foreach ($unapproved_entities as $entity) {
					$entities_data[] = [
						'idcard_template_order_id' => $new_order_id,
						'avatar_type'              => $entity->avatar_type,
						'avatar_id'                => $entity->avatar_id,
						'status'                   => 'in review',
					];

					// Update the original entity status to 'removed'
					$this->db->where('id', $entity->id);
					$this->db->update('idcard_template_order_entities', ['status' => 're-ordered']);
				}

				// Insert the entities for the new order
				if (!empty($entities_data)) {
					$this->db->insert_batch('idcard_template_order_entities', $entities_data);
				}

				// Get the unit price from the original order
				$unit_price = isset($original_order->unit_price) ? $original_order->unit_price : 0;

				// Calculate financial values for the new order
				$new_order_count = count($entities_data);
				$new_order_sub_total = $new_order_count * $unit_price;
				$new_order_cgst = 0;
				$new_order_sgst = 0;

				// Calculate GST if the original order has it
				if (isset($original_order->sub_total) && $original_order->sub_total > 0) {

					$cgst_rate = (isset($original_order->c_gst) && $original_order->sub_total) ? ($original_order->c_gst / $original_order->sub_total) : 0;
					$sgst_rate = (isset($original_order->s_gst) && $original_order->sub_total) ? ($original_order->s_gst / $original_order->sub_total) : 0;

					$new_order_cgst = $new_order_sub_total * $cgst_rate;
					$new_order_sgst = $new_order_sub_total * $sgst_rate;
				}

				$new_order_grand_total = $new_order_sub_total + $new_order_cgst + $new_order_sgst;

				// Update the new order with quantity and financial details
				$this->db->where('id', $new_order_id);
				$this->db->update('idcard_template_orders', array(
					'quantity' => $new_order_count,
					'sub_total' => $new_order_sub_total,
					'grand_total' => $new_order_grand_total,
					'c_gst' => $new_order_cgst,
					's_gst' => $new_order_sgst,
					'unit_price' => $unit_price
				));

				// Update the original order's quantity and financial details
				// First, get the count of remaining entities (approved, in review, etc.)
				$this->db->select('COUNT(*) as count');
				$this->db->from('idcard_template_order_entities');
				$this->db->where('idcard_template_order_id', $order_id);
				$this->db->where_in('status', array('approved', 'in review'));
				$query = $this->db->get();
				$row = $query->row();
				$remaining_count = isset($row->count) ? $row->count : 0;

				// Calculate new financial values for the original order
				$original_sub_total = $remaining_count * $unit_price;
				$original_cgst = 0;
				$original_sgst = 0;


				// Calculate GST if the original order has it
				if (isset($original_order->sub_total) && $original_order->sub_total > 0) {
					$original_cgst = $original_sub_total * $cgst_rate;
					$original_sgst = $original_sub_total * $sgst_rate;

				}

				$original_grand_total = $original_sub_total + $original_cgst + $original_sgst;

				// Update the original order
				$this->db->where('id', $order_id);
				$this->db->update('idcard_template_orders', array(
					'quantity' => $remaining_count,
					'sub_total' => $original_sub_total,
					'grand_total' => $original_grand_total,
					'c_gst' => $original_cgst,
					's_gst' => $original_sgst
				));

				// Commit the transaction
				if ($this->db->trans_status() === FALSE) {
					$this->db->trans_rollback();
					return false;
				}

				$this->db->trans_commit();

				return [
					'success' => true,
					'new_order_id' => $new_order_id,
					'entity_count' => count($entities_data)
				];
			} catch (Exception $e) {
				$this->db->trans_rollback();
				return ['success' => false, 'message' => $e->getMessage()];
			}
		}

		private function get_unapproved_entities($order_id) {
			$this->db->select('*');
			$this->db->from('idcard_template_order_entities');
			$this->db->where('idcard_template_order_id', $order_id);
			$this->db->where_not_in('status', ['approved', 'delivered']);
			return $this->db->get()->result();
		}

		public function getSelectedEntities($order_id, $profile_type) {
		if ($profile_type === 'Staff') {
			// Get staff entities with details
			$this->db_readonly->select('itoe.id, itoe.avatar_id as id, itoe.status, CONCAT(IFNULL(sm.first_name, ""), " ", IFNULL(sm.last_name, "")) AS staff_name, sm.employee_code, sd.department, sdes.designation');
			$this->db_readonly->from('idcard_template_order_entities itoe');
			$this->db_readonly->join('staff_master sm', 'itoe.avatar_id = sm.id', 'left');
			$this->db_readonly->join('staff_departments sd', 'sd.id = sm.department', 'left');
			$this->db_readonly->join('staff_designations sdes', 'sdes.id = sm.designation', 'left');
			$this->db_readonly->where('itoe.idcard_template_order_id', $order_id);
			$this->db_readonly->where('itoe.avatar_type', 'staff');
		} else {
			// Get student entities with details
			$this->db_readonly->select('itoe.id, itoe.avatar_id as id, itoe.status, CONCAT(IFNULL(sa.first_name, ""), " ", IFNULL(sa.last_name, "")) AS name, sa.admission_no, CONCAT(IFNULL(c.class_name, ""), " ", IFNULL(cs.section_name, "")) AS class_section');
			$this->db_readonly->from('idcard_template_order_entities itoe');
			$this->db_readonly->join('student_admission sa', 'itoe.avatar_id = sa.id', 'left');
			$this->db_readonly->join('student_year sy', 'sa.id = sy.student_admission_id AND sy.acad_year_id = ' . $this->yearId, 'left');
			$this->db_readonly->join('class_section cs', 'sy.class_section_id = cs.id', 'left');
			$this->db_readonly->join('class c', 'sy.class_id = c.id', 'left');
			$this->db_readonly->where('itoe.idcard_template_order_id', $order_id);
			$this->db_readonly->where('itoe.avatar_type', 'student');
		}

		$query = $this->db_readonly->get();
		return $query->result();
	}

	/**
	 * Get entities for specific order with chunked/paginated data retrieval
	 * Optimized version with JOINs instead of N+1 queries
	 */
	public function getEntitiesForOrderChunked($order_id, $id_card_for, $status_filter = 'all', $limit = 50, $offset = 0, $search = '') {
		try {
			// First get total count for pagination
			$total_count = $this->getEntitiesCountForOrder($order_id, $id_card_for, $status_filter, $search);

			// Get status counts
			$status_counts = $this->getOrderStatusCounts($order_id, $id_card_for);

			// Get chunked data
			$data = $this->getEntitiesChunkedData($order_id, $id_card_for, $status_filter, $limit, $offset, $search);

			return [
				'data' => $data,
				'total_count' => $total_count,
				'status_counts' => $status_counts
			];

		} catch (Exception $e) {
			log_message('error', 'Error in getEntitiesForOrderChunked: ' . $e->getMessage());
			return [
				'data' => [],
				'total_count' => 0,
				'status_counts' => []
			];
		}
	}

	/**
	 * Get total count of entities for pagination
	 */
	private function getEntitiesCountForOrder($order_id, $id_card_for, $status_filter = 'all', $search = '') {
		if ($id_card_for == 'Staff') {
			$this->db_readonly->select('COUNT(DISTINCT itoe.id) as total');
			$this->db_readonly->from('idcard_template_order_entities itoe');
			$this->db_readonly->join('staff_master sm', 'sm.id = itoe.avatar_id');
			$this->db_readonly->where('itoe.idcard_template_order_id', $order_id);

			// Add status filter
			if ($status_filter && $status_filter !== 'all') {
				$this->db_readonly->where('itoe.status', $status_filter);
			}

			// Add search filter
			if (!empty($search)) {
				$this->db_readonly->group_start();
				$this->db_readonly->like('CONCAT(IFNULL(sm.first_name, ""), " ", IFNULL(sm.last_name, ""))', $search);
				$this->db_readonly->or_like('sm.employee_code', $search);
				$this->db_readonly->group_end();
			}

		} else if ($id_card_for == 'Student' || $id_card_for == 'Parent') {
			$stakeholderId = $this->authorization->getAvatarStakeHolderId();
			$schoolAdmin = $this->authorization->isAuthorized('ID_CARDS.MANAGE_ID_CARD_ORDERS');

			$this->db_readonly->select('COUNT(DISTINCT itoe.id) as total');
			$this->db_readonly->from('idcard_template_order_entities itoe');
			$this->db_readonly->join('student_admission sa', 'sa.id = itoe.avatar_id');
			$this->db_readonly->join('student_year sy', 'sy.student_admission_id = sa.id AND sy.acad_year_id = ' . $this->yearId);
			$this->db_readonly->join('class_section cs', 'sy.class_section_id = cs.id', 'left');
			$this->db_readonly->join('class c', 'sy.class_id = c.id');
			$this->db_readonly->where('itoe.idcard_template_order_id', $order_id);

			// Add authorization filter
			if (!$schoolAdmin && $stakeholderId != '0') {
				$this->db_readonly->group_start();
				$this->db_readonly->where('cs.class_teacher_id', $stakeholderId);
				$this->db_readonly->or_where('cs.assistant_class_teacher_id', $stakeholderId);
				$this->db_readonly->or_where('cs.assistant_class_teacher_2', $stakeholderId);
				$this->db_readonly->group_end();
			}

			// Add status filter
			if ($status_filter && $status_filter !== 'all') {
				$this->db_readonly->where('itoe.status', $status_filter);
			}

			// Add search filter
			if (!empty($search)) {
				$this->db_readonly->group_start();
				$this->db_readonly->like('CONCAT(IFNULL(sa.first_name, ""), " ", IFNULL(sa.last_name, ""))', $search);
				$this->db_readonly->or_like('sa.admission_no', $search);
				$this->db_readonly->or_like('CONCAT(IFNULL(c.class_name, ""), " ", IFNULL(cs.section_name, ""))', $search);
				$this->db_readonly->group_end();
			}
		}

		$result = $this->db_readonly->get()->row();
		return $result ? (int)$result->total : 0;
	}

	/**
	 * Get status counts for order
	 */
	private function getOrderStatusCounts($order_id, $id_card_for) {
		$this->db_readonly->select('itoe.status, COUNT(*) as count');
		$this->db_readonly->from('idcard_template_order_entities itoe');

		if ($id_card_for == 'Staff') {
			$this->db_readonly->join('staff_master sm', 'sm.id = itoe.avatar_id');
		} else {
			$stakeholderId = $this->authorization->getAvatarStakeHolderId();
			$schoolAdmin = $this->authorization->isAuthorized('ID_CARDS.MANAGE_ID_CARD_ORDERS');

			$this->db_readonly->join('student_admission sa', 'sa.id = itoe.avatar_id');
			$this->db_readonly->join('student_year sy', 'sy.student_admission_id = sa.id AND sy.acad_year_id = ' . $this->yearId);
			$this->db_readonly->join('class_section cs', 'sy.class_section_id = cs.id', 'left');

			// Add authorization filter
			if (!$schoolAdmin && $stakeholderId != '0') {
				$this->db_readonly->group_start();
				$this->db_readonly->where('cs.class_teacher_id', $stakeholderId);
				$this->db_readonly->or_where('cs.assistant_class_teacher_id', $stakeholderId);
				$this->db_readonly->or_where('cs.assistant_class_teacher_2', $stakeholderId);
				$this->db_readonly->group_end();
			}
		}

		$this->db_readonly->where('itoe.idcard_template_order_id', $order_id);
		$this->db_readonly->group_by('itoe.status');

		$results = $this->db_readonly->get()->result();

		$counts = [
			'total' => 0,
			'in_review' => 0,
			'approved' => 0,
			'removed' => 0,
			'modify' => 0,
			're_ordered' => 0
		];

		foreach ($results as $result) {
			$status = str_replace([' ', '-'], '_', strtolower($result->status));
			if (isset($counts[$status])) {
				$counts[$status] = (int)$result->count;
			}
			$counts['total'] += (int)$result->count;
		}

		return $counts;
	}

	/**
	 * Get chunked entity data with optimized JOINs
	 */
	private function getEntitiesChunkedData($order_id, $id_card_for, $status_filter = 'all', $limit = 50, $offset = 0, $search = '') {
		if ($id_card_for == 'Staff') {
			return $this->getStaffEntitiesChunked($order_id, $status_filter, $limit, $offset, $search);
		} else if ($id_card_for == 'Student' || $id_card_for == 'Parent') {
			return $this->getStudentEntitiesChunked($order_id, $status_filter, $limit, $offset, $search);
		}

		return [];
	}

	/**
	 * Get staff entities with chunked data
	 */
	private function getStaffEntitiesChunked($order_id, $status_filter, $limit, $offset, $search) {
		$this->db_readonly->select("
			sm.id as sm_id,
			itoe.id,
			itoe.avatar_id,
			itoe.status,
			CONCAT(IFNULL(sm.first_name, ''), ' ', IFNULL(sm.last_name, '')) AS staff_name,
			sm.employee_code,
			IFNULL(DATE_FORMAT(sm.dob, '%d-%b-%Y'), '-') as dob,
			sm.contact_number as contact,
			sm.blood_group,
			CONCAT(ai.Address_Line1, ' ', ai.Address_Line2, ' ', ai.area, ' ', ai.district, ' ', ai.state, ' ', ai.pin_code) as address,
			sm.emergency_info as emergency_contact,
			sdes.designation,
			sd.department,
			sm.high_quality_picture_url,
			sm.spouse_name,
			sm.staff_type,
			sm.qualification
		");

		$this->db_readonly->from('idcard_template_order_entities itoe');
		$this->db_readonly->join('staff_master sm', 'sm.id = itoe.avatar_id');
		$this->db_readonly->join('staff_departments sd', 'sd.id = sm.department', 'left');
		$this->db_readonly->join('staff_designations sdes', 'sdes.id = sm.designation', 'left');
		$this->db_readonly->join('address_info ai', 'ai.stakeholder_id = sm.id AND ai.avatar_type = 4 AND ai.address_type = 0', 'left');
		$this->db_readonly->where('itoe.idcard_template_order_id', $order_id);

		// Add status filter
		if ($status_filter && $status_filter !== 'all') {
			$this->db_readonly->where('itoe.status', $status_filter);
		}

		// Add search filter
		if (!empty($search)) {
			$this->db_readonly->group_start();
			$this->db_readonly->like('CONCAT(IFNULL(sm.first_name, ""), " ", IFNULL(sm.last_name, ""))', $search);
			$this->db_readonly->or_like('sm.employee_code', $search);
			$this->db_readonly->group_end();
		}

		$this->db_readonly->order_by('sm.first_name', 'ASC');
		$this->db_readonly->limit($limit, $offset);

		$result = $this->db_readonly->get()->result();

		// Process staff data
		foreach ($result as $key => $val) {
			if (!empty($val->high_quality_picture_url)) {
				$val->picture_url = $this->filemanager->getFilePath($val->high_quality_picture_url);
			} else {
				$val->picture_url = '';
			}

			if (isset($val->staff_type)) {
				$val->staff_type = $this->settings->getSetting('staff_type')[$val->staff_type] ?? '';
			} else {
				$val->staff_type = '';
			}

			// Set default values for fields not applicable to staff
			$val->father_address = '';
			$val->mother_address = '';
			$val->father_photo = '';
			$val->mother_photo = '';
			$val->grade = '';
			$val->grade_section = '';
			$val->father_name = '';
			$val->mother_name = '';
			$val->father_contact = '';
			$val->mother_contact = '';
		}

		return $result;
	}

	/**
	 * Get student entities with chunked data and optimized JOINs
	 */
	private function getStudentEntitiesChunked($order_id, $status_filter, $limit, $offset, $search) {
		$stakeholderId = $this->authorization->getAvatarStakeHolderId();
		$schoolAdmin = $this->authorization->isAuthorized('ID_CARDS.MANAGE_ID_CARD_ORDERS');

		// Main query with all necessary JOINs to avoid N+1 queries
		$this->db_readonly->select("
			sa.id as sa_id,
			CONCAT(IFNULL(sa.first_name,''), ' ', IFNULL(sa.last_name,'')) as name,
			sa.admission_no,
			CONCAT(IFNULL(c.class_name,''), ' ', IFNULL(cs.section_name,'')) as class_section,
			IFNULL(DATE_FORMAT(sa.dob, '%d-%b-%Y'), '-') as dob,
			sa.preferred_contact_no,
			sa.blood_group,
			CONCAT(add.Address_Line1, ' ', add.Address_Line2, ' ', add.area, ' ', add.district, ' ', add.state, ' ', add.pin_code) as address,
			sa.email,
			sy.picture_url,
			c.class_name as grade,
			cs.section_name as grade_section,
			itoe.status,
			IFNULL(cmc.combination_name,'') as combination,
			IFNULL(sa.enrollment_number,'') as enrollment_no,
			IFNULL(sy.alpha_rollnum,'') as alpha_rollnum,
			itoe.avatar_type,
			sa.identification_code as qr_code,
			CONCAT(IFNULL(sa.first_name,''), ' ', IFNULL(sa.last_name,''), '-', IFNULL(c.class_name,'')) as name_class,
			itoe.avatar_type as relation_type,
			sa.custom1 as custom_address,
			sa.point_of_contact,

			-- Father information
			CONCAT(IFNULL(pf.first_name,''), ' ', IFNULL(pf.last_name,'')) as father_name,
			pf.mobile_no as father_contact,
			CONCAT(addf.Address_Line1, ' ', addf.Address_Line2, ' ', addf.area, ' ', addf.district, ' ', addf.state, ' ', addf.pin_code) as father_address,
			pf.picture_url as father_picture_url,

			-- Mother information
			CONCAT(IFNULL(pm.first_name,''), ' ', IFNULL(pm.last_name,'')) as mother_name,
			pm.mobile_no as mother_contact,
			CONCAT(addm.Address_Line1, ' ', addm.Address_Line2, ' ', addm.area, ' ', addm.district, ' ', addm.state, ' ', addm.pin_code) as mother_address,
			pm.picture_url as mother_picture_url,

			-- Guardian information
			CONCAT(IFNULL(pg.first_name,''), ' ', IFNULL(pg.last_name,'')) as guardian_name,
			pg.mobile_no as guardian_contact,
			CONCAT(addg.Address_Line1, ' ', addg.Address_Line2, ' ', addg.area, ' ', addg.district, ' ', addg.state, ' ', addg.pin_code) as guardian_address,
			pg.picture_url as guardian_picture_url,

			-- Guardian_2 information
			CONCAT(IFNULL(pg2.first_name,''), ' ', IFNULL(pg2.last_name,'')) as guardian_2_name,
			pg2.mobile_no as guardian_2_contact,
			CONCAT(addg2.Address_Line1, ' ', addg2.Address_Line2, ' ', addg2.area, ' ', addg2.district, ' ', addg2.state, ' ', addg2.pin_code) as guardian_2_address,
			pg2.picture_url as guardian_2_picture_url,

			-- Driver information
			CONCAT(IFNULL(pd.first_name,''), ' ', IFNULL(pd.last_name,'')) as driver_name,
			pd.mobile_no as driver_contact,
			CONCAT(addd.Address_Line1, ' ', addd.Address_Line2, ' ', addd.area, ' ', addd.district, ' ', addd.state, ' ', addd.pin_code) as driver_address,
			pd.picture_url as driver_picture_url,

			-- Driver_2 information
			CONCAT(IFNULL(pd2.first_name,''), ' ', IFNULL(pd2.last_name,'')) as driver_2_name,
			pd2.mobile_no as driver_2_contact,
			CONCAT(addd2.Address_Line1, ' ', addd2.Address_Line2, ' ', addd2.area, ' ', addd2.district, ' ', addd2.state, ' ', addd2.pin_code) as driver_2_address,
			pd2.picture_url as driver_2_picture_url,

			-- Transport information
			MAX(CASE WHEN tsj.journey_type = 'PICKING' THEN SUBSTRING_INDEX(SUBSTRING_INDEX(tj.journey_name, ' ', 2), ' ', -2) END) AS picking_route,
			MAX(CASE WHEN tsj.journey_type = 'DROPPING' THEN SUBSTRING_INDEX(SUBSTRING_INDEX(tj.journey_name, ' ', 2), ' ', -2) END) AS dropping_route
		");

		$this->db_readonly->from('idcard_template_order_entities itoe');
		$this->db_readonly->join('student_admission sa', 'sa.id = itoe.avatar_id');
		$this->db_readonly->join('student_year sy', 'sy.student_admission_id = sa.id AND sy.acad_year_id = ' . $this->yearId);
		$this->db_readonly->join('class_master_combinations cmc', 'sy.combination_id = cmc.id', 'left');
		$this->db_readonly->join('class_section cs', 'sy.class_section_id = cs.id', 'left');
		$this->db_readonly->join('class c', 'sy.class_id = c.id');
		$this->db_readonly->join('address_info add', 'add.stakeholder_id = sa.id AND add.avatar_type = 1 AND add.address_type = 0', 'left');

		// Father JOIN
		$this->db_readonly->join('student_relation srf', 'srf.std_id = sa.id AND srf.relation_type = "Father"', 'left');
		$this->db_readonly->join('parent pf', 'srf.relation_id = pf.id', 'left');
		$this->db_readonly->join('address_info addf', 'addf.stakeholder_id = pf.id AND addf.avatar_type = 2', 'left');

		// Mother JOIN
		$this->db_readonly->join('student_relation srm', 'srm.std_id = sa.id AND srm.relation_type = "Mother"', 'left');
		$this->db_readonly->join('parent pm', 'srm.relation_id = pm.id', 'left');
		$this->db_readonly->join('address_info addm', 'addm.stakeholder_id = pm.id AND addm.avatar_type = 2', 'left');

		// Guardian JOIN
		$this->db_readonly->join('student_relation srg', 'srg.std_id = sa.id AND srg.relation_type = "Guardian"', 'left');
		$this->db_readonly->join('parent pg', 'srg.relation_id = pg.id', 'left');
		$this->db_readonly->join('address_info addg', 'addg.stakeholder_id = pg.id AND addg.avatar_type = 2', 'left');

		// Guardian_2 JOIN
		$this->db_readonly->join('student_relation srg2', 'srg2.std_id = sa.id AND srg2.relation_type = "Guardian_2"', 'left');
		$this->db_readonly->join('parent pg2', 'srg2.relation_id = pg2.id', 'left');
		$this->db_readonly->join('address_info addg2', 'addg2.stakeholder_id = pg2.id AND addg2.avatar_type = 2', 'left');

		// Driver JOIN
		$this->db_readonly->join('student_relation srd', 'srd.std_id = sa.id AND srd.relation_type = "Driver"', 'left');
		$this->db_readonly->join('parent pd', 'srd.relation_id = pd.id', 'left');
		$this->db_readonly->join('address_info addd', 'addd.stakeholder_id = pd.id AND addd.avatar_type = 2', 'left');

		// Driver_2 JOIN
		$this->db_readonly->join('student_relation srd2', 'srd2.std_id = sa.id AND srd2.relation_type = "Driver_2"', 'left');
		$this->db_readonly->join('parent pd2', 'srd2.relation_id = pd2.id', 'left');
		$this->db_readonly->join('address_info addd2', 'addd2.stakeholder_id = pd2.id AND addd2.avatar_type = 2', 'left');

		// Transport JOIN
		$this->db_readonly->join('tx_student_journeys tsj', 'tsj.entity_source_id = sa.id AND tsj.entity_type = "Student"', 'left');
		$this->db_readonly->join('tx_journeys tj', 'tsj.journey_id = tj.id', 'left');

		$this->db_readonly->where('itoe.idcard_template_order_id', $order_id);

		// Add authorization filter
		if (!$schoolAdmin && $stakeholderId != '0') {
			$this->db_readonly->group_start();
			$this->db_readonly->where('cs.class_teacher_id', $stakeholderId);
			$this->db_readonly->or_where('cs.assistant_class_teacher_id', $stakeholderId);
			$this->db_readonly->or_where('cs.assistant_class_teacher_2', $stakeholderId);
			$this->db_readonly->group_end();
		}

		// Add status filter
		if ($status_filter && $status_filter !== 'all') {
			$this->db_readonly->where('itoe.status', $status_filter);
		}

		// Add search filter
		if (!empty($search)) {
			$this->db_readonly->group_start();
			$this->db_readonly->like('CONCAT(IFNULL(sa.first_name, ""), " ", IFNULL(sa.last_name, ""))', $search);
			$this->db_readonly->or_like('sa.admission_no', $search);
			$this->db_readonly->or_like('CONCAT(IFNULL(c.class_name, ""), " ", IFNULL(cs.section_name, ""))', $search);
			$this->db_readonly->group_end();
		}

		$this->db_readonly->group_by('sa.id, itoe.id');
		$this->db_readonly->order_by('sa.first_name', 'ASC');
		$this->db_readonly->limit($limit, $offset);

		$result = $this->db_readonly->get()->result();
		$schoolName = $this->settings->getSetting('school_short_name');

		// Process student data
		foreach ($result as $key => $val) {
			// Handle school-specific customizations
			if ($schoolName == 'mvmkml' || $schoolName == 'kesarhosahalli') {
				$val->address = $val->custom_address;
			}
			if ($schoolName == 'jspuc') {
				$val->qr_code = $val->custom_address;
			}

			// Process picture URLs
			if (!empty($val->picture_url)) {
				$val->picture_url = $this->filemanager->getFilePath($val->picture_url);
			} else {
				$val->picture_url = '';
			}

			// Process parent/guardian/driver photos
			$this->processParentPhoto($val, 'father');
			$this->processParentPhoto($val, 'mother');
			$this->processParentPhoto($val, 'guardian');
			$this->processParentPhoto($val, 'guardian_2');
			$this->processParentPhoto($val, 'driver');
			$this->processParentPhoto($val, 'driver_2');

			// Set point of contact
			$pointOfContact = strtolower(trim($val->point_of_contact));
			if ($pointOfContact === 'father') {
				$val->parent_name = $val->father_name;
				$val->contact = $val->father_contact;
			} elseif ($pointOfContact === 'mother') {
				$val->parent_name = $val->mother_name;
				$val->contact = $val->mother_contact;
			}

			// Set default values for transport if empty
			$val->picking_route = $val->picking_route ?: '';
			$val->dropping_route = $val->dropping_route ?: '';
		}

		return $result;
	}

	/**
	 * Helper method to process parent/guardian/driver photos
	 */
	private function processParentPhoto($entity, $type) {
		$picture_field = $type . '_picture_url';
		$photo_field = $type . '_photo';

		if (!empty($entity->$picture_field)) {
			$entity->$photo_field = $this->filemanager->getFilePath($entity->$picture_field);
		} else {
			$entity->$photo_field = '';
		}

		// Remove the temporary picture_url field
		unset($entity->$picture_field);
	}

	public function getAllEntitiesForTheSpecificOrder($order_id, $id_card_for, $status_filter = 'all') {
			if($id_card_for == 'Staff') {
				$this->db_readonly->select("sm.id as sm_id, itoe.id,itoe.avatar_id,itoe.status,CONCAT(IFNULL(sm.first_name, ''), ' ', IFNULL(sm.last_name, '')) AS staff_name,sm.employee_code,ifnull(date_format(sm.dob, '%d-%b-%Y'), '-') as dob,sm.contact_number as contact,sm.blood_group,concat(ai.Address_Line1, ' ', ai.Address_Line2,' ', ai.area,' ', ai.district,' ', ai.state,' ', ai.pin_code) as address, sm.emergency_info as emergency_contact, sdes.designation, sd.department, sm.high_quality_picture_url,sm.spouse_name, sm.staff_type,sm.qualification");
				$this->db_readonly->from('idcard_template_order_entities itoe');
				$this->db_readonly->join('staff_master sm','sm.id=itoe.avatar_id');
				$this->db_readonly->join('staff_departments sd','sd.id=sm.department','left');
				$this->db_readonly->join('staff_designations sdes','sdes.id=sm.designation','left');
				$this->db_readonly->join('address_info ai','ai.stakeholder_id=sm.id and ai.avatar_type = 4 and ai.address_type = 0','left');
				$this->db_readonly->where('idcard_template_order_id', $order_id);

				// Add status filter if provided
				if ($status_filter && $status_filter !== 'all') {
					$this->db_readonly->where('itoe.status', $status_filter);
				}

				$result = $this->db_readonly->get()->result();
				foreach ($result as $key => $val) {
					if(!empty($val->high_quality_picture_url)){
						$val->picture_url = $this->filemanager->getFilePath($val->high_quality_picture_url);
					} else {
						$val->picture_url = '';
					}

					if(isset($val->staff_type)){
						$val->staff_type = $this->settings->getSetting('staff_type')[$val->staff_type];
					} else {
						$val->staff_type = '';
					}
					// Set default values for new fields
					$val->father_address = '';
					$val->mother_address = '';
					$val->father_photo = '';
					$val->mother_photo = '';
					$val->grade = '';
					$val->grade_section = '';
					$val->father_name = '';
					$val->mother_name = '';
					$val->father_contact = '';
					$val->mother_contact = '';
				}

			} else if($id_card_for == 'Student' || $id_card_for == 'Parent') {
				$stakeholderId = $this->authorization->getAvatarStakeHolderId();
				$schoolAdmin = $this->authorization->isAuthorized('ID_CARDS.MANAGE_ID_CARD_ORDERS');
				$this->db_readonly->select("sa.id as sa_id,concat(ifnull(sa.first_name,''),' ', ifnull(sa.last_name,'')) as name,sa.admission_no,concat(ifnull(c.class_name,''), ' ' ,ifnull(cs.section_name,'')) as class_section,ifnull(date_format(sa.dob, '%d-%b-%Y'), '-') as dob,sa.preferred_contact_no,sa.blood_group,concat(add.Address_Line1, ' ', add.Address_Line2,' ', add.area,' ', add.district,' ', add.state,' ', add.pin_code) as address,sa.email,sy.picture_url, c.class_name as grade, cs.section_name as grade_section,itoe.status,ifnull(cmc.combination_name,'') as combination, ifnull(sa.enrollment_number,'') as enrollment_no, ifnull(sy.alpha_rollnum,'') as alpha_rollnum,itoe.avatar_type, sa.identification_code as qr_code, concat(ifnull(sa.first_name,''),' ', ifnull(sa.last_name,''), '-',ifnull(c.class_name,'')) as name_class, itoe.avatar_type as relation_type, sa.custom1 as custom_address,sa.point_of_contact");
				$this->db_readonly->from('idcard_template_order_entities itoe');
				$this->db_readonly->join('student_admission sa','sa.id=itoe.avatar_id');
				$this->db_readonly->join('student_year sy','sy.student_admission_id=sa.id and sy.acad_year_id='.$this->yearId);
				$this->db_readonly->join('class_master_combinations cmc','sy.combination_id=cmc.id','left');
				$this->db_readonly->join('class_section cs','sy.class_section_id=cs.id','left');
				$this->db_readonly->join('class c','sy.class_id=c.id');
				$this->db_readonly->join('address_info add','add.stakeholder_id=sa.id and add.avatar_type = 1 and add.address_type = 0','left');
				$this->db_readonly->where('itoe.idcard_template_order_id', $order_id);
				if(!$schoolAdmin){
					if($stakeholderId != '0'){
						$this->db_readonly->group_start();
						$this->db_readonly->where('cs.class_teacher_id', $stakeholderId);
						$this->db_readonly->or_where('cs.assistant_class_teacher_id', $stakeholderId);
						$this->db_readonly->or_where('cs.assistant_class_teacher_2', $stakeholderId);
						$this->db_readonly->group_end();
					}
				}
				// Add status filter if provided
				if ($status_filter && $status_filter !== 'all') {
					$this->db_readonly->where('itoe.status', $status_filter);
				}
				$result= $this->db_readonly->get()->result();
				$schoolName = $this->settings->getSetting('school_short_name');
				foreach ($result as $key => $val) {
					if($schoolName == 'mvmkml' || $schoolName == 'kesarhosahalli'){
						$val->address = $val->custom_address;
					}
					if($schoolName == 'jspuc'){
						$val->qr_code = $val->custom_address;
					}
				}
				foreach ($result as $key => $val) {
					if(!empty($val->picture_url)){
						$val->picture_url = $this->filemanager->getFilePath($val->picture_url);
					} else {
						$val->picture_url = '';
					}

					$father = $this->db_readonly->select("CONCAT(ifnull(p.first_name,''), ' ', ifnull(p.last_name,'')) as father_name,  concat(add.Address_Line1, ' ', add.Address_Line2,' ', add.area,' ', add.district,' ', add.state,' ', add.pin_code) as father_address, high_quality_picture_url, picture_url, p.mobile_no")
					->from('student_relation sr')
					->where('sr.relation_type','Father')
					->join('parent p','sr.relation_id=p.id')
					->join('address_info add', 'add.stakeholder_id = p.id AND add.avatar_type = 2', 'left')
					->where('p.student_id',$val->sa_id)
					->get()->row();
					if (!empty($father)) {
						$val->father_name = $father->father_name;
						$val->father_contact = $father->mobile_no;
						$val->father_address = $father->father_address;
						if(!empty($father->picture_url)){
							$val->father_photo = $this->filemanager->getFilePath($father->picture_url);
						} else {
							$val->father_photo = '';
						}
					}else{
						$val->father_address = '';
						$val->father_photo = '';
						$val->father_name = '';
						$val->father_contact = '';
					}
					$mother = $this->db_readonly->select("CONCAT(ifnull(p.first_name,''), ' ', ifnull(p.last_name,'')) as mother_name,concat(add.Address_Line1, ' ', add.Address_Line2,' ', add.area,' ', add.district,' ', add.state,' ', add.pin_code) as mother_address, high_quality_picture_url, picture_url, p.mobile_no")
					->from('student_relation sr')
					->where('sr.relation_type','Mother')
					->join('parent p','sr.relation_id=p.id')
					->join('address_info add', 'add.stakeholder_id = p.id AND add.avatar_type = 2', 'left')
					->where('p.student_id',$val->sa_id)
					->get()->row();

					if (!empty($mother)) {
						$val->mother_name = $mother->mother_name;
						$val->mother_contact = $mother->mobile_no;
						$val->mother_address = $mother->mother_address;
						if(!empty($mother->picture_url)){
							$val->mother_photo = $this->filemanager->getFilePath($mother->picture_url);
						} else {
							$val->mother_photo = '';
						}
					}else{
						$val->mother_address = '';
						$val->mother_photo = '';
						$val->mother_name = '';
						$val->mother_contact = '';
					}
					$pointOfContact = strtolower(trim($val->point_of_contact));
					if ($pointOfContact === 'father') {
						$val->parent_name = $val->father_name;
						$val->contact = $val->father_contact;
					} elseif ($pointOfContact === 'mother') {
						$val->parent_name = $val->mother_name;
						$val->contact = $val->mother_contact;
					}

					$guardian = $this->db_readonly->select("CONCAT(ifnull(p.first_name,''), ' ', ifnull(p.last_name,'')) as guardian_name,  concat(add.Address_Line1, ' ', add.Address_Line2,' ', add.area,' ', add.district,' ', add.state,' ', add.pin_code) as guardian_address, high_quality_picture_url, picture_url, p.mobile_no")
					->from('student_relation sr')
					->where('sr.relation_type','Guardian')
					->join('parent p','sr.relation_id=p.id')
					->join('address_info add', 'add.stakeholder_id = p.id AND add.avatar_type = 2', 'left')
					->where('p.student_id',$val->sa_id)
					->get()->row();
					if (!empty($guardian)) {
						$val->guardian_name = $guardian->guardian_name;
						$val->guardian_contact = $guardian->mobile_no;
						$val->guardian_address = $guardian->guardian_address;
						if(!empty($guardian->picture_url)){
							$val->guardian_photo = $this->filemanager->getFilePath($guardian->picture_url);
						} else {
							$val->guardian_photo = '';
						}
					}else{
						$val->guardian_address = '';
						$val->guardian_photo = '';
						$val->guardian_name = '';
						$val->guardian_contact = '';
					}

					$guardian_2 = $this->db_readonly->select("CONCAT(ifnull(p.first_name,''), ' ', ifnull(p.last_name,'')) as guardian_2_name,  concat(add.Address_Line1, ' ', add.Address_Line2,' ', add.area,' ', add.district,' ', add.state,' ', add.pin_code) as guardian_2_address, high_quality_picture_url, picture_url, p.mobile_no")
					->from('student_relation sr')
					->where('sr.relation_type','Guardian_2')
					->join('parent p','sr.relation_id=p.id')
					->join('address_info add', 'add.stakeholder_id = p.id AND add.avatar_type = 2', 'left')
					->where('p.student_id',$val->sa_id)
					->get()->row();
					if (!empty($guardian_2)) {
						$val->guardian_2_name = $guardian_2->guardian_2_name;
						$val->guardian_2_contact = $guardian_2->mobile_no;
						$val->guardian_2_address = $guardian_2->guardian_2_address;
						if(!empty($guardian_2->picture_url)){
							$val->guardian_2_photo = $this->filemanager->getFilePath($guardian_2->picture_url);
						} else {
							$val->guardian_2_photo = '';
						}
					}else{
						$val->guardian_2_address = '';
						$val->guardian_2_photo = '';
						$val->guardian_2_name = '';
						$val->guardian_2_contact = '';
					}

					$driver = $this->db_readonly->select("CONCAT(ifnull(p.first_name,''), ' ', ifnull(p.last_name,'')) as driver_name,  concat(add.Address_Line1, ' ', add.Address_Line2,' ', add.area,' ', add.district,' ', add.state,' ', add.pin_code) as driver_address, high_quality_picture_url, picture_url, p.mobile_no")
					->from('student_relation sr')
					->where('sr.relation_type','Driver')
					->join('parent p','sr.relation_id=p.id')
					->join('address_info add', 'add.stakeholder_id = p.id AND add.avatar_type = 2', 'left')
					->where('p.student_id',$val->sa_id)
					->get()->row();
					if (!empty($driver)) {
						$val->driver_name = $driver->driver_name;
						$val->driver_contact = $driver->mobile_no;
						$val->driver_address = $driver->driver_address;
						if(!empty($driver->picture_url)){
							$val->driver_photo = $this->filemanager->getFilePath($driver->picture_url);
						} else {
							$val->driver_photo = '';
						}
					}else{
						$val->driver_address = '';
						$val->driver_photo = '';
						$val->driver_name = '';
						$val->driver_contact = '';
					}

					$driver_2 = $this->db_readonly->select("CONCAT(ifnull(p.first_name,''), ' ', ifnull(p.last_name,'')) as driver_2_name,  concat(add.Address_Line1, ' ', add.Address_Line2,' ', add.area,' ', add.district,' ', add.state,' ', add.pin_code) as driver_2_address, high_quality_picture_url, picture_url, p.mobile_no")
					->from('student_relation sr')
					->where('sr.relation_type','Driver_2')
					->join('parent p','sr.relation_id=p.id')
					->join('address_info add', 'add.stakeholder_id = p.id AND add.avatar_type = 2', 'left')
					->where('p.student_id',$val->sa_id)
					->get()->row();
					if (!empty($driver_2)) {
						$val->driver_2_name = $driver_2->driver_2_name;
						$val->driver_2_contact = $driver_2->mobile_no;
						$val->driver_2_address = $driver_2->driver_2_address;
						if(!empty($driver_2->picture_url)){
							$val->driver_2_photo = $this->filemanager->getFilePath($driver_2->picture_url);
						} else {
							$val->driver_2_photo = '';
						}
					}else{
						$val->driver_2_address = '';
						$val->driver_2_photo = '';
						$val->driver_2_name = '';
						$val->driver_2_contact = '';
					}

					$transport = $this->db_readonly->select(" MAX(CASE WHEN tsj.journey_type = 'PICKING' THEN substring_index(substring_index(tj.journey_name, ' ',2),' ', -2) END) AS picking_route,
  						MAX(CASE WHEN tsj.journey_type = 'DROPPING' THEN substring_index(substring_index(tj.journey_name, ' ',2),' ', -2) END) AS dropping_route")
					->from('tx_student_journeys tsj')
					->join('tx_journeys tj','tsj.journey_id = tj.id')
					->where('tsj.entity_type','Student')
					->where('tsj.entity_source_id',$val->sa_id)
					->get()->row();
					$val->picking_route = '';
					$val->dropping_route ='';
					if(!empty($transport)){
						$val->picking_route = $transport->picking_route;
						$val->dropping_route = $transport->dropping_route;
					}
				}

			}
			return $result;
		}

		public function aprrove_idcards_templates() {
			$input = $this->input->post();
// echo "<pre>"; print_r($input); die();
			$staff_id = $input['staffData'];
			$frontUrl = $input['frontUrl'];
			$backUrl  = $input['backUrl'];
			$type     = $input['type'];
			$order_id = $input['order_id'];
			$avatar   = $this->authorization->getAvatarStakeHolderId();

			// echo "<pre>"; print_r($staff_id);
			// echo "<pre>"; print_r($frontUrl);
			// echo "<pre>"; print_r($backUrl);
			// echo "<pre>"; print_r($type);
			// echo "<pre>"; print_r($order_id);

			//  echo "<pre>"; print_r($input); die();

			

			$data=array(
				"status"             => 'approved',
				"front_page_img_url" => $frontUrl,
				"back_page_img_url"  => $backUrl,
				"approved_by"        => $avatar,
				"approved_on"        => date('Y-m-d H:i:s')
			
			);
			$this->db->where('avatar_id', $staff_id);
			$this->db->where('idcard_template_order_id', $order_id);
			$this->db->where('avatar_type', $type);
			$update = $this->db->update('idcard_template_order_entities',$data);

			return $update ? true : false;
		}



		// public function downlaodcards($input) {
		// 	echo "<pre>"; print_r($input); die();

		// 	if ($page === 'front') {
		// 		$column = 'front_page';
		// 	} elseif ($page === 'back') {
		// 		$column = 'back_page';
		// 	} else {
		// 		return [];
		// 	}
		// 	$select = " $column as page";

		// 	$query = $this->db_readonly
		// 				  ->select($select)
		// 				  ->from('idcard_template_order_entities')
		// 				  ->where('idcard_template_order_id',$order_id)
		// 				  ->get()
		// 				  ->result();

		// 	return $query;
		// }

		public function downlaodIDcards_order_wise($input){
			$page= $input['page'];
			$order=$input['order_id'];
			if ($page === 'front') {
						$column = 'front_page_img_url';
						$oriented_column = 'front_oriented_img_url';
					} elseif ($page === 'back') {
						$column = 'back_page_img_url';
						$oriented_column = 'back_oriented_img_url';

					} else {
						return [];
					}
					$select = " $column as page, id as entity_id, $oriented_column";

					$query = $this->db_readonly
								  ->select($select)
								  ->from('idcard_template_order_entities')
								  ->where('idcard_template_order_id',$order)
								  ->where('status','approved')
								  ->get()
								  ->result();

					return $query;

		}


		public function get_order_status_counts($order_id) {
			$this->db_readonly->select("
				COUNT(*) as total,
				SUM(CASE WHEN status = 'in review' THEN 1 ELSE 0 END) as in_review,
				SUM(CASE WHEN status = 'approved' THEN 1 ELSE 0 END) as approved,
				SUM(CASE WHEN status = 'removed' THEN 1 ELSE 0 END) as removed,
				SUM(CASE WHEN status = 'modify' THEN 1 ELSE 0 END) as modify,
				SUM(CASE WHEN status = 're-ordered' THEN 1 ELSE 0 END) as re_ordered
			");
			$this->db_readonly->from('idcard_template_order_entities');
			$this->db_readonly->where('idcard_template_order_id', $order_id);
			$result = $this->db_readonly->get()->row();

			return $result;
		}



		public function get_update_order_status_counts($order_id,  $type, $entityId) {
			$this->db_readonly->select("
				COUNT(*) as total,
				SUM(CASE WHEN status = 'in review' THEN 1 ELSE 0 END) as in_review,
				SUM(CASE WHEN status = 'approved' THEN 1 ELSE 0 END) as approved,
				SUM(CASE WHEN status = 'removed' THEN 1 ELSE 0 END) as removed
			");
			$this->db_readonly->from('idcard_template_order_entities');
			$this->db_readonly->where('avatar_type', $type);
			$this->db_readonly->where('avatar_id', $entityId);
			$result = $this->db_readonly->get()->row();

			return $result;
		}


		public function getAllOrders(){
			$result = $this->db_readonly->select("id,status, order_name")
			->from('idcard_template_orders')
			->where('status !=','draft')
			->order_by('created_on', 'desc')
			->order_by('id', 'desc')
			->get()->result();

			return $result;

		}
		public function updateIdcardHtml($html, $order_id, $page_type){

			if($page_type == 'front'){
				$data = array(
					'front_page_pdf_html' => $html,
					'front_page_pdf_status' => 0,

				);
			}else{
				$data = array(
					'back_page_pdf_html' => $html,
					'back_page_pdf_status' => 0,
				);
			}


			$this->db->where('id', $order_id);
			$this->db->update('idcard_template_orders', $data);

			$insert_id = $this->db->insert_id();

			return array(
				'id' => $insert_id,
				'pdf_html' => $html
			);

		}

		public function updateGenertaedIdcardPath($path, $order_id, $page_type) {
			$this->db->where('id',$order_id);
			$name='';
			if($page_type == 'front'){
				$name='front_page_pdf_path';
			}else{
				$name='back_page_pdf_path';
			}
			return $this->db->update('idcard_template_orders', array( $name => $path));
		}



		public function update_pdf_status($id, $status) {
			$this->db->where('id', $id);
			return $this->db->update('idcard_pdf_generate', ['pdf_status' => $status]);
		}



		public function updateGenertaedIdcardPathLink($path, $status,$id) {
			$this->db->where('id', $id);
			return $this->db->update('idcard_pdf_generate', ['pdf_status' => $status]);

			// $page_name='';
			// $pdf_status='';
			// if($page_type == 'front'){
			// 	$page_name='front_page_pdf_path';
			// 	$pdf_status='front_page_pdf_status';

			// }else{
			// 	$page_name='back_page_pdf_path';
			// 	$pdf_status='back_page_pdf_status';
			// }

			// $this->db->where($page_name,$path);
			// return $this->db->update('idcard_template_orders', array($pdf_status => $status));
		}

		public function check_idcard_pdf_status($order_id, $page_type){
			if($page_type == 'front'){
				$path_name='front_page_pdf_path';
				$pdf_status='front_page_pdf_status';

			}else{
				$path_name='back_page_pdf_path';
				$pdf_status='back_page_pdf_status';
			}
			$result = $this->db_readonly->select("id,$path_name,$pdf_status")
							->from('idcard_template_orders')
							->where('id ',$order_id)
							->get()->row();
			return $result;
		}

		public function check_pdf_status($id) {
			$result = $this->db_readonly->select('pdf_status')
							->from('idcard_pdf_generate')
							->where('id', $id)
							->get()->row();

			if ($result && isset($result->pdf_status)) {
				return $result->pdf_status;
			}

			return 0;
		}

		public function save_generated_orientation_image_path($path, $entityId, $page_type){

			$this->db->where('id',$entityId);
			$name='';
			if($page_type == 'front'){
				$name='front_oriented_img_url';
			}else{
				$name='back_oriented_img_url';
			}
			return $this->db->update('idcard_template_order_entities', array( $name => $path));
		}

		public function updateIdcardpdfHtml($html, $order_id, $page_type) {
			$data = [
				'html' => $html,
				'order_id' => $order_id,
				'page_type' => $page_type,
				'pdf_status' => 0,
				'created_on' => $this->Idcards_model->Kolkata_datetime(),
				'created_by' => $this->authorization->getAvatarStakeHolderId()
			];

			$this->db->insert('idcard_pdf_generate', $data);
			$insert_id = $this->db->insert_id();

			if ($insert_id) {
				return [
					'id' => $insert_id,
					'pdf_html' => $html
				];
			}

			return false;
		}

		public function count_idcard_orders_by_status_for_class_students($status = null) {
			$this->db_readonly->select('COUNT(*) as total');
			$this->db_readonly->from('idcard_template_order_entities itoe');
			$this->db_readonly->join('idcard_template_orders ito', 'ito.id = itoe.idcard_template_order_id');
			$this->db_readonly->join('student_year sy', 'sy.student_admission_id = itoe.avatar_id AND sy.acad_year_id=' . $this->yearId);
			$this->db_readonly->join('class_section cs', 'cs.id = sy.class_section_id');
			$this->db_readonly->where('itoe.avatar_type', 'student');
			$this->db_readonly->where('ito.status !=','draft');
			$this->db_readonly->where('ito.id_card_type !=','Digital Card');
			$this->db_readonly->where('(
				JSON_CONTAINS(ito.id_card_verifier, \'\"by_school_admin\"\') 
				OR JSON_CONTAINS(ito.id_card_verifier, \'\"by_class_teacher\"\')
			)', null, false);

			$stakeholderId = $this->authorization->getAvatarStakeHolderId();
			$schoolAdmin = $this->authorization->isAuthorized('ID_CARDS.MANAGE_ID_CARD_ORDERS');
			if(!$schoolAdmin){
				if($stakeholderId != '0'){
					$this->db_readonly->group_start();
					$this->db_readonly->where('cs.class_teacher_id', $stakeholderId);
					$this->db_readonly->or_where('cs.assistant_class_teacher_id', $stakeholderId);
					$this->db_readonly->or_where('cs.assistant_class_teacher_2', $stakeholderId);
					$this->db_readonly->group_end();
				}
			}

			if (!is_null($status)) {
				$this->db_readonly->where('itoe.status', $status);
			}

			$query = $this->db_readonly->get();
			$result = $query->row();

			return $result ? $result->total : 0;
		}

		/**
		 * Get all entities for a specific order filtered by class teacher
		 *
		 * This function retrieves all entities for a specific order that are related to the
		 * logged-in class teacher, assistant teacher 1, or assistant teacher 2.
		 *
		 * @param int $order_id The ID of the order
		 * @param string $id_card_for The type of ID card (Staff or Student)
		 * @param string $status_filter Optional status filter (default: 'all')
		 * @return array Array of entity objects
		 */
		public function get_all_entities_for_the_specific_order_class_teacher($order_id, $id_card_for, $status_filter = 'all') {
			$stakeholderId = $this->authorization->getAvatarStakeHolderId();
				
			// For students, filter by class teacher
			$this->db_readonly->select("sa.id as sa_id, itoe.id, itoe.avatar_id, itoe.status, concat(ifnull(sa.first_name,''),' ', ifnull(sa.last_name,'')) as name, sa.admission_no, concat(ifnull(c.class_name,''), ' ' ,ifnull(cs.section_name,'')) as class_section, ifnull(date_format(sa.dob, '%d-%b-%Y'), '-') as dob, sa.preferred_contact_no, sa.blood_group, concat(add.Address_Line1, '', add.Address_Line2,'', add.area,'', add.district,'', add.state,'', add.pin_code) as address, sa.email, sy.picture_url, c.class_name as grade, cs.section_name as grade_section");
			$this->db_readonly->from('idcard_template_order_entities itoe');
			$this->db_readonly->join('student_admission sa','sa.id=itoe.avatar_id');
			$this->db_readonly->join('student_year sy','sy.student_admission_id=sa.id and sy.acad_year_id='.$this->yearId);
			$this->db_readonly->join('class_section cs','sy.class_section_id=cs.id','left');
			$this->db_readonly->join('class c','sy.class_id=c.id');
			$this->db_readonly->join('address_info add','add.stakeholder_id=sa.id and add.avatar_type = 1 and add.address_type = 0','left');
			$this->db_readonly->where('itoe.idcard_template_order_id', $order_id);

			// Filter by class teacher roles
			$this->db_readonly->group_start();
			$this->db_readonly->where('cs.class_teacher_id', $stakeholderId);
			$this->db_readonly->or_where('cs.assistant_class_teacher_id', $stakeholderId);
			$this->db_readonly->or_where('cs.assistant_class_teacher_2', $stakeholderId);
			$this->db_readonly->group_end();

			// Add status filter if provided
			if ($status_filter && $status_filter !== 'all') {
				$this->db_readonly->where('itoe.status', $status_filter);
			}

			$result = $this->db_readonly->get()->result();
			foreach ($result as $key => $val) {
				if(!empty($val->picture_url)){
					$val->picture_url = $this->filemanager->getFilePath($val->picture_url);
				} else {
					$val->picture_url = '';
				}

				$father = $this->db_readonly->select("CONCAT(ifnull(p.first_name,''), ifnull(p.last_name,'')) as father_name, concat(add.Address_Line1, '', add.Address_Line2,'', add.area,'', add.district,'', add.state,'', add.pin_code) as father_address, high_quality_picture_url, p.mobile_no")
				->from('student_relation sr')
				->where('sr.relation_type','Father')
				->join('parent p','sr.relation_id=p.id')
				->join('address_info add', 'add.stakeholder_id = p.id AND add.avatar_type = 2', 'left')
				->where('p.student_id',$val->sa_id)
				->get()->row();

				if (!empty($father)) {
					$val->father_name = $father->father_name;
					$val->father_contact = $father->mobile_no;
					if(!empty($father->high_quality_picture_url)){
						$val->father_photo = $this->filemanager->getFilePath($father->high_quality_picture_url);
					} else {
						$val->father_photo = '';
					}
				} else {
					$val->father_address = '';
					$val->father_photo = '';
					$val->father_name = '';
					$val->father_contact = '';
				}

				$mother = $this->db_readonly->select("CONCAT(ifnull(p.first_name,''), ifnull(p.last_name,'')) as mother_name, concat(add.Address_Line1, '', add.Address_Line2,'', add.area,'', add.district,'', add.state,'', add.pin_code) as mother_address, high_quality_picture_url, p.mobile_no")
				->from('student_relation sr')
				->where('sr.relation_type','Mother')
				->join('parent p','sr.relation_id=p.id')
				->join('address_info add', 'add.stakeholder_id = p.id AND add.avatar_type = 2', 'left')
				->where('p.student_id',$val->sa_id)
				->get()->row();

				if (!empty($mother)) {
					$val->mother_name = $mother->mother_name;
					$val->mother_contact = $mother->mobile_no;
					if(!empty($mother->high_quality_picture_url)){
						$val->mother_photo = $this->filemanager->getFilePath($mother->high_quality_picture_url);
					} else {
						$val->mother_photo = '';
					}
				} else {
					$val->mother_address = '';
					$val->mother_photo = '';
					$val->mother_name = '';
					$val->mother_contact = '';
				}
			}
			return $result;
		}

		public function get_class_teacher_wise_idcards() {
			// Get stakeholder ID
			$stakeholderId = $this->authorization->getAvatarStakeHolderId();
			$schoolAdmin = $this->authorization->isAuthorized('ID_CARDS.MANAGE_ID_CARD_ORDERS');

			// First, get all student-related orders where the teacher is assigned
			$this->db_readonly->distinct();
			$this->db_readonly->select("ito.id");
			$this->db_readonly->from('idcard_template_orders ito');
			$this->db_readonly->join('idcard_template_order_entities itoe', 'ito.id = itoe.idcard_template_order_id');
			$this->db_readonly->where('itoe.avatar_type', 'student');
			$this->db_readonly->where('ito.status !=', 'draft');
			$this->db_readonly->join('student_year sy', 'sy.student_admission_id = itoe.avatar_id AND sy.acad_year_id = '.$this->yearId);
			$this->db_readonly->join('class_section cs', 'cs.id = sy.class_section_id');
			$this->db_readonly->where('(
				JSON_CONTAINS(ito.id_card_verifier, \'\"by_school_admin\"\') 
				OR JSON_CONTAINS(ito.id_card_verifier, \'\"by_class_teacher\"\')
			)', null, false);
			// Filter by class teacher roles
			if(!$schoolAdmin){
				if($stakeholderId != '0'){
					$this->db_readonly->group_start();
					$this->db_readonly->where('cs.class_teacher_id', $stakeholderId);
					$this->db_readonly->or_where('cs.assistant_class_teacher_id', $stakeholderId);
					$this->db_readonly->or_where('cs.assistant_class_teacher_2', $stakeholderId);
					$this->db_readonly->group_end();
				}
			}
			// Execute the query directly instead of using get_compiled_select
			$studentOrdersResult = $this->db_readonly->get()->result();
			// Check if we have any results
			if (empty($studentOrdersResult)) {
				// No orders found for this teacher's classes
				return array();
			}

			// Extract order IDs
			$orderIds = array();
			foreach ($studentOrdersResult as $order) {
				$orderIds[] = $order->id;
			}

			// If we have no order IDs, return empty array
			if (empty($orderIds)) {
				return array();
			}

			// Now get the actual order data with additional class teacher filtering
			$this->db_readonly->distinct();
			$this->db_readonly->select("ito.id, ito.id_card_for, ito.id_card_type, ito.created_by,
				DATE_FORMAT(ito.created_on, '%d-%b-%Y') as created_on, ito.quantity,
				ito.sub_total, ito.grand_total, ito.s_gst, ito.c_gst, ito.status, ito.order_name");
			$this->db_readonly->from('idcard_template_orders ito');
			$this->db_readonly->where_in('ito.id', $orderIds);
			$this->db_readonly->order_by('ito.created_on', 'desc');
			$this->db_readonly->order_by('ito.id', 'desc');
			$orderData = $this->db_readonly->get()->result();

			if (!$orderData) {
				return array();
			}

			// Collect created_by IDs (we already have order IDs)
			$createdByIds = array();
			foreach ($orderData as $order) {
				if (!in_array($order->created_by, $createdByIds)) {
					$createdByIds[] = $order->created_by;
				}
			}

			// Fetch approved counts and total class teacher related counts in a single query
			$approvedCounts = array();
			$teacherTotalCounts = array();

			if (!empty($orderIds)) {
				// Get approved counts
				$this->db_readonly->select('itoe.idcard_template_order_id, COUNT(*) as approved_count');
				$this->db_readonly->from('idcard_template_order_entities itoe');
				$this->db_readonly->join('student_year sy', 'sy.student_admission_id = itoe.avatar_id AND itoe.avatar_type = "student" AND sy.acad_year_id = '.$this->yearId, 'left');
				$this->db_readonly->join('class_section cs', 'cs.id = sy.class_section_id', 'left');
				$this->db_readonly->where_in('idcard_template_order_id', $orderIds);
				$this->db_readonly->where('status', 'approved');
				if(!$schoolAdmin){
					if($stakeholderId != '0'){
						$this->db_readonly->group_start();
						$this->db_readonly->where('cs.class_teacher_id', $stakeholderId);
						$this->db_readonly->or_where('cs.assistant_class_teacher_id', $stakeholderId);
						$this->db_readonly->or_where('cs.assistant_class_teacher_2', $stakeholderId);
						$this->db_readonly->group_end();
					}
				}
				$this->db_readonly->group_by('idcard_template_order_id');
				$approvedCountsResult = $this->db_readonly->get()->result();

				foreach ($approvedCountsResult as $row) {
					$approvedCounts[$row->idcard_template_order_id] = $row->approved_count;
				}

				// Get total class teacher related counts
				$this->db_readonly->select('itoe.idcard_template_order_id, COUNT(DISTINCT itoe.id) as teacher_total_count');
				$this->db_readonly->from('idcard_template_order_entities itoe');
				$this->db_readonly->join('student_year sy', 'sy.student_admission_id = itoe.avatar_id AND itoe.avatar_type = "student" AND sy.acad_year_id = '.$this->yearId, 'left');
				$this->db_readonly->join('class_section cs', 'cs.id = sy.class_section_id', 'left');
				$this->db_readonly->where_in('itoe.status', array('in review', 'approved'));
				$this->db_readonly->where_in('itoe.idcard_template_order_id', $orderIds);

				// Filter by class teacher roles
				if(!$schoolAdmin){

					if($stakeholderId != '0'){
						$this->db_readonly->group_start();
						$this->db_readonly->where('cs.class_teacher_id', $stakeholderId);
						$this->db_readonly->or_where('cs.assistant_class_teacher_id', $stakeholderId);
						$this->db_readonly->or_where('cs.assistant_class_teacher_2', $stakeholderId);
						$this->db_readonly->group_end();
					}
				}
				$this->db_readonly->group_by('itoe.idcard_template_order_id');
				$teacherTotalCountsResult = $this->db_readonly->get()->result();

				foreach ($teacherTotalCountsResult as $row) {
					$teacherTotalCounts[$row->idcard_template_order_id] = $row->teacher_total_count;
				}
			}

			// Map created_by IDs to names
			$createdByNames = array();
			foreach ($createdByIds as $uid) {
				$createdByNames[$uid] = $this->_getAvatarNameById($uid);
			}

			// Attach approved count, teacher total count, and creator name to each order
			foreach ($orderData as &$order) {
				$order->approved_count = isset($approvedCounts[$order->id]) ? $approvedCounts[$order->id] : 0;
				$order->teacher_total_count = isset($teacherTotalCounts[$order->id]) ? $teacherTotalCounts[$order->id] : 0;
				$order->created_by = isset($createdByNames[$order->created_by]) ? $createdByNames[$order->created_by] : $order->created_by;
			}
			return $orderData;
		}

		public function status_counts_class_teacher_wise($order_id) {
				// Get stakeholder ID
				$stakeholderId = $this->authorization->getAvatarStakeHolderId();

				// Select counts with status breakdown
				$this->db_readonly->distinct();
				$this->db_readonly->select("
					COUNT(itoe.id) as total,
					SUM(CASE WHEN itoe.status = 'in review' THEN 1 ELSE 0 END) as in_review,
					SUM(CASE WHEN itoe.status = 'approved' THEN 1 ELSE 0 END) as approved,
					SUM(CASE WHEN itoe.status = 'removed' THEN 1 ELSE 0 END) as removed
				");

				// Join necessary tables to filter by class teacher
				$this->db_readonly->from('idcard_template_order_entities itoe');
				$this->db_readonly->join('student_year sy', 'sy.student_admission_id = itoe.avatar_id AND itoe.avatar_type = "student" AND sy.acad_year_id = '.$this->yearId, 'left');
				$this->db_readonly->join('class_section cs', 'cs.id = sy.class_section_id', 'left');

				// Filter by order ID
				$this->db_readonly->where('itoe.idcard_template_order_id', $order_id);

				// Filter by class teacher roles
				if($stakeholderId != '0'){
					$this->db_readonly->group_start();
					$this->db_readonly->where('cs.class_teacher_id', $stakeholderId);
					$this->db_readonly->or_where('cs.assistant_class_teacher_id', $stakeholderId);
					$this->db_readonly->or_where('cs.assistant_class_teacher_2', $stakeholderId);
					$this->db_readonly->group_end();
				}

				$result = $this->db_readonly->get()->row();

				// If no results, return empty counts
				if (!$result) {
					return (object) [
						'total' => 0,
						'in_review' => 0,
						'approved' => 0,
						'removed' => 0
					];
				}

				return $result;
		}

		public function getStudentDetails($studentId){
			$this->db_readonly->select("sa.id as sa_id,concat(ifnull(sa.first_name,''),' ', ifnull(sa.last_name,'')) as name,sa.admission_no,concat(ifnull(c.class_name,''), ' ' ,ifnull(cs.section_name,'')) as class_section, concat(ifnull(c.class_name,''), ' ' ,ifnull(cs.section_name,'')) as grade_section,ifnull(date_format(sa.dob, '%d-%b-%Y'), '-') as dob, ifnull(date_format(sa.dob, '%d-%b-%Y'), '-') as date_of_birth, sa.preferred_contact_no, sa.preferred_contact_no as phone, sa.blood_group,concat(add.Address_Line1, ' ', add.Address_Line2,' ', add.area,' ', add.district,' ', add.state,' ', add.pin_code) as address,sa.email,sy.picture_url, c.class_name as grade, itoe.status,sy.combination, ifnull(sa.enrollment_number,'') as enrollment_no, ifnull(sy.alpha_rollnum,'') as alpha_rollnum,itoe.avatar_type, sa.identification_code as qr_code, concat(ifnull(sa.first_name,''),' ', ifnull(sa.last_name,''), '-',ifnull(c.class_name,'')) as name_class, itoe.avatar_type as relation_type, itoe.idcard_template_order_id, sa.point_of_contact,ito.id_card_verifier");
				$this->db_readonly->from('idcard_template_order_entities itoe');
				$this->db_readonly->join('idcard_template_orders ito','itoe.idcard_template_order_id= ito.id');
				$this->db_readonly->join('student_admission sa','sa.id=itoe.avatar_id');
				$this->db_readonly->join('student_year sy','sy.student_admission_id=sa.id and sy.acad_year_id='.$this->yearId);
				$this->db_readonly->join('class_section cs','sy.class_section_id=cs.id','left');
				$this->db_readonly->join('class c','sy.class_id=c.id');
				$this->db_readonly->join('address_info add','add.stakeholder_id=sa.id and add.avatar_type = 1 and add.address_type = 0','left');
				$this->db_readonly->where('itoe.avatar_id', $studentId);
				$this->db_readonly->where('itoe.avatar_type','Student');
				$this->db_readonly->order_by('itoe.idcard_template_order_id', 'DESC');
				$this->db_readonly->limit(1);
				// $this->db_readonly->where('itoe.status', 'in review');

				$result= $this->db_readonly->get()->result();
				foreach ($result as $key => $val) {
					if(!empty($val->picture_url)){
						$val->picture_url = $this->filemanager->getFilePath($val->picture_url);
					} else {
						$val->picture_url = '';
					}

					$father = $this->db_readonly->select("CONCAT(ifnull(p.first_name,''), ' ', ifnull(p.last_name,'')) as father_name,  concat(add.Address_Line1, '', add.Address_Line2,'', add.area,'', add.district,'', add.state,'', add.pin_code) as father_address, high_quality_picture_url, p.mobile_no")
					->from('student_relation sr')
					->where('sr.relation_type','Father')
					->join('parent p','sr.relation_id=p.id')
					->join('address_info add', 'add.stakeholder_id = p.id AND add.avatar_type = 2', 'left')
					->where('p.student_id',$val->sa_id)
					->get()->row();
					if (!empty($father)) {
						$val->father_name = $father->father_name;
						$val->father_contact = $father->mobile_no;
						$val->father_address = $father->father_address;
						if(!empty($father->high_quality_picture_url)){
							$val->father_photo = $this->filemanager->getFilePath($father->high_quality_picture_url);
						} else {
							$val->father_photo = '';
						}
					}else{
						$val->father_address = '';
						$val->father_photo = '';
						$val->father_name = '';
						$val->father_contact = '';
					}
					$mother = $this->db_readonly->select("CONCAT(ifnull(p.first_name,''), ' ', ifnull(p.last_name,'')) as mother_name,concat(add.Address_Line1, '', add.Address_Line2,'', add.area,'', add.district,'', add.state,'', add.pin_code) as mother_address, high_quality_picture_url, p.mobile_no")
					->from('student_relation sr')
					->where('sr.relation_type','Mother')
					->join('parent p','sr.relation_id=p.id')
					->join('address_info add', 'add.stakeholder_id = p.id AND add.avatar_type = 2', 'left')
					->where('p.student_id',$val->sa_id)
					->get()->row();

					if (!empty($mother)) {
						$val->mother_name = $mother->mother_name;
						$val->mother_contact = $mother->mobile_no;
						$val->mother_address = $mother->mother_address;
						if(!empty($mother->high_quality_picture_url)){
							$val->mother_photo = $this->filemanager->getFilePath($mother->high_quality_picture_url);
						} else {
							$val->mother_photo = '';
						}
					}else{
						$val->mother_address = '';
						$val->mother_photo = '';
						$val->mother_name = '';
						$val->mother_contact = '';
					}

					$pointOfContact = strtolower(trim($val->point_of_contact));
					if($pointOfContact != ''){
						if ($pointOfContact === 'father') {
							$val->parent_name = $val->father_name;
							$val->contact = $val->father_contact;
						} elseif ($pointOfContact === 'mother') {
							$val->parent_name = $val->mother_name;
							$val->contact = $val->mother_contact;
						}
					}else{
						$val->parent_name = $val->father_name;
						$val->contact = $val->father_contact . ',' . $val->mother_contact;
					}
				}
				// echo "<pre>"; print_r($result); die();
				return $result;

		}

		public function getTempaltesDetails($orderID){
				$this->db_readonly->select('idcard_template_id');
				$this->db_readonly->from('idcard_template_orders');
				$this->db_readonly->where('id', $orderID);
				$orderDetails = $this->db_readonly->get()->row();

				if (!$orderDetails || !$orderDetails->idcard_template_id) {
					// Return a default object with the order_id and null template_id
					$defaultDetails = new stdClass();
					$defaultDetails->order_id = $orderID;
					$defaultDetails->idcard_template_id = null;
					return $defaultDetails;
				}

				$this->db_readonly->select('id, front_design, back_design');
				$this->db_readonly->from('idcard_template');
				$this->db_readonly->where('id', $orderDetails->idcard_template_id);
				$this->db_readonly->where('id_card_for', 'Student');
				$tempalteDetails = $this->db_readonly->get()->row();

				if (!$tempalteDetails) {
					// Return a default object with the order_id and template_id
					$defaultDetails = new stdClass();
					$defaultDetails->order_id = $orderID;
					$defaultDetails->idcard_template_id = $orderDetails->idcard_template_id;
					return $defaultDetails;
				}

				// Add the order_id and template_id to the template details
				$tempalteDetails->order_id = $orderID;
				$tempalteDetails->idcard_template_id = $orderDetails->idcard_template_id;

				return $tempalteDetails;
		}

		public function getProfileCompletionStatus($studentId){
				$this->db_readonly->select('id, profile_confirmed');
				$this->db_readonly->from('student_year');
				$this->db_readonly->where('student_admission_id', $studentId);
				$this->db_readonly->where('acad_year_id', $this->yearId);
				$status = $this->db_readonly->get()->row();

				return $status;

		}

		public function get_active_quantity_of_idacrds($order_id){
			$this->db_readonly->select('count(*) as active_ids');
			$this->db_readonly->from('idcard_template_order_entities');
			$this->db_readonly->where('idcard_template_order_id', $order_id);
			$this->db_readonly->group_start();
			$this->db_readonly->or_where('status', 'approved');
			$this->db_readonly->or_where('status', 'in review');
			$this->db_readonly->group_end();
			$active_ids = $this->db_readonly->get()->row()->active_ids;
			return $active_ids;
		}

		public function update_payment_status_post($order_id,$payment_status){
			$data = array(
				'payment_status' => $payment_status
			);
			$this->db->where('id', $order_id);
			return $this->db->update('idcard_template_orders', $data);
		}

		public function update_in_printing_status_post($order_id,$status,$estimated_time_of_completion_for_printing){
			$data = array(
				'status' => $status,
				'payment_status' => 'payment_complete',
				'estimated_time_of_completion_for_printing' => $estimated_time_of_completion_for_printing
			);
			$this->db->where('id', $order_id);
			return $this->db->update('idcard_template_orders', $data);
		}

		public function update_in_delivery_status_post($order_id,$status,$estimated_time_of_completion_for_delivery){
			$data = array(
				'status' => $status,
				'estimated_time_of_completion_for_delivery' => $estimated_time_of_completion_for_delivery
			);
			$this->db->where('id', $order_id);
			return $this->db->update('idcard_template_orders', $data);
		}

		public function update_delivered_status_post($order_id,$status){
			$data = array(
				'status' => $status,
			);
			$this->db->where('id', $order_id);
			return $this->db->update('idcard_template_orders', $data);
		}

		public function update_admin_idcard_table__($school_id = null, $school_code = null) {
			// Fetch individual and admin orders
			$this->db_readonly->select('id');
			$this->db_readonly->from('idcard_template_orders');
			$individual_school_idcard_orders = $this->db_readonly->get()->result();
		
			$this->db_readonly->select('order_id');
			$this->db_readonly->from('iisb.idcards_admin_console_order_master');
			$admin_idcard_orders = $this->db_readonly->get()->result();
		
			// Convert to arrays using older syntax
			$individual_ids = array_map(function($row) {
				return $row->id;
			}, $individual_school_idcard_orders);
		
			$admin_order_ids = array_map(function($row) {
				return $row->order_id;
			}, $admin_idcard_orders);
		
			// Find new orders
			$only_in_individual = array_diff($individual_ids, $admin_order_ids);
		
			if (empty($only_in_individual)) {
				return 0; // Nothing to insert
			}
		
			// Fetch only new orders
			$this->db_readonly->select('id, order_name, id_card_type, id_card_for, status, quantity, created_by');
			$this->db_readonly->from('idcard_template_orders');
			$this->db_readonly->where_in('id', $only_in_individual);
			$order_data = $this->db_readonly->get()->result();
		
			$insert_batch = array();
			foreach ($order_data as $order) {
				$insert_batch[] = array(
					'school_subdomain' => $school_code,
					'order_id' => $order->id,
					'order_name' => $order->order_name,
					'id_card_type' => $order->id_card_for,
					'order_status' => ucwords(strtolower($order->status)),
					'num_of_cards' => $order->quantity,
					'created_by' => $order->created_by,
					'payment_status' => 'not_started',
					'msm_school_list_id' => $school_id
				);
			}
		
			if (!empty($insert_batch)) {
				$this->db->trans_start();
				$this->db->insert_batch('iisb.idcards_admin_console_order_master', $insert_batch);
				$this->db->trans_complete();
		
				if ($this->db->trans_status() === FALSE) {
					log_message('error', 'Batch insert failed for idcards_admin_console_order_master');
				}
			}
		
			return 1;
		}

		public function get_submitted_idcards_orders($school_id = null, $school_code = null) {


			$this->db_readonly->select('id, order_name, id_card_type, id_card_for, status, quantity, created_by');
			$this->db_readonly->from('idcard_template_orders');
			$this->db_readonly->where('status !=','draft');
			$order_data = $this->db_readonly->get()->result();
		
			$batch = array();
			foreach ($order_data as $order) {
				$batch[] = array(
					'school_subdomain' => $school_code,
					'order_id' => $order->id,
					'order_name' => $order->order_name,
					'id_card_type' => $order->id_card_for,
					'order_status' => ucwords(strtolower($order->status)),
					'num_of_cards' => $order->quantity,
					'created_by' => $order->created_by,
					'payment_status' => 'not_started',
					'msm_school_list_id' => $school_id
				);
			}

			return $batch;
		}
		
		public  function getParentDetails($studentId){
			// Get the stakeholder ID of the logged-in user
			$stakeholderId = $this->authorization->getAvatarStakeHolderId();

			// First get the parent ID from student relation table
			$parent = $this->db_readonly->select("p.id, CONCAT(ifnull(p.first_name,''),' ',ifnull(p.last_name,'')) as name, 
				p.email, p.mobile_no, p.occupation, p.designation, p.company,
				p.qualification, p.blood_group, p.aadhar_no, p.picture_url,
				sr.relation_type,
				sa.id as student_id, CONCAT(ifnull(sa.first_name,''),' ',ifnull(sa.last_name,'')) as student_name,
				cs.class_name, cs.section_name,
				a.Address_line1, a.Address_line2, a.area, a.district, a.state, a.country, a.pin_code")
				->from('student_relation sr')
				->join('parent p', 'sr.relation_id = p.id')
				->join('student_admission sa', 'sr.std_id = sa.id')
				->join('student_year sy', 'sa.id = sy.student_admission_id')
				->join('class_section cs', 'sy.class_section_id = cs.id')
				->join('address_info a', 'p.id = a.stakeholder_id AND a.avatar_type = 2', 'left')
				->where('sr.std_id', $studentId)
				->where('p.id', $stakeholderId) // Verify stakeholder ID matches
				->where('sy.acad_year_id', $this->yearId)
				->where('sa.admission_status', 2)
				->where('sy.promotion_status !=', 4)
				->where('sy.promotion_status !=', 5)
				->get()->row();

			if($parent && !empty($parent->picture_url)) {
				$parent->picture_url = $this->filemanager->getFilePath($parent->picture_url);
			}

			return $parent;
		}
		
		function get_avatar_issued_idcards(){
			$input=$this->input->post();
			$idCardFor=strtolower($input['idCardFor']);
			
			$this->db_readonly->distinct();
			$this->db_readonly->select('avatar_id');
			$this->db_readonly->from('idcard_template_order_entities');
			$this->db_readonly->where('avatar_type',$idCardFor);
			$result=$this->db_readonly->get()->result_array();

			$avatarIds = array_column($result, 'avatar_id');
			return $avatarIds;
		}

		public function getIdCard($parentId){
			$this->db_readonly->select('front_page_img_url,back_page_img_url');
			$this->db_readonly->from('idcard_template_order_entities');
			$this->db_readonly->where('avatar_id',$parentId);
			return $this->db_readonly->get()->result();
		}

		public function getParentIdCards($studentId){
			$this->db_readonly->select('id,avatar_type,front_page_img_url,back_page_img_url');
			$this->db_readonly->from('idcard_template_order_entities');
			$this->db_readonly->where('avatar_id',$studentId);
			$this->db_readonly->where('is_active',1);
			$data= $this->db_readonly->get()->result();
			foreach($data as $key => $val){
				$val->front_page_img_url = $this->filemanager->getFilePath($val->front_page_img_url);
				if(!empty($val->back_page_img_url)){
				$val->back_page_img_url = $this->filemanager->getFilePath($val->back_page_img_url);
				}
			}
			return $data;
		}

		public function download_idcard_imageby_id($entity_id, $type){
			$this->db_readonly->select('id,avatar_type,front_page_img_url,back_page_img_url');
			$this->db_readonly->from('idcard_template_order_entities');
			$this->db_readonly->where('id',$entity_id);
			$data = $this->db_readonly->get()->row();
			$imageUrl = '';
			if(!empty($data)){
				if($type == 'front'){
					$imageUrl =  $data->front_page_img_url;
				}else{
					$imageUrl = $data->back_page_img_url;
				}
			}
			return $imageUrl;
			
		}


	}


?>