<?php
class Itari_Admission_Model extends CI_Model
{
    public function Kolkata_datetime(){
        $timezone = new DateTimeZone("Asia/Kolkata" );
        $date = new DateTime();
        $date->setTimezone($timezone );
        $dtobj = $date->format('Y-m-d H:i:s');
        return $dtobj;
    }
    public function insert_itari_admissions_personal_details($input,$photo_path, $academic_year_applied_for) {
        $this->db->trans_start();
        $name = $this->input->post('name');
        $lname = $this->input->post('lname');
        $title = $this->input->post("title");
        $id = $this->input->post("id");
        $full_name = $title." ".$name . " " . $lname;
        $data = array(
            'name' => $name,
            'gender' => ($this->input->post("gender"))? $this->input->post("gender"):'',
            'dob' => ($this->input->post("dob"))? $this->input->post("dob") :'',
            'present_address' => ($this->input->post("present_add"))? $this->input->post("present_add") :'',
            'present_city' => ($this->input->post("present_city"))? $this->input->post("present_city") :'',
            'present_state' => ($this->input->post("present_state"))? $this->input->post("present_state") :'',
            'present_country' => ($this->input->post("present_country"))?$this->input->post("present_country") :'',
            'present_pincode' => ($this->input->post("present_pincode"))? $this->input->post("present_pincode"):'',
            'permanent_address' => ($this->input->post("permanent_add"))?$this->input->post("permanent_add") :'',
            'permanent_city' => ($this->input->post("permanent_city"))? $this->input->post("permanent_city"):'',
            'permanent_state' => ($this->input->post("permanent_state"))?$this->input->post("permanent_state") :'',
            'permanent_country' => ($this->input->post("permanent_country"))? $this->input->post("permanent_country"):'',
            'permanent_pincode' => ($this->input->post("permanent_pincode"))? $this->input->post("permanent_pincode"):'',
            'phone_num' => ($this->input->post("num"))? $this->input->post("num"):'',
            'alternate_num' => ($this->input->post("altnum"))? $this->input->post("altnum"):'',
            'email' => ($this->input->post("email"))? $this->input->post("email"):'',
            'course' => ($this->input->post("course_select"))? $this->input->post("course_select"):'',
            'created_date' => $this->Kolkata_datetime(),
            'academic_year_applied_for' => $academic_year_applied_for
        );
        if ($photo_path['file_name'] != '') {
           $data = array_merge($data, array('photo'=>$photo_path['file_name']));
        }
        if ($id) {
            // Update existing record
            $this->db->where('id', $id);
            $this->db->update('itari_admissions_forms', $data);
        } else {
            // Insert new record
            $this->db->insert('itari_admissions_forms', $data);
            $id = $this->db->insert_id();
        }
        $this->db->trans_complete();
        if ($this->db->trans_status()) {
            return $id;
        }
        return false;
    }
    public function insert_itari_admissions_additional_academic_details($input){
        $degree = $this->input->post('degree');
        $type = $this->input->post('type');
        $college = $this->input->post('college');
        $university = $this->input->post('uni');
        $start_date = $this->input->post('start_date');
        $end_date = $this->input->post('end_date');
        $subject = $this->input->post('sub');
        $gpa = $this->input->post('gpa');
        $itari_adm_id = $this->input->post('itari_admission_id');
        $id = $this->input->post('id');
        $this->db->trans_start(); 
        if($id){
            $data = array(
            'itari_adm_id' => isset($itari_adm_id)? $itari_adm_id: '',
            'degree_type' => isset($type)? $type: '',
            'degree_name' => isset($degree)? $degree: '',
            'college_name' => isset($college)? $college: '',
            'university_name' => isset($university)? $university: '',
            'start_date' => isset($start_date)? $start_date: '',
            'end_date' => isset($end_date)? $end_date: '',
            'subjects' => isset($subject)? $subject: '',
            'gpa' => isset($gpa)? $gpa: ''
            );
            $this->db->where('id', $id);
            $this->db->update('itari_admissions_academic_information', $data);
        }
        else{
        // Insert new record
            $data = array(
                'itari_adm_id' => isset($itari_adm_id)? $itari_adm_id: '',
                'degree_type' => isset($type)? $type: '',
                'degree_name' => isset($degree)? $degree: '',
                'college_name' => isset($college)? $college: '',
                'university_name' => isset($university)? $university: '',
                'start_date' => isset($start_date)? $start_date: '',
                'end_date' => isset($end_date)? $end_date: '',
                'subjects' => isset($subject)? $subject: '',
                'gpa' => isset($gpa)? $gpa: ''
            );
            $this->db->insert('itari_admissions_academic_information', $data);
            $id = $this->db->insert_id();
        }
        $this->db->trans_complete();
        if ($this->db->trans_status()) {
            return $id;
        }
        return false;
    }
    public function insert_itari_admissions_employment_details($input){
        $exp = $this->input->post('exp');
        $name = $this->input->post('emp');
        $start = $this->input->post('start_date');
        $end = $this->input->post('end_date');
        $role = $this->input->post('role');
        $add = $this->input->post('add');
        $itari_adm_id = $this->input->post('itari_admission_id');
        $id = $this->input->post('id');
        $this->db->trans_start(); 
        $data = array(
            'itari_adm_id' => isset($itari_adm_id) ? $itari_adm_id: '',
            'experience' => isset($exp) ? $exp: '',
            'name_of_instituition' => isset($name) ? $name: '',
            'start_date' => isset($start) ? $start: '',
            'end_date' => isset($end) ? $end: '',
            'roles_resp' => isset($role) ? $role: '',
            'address' => isset($add) ? $add: '',
            'type' => 'cur'
        );
        if ($id) {
        // Update existing record
            $this->db->where('itari_adm_id', $itari_adm_id);
            $this->db->where('type', 'cur');
            $this->db->update('itari_admissions_employment_information', $data);
        }
        else {
        // Insert new record
            $this->db->insert('itari_admissions_employment_information', $data);
            $id = $this->db->insert_id();
        }
        $this->db->trans_complete();
        if ($this->db->trans_status()) {
            return $id;
        }
        return false;
    }
    public function insert_itari_admissions_additional_employment_details($input){
        $exp = $this->input->post('exp');
        $name = $this->input->post('emp');
        $start = $this->input->post('start_date');
        $end = $this->input->post('end_date');
        $role = $this->input->post('role');
        $add = $this->input->post('add');
        $itari_adm_id = $this->input->post('itari_admission_id');
        $id = $this->input->post('id');
        $this->db->trans_start(); 
        if($id){
            $data = array(
            'itari_adm_id' => isset($itari_adm_id) ? $itari_adm_id:'',
            'experience' => isset($exp) ? $exp:'',
            'name_of_instituition' => isset($name) ? $name:'',
            'start_date' => isset($start) ? $start:'',
            'end_date' => isset($end) ? $end:'',
            'roles_resp' => isset($role) ? $role:'',
            'address' => isset($add) ? $add:'',
            'type' =>'pre'
            );
            $this->db->where('id', $id);
            $this->db->update('itari_admissions_employment_information', $data);
        }
        else{
        // Insert new record
            $data = array(
            'itari_adm_id' => isset($itari_adm_id) ? $itari_adm_id:'',
            'experience' => isset($exp) ? $exp:'',
            'name_of_instituition' => isset($name) ? $name:'',
            'start_date' => isset($start) ? $start:'',
            'end_date' => isset($end) ? $end:'',
            'roles_resp' => isset($role) ? $role:'',
            'address' => isset($add) ? $add:'',
            'type' =>'pre'
            );
            $this->db->insert('itari_admissions_employment_information', $data);
            $id = $this->db->insert_id();
        }
        $this->db->trans_complete();
        if ($this->db->trans_status()) {
            return $id;
        }
        return false;
    }
    public function insert_itari_admissions_references_information($input,$essay_path){
        $refname = $this->input->post('refname');
        $refphn = $this->input->post('refphn');
        $refemail = $this->input->post('refemail');
        $refcap = $this->input->post('refcap');
        $itari_adm_id = $this->input->post('itari_admission_id');
        $id = $this->input->post('id');
        $refid = $this->input->post('refid');
        $rows = $this->input->post('rows');
        $reportPath = '';
        if ($essay_path['file_name'] != '') {
            $reportPath = $essay_path['file_name'];
        }
        $this->db->trans_start(); 
        if ($id || (array_sum($refid) > 0)) {
            for ($i = 0; $i < count($refname); $i++) {
                if ($refname[$i] != '') {
                    $data = array(
                        'itari_adm_id' => isset($itari_adm_id) ? $itari_adm_id : '',
                        'name' => isset($refname[$i]) ? $refname[$i] : '',
                        'phone_num' => isset($refphn[$i]) ? $refphn[$i] : '',
                        'email' => isset($refemail[$i]) ? $refemail[$i] : '',
                        'capacity' => isset($refcap[$i]) ? $refcap[$i] : ''
                    );
                    if (isset($reportPath) && $reportPath !== '') {
                        $data['essay'] = $reportPath;
                    }
                    $this->db->where('id', $refid[$i]);
                    $this->db->update('itari_admissions_references_information', $data);
                }
            }            
        }
        else{
            for ($i = 0; $i < count($refname); $i++) {
                $data = array(
                'itari_adm_id' => isset($itari_adm_id) ? $itari_adm_id: '',
                'name' => isset($refname[$i])? $refname[$i]: '',
                'phone_num' => isset($refphn[$i])? $refphn[$i]:'',
                'email' => isset($refemail[$i])? $refemail[$i]:'',
                'capacity' => isset($refcap[$i])? $refcap[$i]: '',
                'essay'=>isset($reportPath)? $reportPath: ''
                );
                $this->db->insert('itari_admissions_references_information', $data);
                $id = $this->db->insert_id();
            }
        }
        $rows = $this->db->affected_rows();
        $this->db->trans_complete();
        if ($this->db->trans_status()) {
            return $id." ".$rows;
        // return $this->db_readonly->last_query();
        }
        return false;
    }
    public function add_follow_up_details($input){
        $itari_adm_id = $this->input->post('itari_adm_id');
        $mode_of_study = $this->input->post('mode_of_study');
        $criminal_convection = $this->input->post('criminal_convection');
        $criminal_reason = $this->input->post('criminal_reason');
        $data = array(
            'criminal_convections'=>isset($criminal_convection)? $criminal_convection: '',
            'mode_of_study'=>isset($mode_of_study)? $mode_of_study: '',
            'criminal_reason'=>isset($criminal_reason)? $criminal_reason: '',
            );
        $this->db->where('id', $itari_adm_id);
        $result = $this->db->update('itari_admissions_forms', $data);

        if($result){
            return $itari_adm_id;
        }
    }
    public function final_submit($input){
        $itari_adm_id = $this->input->post('itari_adm_id');
        $data = array(
            'admission_status'=>'submitted'
            );
        $this->db->where('id', $itari_adm_id);
        $result = $this->db->update('itari_admissions_forms', $data);

        if($result){
            return $itari_adm_id;
        }
    }
    public function remove_information($input){
        $len = $this->input->post('len');
        $id = $this->input->post('id');

        if($len > 8){
        $this->db->trans_start();
        $this->db->where('id',$id);
        $this->db->delete('itari_admissions_academic_information');
        $this->db->trans_complete();
        return $this->db->trans_status();
        }
        else{
        $this->db->trans_start();
        $this->db->where('id',$id);
        $this->db->delete('itari_admissions_employment_information');
        $this->db->trans_complete();
        return $this->db->trans_status();
        }
    }
    public function get_itari_enquiry_data_based_on_forms($createdfrom_date,$createdto_date,$followupfrom_date,$followupto_date,$follow_up_status, $grade, $fee_status){
        $this->db_readonly->select("ie.id as id,ie.*, date_format(ie.created_date,'%D% %b') as created_on, date_format(oafpm.tx_date_time, '%D% %b') as fee_paid_date, date_format(ie.fees_collected_on, '%D% %b') as fees_collected_on");
        $this->db_readonly->from('itari_admissions_forms ie');
        $this->db_readonly->join('online_application_fee_payment_master oafpm', 'ie.id = oafpm.source_id and oafpm.tx_response_code = 0', 'left');
        $this->db_readonly->where('ie.admission_status !=', 'Disabled');
        $this->db_readonly->where('ie.academic_year_applied_for ',$this->acad_year->getAcadYearID());
        if ($createdfrom_date && $createdto_date) {
            $createdfrom_date = date('Y-m-d',strtotime($createdfrom_date));
            $createdto_date = date('Y-m-d',strtotime($createdto_date));
            $this->db_readonly->where('date_format(ie.created_date,"%Y-%m-%d") BETWEEN "'.$createdfrom_date. '" and "'.$createdto_date.'"');
        }
        if ($grade) {
            $this->db_readonly->where_in('ie.course',$grade);
        }
        if ($follow_up_status) {
            $this->db_readonly->where_in('ie.admission_status',$follow_up_status);
        }
        if ($fee_status) {
            if($fee_status == "online"){
                $this->db_readonly->where('ie.fees_paid_status',1);
                $this->db_readonly->where('tx_response_code', '0');
                $this->db_readonly->where('ie.fees_payment_mode','Online');
                $this->db_readonly->order_by('oafpm.tx_date_time','desc');
            }
            else if($fee_status == "offline"){
                $this->db_readonly->where('ie.fees_paid_status',1);
                $this->db_readonly->where('ie.fees_payment_mode','Offline');
            }
            else if($fee_status == "no"){
                $this->db_readonly->where('ie.fees_paid_status',0);
                $this->db_readonly->order_by('ie.created_date','desc');
            }
        }
        else{
            $this->db_readonly->order_by('ie.created_date','desc');
        }
        if($followupfrom_date && $followupto_date){
            $this->db_readonly->join('itari_admissions_follow_up ifu','ie.id = ifu.itari_adm_id');
            $followupfrom_date = date('Y-m-d',strtotime($followupfrom_date));
            $followupto_date = date('Y-m-d',strtotime($followupto_date));
            $this->db_readonly->where('date_format(ifu.follow_up_date,"%Y-%m-%d") BETWEEN "'.$followupfrom_date. '" and "'.$followupto_date.'"');
        }
        $result =  $this->db_readonly->get()->result();
        // return $this->db_readonly->last_query();die();
        return $result;
    }
    public function get_itari_details_by_Id_for_forms($id){
        $personal =  $this->db_readonly->select("ie.*, ifnull(name,'') as student_name,
        date_format(ie.created_date,'%d-%m-%Y') as created_date,ie.course,date_format(ie.dob,'%d-%m-%Y') as dob")
        ->from('itari_admissions_forms ie')
        ->where('ie.id',$id)
        ->get()->row();
        if($personal){
            $photo = $this->filemanager->getFilePath($personal->photo);
        }
        $academic =  $this->db_readonly->select("ad.*")
        ->from('itari_admissions_academic_information ad')
        ->where('ad.itari_adm_id',$id)
        ->get()->result();
        $employment =  $this->db_readonly->select("ed.*")
        ->from('itari_admissions_employment_information ed')
        ->where('ed.itari_adm_id',$id)
        ->get()->result();
        $reference =  $this->db_readonly->select("rd.*")
        ->from('itari_admissions_references_information rd')
        ->where('rd.itari_adm_id',$id)
        ->get()->result();
        if($reference){
            $essay = $this->filemanager->getFilePath($reference[0]->essay);
        }
        else{
            $essay = null;
        }
        $followup =  $this->db_readonly->select('ifu.itari_adm_id, ifu.follow_up_type, ifu.current_admission_status,ifnull(date_format(ifu.follow_up_date,"%d-%m-%Y")," ") as next_follow_date, date_format(ifu.created_date,"%d-%m-%Y %H:%i") as created_on')
        ->from('itari_admissions_follow_up ifu')
        ->where('ifu.itari_adm_id',$id)
        ->order_by('ifu.id','desc')
        ->get()->result();
        if (isset($personal->fees_payment_mode) && $personal->fees_payment_mode === "Offline") {
            // Offline fees data query
            $fees_data = $this->db_readonly
                ->select("concat(ifnull(sm.first_name, ''), ' ', ifnull(sm.last_name, '')) as fees_collected, date_format(af.fees_collected_on, '%d-%M-%Y %H:%i') as tx_dt_time, 'Successful' as status, ifnull(af.offline_fees_remarks, '') as remarks")  
                ->from('itari_admissions_forms af')
                ->join('staff_master sm', 'sm.id = af.fees_collected_by')
                ->where('af.id', $id)
                ->get()
                ->result();
        } else {
            // Online fees data query
            $fees_data = $this->db_readonly
                ->select('oafpm.*, date_format(oafpm.tx_date_time, "%d-%M-%Y %H:%i") as tx_dt_time')
                ->from('online_application_fee_payment_master oafpm')
                ->where('oafpm.source_id', $id)
                ->where('oafpm.tx_response_code', '0')
                ->order_by('oafpm.tx_date_time', 'desc')
                ->get()
                ->result();
        }
        $personal->academic = $academic;
        $personal->employment = $employment;
        $personal->reference = $reference;
        $personal->followup = $followup;
        $personal->essay = $essay;
        $personal->fees_data = $fees_data;
        $personal->photo = $photo;
        return $personal;
    }
    public function update_itari_form_status_on_success($id){
        $this->db->where('id', $id)->update('itari_admissions_forms', array('admission_status' => 'Application Fees Paid', 'fees_paid_status' => 1, 'fees_payment_mode' => 'Online'));
    }
    public function itari_admissions_follow_up($input){
        $follow_up_type = $this->input->post('followup_action');
        $remarks = $this->input->post('remarks');
        $status = $this->input->post('status');
        $follow_up_date = $this->input->post('next_follow_date');
        $itari_admission_id = $this->input->post('itari_admission_id');

        $data_follow_up = array(
            'follow_up_type' => isset($follow_up_type)?$follow_up_type:'',
            'remarks' => isset($remarks)?$remarks:'',
            'follow_up_date' => isset($follow_up_date)?$follow_up_date:'',
            'current_admission_status' => isset($status)? $status:'',
            'itari_adm_id' => $itari_admission_id
        );
        $data_forms= array(
            'admission_status' => $status,
        );
        // Start transaction
        $this->db->trans_start();

        $this->db->insert('itari_admissions_follow_up',$data_follow_up);

        $this->db->where('id',$itari_admission_id);
        $this->db->update('itari_admissions_forms',$data_forms);

        $this->db->trans_complete();
        $stat = $this->db->trans_status();
        if (!$stat) {
            return false;
        }
        $this->db->select('f.mode_of_study, f.criminal_convections, f.criminal_reason, ifu.current_admission_status, ifu.follow_up_type, ifu.remarks, ifnull(date_format(ifu.follow_up_date,"%d-%m-%Y")," ") as next_follow_date, date_format(ifu.created_date,"%d-%m-%Y %H:%i") as created_on');
        $this->db->from("itari_admissions_follow_up ifu, itari_admissions_forms f");
        $this->db->where('itari_adm_id',$itari_admission_id);
        $this->db->where('f.id=ifu.itari_adm_id');
        $ret_data =  $this->db->get()->result();
        $ret = array(
            'status' => $stat,
            'followup' => $ret_data
        );
        return $ret;
    }
    public function insertOTP($input, $otp) {
        $this->db->where('mobile_no', $input['mobileNumber']);
        $q = $this->db->get('admission_user');
        $this->db->reset_query();
        if ( $q->num_rows() > 0 ) 
        {
        $admissionData = array (
            'otp' => $otp
        );
        $this->db->where('mobile_no', $input['mobileNumber'])->update('admission_user', $admissionData);
        } 
        else {
        $admissionData = array(
            'mobile_no' => $input['mobileNumber'],
            'otp' => $otp
        );
        $this->db->insert('admission_user', $admissionData);
        }
    }
    public function verifyOTP($input) {
        if (!isset($input['mobileNumber']))
            return 0;
        $this->db_readonly->where('mobile_no', $input['mobileNumber']);
        $this->db_readonly->where('otp', $input['otpCode']);
        $q = $this->db_readonly->get('admission_user');
        if ( $q->num_rows() > 0 ) {
            return 1;
        } else {
            return 0;
        }
    }
    public function get_id_mobile_number($mobile_no){
        $this->db_readonly->where('mobile_no', $mobile_no);
        return $this->db_readonly->get('admission_user')->row()->id;
    }
    public function my_application_data_cards($mobile){
        if(filter_var($mobile, FILTER_VALIDATE_EMAIL)){
            $this->db_readonly->where('email',$mobile);
            $this->db_readonly->where('admission_status !=', 'Disabled');
        }
        else{
            $this->db_readonly->where('phone_num',$mobile);
            $this->db_readonly->where('admission_status !=', 'Disabled');
        }
        return $this->db_readonly->get('itari_admissions_forms')->result();
    }
    public function my_application_data($id){
        $this->db_readonly->select("*");
        $this->db_readonly->from("itari_admissions_forms pd");
        $this->db_readonly->where("pd.id",$id);
        $personal = $this->db_readonly->get()->row();
        $this->db_readonly->select("*");
        $this->db_readonly->from("itari_admissions_academic_information ad");
        $this->db_readonly->where("ad.itari_adm_id",$id);
        $academic = $this->db_readonly->get()->result();
        $this->db_readonly->select("*");
        $this->db_readonly->from("itari_admissions_employment_information ed");
        $this->db_readonly->where("ed.itari_adm_id",$id);
        $this->db_readonly->where("ed.type","cur");
        $cur_employment = $this->db_readonly->get()->row();
        $this->db_readonly->select("*");
        $this->db_readonly->from("itari_admissions_employment_information ed");
        $this->db_readonly->where("ed.itari_adm_id",$id);
        $this->db_readonly->where("ed.type","pre");
        $pre_employment = $this->db_readonly->get()->result();
        $this->db_readonly->select("*");
        $this->db_readonly->from("itari_admissions_references_information rd");
        $this->db_readonly->where("rd.itari_adm_id",$id);
        $references = $this->db_readonly->get()->result();
        $data = array(
            'personal' => $personal,
            'academic' => $academic,
            'cur_employment' => $cur_employment,
            'pre_employment' => $pre_employment,
            'references' => $references
        );
        return $data;
    }

    public function check_application_form($id){
        $sql = "select * from itari_admissions_forms where id = $id";
        $pdf_status = $this->db_readonly->query($sql)->row()->pdf_status;
        if(!empty($pdf_status) && $pdf_status == 1){
            return 1;
        }
        else{
            return 0;
        }
    }

    public function get_application_form_pdf_path($afId){
        $result = $this->db->select('template_pdf_path')
        ->where('id',$afId)
        ->get('itari_admissions_forms')->row();
        
        if (empty($result->template_pdf_path)) {
            return 0;
        }else{
            return $result->template_pdf_path;
        }
    }

    public function admission_settings_getbyId(){
        return $this->db->get('admission_settings')->row_array();
    }

    public function update_admission_form_path($afId, $path){
        $this->db->where('id',$afId);
        return $this->db->update('itari_admissions_forms', array('template_pdf_path'=> $path,'template_modified_on'=> $this->Kolkata_datetime()));
    }

    public function updateItariApplicationPdfLink($path, $status) {
        $this->db->where('template_pdf_path',$path);
        return $this->db->update('itari_admissions_forms', array('pdf_status' => $status,'template_modified_on'=> $this->Kolkata_datetime()));
    }

    public function get_classes($id){
        $sql = "select cm.class_name, c.id as class_id 
                from class c
                join class_master cm on c.class_master_id = cm.id
                join itari_admissions_forms ia on ia.academic_year_applied_for = c.acad_year_id
                where ia.id = $id and cm.class_name != 'PH'
                group by cm.class_name";
        return $this->db_readonly->query($sql)->result();
    }
    
    public function get_sections($class_id){
        $sql = "select section_name, id as section_id
                from class_section where class_id = $class_id";
        return $this->db_readonly->query($sql)->result();
    }

    public function move_to_erp($id, $admission_no, $class_id, $section_id) {
        $db_debug_original = $this->db->db_debug;
        $this->db->db_debug = false;
        try {
            $this->db->trans_start();
    
            $sql = "SELECT * FROM itari_admissions_forms WHERE id = $id";
            $forms_result = $this->db_readonly->query($sql)->row();
            if (!$forms_result) {
                return ['success' => false, 'message' => 'Admission form not found'];
            }
    
            $full_name = $forms_result->name;
            $last_space_pos = strrpos($full_name, ' ');
            $first_name = $last_space_pos !== false ? substr($full_name, 0, $last_space_pos) : $full_name;
            $last_name = $last_space_pos !== false ? substr($full_name, $last_space_pos + 1) : '';
    
            $sa_data = [
                "admission_no" => $admission_no,
                "first_name" => $first_name,
                "last_name" => $last_name,
                "dob" => $forms_result->dob,
                "gender" => ($forms_result->gender == "male") ? "M" : (($forms_result->gender == "female") ? "F" : $forms_result->gender),
                "preferred_contact_no" => $forms_result->phone_num,
                "created_on" => $forms_result->created_date,
                "modified_on" => $this->Kolkata_datetime(),
                "last_modified_by" => $this->authorization->getAvatarId(),
                "admission_status" => 2,
                "admission_acad_year_id" => $forms_result->academic_year_applied_for,
                "email" => $forms_result->email,
                "admission_form_id" => $id
            ];
    
            if (!$this->db->insert('student_admission', $sa_data)) {
                $error = $this->db->error();
                if (strpos($error['message'], 'Duplicate entry') !== false) {
                    return ['success' => false, 'message' => 'Admission number already exists.'];
                }
                return ['success' => false, 'message' => 'Failed to insert student admission: ' . $error['message']];
            }
    
            $sa_inserted_id = $this->db->insert_id();
            $identification_code = "ITARISD" . str_pad($sa_inserted_id, 6, "0", STR_PAD_LEFT);
            $this->db->where('id', $sa_inserted_id);
            if (!$this->db->update('student_admission', ["identification_code" => $identification_code])) {
                return ['success' => false, 'message' => 'Failed to update identification code'];
            }
    
            $current_acad_year = $this->settings->getSetting("academic_year_id");
            if ($current_acad_year == $forms_result->academic_year_applied_for) {
                $sy_data = [[
                    "class_id" => $class_id,
                    "class_section_id" => $section_id,
                    "boarding" => "1",
                    "created_on" => $this->Kolkata_datetime(),
                    "modified_on" => $this->Kolkata_datetime(),
                    "last_modified_by" => $this->authorization->getAvatarId(),
                    "admission_type" => "2",
                    "board" => "1",
                    "medium" => "1",
                    "student_admission_id" => $sa_inserted_id,
                    "acad_year_id" => $forms_result->academic_year_applied_for,
                    "promotion_status" => "STUDYING",
                    "picture_url" => $forms_result->photo
                ]];
            } else if ($current_acad_year < $forms_result->academic_year_applied_for) {
                $sy_data = [
                    ["class_id" => $class_id, "class_section_id" => $section_id, "boarding" => "1", "created_on" => $this->Kolkata_datetime(), "modified_on" => $this->Kolkata_datetime(), "last_modified_by" => $this->authorization->getAvatarId(), "admission_type" => "2", "board" => "1", "medium" => "1", "student_admission_id" => $sa_inserted_id, "acad_year_id" => $current_acad_year, "promotion_status" => "JOINED", "picture_url" => $forms_result->photo],
                    ["class_id" => $class_id, "class_section_id" => $section_id, "boarding" => "1", "created_on" => $this->Kolkata_datetime(), "modified_on" => $this->Kolkata_datetime(), "last_modified_by" => $this->authorization->getAvatarId(), "admission_type" => "2", "board" => "1", "medium" => "1", "student_admission_id" => $sa_inserted_id, "acad_year_id" => $forms_result->academic_year_applied_for, "promotion_status" => "STUDYING", "picture_url" => $forms_result->photo]
                ];
            }
    
            if (!$this->db->insert_batch('student_year', $sy_data)) {
                return ['success' => false, 'message' => 'Failed to insert student year data'];
            }
    
            $this->insert_into_user_avatar($sa_data, $sa_inserted_id, '1');
    
            $add_data = [
                "Address_line1" => $forms_result->permanent_address,
                "district" => $forms_result->permanent_city,
                "state" => $forms_result->permanent_state,
                "country" => $forms_result->permanent_country,
                "pin_code" => $forms_result->permanent_pincode,
                "created_on" => $forms_result->created_date,
                "modified_on" => $this->Kolkata_datetime(),
                "last_modified_by" => $this->authorization->getAvatarId(),
                "avatar_type" => 1,
                "stakeholder_id" => $sa_inserted_id
            ];
            if (!$this->db->insert('address_info', $add_data)) {
                return ['success' => false, 'message' => 'Failed to insert address'];
            }
    
            $acad_result = $this->db_readonly->query("SELECT * FROM itari_admissions_academic_information WHERE itari_adm_id = $id")->result();
            $acad_data = [];
            foreach ($acad_result as $acad_res) {
                $acad_data[] = [
                    "student_id" => $sa_inserted_id,
                    "school_name" => $acad_res->college_name,
                    "class" => $acad_res->degree_name,
                    "total_percentage" => $acad_res->gpa
                ];
            }
            if (!empty($acad_data) && !$this->db->insert_batch('student_prev_school', $acad_data)) {
                return ['success' => false, 'message' => 'Failed to insert academic info'];
            }
    
            $father_data = ["first_name" => $forms_result->name . " Father", "student_id" => $sa_inserted_id, "mobile_no" => $forms_result->phone_num, "created_on" => $this->Kolkata_datetime(), "modified_on" => $this->Kolkata_datetime(), "last_modified_by" => $this->authorization->getAvatarId(), "email" => $forms_result->email];
            if (!$this->db->insert('parent', $father_data)) {
                return ['success' => false, 'message' => 'Failed to insert father info'];
            }
            $father_inserted_id = $this->db->insert_id();
    
            $mother_data = ["first_name" => $forms_result->name . " Mother", "student_id" => $sa_inserted_id, "mobile_no" => $forms_result->phone_num, "created_on" => $this->Kolkata_datetime(), "modified_on" => $this->Kolkata_datetime(), "last_modified_by" => $this->authorization->getAvatarId(), "email" => $forms_result->email];
            if (!$this->db->insert('parent', $mother_data)) {
                return ['success' => false, 'message' => 'Failed to insert mother info'];
            }
            $mother_inserted_id = $this->db->insert_id();
    
            $this->insert_into_user_avatar($father_data, $father_inserted_id, '2');
            $this->insert_into_user_avatar($mother_data, $mother_inserted_id, '2');
    
            $father_relation_data = ['std_id' => $sa_inserted_id, 'relation_id' => $father_inserted_id, 'relation_type' => 'Father', 'active' => '1', "created_on" => $this->Kolkata_datetime(), "modified_on" => $this->Kolkata_datetime(), "last_modified_by" => $this->authorization->getAvatarId()];
            if (!$this->db->insert('student_relation', $father_relation_data)) {
                return ['success' => false, 'message' => 'Failed to relate father'];
            }
    
            $mother_relation_data = ['std_id' => $sa_inserted_id, 'relation_id' => $mother_inserted_id, 'relation_type' => 'Mother', 'active' => '1', "created_on" => $this->Kolkata_datetime(), "modified_on" => $this->Kolkata_datetime(), "last_modified_by" => $this->authorization->getAvatarId()];
            if (!$this->db->insert('student_relation', $mother_relation_data)) {
                return ['success' => false, 'message' => 'Failed to relate mother'];
            }
    
            $this->db->where('id', $id);
            if (!$this->db->update('itari_admissions_forms', ["admission_status" => "Moved To ERP"])) {
                return ['success' => false, 'message' => 'Failed to update form status'];
            }
    
            $this->db->trans_complete();
    
            if ($this->db->trans_status() === FALSE) {
                return ['success' => false, 'message' => 'Transaction failed'];
            }
    
            return ['success' => true, 'message' => 'Student moved to ERP successfully'];
        } catch (Exception $e) {
            return ['success' => false, 'message' => 'Server error: ' . $e->getMessage()];
        } finally {
            $this->db->db_debug = $db_debug_original;
        }
    }    

    public function insert_into_user_avatar($data, $student_id, $avatar_type){
        if(!empty($data)) {
            $firstName = $data["first_name"];
            $lastName = $data["last_name"];
            $email = $data["email"];

            $pUsername = $this->generateUsername($firstName, $lastName);
            $student_user_id = $this->_insertIntoUsers($pUsername, $email);

            if (!$student_user_id) {
                return 0;
            }
            //Avatar Type for Student is 1
            $avatarId = $this->_insertIntoAvatar($student_user_id, $avatar_type, $student_id, $firstName . ' ' . $lastName);
            if (!$avatarId) {
                return 0;
            }
            return $student_id;
        }
        return false;
    }

    private function generateUsername($firstName, $lastName){
        $names = $this->db->select('username')->get('users')->result();
        $users = array();
        if (!empty($names)) {
            foreach ($names as $val) {
                array_push($users, $val->username);
            }
        }
        $firstName = preg_replace('/\s+/', '', $firstName);
        $lastName = preg_replace('/\s+/', '', $lastName);
        $firstName = preg_replace('/[^A-Za-z0-9]/', '', $firstName);
        $lastName = preg_replace('/[^A-Za-z0-9]/', '', $lastName);
        $name = '';
        $fullName = $firstName . $lastName;
        if ($firstName == '' && $lastName == '') {
            $username = $this->first($users);
        } else if ($firstName == '') {
            if (!in_array($lastName, $users)) {
                $username = substr($lastName, 0, 6);
            } else {
                $username = $this->second($lastName, $users);
            }
        } else if ($lastName == '') {
            if (!in_array($firstName, $users)) {
                $username = substr($firstName, 0, 6);
            } else {
                $username = $this->second($firstName, $users);
            }
        } else {
            $username = $this->third($firstName, $lastName, $users);
        }
        return $username;
    }

    private function first($users){
        do {
            $username = $this->generateRandomCode(4, 0);
            $username .= $this->generateRandomCode(2, 1);
        } while (in_array($username, $users));
        return $username;
    }

    private function second($name, $users){
        $name = substr($name, 0, 6);
        if (!in_array($name, $users))return $name;
        $len = strlen($name);
        $random = '';
        $num = 6 - $len;
        do {
            $times = pow(10, $num);
            for ($i = 0; $i < $times; $i++) {
                $random = $this->generateRandomCode($num, 1);
            }
            $num++;
        } while (in_array($name . $random, $users));
        return $name . $random;
    }

    private function third($firstName, $lastName, $users){
        $username = substr($firstName, 0, 4) . substr($lastName, 0, 2);
        if (!in_array($username, $users))
        return $username;

        $username = substr($firstName, 0, 2) . substr($lastName, 0, 4);
        if (!in_array($username, $users))
        return $username;

        $username = substr($lastName, 0, 4) . substr($firstName, 0, 2);
        if (!in_array($username, $users))
        return $username;

        $username = substr($lastName, 0, 2) . substr($firstName, 0, 4);
        if (!in_array($username, $users))
        return $username;

        $username = substr($firstName, 0, 3) . substr($lastName, 0, 3);
        if (!in_array($username, $users))
        return $username;

        $username = substr($lastName, 0, 3) . substr($firstName, 0, 3);
        if (!in_array($username, $users))
        return $username;

        $username = substr($firstName, 0, 5) . substr($lastName, 0, 1);
        if (!in_array($username, $users))
        return $username;

        $username = substr($firstName, 0, 1) . substr($lastName, 0, 5);
        if (!in_array($username, $users))
        return $username;

        $username = substr($lastName, 0, 5) . substr($firstName, 0, 1);
        if (!in_array($username, $users))
        return $username;

        $username = substr($lastName, 0, 1) . substr($firstName, 0, 5);
        if (!in_array($username, $users))
        return $username;

        $username = substr($firstName, 0, 6);
        if (!in_array($username, $users))
        return $username;

        $username = substr($lastName, 0, 6);
        if (!in_array($username, $users))
        return $username;

        $username = substr($firstName, 0, 4) . substr($lastName, 0, 2) . $this->generateRandomCode(2, 1);
        if (!in_array($username, $users))
        return $username;

        $username = substr($lastName, 0, 4) . substr($firstName, 0, 2) . $this->generateRandomCode(2, 1);
        if (!in_array($username, $users))
        return $username;

        $username = substr($firstName, 0, 3) . substr($lastName, 0, 3) . $this->generateRandomCode(2, 1);
        if (!in_array($username, $users))
        return $username;

        $username = substr($lastName, 0, 3) . substr($firstName, 0, 3) . $this->generateRandomCode(2, 1);
        if (!in_array($username, $users))
        return $username;

        $username = substr($firstName, 0, 5) . substr($lastName, 0, 1) . $this->generateRandomCode(2, 1);
        if (!in_array($username, $users))
        return $username;

        $username = substr($lastName, 0, 5) . substr($firstName, 0, 1) . $this->generateRandomCode(2, 1);
        if (!in_array($username, $users)) {
            return $username;
        } else {
            $username = substr($lastName, 0, 3) . substr($firstName, 0, 2) . $this->generateRandomCode(3, 1);
        }
        return $username;
    }

    private function generateRandomCode($length = 6, $isNumber = 1){
        if ($isNumber)
            return substr(str_shuffle(str_repeat($x = '1234567890', ceil($length / strlen($x)))), 1, $length);
        else
            return substr(str_shuffle(str_repeat($x = 'abcdefghijklmnopqrstuvwxyz', ceil($length / strlen($x)))), 1, $length);
    }

    private function _insertIntoUsers($username, $email){
        $ci = &get_instance();
        $username = strtolower(str_replace(' ', '', $username));
        $flag = true;
        do {
            $this->db->where('username', $username);
            $query = $this->db->get('users');
            if ($query->num_rows() > 0) {
                $username = $username . rand(10, 99);
            } else {
                $flag = false;
            }
        } while ($flag);
        $userId = $ci->ion_auth->register_1($username, 'welcome123', $email);
        return $userId;
    }

    private function _insertIntoAvatar($student_userIdu, $avatar_type, $insert_id, $friendly_name){
        $ci = &get_instance();
        $param = array(
            'user_id' => $student_userIdu,
            'avatar_type' => $avatar_type,
            'stakeholder_id' => $insert_id,
            'friendly_name' => $friendly_name,
            'last_modified_by' => $this->authorization->getAvatarId() ? $this->authorization->getAvatarId() : 0
        );

        return $ci->db->insert('avatar', $param);
    }
}
?>