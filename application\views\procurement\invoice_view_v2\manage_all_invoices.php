<ul class="breadcrumb">
    <li><a href="<?php echo site_url('dashboard') ?>">Dashboard</a></li>
    <li><a href="<?php echo site_url('procurement/requisition_controller_v2'); ?>">Procurement</a></li>
    <li><a href="<?php echo site_url('procurement/invoice_controller_v2/invoice_management_dashboard'); ?>">Invoice Management Dashboard</a></li>
    <li>Invoice Management</li>
</ul>

<div class="col-md-12">
    <div class="card cd_border">
        <div class="card-header panel_heading_new_style_staff_border">
            <div class="row" style="margin: 0px">
                <div style="width: 100%;" class="d-flex justify-content-between">
                    <h3 class="card-title panel_title_new_style_staff">
                        <a class="back_anchor" href="<?php echo site_url('procurement/invoice_controller_v2/invoice_management_dashboard') ?>">
                        <span class="fa fa-arrow-left"></span>
                        </a> 
                        Invoice Management
                    </h3>
                    <a href="<?php echo site_url("procurement/invoice_controller_v2/add_new_invoice_v2") ?>">
                        <div class="pull-right"
                            style="background: #676363;padding: 5px;border-radius: 9px;color: #fff;font-size: 1rem;padding-right: 1.4rem;cursor: pointer;height: 3rem;display: flex;justify-content: center;align-items: center;">
                            <div style="display: flex;justify-content: center;align-items: center;">
                                <svg style="height: 1.3rem;width: 3rem;" xmlns="http://www.w3.org/2000/svg" width="16"
                                    height="16" fill="currentColor" class="bi bi-plus-lg" viewBox="0 0 16 16">
                                    <path fill-rule="evenodd"
                                        d="M8 2a.5.5 0 0 1 .5.5v5h5a.5.5 0 0 1 0 1h-5v5a.5.5 0 0 1-1 0v-5h-5a.5.5 0 0 1 0-1h5v-5A.5.5 0 0 1 8 2" />
                                </svg>Create New
                            </div>
                        </div>
                    </a>

                </div>
            </div>
        </div>
        <div class="panel-body">
            <div class="col-md-12" id="filter-div">
                <div class="form-group col-md-4">
                    <label for="staff_filter">Filter Records:</label>
                    <div class="input-group">
                        <select name="staff_filter" id="staff_filter" class="form-control">
                            <!-- PushpaRaj certified -->
                            <option value="all">All</option>
                            <option value="approval_by_me">Approval by me</option>
                            <option value="created_by_me">Created by me</option>
                        </select>
                    </div>
                    <!-- <div class="radio">
                        <label>
                            <input type="radio" name="staff_filter" value="all" checked> 
                            All
                        </label>
                    </div>
                    <div class="radio">
                        <label>
                            <input type="radio" name="staff_filter" value="approval_by_me"> 
                            Approval by me
                        </label>
                    </div>
                    <div class="radio">
                        <label>
                            <input type="radio" name="staff_filter" value="created_by_me"> 
                            Created by me
                        </label>
                    </div> -->
                </div>
                <div class="form-group col-md-4">
                    <label>Invoice Status</label>
                    <div class="input-group">
                        <select name="invoice_status" id="invoice_status" class="form-control">
                            <!-- 'Draft,Pending Approval,Approved,Rejected, Cancelled -->
                            <option value="all">All</option>
                            <option value="Draft">Draft</option>
                            <option value="Pending">Pending</option>
                            <option value="Approved">Approved</option>
                            <option value="Rejected">Rejected</option>
                            <option value="Cancelled">Cancelled</option>
                            <option value="Paid">Paid</option>
                            <option value="Unpaid">Unpaid</option>
                        </select>
                    </div>
                </div>
                <div class="col-md-4 form-group">
                    <label style="opacity: 0;">I</label><br>
                    <button class="btn btn-dark" id="button-id" style="max-width: 130px;min-width: 130px;" onclick="__get_invoice_list()">Get</button>
                </div>
            </div>




            <div class="col-md-12" id="table-div">
                <div class="no-data-display">Click 'Get' button to see all invoices and click on '+' button to create one.</div>
            </div>
        </div>
    </div>
</div>


<!-- Confetti start
<script src="https://cdn.jsdelivr.net/npm/canvas-confetti@1.5.1/dist/confetti.browser.min.js"></script> 
Confetti end
-->
<script src="https://cdn.jsdelivr.net/npm/sweetalert2@11"></script>
<script>
    $(document).ready(function () {
        __get_invoice_list();
        // // Confetti start
        // triggerFigetti({ preventDefault: () => { } }); // Trigger confetti
        //  // Confetti end
    });

    //  // Confetti start
    // function triggerFigetti(event) {
    //     event.preventDefault();

    //     // Trigger confetti animation
    //     const duration = 2 * 1000; // 2 seconds
    //     const end = Date.now() + duration;

    //     (function frame() {
    //         confetti({
    //             particleCount: 5,
    //             angle: 60,
    //             spread: 55,
    //             origin: { x: 0 }
    //         });
    //         confetti({
    //             particleCount: 5,
    //             angle: 120,
    //             spread: 55,
    //             origin: { x: 1 }
    //         });

    //         if (Date.now() < end) {
    //             requestAnimationFrame(frame);
    //         }
    //     })();

    // }
    //  // Confetti end

    function __get_invoice_list() {
        const staff_filter = $("#staff_filter").val();
        const invoice_status = $('#invoice_status').val();
        const data = {
            staff_filter,
            invoice_status
        };
        if(staff_filter) {
            $("#table-div").html(`
            <center>
                                <div class="" style="max-width: 90px; max-height: 90px   ;">
                                    <svg xmlns="http://www.w3.org/2000/svg" xmlns:xlink="http://www.w3.org/1999/xlink" style="margin:auto;background:#fff;display:block;" width="50px" height="50px" viewBox="0 0 100 100" preserveAspectRatio="xMidYMid">
                                        <g><circle cx="73.801" cy="68.263" fill="#e15b64" r="3">
                                        <animateTransform attributeName="transform" type="rotate" calcMode="spline" values="0 50 50;360 50 50" times="0;1" keySplines="0.5 0 0.5 1" repeatCount="indefinite" dur="1.4925373134328357s" begin="0s"></animateTransform>
                                        </circle><circle cx="68.263" cy="73.801" fill="#f47e60" r="3">
                                        <animateTransform attributeName="transform" type="rotate" calcMode="spline" values="0 50 50;360 50 50" times="0;1" keySplines="0.5 0 0.5 1" repeatCount="indefinite" dur="1.4925373134328357s" begin="-0.062s"></animateTransform>
                                        </circle><circle cx="61.481" cy="77.716" fill="#f8b26a" r="3">
                                        <animateTransform attributeName="transform" type="rotate" calcMode="spline" values="0 50 50;360 50 50" times="0;1" keySplines="0.5 0 0.5 1" repeatCount="indefinite" dur="1.4925373134328357s" begin="-0.125s"></animateTransform>
                                        </circle><circle cx="53.916" cy="79.743" fill="#abbd81" r="3">
                                        <animateTransform attributeName="transform" type="rotate" calcMode="spline" values="0 50 50;360 50 50" times="0;1" keySplines="0.5 0 0.5 1" repeatCount="indefinite" dur="1.4925373134328357s" begin="-0.187s"></animateTransform>
                                        </circle><circle cx="46.084" cy="79.743" fill="#849b87" r="3">
                                        <animateTransform attributeName="transform" type="rotate" calcMode="spline" values="0 50 50;360 50 50" times="0;1" keySplines="0.5 0 0.5 1" repeatCount="indefinite" dur="1.4925373134328357s" begin="-0.25s"></animateTransform>
                                        </circle><circle cx="38.519" cy="77.716" fill="#6492ac" r="3">
                                        <animateTransform attributeName="transform" type="rotate" calcMode="spline" values="0 50 50;360 50 50" times="0;1" keySplines="0.5 0 0.5 1" repeatCount="indefinite" dur="1.4925373134328357s" begin="-0.312s"></animateTransform>
                                        </circle><circle cx="31.737" cy="73.801" fill="#637cb5" r="3">
                                        <animateTransform attributeName="transform" type="rotate" calcMode="spline" values="0 50 50;360 50 50" times="0;1" keySplines="0.5 0 0.5 1" repeatCount="indefinite" dur="1.4925373134328357s" begin="-0.375s"></animateTransform>
                                        </circle><circle cx="26.199" cy="68.263" fill="#6a63b6" r="3">
                                        <animateTransform attributeName="transform" type="rotate" calcMode="spline" values="0 50 50;360 50 50" times="0;1" keySplines="0.5 0 0.5 1" repeatCount="indefinite" dur="1.4925373134328357s" begin="-0.437s"></animateTransform>
                                        </circle><animateTransform attributeName="transform" type="rotate" calcMode="spline" values="0 50 50;0 50 50" times="0;1" keySplines="0.5 0 0.5 1" repeatCount="indefinite" dur="1.4925373134328357s"></animateTransform></g>
                                    </svg>
                                </div>
                                </center>
                                `);
            $.ajax({
                url: "<?php echo site_url('procurement/invoice_controller_v2/get_invoice_list') ?>",
                type: "POST",
                data: data,
                success: function (data) {
                    let response = JSON.parse(data);
                    if(Object.keys(response)?.length) {
                        __construct_invoice_list(response);
                    } else {
                        $('#table-div').html('<div class="no-data-display">No invoices found. Please click on "+" button to create one.</div>');
                    }
                },
                error: function (jqXHR, textStatus, errorThrown) {
                    console.log(textStatus, errorThrown);
                }
            });
        } else {
            $('#table-div').html('<div class="no-data-display">No invoices found. Please click on "+" button to create one.</div>');
        }
    }

    function __construct_invoice_list(response) {
        let html = '';
        if (response.length > 0) {
            html += '<table class="table table-bordered table-striped table-hover" id="invoice_list_table">';
            html += '<thead>';
            html += '<tr>';
            html += '<th>Invoice No</th>';
            html += '<th>Narration</th>';
            html += '<th>Supplier Name</th>';
            html += '<th>Invoice Amount</th>';
            html += '<th>Invoice Date</th>';
            html += '<th>Status</th>';
            html += '<th>Action</th>';
            html += '</tr>';
            html += '</thead>';
            html += '<tbody>';

            response.forEach(function (invoice) {
                let href= '';
                if(invoice.invoice_status == 'Draft') {
                    href= `
                            <div class='dropdown' style=''>
                                <div style='cursor: pointer;' data-toggle='dropdown' class='btn btn-dark'>Actions <span class="fa fa-caret-down"></span></div>
                                <div class='dropdown-menu' style='margin-right: 5px; margin-top: 5px; position: relative;'>
                                    <a class='dropdown-item' href="<?php echo site_url('procurement/invoice_controller_v2/continue_add_new_invoice_v2/') ?>${invoice.invoice_master_id}" target='_blank'>Continue Draft Invoice</a>
                                    <button class='dropdown-item' onclick="cancel_invoice('${invoice.invoice_master_id}', '${invoice.invoice_status}')">Cancel Invoice</button>
                                </div>
                            </div>
                        `;
                    // href= `<a  class="btn btn-dark"><span class="fa fa-plus"></span> Continue <span class="fa fa-angle-double-right"></span></a>`;
                } else {
                    href= `<a href="<?php echo site_url('procurement/invoice_controller_v2/view_invoice/') ?>${invoice.invoice_master_id}" class="btn btn-dark"><span class="fa fa-eye"></span> View <span class="fa fa-angle-double-right"></span></a>`;
                }
                html += `<tr style="${invoice.invoice_status == 'Cancelled' ? 'font-style: italic; color: #a8a8a8ff;' : ''}">`;
                html += `<td>${invoice.invoice_number}</td>`;
                html += `<td>${(invoice.invoice_remarks).trim() != '' ? invoice.invoice_remarks : '-'}</td>`;
                html += `<td>${invoice.vendor_name}${invoice.vendor_code && invoice.vendor_code.trim() ? '-'+invoice.vendor_code : ''}</td>`;
                html += `<td>${invoice.total_amount}</td>`;
                html += `<td>${invoice.invoice_date}</td>`;
                html += `<td>${invoice.invoice_status}</td>`;
                html += `<td>${href}</td>`;
                html += '</tr>';
            });

            html += '</tbody>';
            html += '</table>';

            $('#table-div').html(html);

            __apply_dataTable();
        } else {
            $('#table-div').html('<div class="no-data-display">No invoices found. Please click on "+" button to create one.</div>');
        }

    }

    function cancel_invoice(invoice_master_id, invoice_status) {
        if ( ['Approved', 'Finished', 'Rejected', 'Cancelled'].includes(invoice_status) ) {
            Swal.fire({
                title: 'Status: ' + invoice_status,
                text: 'You cannot cancel an invoice that is already approved, finished, rejected or cancelled.',
                icon: 'error',
                confirmButtonText: 'OK'
            });
            return;
        }

        Swal.fire({
            title: 'Cancel Invoice?',
            html: `You won't be able to revert this!<br><br><textarea placeholder="Enter cancel reason" id="cancel_reason" class="form-control" rows="3"></textarea>`,
            icon: 'question',
            showCancelButton: true,
            confirmButtonText: 'Yes, cancel it!',
            cancelButtonText: 'No, keep it'
        }).then((result) => {
            if (result.isConfirmed) {
                let cancel_reason = $('#cancel_reason').val();
                cancel_the_invoice(invoice_master_id, cancel_reason);
            } else {
                Swal.fire(
                    'Not Cancelled',
                    'Your invoice is safe 😊',
                    'warning'
                );
            }
        });
        
    }

    function cancel_the_invoice(invoice_master_id, cancel_reason) {
        $.ajax({
            url: '<?= base_url('procurement/invoice_controller_v2/cancel_invoice') ?>',
            type: 'post',
            data: {invoice_master_id, cancel_reason},
            success: function(data) {
                let p_data= JSON.parse(data);
                if (p_data.status == 1) {
                    Swal.fire({
                        title: 'Success',
                        text: p_data.message,
                        icon: 'success',
                        confirmButtonText: 'OK'
                    }).then(() => {
                        location.reload();
                    });
                } else {
                    Swal.fire({
                        title: 'Error',
                        text: p_data.message,
                        icon: 'error',
                        confirmButtonText: 'OK'
                    });
                }
            },
            error: function(dust) {
            console.log(dust);
            }
        });
    }

    function __apply_dataTable() {
        $('#invoice_list_table').DataTable( {
            "language": {
                    "search": "",
                    "searchPlaceholder": "Enter Search..."
                },
                "lengthMenu": [ [10, 25, 50, -1], [10, 25, 50, "All"] ],
                "pageLength": 10,
                "order": false,
                // dom: 'lBfrtip',
                // buttons: [
                //     {
                //         extend: 'excelHtml5',
                //         text: 'Excel',
                //         filename: 'visitor_not_checked_out_report',
                //         className: 'btn btn-info'
                //     },
                //     {
                //         extend: 'csvHtml5',
                //         text: 'CSV',
                //         filename: 'visitor_not_checked_out_report',
                //         className: 'btn btn-info'
                //     },
                //     {
                //         extend: 'pdfHtml5',
                //         text: 'PDF',
                //         filename: 'visitor_not_checked_out_report',
                //         className: 'btn btn-info'
                //     }
                // ]
            } );
    }
    
</script>


<style>
    .dataTables_wrapper .dt-buttons {
		float: right;
	}

	.dataTables_filter input {
		background-color: #f2f2f2;
		border: 1px solid #ccc;
		border-radius: 4px;
		margin-right: 5px;
	}
  
	.dataTables_wrapper .dataTables_filter {
		float: right;
		text-align: left;
		width: unset;
	}
</style>