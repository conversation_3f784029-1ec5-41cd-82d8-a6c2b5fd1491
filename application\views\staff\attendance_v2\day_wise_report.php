<ul class="breadcrumb">
  <li><a href="<?php echo site_url('dashboard');?>">Dashboard</a></li>
  <li><a href="<?php echo site_url('staff/attendance');?>">Staff Attendance</a></li>
  <li>Day wise status report</li>
</ul>

<div class="col-md-12">
  	<div class="card cd_border">
	    <div class="card-header panel_heading_new_style_staff_border">
	      <div class="row" style="margin: 0px;">
	        <div class="d-flex justify-content-between" style="width:100%;">
	          <h3 class="card-title panel_title_new_style_staff">
	            <a class="back_anchor" href="<?php echo site_url('staff/attendance'); ?>">
	              <span class="fa fa-arrow-left"></span>
	            </a> 
	            Day wise status report
	          </h3>   
	        </div>
	      </div>
	    </div>
		
		<div class="card-body pt-1">
			<div class="row" style="margin: 0px;">

				<div class="col-md-2 form-group">
					<label for="fromdateId" class="control-label">Attendance range</label>
					<div id="reportrange" class="dtrange" style="width: 100%">                                            
						<span></span>
						<input type="hidden" id="from_date">
						<input type="hidden" id="to_date">
					</div>
				</div>

				<div class="col-md-2 form-group">
					<label class="control-label">Staff Type</label>
					<select class="form-control" name="selected_staff_type" id="selected_staff_type">
						<option value="all">All</option>
						<?php foreach ($staff_types as $key=>$val) {
								echo "<option value='$key'>$val</option>";
							}
						?>
					</select>
				</div>

				<div class="col-md-2 form-group">
					<label class="control-label">Staff Status Type</label>
					<select class="form-control" name="staff_status_type" id="staff_status_type" onChange="hideDataArea()">
						<!-- <option value="all">All</option> -->
							<option value='2'>Approved</option>
							<option value='4'>Resigned</option>
					</select>
				</div>

				<div class="col-md-2 form-group">
					<label class="control-label">Late</label>
					<select class="form-control" name="show_late_only" id="show_late_only">
						<option value="all">All</option>
						<option value='1'>Show Late Only</option>
					</select>
				</div>

				<div class="col-md-3 form-group pt-3">
					<button class="btn btn-primary mt-3" id="getReportBtn" onclick="getAttendanceData()">Get Report</button>
				</div>
			</div>
			<div class="form-group mt-3 check-boxes" style="display:none;">
				<label class="radio-inline" for="show_in_out_time">
					<input type="checkbox" style="width: 1.3rem;height: 1.3rem;" name="report_type" id="show_in_out_time" onclick="show_hide_timings()" value="percentage">&nbsp;&nbsp;<b>Show In/Out Time</b>
				</label>
			</div>

			<div class="form-group mt-3 check-boxes" style="display:none;">
				<label class="radio-inline" for="show_hide_leave_Categories">
					<input type="checkbox" style="width: 1.3rem;height: 1.3rem;" name="report_type" id="show_hide_leave_Categories" onclick="showHideLeaveCategories()" value="percentage">&nbsp;&nbsp;<b>Show Leave Categories</b>
				</label>
			</div>

			<div id="attendance-data">
				<div style="color:red;text-align:center;
						color: black;
						border: 2px solid #fffafa;
						text-align: center;
						border-radius: 6px;
						position: relative;
						margin-left: 14px;
						padding: 10px;
						font-size: 14px;
						margin-top: 14px;
						background: #ebf3ff;">
						Click Get Report to fetch data!
				</div>
			</div>
	   
		</div>

  	</div>
</div>

<script src="https://cdnjs.cloudflare.com/ajax/libs/xlsx/0.16.9/xlsx.full.min.js" integrity="sha512-wBcFatf7yQavHQWtf4ZEjvtVz4XkYISO96hzvejfh18tn3OrJ3sPBppH0B6q/1SHB4OKHaNNUKqOmsiTGlOM/g==" crossorigin="anonymous"></script>
<script src="https://cdnjs.cloudflare.com/ajax/libs/FileSaver.js/2.0.0/FileSaver.min.js" integrity="sha512-csNcFYJniKjJxRWRV1R7fvnXrycHP6qDR21mgz1ZP55xY5d+aHLfo9/FcGDQLfn2IfngbAHd8LdfsagcCqgTcQ==" crossorigin="anonymous"></script>
<script type="text/javascript" src="<?php echo base_url('assets/js/plugins/moment.min.js') ?>"></script>
<script type="text/javascript" src="<?php echo base_url('assets/js/plugins/daterangepicker/daterangepicker.js') ?>"></script>

<script type="text/javascript">
var headers = [];
var json_data = [];
$(document).ready(function() {
	$("#reportrange").daterangepicker({
		ranges: {
			// 'Today': [moment(), moment()],
			'Yesterday': [moment().subtract(1, 'days'), moment().subtract(1, 'days')],
			'Last 7 Days': [moment().subtract(7, 'days'), moment().subtract(1, 'days')],
			'Last 30 Days': [moment().subtract(30, 'days'), moment().subtract(1, 'days')],
			// 'This Month': [moment().startOf('month'), moment()],
			// 'Last Month': [moment().subtract(1, 'month').startOf('month'), moment().subtract(1, 'month').endOf('month')],
		},
		dateLimit: {
		'months': 2,
		'days': 0
		},
		opens: 'right',
		buttonClasses: ['btn btn-default'],
		applyClass: 'btn-small btn-primary',
		cancelClass: 'btn-small',
		format: 'DD-MM-YYYY',
		separator: ' to ',
		startDate: moment().subtract(6, 'days'),
		endDate: moment().subtract(1, 'days'),
		maxDate: moment().subtract(1, 'days')
		},function(start, end) {
		$('#reportrange span').html(start.format('MMM D, YYYY') + ' - ' + end.format('MMM D, YYYY'));
		$('#from_date').val(start.format('DD-MM-YYYY'));
		$('#to_date').val(end.format('DD-MM-YYYY'));
	});

	$("#reportrange span").html(moment().subtract(7, 'days').format('MMM D, YYYY') + ' - ' + moment().subtract(1, 'days').format('MMM D, YYYY'));
	$('#from_date').val(moment().subtract(7, 'days').format('DD-MM-YYYY'));
	$('#to_date').val(moment().subtract(1, 'days').format('DD-MM-YYYY'));
});

function hideDataArea(){
	$("#attendance-data").html("");
}

function show_hide_timings() {
	if($("#show_in_out_time").is(':checked')) {
		$(".date-td").attr('colspan', 4);
		$(".in-out").show();
		add_scroller('report-container');
	} else {
		$(".date-td").attr('colspan', 1);
		$(".in-out").hide();
		add_scroller('report-container');
	}
}

let isLeaveCateVissible=false;
function showHideLeaveCategories() {
	if(isLeaveCateVissible){
		$(".leave-cat-show-hide").hide();
	}else{
		$(".leave-cat-show-hide").show();
	}
	isLeaveCateVissible=!isLeaveCateVissible;
}

function constructHeader(dates,all_active_leave_categories) {
	headers = ['Sl', 'Staff','Email','Working Days','Present Days','Late Days','Absent Days'];
	var html = '';
	html += '<thead>';
	html += '<tr>';
	html += '<th rowspan="2">#</th>';
	html += '<th rowspan="2" style="min-width: 150px;">Staff Code</th>';
	html += '<th rowspan="2" style="min-width: 150px;">Staff Name</th>';
	html += '<th rowspan="2" style="min-width: 150px;">Email</th>';
	html += '<th rowspan="2" style="min-width: 150px;">Working Days</th>';
	html += '<th rowspan="2" style="min-width: 150px;">Present Days</th>';
	html += '<th rowspan="2" style="min-width: 150px;">Late Days</th>';
	html += '<th rowspan="2" style="min-width: 150px;">Absent Days</th>';

	//Here we need to show the all the active leave categories 
	if(Object.entries(all_active_leave_categories)?.length){
		for(let [catId,catName] of Object.entries(all_active_leave_categories)){
			headers.push(catName);
			html += `<th class="leave-cat-show-hide" rowspan="2" style="min-width: 150px;display:${isLeaveCateVissible==false && "none" || ""};" data-catid="${catId}">${catName}</th>`;
		}
	}

	for(var i in dates) {
		headers.push(dates[i].format2);
		if($("#show_in_out_time").is(':checked')) {
			html += '<th class="date-td" style="text-align:center;" colspan="4">'+dates[i].format2+'</th>';
		} else {
			html += '<th class="date-td" style="text-align:center;">'+dates[i].format2+'</th>';
		}
	}
	html += '</tr>';
	html += '<tr>';
	for(var i in dates) {
		if($("#show_in_out_time").is(':checked')) {
			html += '<th class="in-out" style="text-align:center;min-width: 80px;">In</th>';
			html += '<th class="in-out" style="text-align:center;min-width: 80px;">Out</th>';
			html += '<th class="in-out" style="text-align:center;min-width: 100px;">Duration</th>';
		} else {
			html += '<th class="in-out" style="text-align:center;display:none;min-width: 80px;">In</th>';
			html += '<th class="in-out" style="text-align:center;display:none;min-width: 80px;">Out</th>';
			html += '<th class="in-out" style="text-align:center;display:none;min-width: 100px;">Duration</th>';
		}
		html += '<th style="text-align:center;min-width: 100px;">Status</th>';
	}
	html += '</tr>';
	html += '</thead>';
	return html;
}

function constructReport(dates, attendance, all_active_leave_categories) {
	json_data = [];
	var html = '<tbody>';
	var j = 1;
	var colors = {
		AB : '#ff8b90',
		HD : '#6aa54d',
		P:"#6aa54d",
		OOD:"#6aa54d",
		WO:"#a6aab5",
		H: "#a6aab5"
	};
	for(var i in attendance) {
		var json = {};
		json['Sl'] = j;
		json['employee_code'] = attendance[i].employee_code;
		json['Staff'] = attendance[i].staff_name;
		json['Email'] = attendance[i].email;
		json['Working Days'] = attendance[i].totalWorkingDays;
		json['Present Days'] = attendance[i].totalPresentDays;
		json['Late Days'] = attendance[i].total_late_count;
		const totalAbsentDays=attendance[i].totalWorkingDays-attendance[i].totalPresentDays;
		json['Absent Days'] = `${totalAbsentDays}, LF: ${attendance[i].totalleavestaken}, LNF: ${totalAbsentDays - attendance[i].totalleavestaken}`;

		html += '<tr>';
		html += '<td>'+(j++)+'</td>';
		html += '<td>'+attendance[i].employee_code+'</td>';
		html += '<td>'+attendance[i].staff_name+'</td>';
		if (attendance[i].email)
			html += `<td><a href="mailto:${attendance[i].email}">${attendance[i].email}</a></td>`;
		else
			html += `<td>-</td>`;
		html += '<td style="text-align:center;font-size: 14px;">'+attendance[i].totalWorkingDays+'</td>';
		html += '<td style="text-align:center;font-size: 14px;">'+attendance[i].totalPresentDays+'</td>';
		html += '<td style="text-align:center;font-size: 14px;">'+attendance[i].total_late_count+'</td>';
		html += `<td style="text-align:center;font-size: 14px;">${totalAbsentDays}, LF: ${attendance[i].totalleavestaken}, LNF: ${totalAbsentDays - attendance[i].totalleavestaken}</td>`;

		// here we need to the leave category's leave taken count
		if(Object.entries(all_active_leave_categories)?.length){
			for(let [catId,catName] of Object.entries(all_active_leave_categories)){
				if(catId in attendance[i].total_leave_taken_for_duration){
					const noOfDays=attendance[i].total_leave_taken_for_duration[catId];
					html += `<td class="leave-cat-show-hide" style="text-align:center;font-size: 14px;display:${isLeaveCateVissible==false && "none" || ""};">${noOfDays}</td>`;
					json[catName] = noOfDays;
				}else{
					html += `<td class="leave-cat-show-hide" style="text-align:center;font-size: 14px;display:${isLeaveCateVissible==false && "none" || ""};">0</td>`;
					json[catName] = 0;
				}
			}
		}

		for(var k in dates) {
			if(dates[k].format2 in attendance[i]) {
				var att_status = attendance[i][dates[k].format2].status;
				var is_late = attendance[i][dates[k].format2].is_late;
				const isToday=attendance[i][dates[k].format2].is_today;
				if($("#show_in_out_time").is(':checked')) {
					html += '<td class="in-out" style="text-align:center;min-width: 80px;">'+attendance[i][dates[k].format2].first_check_in_time+'</td>';
					html += `<td class="in-out" style="text-align:center;min-width: 80px;">${attendance[i][dates[k].format2].last_check_out_time} ${attendance[i][dates[k].format2].is_auto_check_out}</td>`;
					html += '<td class="in-out" style="text-align:center;min-width: 100px;">'+calculateTime(attendance[i][dates[k].format2].duration)+'</td>';
				} else {
					html += '<td class="in-out" style="text-align:center;display:none;min-width: 80px;">'+attendance[i][dates[k].format2].first_check_in_time+'</td>';
					html += `<td class="in-out" style="text-align:center;display:none;min-width: 80px;">${attendance[i][dates[k].format2].last_check_out_time} ${attendance[i][dates[k].format2].is_auto_check_out}</td>`;
					html += '<td class="in-out" style="text-align:center;display:none;min-width: 100px;">'+calculateTime(attendance[i][dates[k].format2].duration)+'</td>';
				}

				if(isToday==1){
					html += '<td style="min-width: 100px;text-align:center;">';
					html += "-";
					json[dates[k].format2] = att_status;
				}else{
					html += '<td style="min-width: 100px;text-align:center;background-color: '+colors[att_status]+'">';

					if(attendance[i][dates[k].format2].on_leave==1 && attendance[i][dates[k].format2]?.leave_information?.all_taken_leaves_names_for_single_Day){
						html += attendance[i][dates[k].format2]?.leave_information?.all_taken_leaves_names_for_single_Day;
						json[dates[k].format2] = attendance[i][dates[k].format2]?.leave_information?.all_taken_leaves_names_for_single_Day;
					}else{
						html += att_status;
						json[dates[k].format2] = att_status;
					}
				}
				// if(att_status == 'AB' || att_status == 'HD') {
				// 	// html += ' (LNF)';
				// }
				if(att_status.slice(0,2) !== 'AB' && is_late == 1) {
					html += ' <span style="color: #a30404;">(L)</span>';
				}
				html += '</td>';
			} else {
				const isVisible = $("#show_in_out_time").is(':checked');

				const displayStyle = isVisible ? '' : 'display:none;';
				
				html += `<td class="in-out" style="text-align:center;${displayStyle}min-width: 80px;">-</td>`;
				html += `<td class="in-out" style="text-align:center;${displayStyle}min-width: 80px;">-</td>`;
				html += `<td class="in-out" style="text-align:center;${displayStyle}min-width: 100px;">-</td>`;
				html += `<td style="min-width: 100px;text-align:center;">-</td>`;
				json[dates[k].format2] = '-';
			}
		}
		json_data.push(json);
		html += '</tr>';
	}
	html += '</tbody>';
	return html;
}

function calculateTime(duration) {
	if(duration == 0) return '';
    if(duration < 60) {
      return duration + ' mins';
    } else {
      var hr = duration / 60;
      return parseInt(hr) + ' hr ' + (duration % 60) + ' mins';
    }
  }

function getAttendanceData() {
	$("#getReportBtn").prop("disabled",true).text("Please wait...");

	$("#attendance-data").html('<div class="text-center"><i class="fa fa-spinner fa-spin" style="font-size: 3rem;"></i></div>');
	var from_date = $("#from_date").val();
	var to_date = $("#to_date").val();
	var selected_staff_type = $("#selected_staff_type").val();
	var staff_status_type = $("#staff_status_type").val();
	var show_late_only = $("#show_late_only").val();

	$.ajax({
        url: '<?php echo site_url('staff/attendance/getDayWiseAttendance'); ?>',
        type: 'post',
        data: {'from_date':from_date, 'to_date': to_date, 'selected_staff_type': selected_staff_type, 'show_late_only': show_late_only,"staff_status_type":staff_status_type},
        success: function(data) {
			if(data!=0){
				var response = JSON.parse(data);

				// Check if chunked processing is needed
				if(response.chunked_processing) {
					processChunkedData(response);
				} else {
					// Original processing for smaller date ranges
					var dates = response.dates;
					var attendance = response.attendance;
					var all_active_leave_categories = response.all_active_leave_categories;

					displayAttendanceReport(dates, attendance, all_active_leave_categories);
					$("#getReportBtn").prop("disabled",false).text("Get Report");
				}
			}else{
				$("#getReportBtn").prop("disabled",false).text("Get Report");
				$(".check-boxes").hide();
				  let msg = `
					<div style="color:red;text-align:center;
						color: black;
						border: 2px solid #fffafa;
						text-align: center;
						border-radius: 6px;
						position: relative;
						margin-left: 14px;
						padding: 10px;
						font-size: 14px;
						margin-top: 14px;
						background: #ebf3ff;">
						Found no data to show
						</div>
					`;
				$("#attendance-data").html(msg);
			}
        },
        error: function (err) {
		    console.log(err);
			$("#getReportBtn").prop("disabled",false).text("Get Report");
		}
    });
}

// Global variables for chunked processing
var chunkedAttendanceData = [];
var chunkedDates = [];
var chunkedLeaveCategories = {};
var chunkedFilters = {};

function processChunkedData(response) {
	chunkedDates = response.dates;
	chunkedLeaveCategories = response.all_active_leave_categories;
	chunkedFilters = response.filters;
	chunkedAttendanceData = [];

	var totalChunks = response.total_date_chunks * response.total_staff_chunks;
	var processedChunks = 0;

	// Show progress indicator
	showChunkedProgress(0, totalChunks);

	// Process each date chunk with each staff chunk
	var chunkPromises = [];

	for(var dateChunkIndex = 0; dateChunkIndex < response.date_chunks.length; dateChunkIndex++) {
		for(var staffChunkIndex = 0; staffChunkIndex < response.staff_chunks.length; staffChunkIndex++) {
			var dateChunk = response.date_chunks[dateChunkIndex];
			var staffChunk = response.staff_chunks[staffChunkIndex];

			var chunkPromise = processAttendanceChunk(
				dateChunk.from_date,
				dateChunk.to_date,
				staffChunk,
				chunkedFilters.selected_staff_type,
				chunkedFilters.staff_status_type,
				processedChunks,
				totalChunks
			).then(function(chunkData) {
				processedChunks++;
				updateChunkedProgress(processedChunks, totalChunks);

				if(chunkData && chunkData.attendance_data) {
					// Merge chunk data with existing data
					mergeChunkData(chunkData.attendance_data);
				}

				// If all chunks processed, display final result
				if(processedChunks >= totalChunks) {
					finalizeChunkedReport();
				}
			});

			chunkPromises.push(chunkPromise);
		}
	}
}

function processAttendanceChunk(chunk_from_date, chunk_to_date, staff_ids, selected_staff_type, staff_status_type, chunk_index, total_chunks) {
	return new Promise(function(resolve, reject) {
		$.ajax({
			url: '<?php echo site_url('staff/attendance/processAttendanceChunk'); ?>',
			type: 'post',
			data: {
				'chunk_from_date': chunk_from_date,
				'chunk_to_date': chunk_to_date,
				'staff_ids': staff_ids,
				'selected_staff_type': selected_staff_type,
				'staff_status_type': staff_status_type,
				'chunk_index': chunk_index,
				'total_chunks': total_chunks
			},
			success: function(data) {
				try {
					var chunkResponse = JSON.parse(data);
					if(chunkResponse.success) {
						resolve(chunkResponse);
					} else {
						console.error('Chunk processing failed:', chunkResponse.error);
						resolve(null);
					}
				} catch(e) {
					console.error('Error parsing chunk response:', e);
					resolve(null);
				}
			},
			error: function(err) {
				console.error('Chunk request failed:', err);
				resolve(null);
			}
		});
	});
}

function mergeChunkData(chunkAttendanceData) {
	// Merge attendance data by staff_id
	chunkAttendanceData.forEach(function(staffData) {
		var existingStaffIndex = chunkedAttendanceData.findIndex(function(existing) {
			return existing.staff_id === staffData.staff_id;
		});

		if(existingStaffIndex >= 0) {
			// Merge attendance records for existing staff
			var existingStaff = chunkedAttendanceData[existingStaffIndex];

			// Merge daily attendance records
			Object.keys(staffData).forEach(function(key) {
				if(key !== 'staff_id' && key !== 'staff_name' && key !== 'employee_code' &&
				   key !== 'email' && key !== 'picture_url' && !key.startsWith('total')) {
					existingStaff[key] = staffData[key];
				}
			});

			// Update totals by adding values
			['totalPresentDays', 'totalWorkingDays', 'totalabsentDays', 'totalweekoff',
			 'totalhoiday', 'totalleavestaken', 'total_work_duration', 'total_ot',
			 'total_lateby', 'total_late_count', 'total_earlyby', 'total_duration'].forEach(function(totalKey) {
				if(staffData[totalKey] !== undefined) {
					existingStaff[totalKey] = (existingStaff[totalKey] || 0) + (staffData[totalKey] || 0);
				}
			});
		} else {
			// Add new staff data
			chunkedAttendanceData.push(staffData);
		}
	});
}

function showChunkedProgress(processed, total) {
	var progressHtml = `
		<div class="text-center" id="chunked-progress">
			<div class="mb-3">
				<i class="fa fa-spinner fa-spin" style="font-size: 2rem;"></i>
			</div>
			<div class="progress" style="height: 25px;">
				<div class="progress-bar progress-bar-striped progress-bar-animated"
					 role="progressbar" style="width: 0%" id="chunk-progress-bar">
					0%
				</div>
			</div>
			<p class="mt-2">
				<small>Processed <span id="processed-chunks">0</span> of <span id="total-chunks">${total}</span> chunks</small>
			</p>
		</div>
	`;
	$("#attendance-data").html(progressHtml);
}

function updateChunkedProgress(processed, total) {
	var percentage = Math.round((processed / total) * 100);
	$("#chunk-progress-bar").css('width', percentage + '%').text(percentage + '%');
	$("#processed-chunks").text(processed);
}

function finalizeChunkedReport() {
	// Apply late filter if needed
	var finalAttendanceData = chunkedAttendanceData;
	if(chunkedFilters.show_late_only === '1') {
		finalAttendanceData = filterLateAttendance(chunkedAttendanceData);
	}

	// Display the final report
	displayAttendanceReport(chunkedDates, finalAttendanceData, chunkedLeaveCategories);
	$("#getReportBtn").prop("disabled",false).text("Get Report");
}

function filterLateAttendance(attendanceData) {
	return attendanceData.filter(function(staffData) {
		// Check if staff has any late attendance
		for(var key in staffData) {
			if(staffData[key] && typeof staffData[key] === 'object' && staffData[key].is_late == 1) {
				return true;
			}
		}
		return false;
	});
}

function displayAttendanceReport(dates, attendance, all_active_leave_categories) {
	var html = '<div class="d-flex justify-content-between align-items-center"><p><b style="color: red;">LF </b>: Leave Filed, <b style="color: red;">LNF </b>: Leave Not Filed, <b style="color: red;">L </b>: Late</p><div class="d-flex align-items-center"><div class="form-group" id="range-input" style="width: 300px;"></div><button class="mx-2 btn btn-primary mb-2 pull-right" onclick="exportToExcel()">Excel</button></div></div>';
	html += '<div class="table-responsive" id="report-container">';
	html += '<table id="att-table" class="table table-bordered">';
	html += constructHeader(dates, all_active_leave_categories);
	html += constructReport(dates, attendance, all_active_leave_categories);
	html += '</table>';
	html += '</div>';
	$("#attendance-data").html(html);
	$(".check-boxes").show();
	add_scroller('report-container');
}

function exportToExcel() {
	var wb = XLSX.utils.book_new();
	var from_date = $("#from_date").val();
	var to_date = $("#to_date").val();
	wb.Props = {
	      Title: "Staff Attendance Report - "+from_date+" to "+to_date,
	      Subject: "Attendance Report",
	      Author: "NextElement",
	      CreatedDate: new Date()
	};

	wb.SheetNames.push("Attendance");
	var ws_school;
	if($("#show_in_out_time").is(':checked')) {
		ws_school = XLSX.utils.table_to_sheet(document.getElementById('att-table'));
	} else {
		ws_school = XLSX.utils.json_to_sheet(json_data, {'headers' : headers});
	}
	wb.Sheets["Attendance"] = ws_school;

	var wbout = XLSX.write(wb, {bookType:'xlsx',  type: 'binary'});
	downloadSample(wbout);
}

function s2ab(s) {
  	var buf = new ArrayBuffer(s.length);
  	var view = new Uint8Array(buf);
  	for (var i=0; i<s.length; i++) view[i] = s.charCodeAt(i) & 0xFF;
  	return buf;
  
}

const reportName=`staff_attendance_day_wise_status_report_${new Date().toLocaleString('default', { month: 'short' })+" "+new Date().getDate()+" "+new Date().getFullYear()}_${new Date().getHours()+""+new Date().getMinutes()}.xlsx`;


function downloadSample(wbout){
  	saveAs(new Blob([s2ab(wbout)],{type:"application/octet-stream"}), reportName);
};
</script>