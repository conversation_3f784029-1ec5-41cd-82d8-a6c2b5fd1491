

<link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0/css/all.min.css">
<link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/select2/4.0.13/css/select2.min.css">
<script src="https://cdn.jsdelivr.net/npm/sweetalert2@11"></script>
<script src="https://cdnjs.cloudflare.com/ajax/libs/select2/4.0.13/js/select2.min.js"></script>
<script>
    $(document).ready(function() {
        $('#invoice_date, #due_date').datetimepicker({
            format: 'DD-MM-YYYY',
            useCurrent: true
        });

    });

    function save_basic_details(current, step_selector, step_number) {
        var $form = $('#'+step_selector+'-form');
        if ($form.parsley().validate()) {
            var isDatesValid= validateDateRange();
            if(isDatesValid) {
                
            $(current).prop('disabled', true).html('Please Wait...');
            var form = $('#'+step_selector+'-form')[0];
            var formData = new FormData(form);
            formData.append('step_number',step_selector);
            // formData.append('step_selector',step_selector);

            $.ajax({
                url: '<?php echo site_url('procurement/invoice_controller_v2/save_invoice_basic_details'); ?>',
                type: 'post',
                data: formData,
                processData: false,
                contentType: false,
                cache : false,
                success: function(data) {
                    let p_data= JSON.parse(data);
                    if(Object.keys(p_data)?.length) {
                        if(p_data.status == '0') {
                            Swal.fire({
                                icon: 'error',
                                title: 'Something went wrong'
                            });
                        } else {
                            
                            if( $("#insert_update_type_basic").val() == 'Add' ) {
                                __construct_delivery_items(p_data);
                            }
                            $(".invoice_master_id").val(p_data.invoice_master_id);
                            $("#insert_update_type_basic").val('update');
                            $("#invoice_number").val(p_data.derivedInvoiceNumber);
                            $(".selected_purchase_order_id").val(p_data.purchase_order_id);
                            $(".selected_vendor_id").val(p_data.vendor_id);
                            $("#step-1").addClass('hidden');
                            $("#step-2").removeClass('hidden');

                            $("div.circle-" +step_number).html(`<span class="fa fa-check"></span>`).css('background', 'green');
                            $("div.circle-" + (Number(step_number) + 1)).html(`o`).css('background', 'green');

                            // __check_approvers_availability(p_data.is_approvers_added);
                        }

                    } else {
                        Swal.fire({
                            icon: 'error',
                            title: 'Something went wrong'
                        });
                    }
                    $(current).prop('disabled', false).html('Next');
                }
            });
        }
        }
    }

    function __check_approvers_availability(is_approvers_added) {
        if(is_approvers_added == '0') {
            return Swal.fire({
                icon: 'error',
                title: "Looks like there is no approvers added for this purchase order",
                showCancelButton: true,
                showConfirmButton: true,
                confirmButtonText: 'Change Purchase Order',
                cancelButtonText: 'Cancel Invoice Creation',
            }).then((result) => {
                if(result.isConfirmed) {
                    $("#delivery-challan-prev-btn").click();
                } else {
                    window.location.href= '<?php echo site_url('procurement/invoice_controller_v2/manage_all_invoices'); ?>';
                }
            });
        }
    }

    function __construct_po(details, invoiced_amount, released_amount) {
        let pending_amount_to_invoice= Number(details.total_po_amount) - Number(invoiced_amount);
                let total_po_gst_amount= Number(details.total_po_gst_amount);


                                if(details.request_type != 'Service Milestones') {
                                    po_details= `<h4>PO Details</h4><div style="margin-left: -10px;">
                                                <div class="col-md-4">
                                                    <p><strong>Number:</strong>
                                                        <a target="_blank" href="<?php echo site_url('procurement/requisition_controller_v2/view_purchase_order/') ?>${details.id}/direct/0">${details.request_number}</a>
                                                    </p>
                                                    <p><strong>Name:</strong>
                                                    ${details.po_name && details.po_name != 'null' ? details.po_name : '-'}
                                                    </p>
                                                    <p><strong>Narration:</strong>
                                                        ${details.remarks && details.remarks != 'null' ? details.remarks : '-'}
                                                    </p>
                                                </div>
                                                <div class="col-md-4">
                                                    <p><strong>Total PO Amount:</strong>
                                                        ₹${Number(details.total_po_amount).toLocaleString()}
                                                    </p>
                                                    <p><strong>Total PO GST Amount:</strong>
                                                        ₹${Number(total_po_gst_amount).toLocaleString()}
                                                    </p>
                                                    <p><strong>Amount Invoiced:</strong>
                                                        ₹${(Number(invoiced_amount)).toLocaleString()}
                                                    </p>
                                                    <p><strong>Amount Pending:</strong>
                                                        ₹${Number(pending_amount_to_invoice).toLocaleString()}
                                                    </p>
                                                </div>
                                                <div class="col-md-4">
                                                    <p><strong>No. of Items:</strong>
                                                        ${Number(details.total_items).toLocaleString()}
                                                    </p>
                                                    <p><strong>PO Status:</strong>
                                                        ${details.status}
                                                    </p>
                                                    <p><strong>Delivery Status:</strong>
                                                        ${details.delivery_status}
                                                    </p>
                                                    
                                                </div>
                                            </div>
                                            <div class="col-md-12" style="height: 10px;"></div>`;
                                } else {
                                    po_details= `<h4>PO Details</h4><div style="margin-left: -10px;">
                                                <div class="col-md-4">
                                                    <p><strong>Number:</strong>
                                                        <a target="_blank" href="<?php echo site_url('procurement/requisition_controller_v2/view_purchase_order/') ?>${details.id}/direct/0">${details.request_number}</a>
                                                    </p>
                                                    <p><strong>Name:</strong>
                                                    ${details.po_name && details.po_name != 'null' ? details.po_name : '-'}
                                                    </p>
                                                    <p><strong>Narration:</strong>
                                                        ${details.remarks && details.remarks != 'null' ? details.remarks : '-'}
                                                    </p>
                                                </div>
                                                <div class="col-md-4">
                                                    <p><strong>Total PO Amount:</strong>
                                                        ₹${Number(details.total_po_amount).toLocaleString()}
                                                    </p>
                                                    <p><strong>Total PO GST Amount:</strong>
                                                        ₹${Number(total_po_gst_amount).toLocaleString()}
                                                    </p>
                                                    <p><strong>Amount Invoiced:</strong>
                                                        ₹${(Number(invoiced_amount)).toLocaleString()}
                                                    </p>
                                                    <p><strong>Amount Pending:</strong>
                                                        ₹${Number(pending_amount_to_invoice).toLocaleString()}
                                                    </p>
                                                </div>
                                                <div class="col-md-4">
                                                    <p><strong>No. of Milestones:</strong>
                                                        ${Number(details.total_milestones).toLocaleString()}
                                                    </p>
                                                    <p><strong>PO Status:</strong>
                                                        ${details.status}
                                                    </p>
                                                    <p><strong>Delivery Status:</strong>
                                                        ${details.delivery_status}
                                                    </p>
                                                    
                                                </div>
                                            </div>
                                            <div class="col-md-12" style="height: 10px;"></div>`;
                                }



        $("#po-details-div").html(po_details);
    }

    function __construct_deliveries_gdc(details, reference_type) {
        let delivery= ` <h4>Delivery Challan Details</h4><table class="table table-bordered">
                                <thead class="thead-dark">
                                    <tr>
                                        <th>Number</th>
                                        <th>Type</th>
                                        <th>Narration</th>
                                        <th>Delivered Quantity</th>
                                    </tr>
                                </thead>
                                <tbody>`;
        for(var i= 0; i<details.length; i++) {
            delivery += `
                                    <tr>
                                        <td><a target="_blank" href="<?php echo site_url('procurement/invoice_controller_v2/readInvoiceV2/') ?>${details[i].delivery_challan_master_id}/direct/0">${details[i].delivery_challan_note_number}</a></td>
                                        <td>${reference_type}</td>
                                        <td>${(details[i].narration).trim() == '' ? '-' : details[i].narration}</td>
                                        <td>${details[i].initial_quantity}</td>
                                    </tr>`;
        }
        delivery += `</tbody> </table>`;
        $("#delivery-details-div").html(delivery);
    }

    function __construct_deliveries_sdc(details, reference_type) {
        let delivery= `<h4>Delivery Challan Details</h4><table class="table table-bordered">
                                <thead class="thead-dark">
                                    <tr>
                                        <th>Number</th>
                                        <th>Type</th>
                                        <th>Narration</th>
                                        <th>Delivered Quantity</th>
                                    </tr>
                                </thead>
                                <tbody>`;
        for(var i= 0; i<details.length; i++) {
            delivery += `
                                    <tr>
                                        <td><a target="_blank" href="<?php echo site_url('procurement/Requisition_controller_v2/view_service_delivery_challan/') ?>${details[i].procurement_sdc_id}">${details[i].sdc_number}</a></td>
                                        <td>${reference_type}</td>
                                        <td>${details[i].narration}</td>
                                        <td>${details[i].initial_quantity}</td>
                                    </tr>`;
        }
        delivery += `</tbody> </table>`;
        $("#delivery-details-div").html(delivery);
    }

    function __construct_payment_details(po_details, invoiced_details) {
        let po_amount= Number(po_details.total_po_amount);
        let total_po_gst_amount= Number(po_details.total_po_gst_amount);
        let total_invoiced_amount= Number(invoiced_details.invoiced_amount);

        let pending_amount_to_invoice= po_amount - total_invoiced_amount;
        let payment= `<div class="col-md-12" style="height: 10px;"></div><h4>Payment Details</h4><div style="width: 100%;">
                        <div class="col-md-6">
                            <label style="width: 100%;">Invoice Amount</label>
                            <input
                                step="0.1"
                                class="form-control"
                                onkeyup="calculate_total_invoiced_amount('yes')"
                                onpaste="calculate_total_invoiced_amount('yes')" 
                                name="invoiced_amount_form"
                                id="invoiced_amount_form"
                                type="number" 
                                style="
                                    height: 30px;
                                    width: 100%;
                                    padding: 10px;
                                    border: 1px solid #ccc;
                                    border-radius: 5px;
                                "
                                min="1"
                                max="${pending_amount_to_invoice}"
                                value="${Number(pending_amount_to_invoice) > 0 ? Number(pending_amount_to_invoice) : 0}" 
                                placeholder="Enter number"
                            />
                        </div>
                        <div class="col-md-6">
                            <label style="width: 100%;">cGST %</label>
                            <input
                                step="0.1"
                                class="form-control"
                                onkeyup="calculate_gst_amount('cGST', 'cgst_per')"
                                onpaste="calculate_gst_amount('cGST', 'cgst_per')" 
                                 name="cgst_per"
                                id="cgst_per"
                                type="number" 
                                style="
                                    height: 30px;
                                    width: 100%;
                                    padding: 10px;
                                    border: 1px solid #ccc;
                                    border-radius: 5px;
                                "
                                min="0"
                                value="0" 
                                placeholder="Enter cGST %"
                            />
                        </div>
                         <div class="col-md-12" style="height: 10px;"></div>
                        <div class="col-md-6">
                            <label style="width: 100%;">cGST Amount</label>
                            <input
                                step="0.1"
                                class="form-control"
                                name="cgst_amt"
                                id="cgst_amt"
                                type="number" 
                                style="
                                    height: 30px;
                                    width: 100%;
                                    padding: 10px;
                                    border: 1px solid #ccc;
                                    border-radius: 5px;
                                "
                                min="0"
                                value="0" 
                                readonly
                                placeholder="Enter cGST Amount"
                            />
                        </div>
                       
                        <div class="col-md-6">
                            <label style="width: 100%;">sGST %</label>
                            <input
                                step="0.1"
                                class="form-control"
                                onkeyup="calculate_gst_amount('sGST', 'sgat_per')"
                                onpaste="calculate_gst_amount('sGST', 'sgat_per')" 
                                name="sgat_per"
                                id="sgat_per"
                                type="number" 
                                style="
                                    height: 30px;
                                    width: 100%;
                                    padding: 10px;
                                    border: 1px solid #ccc;
                                    border-radius: 5px;
                                "
                                min="0"
                                value="0" 
                                placeholder="Enter sGST %"
                            />
                        </div>
                         <div class="col-md-12" style="height: 10px;"></div>
                        <div class="col-md-6">
                            <label style="width: 100%;">sGST Amount</label>
                            <input
                                step="0.1"
                                class="form-control"
                                name="sgat_amt"
                                id="sgat_amt"
                                type="number" 
                                style="
                                    height: 30px;
                                    width: 100%;
                                    padding: 10px;
                                    border: 1px solid #ccc;
                                    border-radius: 5px;
                                "
                                min="0"
                                value="0" 
                                readonly
                                placeholder="Enter sGST Amount"
                            />
                        </div>


                        
                        <div class="col-md-6">
                            <label style="width: 100%;">Round</label>
                            <input
                                step="0.1"
                                class="form-control"
                                onkeyup="calculate_total_invoiced_amount('no')"
                                onpaste="calculate_total_invoiced_amount('no')" 
                                name="round_form"
                                id="round_form"
                                type="number" 
                                style="
                                    height: 30px;
                                    width: 100%;
                                    padding: 10px;
                                    border: 1px solid #ccc;
                                    border-radius: 5px;
                                "
                                min="-0.99"
                                max="0.99"
                                value="0" 
                                placeholder="Enter rounding adjustment"
                            />
                        </div>
                         <div class="col-md-12" style="height: 10px;"></div>
                        <div class="col-md-6">
                            <label style="width: 100%;">Total</label>
                            <input
                                step="0.1"
                                class="form-control" 
                                name="total_invoiced_amount_form"
                                id="total_invoiced_amount_form"
                                type="number" 
                                style="
                                    height: 30px;
                                    width: 100%;
                                    padding: 10px;
                                    border: 1px solid #ccc;
                                    border-radius: 5px;
                                    pointer-events: none;
                                    background: #eee;
                                " 
                                value="${pending_amount_to_invoice}"
                                placeholder="Enter number"
                            />
                        </div>
                         
                    </div>
                    <div class="col-md-12" style="height: 5px;"></div>
                    `;
        $("#payment-details-div").html(payment);
    }

    let timeout;
    function calculate_gst_amount(gst_type, input_id) {
        let gst_percentage= Number($("#"+input_id).val());
        if(gst_type == 'cGST') {
            let invoiced_amount= Number($("#invoiced_amount_form").val()) || 0;
            let cgst_amt= (Number(invoiced_amount) * Number(gst_percentage)) / 100;
            $("#cgst_amt").val(cgst_amt.toFixed(2));
        } else {
            let invoiced_amount= Number($("#invoiced_amount_form").val()) || 0;
            let sgat_amt= (Number(invoiced_amount) * Number(gst_percentage)) / 100;
            $("#sgat_amt").val(sgat_amt.toFixed(2));
        }
        if(timeout!==undefined && clearTimeout(timeout));
        timeout= setTimeout(calculate_total_invoiced_amount('no'), 500);
    }

    function calculate_total_invoiced_amount(should_gst_calculate) { // 'should_gst_calculate' is required to calculate the GST at once otherwise it will become the recurring function
        if(should_gst_calculate == 'yes') {
            calculate_gst_amount('cGST', 'cgst_per');
            calculate_gst_amount('sGST', 'sgat_per');
        }
        let invoiced_amount= Number($("#invoiced_amount_form").val()) || 0;
        let cgst_amt= Number($("#cgst_amt").val()) || 0;
        let sgat_amt= Number($("#sgat_amt").val()) || 0;
        let round_form= Number($("#round_form").val()) || 0;
        let total_invoiced_amount= Number(invoiced_amount) + Number(sgat_amt) + Number(cgst_amt) + Number(round_form);
        $("#total_invoiced_amount_form").val(total_invoiced_amount.toFixed(2));
    }

    function __construct_delivery_items(ObjectData) {
        let reference_type= ObjectData.reference_type;
        let gdc= ObjectData.gdc;
        let sdc= ObjectData.sdc;
        let po_details= ObjectData.po_details;
        let invoiced_details= ObjectData.invoiced_details;
        let invoice_master_id= ObjectData.invoice_master_id;
        let vendor_id= ObjectData.vendor_id;
        let purchase_order_id= ObjectData.purchase_order_id;
        let derivedInvoiceNumber= ObjectData.derivedInvoiceNumber;

        // if(Number(po_details.total_po_amount) == (Number(invoiced_details.subtotal_amount) + Number(invoiced_details.discount_amount))) {
        //     return Swal.fire({
        //         icon: 'error',
        //         title: "Looks like whole PO amount have been invoiced already.",
        //         showCancelButton: true,
        //         showConfirmButton: true,
        //         confirmButtonText: 'Change Purchase Order',
        //         cancelButtonText: 'Cancel Invoice Creation',
        //     }).then((result) => {
        //         if(result.isConfirmed) {
        //             $("#delivery-challan-prev-btn").click();
        //         } else {
        //             window.location.href= '<?php // echo site_url('procurement/invoice_controller_v2/manage_all_invoices'); ?>';
        //         }
        //     });
        // }

        if(Object.keys(po_details)?.length) {
            __construct_po(po_details, invoiced_details.invoiced_amount, invoiced_details.released_amount);
        } else {
            $("#po-details-div").html('');
        }

        if(gdc.length) {
            __construct_deliveries_gdc(gdc, reference_type);
        } else if(sdc.length) {
            __construct_deliveries_sdc(sdc, reference_type);
        } else {
            $("#delivery-details-div").html('');
        }

        if(Object.keys(invoiced_details)?.length && Object.keys(po_details)?.length) {
            __construct_payment_details(po_details, invoiced_details);
        } else {
            $("#payment-details-div").html('');
        }


        
    }

    function info_about_calculations() {
        Swal.fire({
            icon: 'info',
            html: `Both cGST and sGST are not applied at the unit level; rather, they are calculated based on the total taxable value (unit price * quantity). Similarly, discount amounts are also computed at the aggregate level (price * quantity) rather than per unit. This ensures accurate tax assessment and discount application in accordance with standard accounting and taxation practices.`
        });
    }

    function calculate_individual_delivery(current, changed_field_type, challan_master_id) {
        let price_for_invoicing= $("input#amount_to_be_invoiced"+challan_master_id).val();
        let discount_for_invoicing= $("input#discount_for_invoicing"+challan_master_id).val();
        console.log('price_for_invoicing- ', discount_for_invoicing)
        
        let total_after_discount= Number(price_for_invoicing) - Number(discount_for_invoicing);
        
        $("input#total_after_discount_delivery_level_for_invoicing"+challan_master_id).val(total_after_discount);
        
        setTimeout(calculate_total_and_subtotal, 600);
    }

    function calculate_total_and_subtotal() {
        
        let price_summary= 0;
        let discount_summary= 0;
        let total_after_discount_summary= 0;

        let index= 0;
        let no_of_deliveries= 0;
        $("input.amount_to_be_invoiced_class").each(function() {
            let price_for_invoicing= $("input.amount_to_be_invoiced_class").eq(index).val();
            let discount_for_invoicing= $("input.discount_for_invoicing_class").eq(index).val();
            let total_after_discount_class= $("input.total_after_discount_class").eq(index).val();

            if(Number(price_for_invoicing) > 0) {
                no_of_deliveries++;
            }

            price_summary += Number(price_for_invoicing);
            discount_summary += Number(discount_for_invoicing);
            total_after_discount_summary += Number(total_after_discount_class);

            index++;
        });
        
        $("input#price_summary_td").val(price_summary);
        $("input#total_deliveries_summary_td").val(no_of_deliveries);
        $("input#discount_summary_td").val(discount_summary);
        $("input#total_after_discount_summary_td").val(total_after_discount_summary);
    }

    function validateDateRange() {
        let start_date = $("#invoice_date").val();
        let end_date = $("#due_date").val();

        // Check if both dates are empty
        if (!start_date && !end_date) {
            Swal.fire({
                icon: 'error',
                title: 'Oops...',
                text: 'Both invoice date and due date are required!',
            });
            return false;
        }

        // Check if start date is empty
        if (!start_date) {
            Swal.fire({
                icon: 'error',
                title: 'Oops...',
                text: 'Invoice date is required!',
            });
            return false;
        }

        // Check if end date is empty
        if (!end_date) {
            Swal.fire({
                icon: 'error',
                title: 'Oops...',
                text: 'Due date is required!',
            });
            return false;
        }

        var startDd = start_date.split('-');
        var endDd = end_date.split('-');
        startD= Number(startDd[2] + startDd[1] + startDd[0]);
        endD= Number(endDd[2] + endDd[1] + endDd[0]);
        if(startD > endD) {
            Swal.fire({
                icon: 'error',
                title: 'Invalid Dates picked',
                text: 'Invoice date must be before due date!',
            });
            return false;

        }
        // If all validations pass
        return true;
    }

    function onchange_PO() {
        let purchase_order_id= $("#purchase_order_id").val();
        if(purchase_order_id) {
            $.ajax({
                url: '<?php echo site_url('procurement/invoice_controller_v2/get_details_PO_wise'); ?>',
                type: 'POST',
                data: {purchase_order_id},
                success: function(goodies) {
                    let data= JSON.parse(goodies);
                    if(Object.keys(data)?.length) {
                        var vendor= data.vendor;
                        var instruments= data.instruments;
                        if(vendor && vendor != 'null' && Object.keys(vendor)?.length) {
                            let vendor_id= vendor.vendor_id;
                            let vendor_code= vendor.vendor_code;
                            let vendor_name= vendor.vendor_name;
                            $("#vendor_id").html(`<option value="${vendor_id}">${vendor_name}${vendor_code != '-' ? '-'+vendor_code : ''}</option>`);
                        }
                        if(instruments && instruments != 'null' && Object.keys(instruments)?.length) {
                            var options= '<option value="">Select..</option>';
                            for(var v of instruments) {
                                var id= v.id;
                                var instrument_name= v.instrument_name;
                                var payment_type= v.payment_type;
                                var bank_name= v.bank_name;
                                options += `<option value="${id}">${instrument_name != '-' ? instrument_name : ''}${payment_type != '-' ? '~'+payment_type : ''} ${bank_name != '-' ? bank_name : ''}</option>`;
                            }
                            $("#vendor_instruments_id").html(options);
                        }
                    }
                },
                error: function(glitch) {
                    console.warn(glitch);
                }
            });
        }
    }

    // Step- 2
    function save_delivery_items(current, step_selector, step_number) {

        let current_status= $("#insert_update_type_items").val();
        if(current_status != 'Add') {
            $("#step-2").addClass('hidden');
            $("#step-3").removeClass('hidden');
            $("div.circle-" +step_number).html(`<span class="fa fa-check"></span>`).css('background', 'green');
            $("div.circle-" + (Number(step_number) + 1)).html(`o`).css('background', 'green');

            return false;
            
        }

        // let isValid= false;

        // $("input.amount_to_be_invoiced_class").each(function() {
        //     if(Number($(this).val()) > 0) {
        //         isValid= true;
        //         return false;
        //     }
        // });

        // if(!isValid) {
        //     return Swal.fire({
        //         icon: 'error',
        //         title: 'Invalid Amount',
        //         text: 'Sub total amount should not be zero or less than zero.',
        //     });
        // }

        var $form = $('#'+step_selector+'-form');
        if ($form.parsley().validate()) {
            $(current).prop('disabled', true).html('Please Wait...');
            var form = $('#'+step_selector+'-form')[0];
            var formData = new FormData(form);
            formData.append('step_number',step_selector);
            // formData.append('step_selector',step_selector);

            $.ajax({
                url: '<?php echo site_url('procurement/invoice_controller_v2/save_delivery_items'); ?>',
                type: 'post',
                data: formData,
                processData: false,
                contentType: false,
                cache : false,
                success: function(data) {
                    let p_data= JSON.parse(data);
                    if(Object.keys(p_data)?.length) {
                        if(p_data.status == '0') {
                            Swal.fire({
                                icon: 'error',
                                title: 'Error',
                                text: p_data.message
                            });
                        } else {
                            __construct_approvers_details(p_data.approvers);
                            $(".invoice_master_id").val(p_data.invoice_master_id);
                            $("#insert_update_type_items").val('Update');
                            $("#step-2").addClass('hidden');
                            $("#step-3").removeClass('hidden');

                            $("div.circle-" +step_number).html(`<span class="fa fa-check"></span>`).css('background', 'green');
                            $("div.circle-" + (Number(step_number) + 1)).html(`o`).css('background', 'green');

                            $('#'+step_selector+'-form input').each(function() {
                                $(this).attr("readonly", true);
                            });

                            setTimeout(() => {
                                $("input.invoice_master_id, input.selected_purchase_order_id, input.selected_vendor_id, input.selected_reference_type").prop('readonly', false);
                                $("input#insert_update_type_items").prop('readonly', false);
                            }, 800);

                            $("#warning-message").html('Not editable');
                            // $(current).append(`<button type="button" onclick="next_page_from_delivery_items(this, 'step-2', 2)" class="btn btn-dark pull-right">Next</button>`).remove();

                        }

                    } else {
                        Swal.fire({
                            icon: 'error',
                            title: 'Something went wrong',
                                text: p_data.message
                        });
                    }
                    $(current).prop('disabled', false).html('Next');
                }
            });
        }
    }

    let has_department_approvers= false;
    function __construct_approvers_details(approvers) {
        if(!Object.keys(approvers)?.length) {
            $("#approvers_div").html(`
            <center> <img src="<?php echo base_url('assets/illustrations/no_approvers.svg'); ?>" class="img-fluid" style="width: 300px; height: 240px; object-fit: contain; margin-top: 20px;" /> <br><br> <h2>Approvers not found</h2> <p>Your invoice has not been assigned to any approver yet.</p> </center>
            `);
            return;
            has_department_approvers= false;
        }
        has_department_approvers= true;
        var html= `<table class="table table-bordered">
                        <thead class="thead-dark">
                            <tr>
                                <th>Approver Type</th>
                                <th>Approver</th>
                                <th>Department</th>
                                <th>Status</th>
                            </tr>
                        </thead>
                        <tbody>`;
        if(Object.keys(approvers)?.length) {
            for(var v of approvers) {
                html += `<tr>
                            <td>${v.approval_level}</td>
                            <td>${v.staff}</td>
                            <td>${v.department}</td>
                            <td>Pending</td>
                        </tr>`;
            }
        }
        html += ` </tbody>
                </table>`;
        $("#approvers_div").html(html);
    }

    function save_approver_details(current, step_selector, step_number) {
        $("#step-3").addClass('hidden');
        $("#step-4").removeClass('hidden');

        $("div.circle-" +step_number).html(`<span class="fa fa-check"></span>`).css('background', 'green');
        $("div.circle-" + (Number(step_number) + 1)).html(`o`).css('background', 'green');

    }

    async function add_additional_notes(current, close_addClose) {
        
        let additional_description_notes= $("#additional_description_notes").val();
        let invoice_master_id= $(".invoice_master_id").val();
        
        const fileInput = document.getElementById('additional_attachements');
        const file = fileInput.files[0];
        const fileInfoDiv = document.getElementById('fileInfo');

        // Check if the file is a PDF
        if (file.type !== 'application/pdf') {
            Swal.fire({
                icon: 'error',
                title: 'Error',
                text: 'Only PDF type files can be uploaded'
            });
            // Clear the file input
            fileInput.value = '';
            fileInput.value = '';
            fileName.textContent = 'No file selected';
            $("form#docs_form").trigger('reset');
            return false;
        }



        $("#attachments-list-empty").hide();
        $("#additional_attachements_div").show();
        $("tr.tr_class_remover").remove();
        $("tbody#additional_attachements_tbody").append(
            `<tr class="tr_class_remover" class="text-center">
                                    <td colspan="3" style="height: 60px; vertical-align: middle; width: 100%; border-right: none;">
                                        <div class="col-md-12" style="width: 100%;"></div>
                                            <div id="show_progress_color" style="height: 20px; background: lightgreen; width: 1%;"><div>
                                        </div>
                                    </td>

                                    <td style="width: 50px; border-left: none;">
                                        <div class="pull-right" id="show_progress_percentage" style="text-align: right; font-weight: bold; color: black; font-weight: bold; font-size: 24px;"><div>
                                    </td>
                            </tr>`
        );

        let fileName1= '';
        let fileSizeFormatted1= '';
        let size= 0;
        let splittedFileType= [];

        const MAX_FILE_SIZE= '<?php echo $MAX_FILE_SIZE; ?>';
        const MAX_FILE_SIZE_BYTES = convertSizeToBytes(MAX_FILE_SIZE);
        
        if (file) {
            const fileName = file.name;
            const fileSizeBytes = file.size;
            const fileExtentionType = file.type;
            size= fileSizeBytes;
            splittedFileType= fileExtentionType.split('/');
            let fileSizeFormatted;
            if (fileSizeBytes < 1024) {
                fileSizeFormatted = fileSizeBytes + ' bytes';
            } else if (fileSizeBytes < 1024 * 1024) {
                fileSizeFormatted = (fileSizeBytes / 1024).toFixed(2) + ' KB';
            } else {
                fileSizeFormatted = (fileSizeBytes / (1024 * 1024)).toFixed(2) + ' MB';
            }
            fileName1= fileName;
            fileSizeFormatted1= fileSizeFormatted;
        } else {
            return Swal.fire({
                icon: 'error',
                title: 'Fill all the necessary fields'
            });
        }

        if (Number(size) > Number(MAX_FILE_SIZE_BYTES)) {
            Swal.fire({
                icon: 'error',
                title: 'File size exceeds the limit',
                text: `Maximum allowed file size is ${MAX_FILE_SIZE}.`
            });
            fileInput.value = '';
            fileName.textContent = 'No file selected';
            $("form#docs_form").trigger('reset');
            return false;
        }
        

        var form = $('#docs_form')[0];
        var formData = new FormData(form);
        formData.append('fileName', fileName1);
        formData.append('fileSizeBytes', size);
        formData.append('fileExtentionType', splittedFileType[1]);

         let relative_path_details= await upload_file_to_wasabi();
        let path= relative_path_details.path;

        formData.append('path', path);

        $.ajax({
            url: '<?php echo site_url('procurement/invoice_controller_v2/add_document'); ?>',
            type: 'post',
            data: formData,
            processData: false,
            contentType: false,
            cache : false,

            xhr: function() {
                var xhr = new window.XMLHttpRequest();
                var lastPercent = 0; // Track the last logged percentage
                var interval; // For simulated progress

                // $("#show_attachements_progress_modal").modal('show');
                xhr.upload.addEventListener("progress", function(evt) {
                    if (evt.lengthComputable) {
                        var currentPercent = Math.round((evt.loaded / evt.total) * 100);
                        
                        // Clear any previous interval to avoid duplicates
                        if (interval) clearInterval(interval);
                        
                        // Simulate smooth progress between real events
                        interval = setInterval(function() {
                            if (lastPercent < currentPercent) {
                                lastPercent++;
                                $("div#show_progress_percentage, div#show_progress_color_modal").html(lastPercent + '%');
                                $("div#show_progress_color, div#show_progress_percentage_modal").css('width', lastPercent +'%');
                            } else {
                                clearInterval(interval);
                            }
                        }, 50); // Adjust speed (ms) for smoother/faster increments
                    }
                }, false);

                // Ensure 100% is logged when upload completes
                xhr.addEventListener("load", function() {
                    clearInterval(interval); // Stop simulation
                    // $("#show_attachements_progress_modal").modal('hide');
                    if (lastPercent < 100) {
                        $("div#show_progress_percentage, div#show_progress_color_modal").html(100 + '%');
                        $("div#show_progress_color, div#show_progress_percentage_modal").css('width', 100 +'%');
                    }
                });

                return xhr;
            },

            success: function(data) {
                let p_data= JSON.parse(data);
                if(Object.keys(p_data)?.length) {
                    if(p_data.status == '1') {
                        // var download_url= '<?php // echo site_url('procurement/invoice_controller_v2/download_invoice_attachement/'); ?>' + p_data.invoice_attachments_id;

                        $("tbody#additional_attachements_tbody").append(
                            `<tr id="document_tr_id_${p_data.invoice_attachments_id}">
                                    <td>
                                        ${fileName1}
                                    </td>
                                    <td>${fileSizeFormatted1}</td>
                                    <td>${additional_description_notes.trim() == '' ? '-' : additional_description_notes}</td>
                                    <td>
                                    <span style="cursor: pointer;" class="fa fa-times text-danger text-bold" onclick="remove_document('${p_data.invoice_attachments_id}')"></span>
                                    <a style="cursor: pointer;" class="fa fa-eye text-warning text-bold pull-right" href="${p_data.absolute_path}" target="_blank"></a>
                                    </td>
                            </tr>
                            `
                        );
                        $("tr.tr_class_remover").remove();
                    }
                } else {
                    Swal.fire({
                        icon: 'error',
                        title: 'Something went wrong'
                    });
                }
            }
        });

        if(close_addClose == 'close') {
            $("#add_additional_attachements_modal").modal('hide');
        }
        fileInput.value = '';
        fileName.textContent = 'No file selected';
        $("form#docs_form").trigger('reset');
    }

function convertSizeToBytes(sizeStr) {
    /**
     * Converts human-readable size strings (e.g., "5MB", "10 KB", "3bytes") into bytes.
     * Supports:
     * - KB, MB, GB (case-insensitive)
     * - Spaces or no spaces (e.g., "5MB" or "5 MB")
     * - Bytes (e.g., "5 Bytes" or "5bytes")
     * Returns integer value.
     */
    const sizeUnits = {
        'kb': 1024,
        'mb': 1024 ** 2,
        'gb': 1024 ** 3,
        'bytes': 1,
        'byte': 1,
    };

    // Extract numeric value and unit using regex
    const match = sizeStr.match(/^\s*(\d+)\s*([a-zA-Z]+)\s*$/i);
    if (!match) {
        throw new Error(`Invalid size format: "${sizeStr}"`);
    }

    const numericValue = parseInt(match[1], 10);
    const unit = match[2].toLowerCase();

    // Find the matching unit (supports 'kb', 'mb', 'gb', 'bytes', 'byte')
    const normalizedUnit = Object.keys(sizeUnits).find(key => 
        unit === key || unit === `${key}s`
    ) || unit; // Fallback to exact match if not found

    if (!sizeUnits[normalizedUnit]) {
        throw new Error(`Unknown size unit: "${unit}"`);
    }

    return numericValue * sizeUnits[normalizedUnit];
}

    function upload_file_to_wasabi() {
        return new Promise(function(resolve, reject) {
            try {

                const fileInput = document.getElementById('additional_attachements');
                const file = fileInput.files[0]; // Get the first selected file
                
                if (!file) {
                    return Swal.fire({
                        icon: 'error',
                        title: 'No file selected',
                        text: 'Please select a file to upload.'
                    });
                }

                // Get file details
                const fileName = file.name;
                const fileType = file.type;
                const fileSize = file.size; // in bytes

// const maxFileSize = 10 * 1024 * 1024; // 10MB

                $.ajax({
                    url: '<?php echo site_url("S3_controller/getSignedUrl"); ?>',
                    type: 'post',
                    data: {'filename':file.name, 'file_type':file.type, 'folder':'invoice_documents'},
                    success: function(response) {
                        // Show progress bar
                        $('#upload_progress').show();
                        
                        response = JSON.parse(response);
                        var path = response.path;
                        var signedUrl = response.signedUrl;

                        $.ajax({
                            url: signedUrl,
                            type: 'PUT',
                            headers: {
                                "Content-Type": file.type, 
                                "x-amz-acl":"public-read" 
                            },
                            processData: false,
                            data: file,
                            xhr: function () {
                                var xhr = $.ajaxSettings.xhr();
                                xhr.upload.onprogress = function (e) {
                                    if (e.lengthComputable) {
                                        const percent = Math.round((e.loaded / e.total) * 100);
                                        $('#upload_progress_bar').css('width', percent + '%');
                                        $('#upload_percentage').text(percent + '%');
                                    }
                                };
                                return xhr;
                            },
                            success: function(response) {
                                $('#uploaded_photo_url').val(path);
                                Swal.fire({
                                    icon: 'success',
                                    title: 'Upload Complete',
                                    text: 'Photo uploaded successfully',
                                    timer: 2000,
                                    showConfirmButton: false
                                });
                                resolve({path:path, name:file.name, type:file.type});
                            },
                            error: function(err) {
                                console.error('Upload error:', err);
                                Swal.fire({
                                    icon: 'error',
                                    title: 'Upload Failed',
                                    text: 'Failed to upload photo. Please try again.',
                                    timer: 3000,
                                    showConfirmButton: false
                                });
                                reject(err);
                            },
                            complete: function() {
                                // Hide progress bar after a short delay
                                setTimeout(() => {
                                    $('#upload_progress').hide();
                                }, 1000);
                            }
                        });
                    },
                    error: function (err) {
                        Swal.fire({
                            icon: 'error',
                            title: 'Upload Failed',
                            text: 'Failed to get upload URL. Please try again.',
                            timer: 3000,
                            showConfirmButton: false
                        });
                        reject(err);
                    }
                });
            } catch(err) {
                console.error('Error:', err);
                Swal.fire({
                    icon: 'error',
                    title: 'Upload Failed',
                    text: 'An unexpected error occurred. Please try again.',
                    timer: 3000,
                    showConfirmButton: false
                });
                reject(err);
            }
        });
    }

    function remove_document(invoice_attachment_id) {
        $.ajax({
            url: '<?php echo site_url('procurement/invoice_controller_v2/remove_document'); ?>',
            type: 'post',
            data: {invoice_attachment_id},
            success: function(data) { 
                $("#document_tr_id_"+invoice_attachment_id).remove();
                var trCounts= $("tbody#additional_attachements_tbody").html();
                if(trCounts.trim() == '') {
                    $("tbody#additional_attachements_tbody").html(`<tr class="tr_class_remover">
                                                                            <td colspan="4" class="text-center">
                                                                            No documents found
                                                                            </td>
                                                                    </tr>`);
                }
            }
        });
       
    }

    function reach_at_prev_tab(current, previous_tab_selector) {
        $(".step-content").addClass('hidden');
        $("#" +previous_tab_selector).removeClass('hidden');
    }












    function save_and_close(current, step_selector) {
        let invoice_master_id= $(".invoice_master_id").val();

        if(!has_department_approvers) {
            return Swal.fire({
                icon: 'error',
                title: 'No approvers found',
                text: `Please add approvers to the PO's department before submitting it.`
            });
        }

        Swal.fire({
            title: 'Are you sure?',
            text: "You won't be able to revert this! But you can edit it later.",
            icon: 'warning',
            showCancelButton: true,
            confirmButtonColor: '#3085d6',
            cancelButtonColor: '#d33',
            confirmButtonText: 'Save & Send for Approval!'
        }).then((result) => {
            if (result.isConfirmed) {
                $(current).prop('disabled', true).html('Please Wait...');
                submit_invoice(invoice_master_id);
            } else {
                $(current).prop('disabled', false).html('Save & Close');
                Swal.fire({
                    icon: 'warning',
                    title: 'Cancelled',
                    text: 'Your invoice is not saved!'
                });
            }
        });
    }

    function submit_invoice(invoice_master_id) {
        $.ajax({
            url: '<?php echo site_url('procurement/invoice_controller_v2/submit_invoice'); ?>',
            type: 'post',
            data: {invoice_master_id},
            success: function(data) {
                let p_data= JSON.parse(data);
                if(Object.keys(p_data)?.length) {
                    Swal.fire({
                        icon: 'success',
                        title: 'Successfully created invoice'
                    }).then((result) => {
                        window.location.href= '<?php echo site_url('procurement/invoice_controller_v2/manage_all_invoices'); ?>';
                    });

                } else {
                    Swal.fire({
                        icon: 'error',
                        title: 'Something went wrong'
                    });
                }
            }
        });
    }

    
</script>

<!-- Document Drag & drop -->
<script>
    // Get elements
    const dropZone = document.getElementById('dropZone');
    const fileInput = document.getElementById('additional_attachements');
    const fileName = document.getElementById('fileName');

    // Handle file selection via browse
    fileInput.addEventListener('change', function(e) {
        if (this.files.length) {
            fileName.textContent = this.files[0].name;
        }
    });

    // Handle drag and drop
    dropZone.addEventListener('dragover', function(e) {
        e.preventDefault();
        this.classList.add('dragover');
    });
    
</script>