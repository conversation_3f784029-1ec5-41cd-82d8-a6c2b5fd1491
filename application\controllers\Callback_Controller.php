<?php

defined('BASEPATH') OR exit('No direct script access allowed');

//this controller handles the call from remote server (silicon) to upadte the database
class Callback_Controller extends CI_Controller 
{
    function __construct()
	{
		parent::__construct();
    $this->load->model('english_roots/root_model');
    $this->load->model('suycrm/suy_model');
    $this->load->library('payment');
    $this->load->library('payment_application');
    $this->acad_year->loadAcadYearDataToSession();
    $this->load->model('Internalticketing_model');
    $this->load->helper('texting_helper');
	}

  public function Kolkata_datetime(){
    $timezone = new DateTimeZone("Asia/Kolkata" );
    $date = new DateTime();
    $date->setTimezone($timezone );
    $dtobj = $date->format('Y-m-d H:i:s');
    return $dtobj;
  }

    //update the pdf link path and status of its generation
    public function updatePdfLinkStatus() {
        $this->load->model('examination/assessment_model');
        $input = $this->input->post();
        // trigger_error('In Callback_controller for Report Card PDF');
        // trigger_error(json_encode($input));
        $this->assessment_model->updatePdfLinkStatus($input['path'], $input['status']);
    }

    public function updateFeePdfLink() {
        // trigger_error('In Callback_controller for Fee PDF');
        // trigger_error(json_encode($_POST));
        $this->load->model('feesv2/fees_collection_model');
        $input = $this->input->post();
        if(!empty($input)){
            $this->fees_collection_model->updateFeePdfLink($input['path'], $input['status']);
            if ($input['status'] == 1) {
              $this->_email_send_to_parent_fee_receipts($input['path']);
            }
        }
    }

    public function updateFeeInvoicePdfLink() {
      $this->load->model('feesv2/fees_student_model');
      $input = $this->input->post();
      $this->fees_student_model->updateFeeInvoicePdfLink($input['path'], $input['status']);  
    }

    public function updatePayrollPdfLink() {
      $this->load->model('payroll_model');
      $input = $this->input->post();
      $this->payroll_model->updatePayslipPdfLink($input['path'], $input['status']);  
    }

    public function _email_send_to_parent_fee_receipts($path){
      // trigger_error('In Callback_controller for Fee PDF');
      // trigger_error(json_encode($path));
      if(empty($path)){
        return false;
      }
      $this->load->model('feesv2/fees_collection_model');
      $this->load->model('communication/emails_model');
      $result = $this->fees_collection_model->constrcut_fee_receipt_for_email($path);
      if($result == 0){
        return;
      }
      $payment_modes = json_decode($result->allowed_payment_modes);
      $registerEmail = json_decode($this->settings->getSetting('registered_emails'));
      
      $email_content =$this->fees_collection_model->get_email_templatefor_pdf_receipt();
      $class = $result->student->classSection;
      if ($result->student->is_placeholder == 1) {
        $class = $result->student->clsName;
      }
      $totalAmountString = $result->amount_paid + $result->fine_amount - $result->discount_amount;

      $paymentTypeString = '';
      if($result->payment_type == '10'){
        $paymentTypeString = 'Online Payment';
      }else{
        foreach ($payment_modes as $key => $type) {
          if ($type->value == $result->payment_type ) {
            $paymentTypeString = strtoupper($type->name);
          }
        }
      }
      $taxId = 'NA';
      if (!empty($result->onlineTrans)) {
        $taxId = $result->onlineTrans->tx_id;
      }
      $this->load->library('filemanager');
      $filePath = $this->filemanager->getFilePath($result->receipt_pdf_link);
      $download ='<br><a target="_blank" rel="noopener noreferrer" href="'.$filePath.'">Fee receipts</a>';
      $content = $email_content->content;
      $content = str_replace('%%blueprint_name%%',$result->blueprint_name, $content);
      $content = str_replace('%%transaction_date%%',date('d-m-Y', strtotime($result->paid_datetime)), $content);
      $content = str_replace('%%transaction_id%%',$taxId, $content);
      $content = str_replace('%%receipt_no%%',$result->receipt_number, $content);
      $content = str_replace('%%student_name%%',$result->student->stdName, $content);
      $content = str_replace('%%admission_no%%',$result->student->admission_no, $content);
      $content = str_replace('%%class%%',$class, $content);
      $content = str_replace('%%totalAmountString%%',$totalAmountString, $content);
      $content = str_replace('%%paymentTypeString%%',$paymentTypeString, $content);
      $content = str_replace('%%downloadbutton%%',$download, $content);

      $members = [];
      array_push($members, $result->student->f_mail, $result->student->m_email);

      $memberEmail = [];
      foreach ($members as $key => $val) {
        $memberEmail[] = $val;
      }

      $sent_by = $this->authorization->getAvatarStakeHolderId();
      $senderList = implode(',',$members);
      $email_master_data = array(
        'subject' => $email_content->email_subject,
        'body' => $content,
        'source' => '',
        'sent_by' => $sent_by,
        'recievers' => "Parents",
        'from_email' => $email_content->registered_email,
        'files' => NULL ,
        'acad_year_id' => $this->acad_year->getAcadYearID(),
        'visible' => 1,
        'sender_list'=>implode(',',$senderList),
        'sending_status' => 'Completed'
      );
      $email_master_id = $this->emails_model->saveEmail($email_master_data);

      $email_data = [];

      $email_obj1 = new stdClass();
      $email_obj1->stakeholder_id = $result->student->father_id;
      $email_obj1->avatar_type = 2;
      $email_obj1->email = $result->student->f_mail;
      $email_data[] = $email_obj1;

      $email_obj2 = new stdClass();
      $email_obj2->stakeholder_id = $result->student->mother_id;
      $email_obj2->avatar_type = 2;
      $email_obj2->email = $result->student->m_email;
      $email_data[] = $email_obj2;
      $this->emails_model->save_sending_email_data($email_data, $email_master_id);
      $this->load->helper('email_helper');
      sendEmail($content, $email_content->email_subject, $email_master_id, $memberEmail, $email_content->registered_email, []);
    }

    public function updateApplicationPdfLink(){
      $this->load->model('Admission_model');
      $input_var = $this->input->post();
      $this->Admission_model->updateApplicationPdfLink($input_var['path'], $input_var['status']);
    }

    public function updateItariApplicationPdfLink(){
      $this->load->model('itari/Itari_Admission_Model');
      $input_var = $this->input->post();
      $this->Itari_Admission_Model->updateItariApplicationPdfLink($input_var['path'], $input_var['status']);
    }

    public function update_enquiry_PdfLink(){
      $this->load->model('enquiry_model');
      $input_var = $this->input->post();
      $this->enquiry_model->update_enquiry_PdfLink($input_var['path'], $input_var['status']);
    }

    public function updateConsolidatedFeePdfLink() {
        // trigger_error('In Callback_controller for Fee PDF');
        // trigger_error(json_encode($_POST));
        $this->load->model('feesv2/fees_collection_model');
        $input = $this->input->post();
        $this->fees_collection_model->updateConsolidatedFeePdfLink($input['path'], $input['status']);
    }

    public function updateTextStatus() {
        $this->load->model('communication/texting_model');
        $input = $this->input->post();
        $finalResult = json_decode($input['finalResult'], true);
        $this->texting_model->updateTextStatus($finalResult);
    }

    public function payslipPdfStatus() {
        $this->load->model('payroll_model');
        $input = $this->input->post(); 
        $this->payroll_model->updatePayslipPdf($input['path'], $input['status']);
    }

    public function updateCertificatePdfLink(){
        // trigger_error('In Callback_controller - Triggering Certificate PDF');
        // trigger_error(json_encode($_POST));
        $this->load->model('student/Certificates_Model');        
        $input = $this->input->post();
        $this->Certificates_Model->updateCertificatePdfLink($input['path'], $input['status']);
    }

    public function updateSalesPdfLink() {
        // trigger_error('In Callback_controller - Generating Sales PDF');
        // trigger_error(json_encode($_POST));
        $this->load->model('sales/sales_model');
        $input = $this->input->post();
        $this->sales_model->updateSalesPdfLink($input['path'], $input['status']);
    }

    public function sms_status_refresh() {
        $this->load->helper('sms_v2_helper');
        $responseData = $this->__getUnDeliveredTexts();
        if(!empty($responseData) && !empty($responseData['msgIds']) && !empty($responseData['responseMap'])) {
            __callSmsStatusCheck($responseData['msgIds'], $responseData['responseMap']);
        }
    }

    private function __getUnDeliveredTexts() {
        $date = date('Y-m-d', strtotime('-2 days'));
        // $today = date('Y-m-d');
        $sms_credit_rollback_array = $this->db->select("tst.texting_master_id, count(tst.texting_master_id) as sent_no_of_times")
          ->from("text_sent_to tst")
          ->where("texting_master_id IN (SELECT id FROM texting_master WHERE DATE_FORMAT(sent_on, '%Y-%m-%d') >= '$date')", NULL, FALSE)
          ->where("tst.mode", 2)
          ->where_in("tst.status", ["DELIVRD", "DELIVERED"])
          ->group_by("tst.texting_master_id")
          ->get()->result();
        if (!empty($sms_credit_rollback_array)) {
            foreach ($sms_credit_rollback_array as $val) {
                $texting_master_data = $this->db->select("id as texting_master_id, sms_credits as rollback_credits_count, sent_by")
                    ->from("texting_master")
                    ->where("id", $val->texting_master_id)
                    ->get()->row();

                if ($texting_master_data) {
                    $total_credits = $texting_master_data->rollback_credits_count * $val->sent_no_of_times;

                    if ($total_credits > 0) {
                        $texting_credits = [
                            'sms_credits' => $total_credits,
                            'action' => 'Unload',
                            'action_by' => $texting_master_data->sent_by,
                            'texting_master_id' => $val->texting_master_id
                        ];

                        $reduced_credits = $this->db->insert('texting_credits_usage', $texting_credits);

                        if ($reduced_credits) {
                            $this->db->where("texting_master_id", $val->texting_master_id)
                                      ->where_in("status", ["DELIVRD", "DELIVERED"])
                                      ->update("text_sent_to", ["status" => "DELIVRD_UNLOAD_ADDED"]);
                        }
                    }
                }
            }
        }

        $this->db->where("texting_master_id IN (SELECT id FROM texting_master WHERE DATE_FORMAT(sent_on, '%Y-%m-%d') <= '$date')", NULL, FALSE)
                ->where("status", 'AWAITED-DLR')
                ->where("mode", 2)
                ->update('text_sent_to', ['status' => 'NO-DLR-OPTR']);

        $submitted = $this->db->select("ts.response_id, ts.id as text_sent_to_id")
                              ->from('text_sent_to ts')
                              ->where_in('ts.status', ['SUBMITTED', 'AWAITED-DLR'])
                              ->where('ts.mode', 2)
                              ->where('ts.response_id IS NOT NULL', null, false)
                              ->get()->result();

        $msgIds = [];
        $responseIdMap = [];
        foreach ($submitted as $row) {
            $responseId = $row->response_id;
            $msgIds[] = $responseId;
            $responseIdMap[$row->response_id] = $row->text_sent_to_id;
        }
        return [
            'msgIds' => $msgIds,
            'responseMap' => $responseIdMap
        ];
    }

    public function fine_amount_refresh(){
      $this->load->model('feesv2/Fees_student_model');        
      $result = $this->Fees_student_model->update_fine_amount_every_day();
      // echo "<pre>"; print_r($result); die();
      if ($result) {
        // trigger_error('fine amount refresh triggered successfully');
      }else{
        // trigger_error('fine amount refresh triggered un-successfully');
      }
    }

    public function library_return_book_reminder_sms(){
      $this->load->model('library_model');       
      $result = $this->library_model->reminder_student_return_books_data();
      if ($result) {
        // trigger_error('Library reminder sms sent successfully');
      }else{
        trigger_error('Library reminder sms sent un-successfully');
      }
    }

    public function bulkNotifications() {
      $input = $this->input->post();
      $type = $input['type'];
      $master_id = $input['master_id'];

      switch ($type) {
        case 'circular':
          $this->_sendCircularNotifications($master_id);
          break;
        
        default:
          break;
      }
    }

    private function _sendCircularNotifications($circular_id) {
      $this->load->model('communication/circular_model', 'circular');
      $circular = $this->circular->getCircularDetails($circular_id);
      $stdIds = $this->circular->getCircularRcieverStdIds($circular_id);
      $staff_ids = $this->circular->getCircularRcieverStaffIds($circular_id);
      $content = "New Circular with title '" .  $circular->title . "' posted under category '" . $circular->category."'";
      $school_name = $this->settings->getSetting('school_name');
      $acad_year_id = $this->settings->getSetting('academic_year_id');
      $input_array = array(
        'mode' => 'notification', 
        'title' => $school_name, 
        'message' => $content,
        'source' => 'Circular',
        'student_url' => site_url('parent/Circular_inbox'),
        'staff_url' => site_url('staff/Circular_view'),
        'visible' => 1,
        'send_to' => 'Both',
        'acad_year_id' => $acad_year_id
      );
      if(!empty($stdIds)) {
        $input_array['student_ids'] = $stdIds;
      }

      if(!empty($staff_ids)) {
        $input_array['staff_ids'] = $staff_ids;
      }
      
      $this->load->helper('texting_helper');
      $result = sendText($input_array, 'External');
    }

    public function english_roots(){
      $data = (array) json_decode(file_get_contents('php://input'), TRUE);
      $insert_id = $this->root_model->insert_register_users($data);
      $success = 0;
      if ($insert_id) {
       $success = $insert_id;
      }
      echo json_encode($success);
    }

    public function english_roots_users(){
      $data = (array) json_decode(file_get_contents('php://input'), TRUE);
      $success = [];
      $result = $this->root_model->update_regishter_users_details($data);
      if ($result) {
        $success = ['status' => '1', 'message' => 'Successull', 'data' => $result];
      }else{ 
        $success = ['status' => '0', 'message' => 'error', 'data' =>[]];
      }
      echo json_encode($success);
    }

    public function english_roots_update_trans(){
      $data = (array) json_decode(file_get_contents('php://input'), TRUE);
      $success = [];
      $result = $this->root_model->english_roots_update_trans_by_id($data);
      if ($result) {
        $success = ['status' => '1', 'message' => 'Successull', 'data' => $result];
      }else{ 
        $success = ['status' => '0', 'message' => 'error', 'data' =>[]];
      }
      echo json_encode($success);
    }

    public function english_roots_all_programme(){
      $result = $this->root_model->getProgramNames();
      $success = [];
      if ($result) {
        $success = ['status' => '1', 'message' => 'Successull', 'data' => $result];
      }else{ 
        $success = ['status' => '0', 'message' => 'error', 'data' =>[]];
      }
      echo json_encode($success);
    }

    public function english_roots_language(){
      $data = (array) json_decode(file_get_contents('php://input'), TRUE);
      $dataId = $data[0];
      $result = $this->root_model->getLanguages($dataId);
      $success = [];
      if ($result) {
        $success = ['status' => '1', 'message' => 'Successull', 'data' => $result];
      }else{ 
        $success = ['status' => '0', 'message' => 'error', 'data' =>[]];
      }
      echo json_encode($success);
    }

    public function english_roots_get_register_user_byid(){
      $data = (array) json_decode(file_get_contents('php://input'), TRUE);
      $last_id = $data['insert_id'];
      $result = $this->root_model->getRegister_users_details($last_id);
      $success = [];
      if ($result) {
        $success = ['status' => '1', 'message' => 'Successull', 'data' => $result];
      }else{ 
        $success = ['status' => '0', 'message' => 'error', 'data' =>[]];
      }
      echo json_encode($success);
    }

    public function english_roots_level(){
      $data = (array) json_decode(file_get_contents('php://input'), TRUE);
      $prgrId = $data['program'];
      $lang = $data['language'];
      $result = $this->root_model->getLevels($prgrId, $lang);
      $success = [];
      if ($result) {
        $success = ['status' => '1', 'message' => 'Successull', 'data' => $result];
      }else{ 
        $success = ['status' => '0', 'message' => 'error', 'data' =>[]];
      }
      echo json_encode($success);
    }

    public function english_roots_getLevelDetails(){
      $data = (array) json_decode(file_get_contents('php://input'), TRUE);
      $prgrId = $data['program'];
      $lang = $data['language'];
      $level = $data['level'];
      $result = $this->root_model->getBatch($level,$prgrId, $lang);
      $success = [];
      if ($result) {
        $success = ['status' => '1', 'message' => 'Successull', 'data' => $result];
      }else{ 
        $success = ['status' => '0', 'message' => 'error', 'data' =>[]];
      }
      echo json_encode($success);
    }

    public function english_roots_getBatchDetails(){
      $data = (array) json_decode(file_get_contents('php://input'), TRUE);
      $prgrId = $data['program'];
      $lang = $data['language'];
      $level = $data['level'];
      $batch_name = $data['batch_name'];
      $result = $this->root_model->getBatch_details($level,$prgrId, $lang, $batch_name);
      $success = [];
      if ($result) {
        $success = ['status' => '1', 'message' => 'Successull', 'data' => $result];
      }else{ 
        $success = ['status' => '0', 'message' => 'error', 'data' =>[]];
      }
      echo json_encode($success);
    }
    public function english_roots_getDurationAmount(){
      $data = (array) json_decode(file_get_contents('php://input'), TRUE);
      $prgrId = $data['program'];
      $lang = $data['language'];
      $level = $data['level'];
      $result = $this->root_model->getDurationAmount($level,$prgrId, $lang);
      $success = [];
      if ($result) {
        $success = ['status' => '1', 'message' => 'Successull', 'data' => $result];
      }else{ 
        $success = ['status' => '0', 'message' => 'error', 'data' =>[]];
      }
      echo json_encode($success);
    }

    public function english_roots_insert_online_application_details(){
      $data = (array) json_decode(file_get_contents('php://input'), TRUE);
      $insert_id = $this->root_model->insert_online_application_details($data);
      $success = 0;
      if ($insert_id) {
       $success = $insert_id;
      }
      echo json_encode($success);
    }

    public function get_english_roots_progams_batch(){
      $programName = (array) json_decode(file_get_contents('php://input'), TRUE);
      // $programName = 'Student';
      $result = $this->root_model->get_english_roots_prgram_batch_list($programName[0]);
      $success = [];
      if ($result) {
        $success = ['status' => '1', 'message' => 'Successull', 'data' => $result];
      }else{ 
        $success = ['status' => '0', 'message' => 'error', 'data' =>[]];
      }
      echo json_encode($success);
    }

    public function english_roots_update_online_application_details(){
      $data = (array) json_decode(file_get_contents('php://input'), TRUE);
      $result = $this->root_model->update_online_application_details($data);
      $success = 0;
      if ($result) {
       $success = $result;
      }
      echo json_encode($success);
    }

    public function english_roots_details_by_id(){
      $source_id = (array) json_decode(file_get_contents('php://input'), TRUE);
      $result = $this->root_model->get_user_registration_form_details($source_id[0]);
      $success = [];
      if ($result) {
        $success = ['status' => '1', 'message' => 'Successull', 'data' => $result];
      }else{ 
        $success = ['status' => '0', 'message' => 'error', 'data' =>[]];
      }
      echo json_encode($success);
    }

    public function english_roots_email_enquire(){
      $data = (array) json_decode(file_get_contents('php://input'), TRUE);
      $sourceId = $data['insert_id'];
      $program_id = $data['program_id'];
      // $updateRes =  $this->root_model->update_program_id($sourceId, $program_id);
      $rData = $this->root_model->get_user_registration_form_details($sourceId);
      $result = $this->root_model->enquiry_email_register_users_data($rData);
      $success = 0;
      if ($result) {
       $success = $program_id;
      }
      echo json_encode($success);
    }

    public function email_sms_to_users(){
      $data = (array) json_decode(file_get_contents('php://input'), TRUE);
      $result = $this->root_model->email_sms_register_users($data);
      $success = 0;
      if ($result) {
       $success = $result;
      }
      echo json_encode($success);
    }

    public function suy_handle_payment_callback() {

      // trigger_error("API response from TraknPay: ");
      // trigger_error(json_encode($_POST));

      //Call the payment callback handler
      $result = $this->payment->payment_callback_school_handler_suy($_POST);

      // trigger_error("Payment Callback handler called");
      // trigger_error(json_encode($result));
      $data = $result;
      $this->load->view('suy/payment_done', $data);

    }

    // public function enquiry_email(){
    //   $data = (array) json_decode(file_get_contents('php://input'), TRUE);
    //   $result = $this->root_model->enquiry_email_register_users_data($data);
    //   $success = 0;
    //   if ($result) {
    //    $success = $result;
    //   }
    //   echo json_encode($success);
    // }

    public function email_sms_to_users_test(){
      // $data = (array) json_decode(file_get_contents('php://input'), TRUE);
      $result = $this->root_model->email_sms_register_users_test();
      $success = 0;
      if ($result) {
       $success = $result;
      }
      echo json_encode($success);
    }

    public function online_payment_status_check(){
      $this->load->model('payment_model');
      $date = date("Y-m-d", strtotime('-7 days'));
      // $date = '2025-03-01';

      $tData=array(
        'check_date' => date('d-m-Y'),
        'transaction_date' => (new DateTime($date))->format('d-m-Y'),
        'settelement_amount' => 0,
        'transaction_amount' => 0,
        'status' => 0
      );

      $query = "select sum(amount_paid) as total_amount from feev2_transaction 
      where date_format(paid_datetime,'%Y-%m-%d')= '".$date."' and transaction_mode = 'ONLINE' and status = 'SUCCESS' ";
      $transTotal = $this->db->query($query)->row()->total_amount;

      if ($transTotal) {
        $tData['transaction_amount'] = $transTotal;
        $settlements = $this->payment->get_settlementdata_status_daily($date);
        $settlement_amount = 0;
        if (isset($settlements->data)) {
          foreach ($settlements->data as $key => $val) {
            $settlement_amount += $val->amount_reimbursed;
          }
        }
        $tData['settelement_amount'] = $settlement_amount;
        $tData['transaction_amount'] = $transTotal;

        if ($transTotal == $settlement_amount) {
          $tData['status'] = 1;
        }
          // trigger_error('Settlement request');
          // $this->db->insert('online_settlement_status',$tData);
      }
      echo json_encode($tData);
    }

    //updating emails delivery status
    public function updateEmailDeliveryStatus() {
        $this->load->model('communication/emails_model');
        $input = $this->input->post();
        $this->emails_model->updateEmailDeliveryStatus($input);
    }

    // public function online_payment_last_seven_days_failed_transactions(){
    //     // $date = date("Y-m-d");
    //     $date = date('Y-m-d', strtotime('-1 day'));
    //     $lastSeventhDate = date('Y-m-d', strtotime('-20 days'));
    //     // $query = "SELECT opm.order_id, paid_datetime, ft.status FROM feev2_transaction ft JOIN online_payment_master opm ON ft.id = opm.source_id WHERE ft.status = 'FAILED' AND DATE_FORMAT(paid_datetime, '%Y-%m-%d') BETWEEN '$lastSeventhDate' AND '$date'";
    //     $query = "SELECT opm.order_id, paid_datetime, ft.status FROM feev2_transaction ft JOIN online_payment_master opm ON ft.id = opm.source_id WHERE ft.status = 'FAILED' AND DATE_FORMAT(paid_datetime, '%Y-%m-%d') BETWEEN '2025-02-01' AND '2025-02-25'";
    //     $trans = $this->db->query($query)->result();
    //     // echo "<pre>";print_r($trans);die();
    //     // $txData=array(
    //     //   'transaction_date'=>$date,
    //     //   'order_id' => 0,
    //     //   'reconcile_status'=> 0
    //     // );
    //     // $noOfValid = 0;
    //     $noOfSuccess = 0;
    //     $noOfFailure = 0;
    //     $noOfInitiated = 0;
    //     $status_arrays = [];

    //     foreach ($trans as $key => $val) {
    //         $txData = array(
    //             'transaction_date' => $date,
    //             'order_id' => $val->order_id,
    //             'reconcile_status' => 'Correct',
    //             'traknpay_status' => 'FAILED'  // Assuming it starts as 'FAILED'
    //         );

    //         $status = $this->payment->get_transaction_details_all($val->order_id);

    //         $txData['reconcile_status'] = 'Correct';
    //         if (!isset($status->error)) {
    //             $oderId = $status->data[0]->order_id;
    //             $opmQuery = "SELECT tx_response_code, tx_response_message, status, source_id FROM online_payment_master opm WHERE opm.order_id='" . $oderId . "'";
    //             $opmStatus = $this->db->query($opmQuery)->row();

    //             if ($status->data[0]->response_code == 0) {
    //                 $noOfSuccess++;
    //                 $txData['traknpay_status'] = 'SUCCESS';
    //                 if ($opmStatus->tx_response_code != 0) {
    //                     $txData['reconcile_status'] = 'Incorrect';
    //                 } else {
    //                     $txQuery = "SELECT id, status FROM feev2_transaction ft WHERE ft.id='" . $opmStatus->source_id . "'";
    //                     $txStatus = $this->db->query($txQuery)->row();
    //                     if ($txStatus->status != 'SUCCESS') {
    //                         $txData['reconcile_status'] = 'Incorrect-Transaction';
    //                     }
    //                 }
    //             } else {
    //                 $noOfFailure++;
    //             }
    //         } else {
    //             $txData['traknpay_status'] = 'INITIATED';
    //             $noOfInitiated++;
    //         }

    //         if ($txData['traknpay_status'] == 'INITIATED') {
    //             array_push($status_arrays, $txData);
    //         }
    //     }
    //     // echo "<pre>";print_r($status_arrays);die();
    //     $table = '';
    //     $table .= '<h3>' . $this->settings->getSetting('school_name') . '</h3>';

    //     if (empty($status_arrays)) {
    //         $table .= '<p>No Failed Transactions</p>';
    //     } else {
    //         $table .= '<table border="1" cellpadding="6" cellspacing="0">
    //             <tr>
    //                 <th colspan="4">Summary</th>
    //             </tr>
    //             <tr>
    //                 <th>No of Failed Transactions: ' . count($status_arrays) . '</th>
    //                 <th>No of Success: ' . $noOfSuccess . '</th>
    //                 <th>No of Initiated: ' . $noOfInitiated . '</th>
    //                 <th>No of Failure: ' . $noOfFailure . '</th>
    //             </tr>
    //             <tr>
    //                 <th colspan="2">Order Id</th>
    //                 <th>Reconcile Status</th>
    //                 <th>Traknpay Status</th>
    //             </tr>';

    //         // Output only failed transactions
    //         foreach ($status_arrays as $key => $val) {
    //             $table .= '<tr>
    //                 <th colspan="2">' . $val['order_id'] . '</th>';
    //             if ($val['reconcile_status'] == 'Incorrect' || $val['reconcile_status'] == 'Incorrect-Transaction') {
    //                 $table .= '<th style="color:red">' . $val['reconcile_status'] . '</th>';
    //             } else {
    //                 $table .= '<th>' . $val['reconcile_status'] . '</th>';
    //             }

    //             $table .= '<th>' . $val['traknpay_status'] . '</th>
    //             </tr>';
    //         }

    //         $table .= '</table><br>';
    //     }
    //     // echo "<pre>";print_r($table);die();
    //     echo $table;
    // }

    public function online_payment_transactions(){
      // $date = date("Y-m-d");
      $date = date('Y-m-d', strtotime('-1 days'));
      $query = "select opm.order_id, paid_datetime from feev2_transaction ft JOIN online_payment_master opm ON FIND_IN_SET(ft.id, REPLACE(REPLACE(opm.source_id, '[', ''), ']', '')) where date_format(paid_datetime,'%Y-%m-%d')= '".$date."'";
      // $query = "select opm.order_id, paid_datetime from feev2_transaction ft join online_payment_master opm on ft.id=opm.source_id where date_format(paid_datetime,'%Y-%m-%d') BETWEEN '2025-03-28' AND '2025-04-03'";
      $trans = $this->db->query($query)->result();

      // $txData=array(
      //   'transaction_date'=>$date,
      //   'order_id' => 0,
      //   'reconcile_status'=> 0
      // );
      // $noOfValid = 0;
      $noOfSuccess = 0;
      $noOfFailure = 0;
      $noOfInitiated = 0;
      $status_arrays = [];
      foreach ($trans as $key => $val) {
        $txData=array(
          'transaction_date'=>$date,
          'order_id' => $val->order_id,
          'reconcile_status'=> 'Correct',
          'traknpay_status' => 'FAILED'
        );

        $status = $this->payment->get_transaction_details_all($val->order_id);

        $txData['reconcile_status'] = 'Correct';
        if(!isset($status->error)) {
          $oderId = $status->data[0]->order_id;
          $opmQuery ="select tx_response_code, tx_response_message, status, source_id from online_payment_master opm where opm.order_id='".$oderId."'";
          $opmStatus = $this->db->query($opmQuery)->row();
          if($status->data[0]->response_code == 0) {
            $noOfSuccess++;
            $txData['traknpay_status'] = 'SUCCESS';
            if($opmStatus->tx_response_code != 0) {
              $txData['reconcile_status'] = 'Incorrect';
            } else {
              $allSourceIds = array(); 
              if (strpos($opmStatus->source_id, '[') !== false) {
                  $ids = trim($opmStatus->source_id, '[]');
                  $idArray = explode(',', $ids);
                  $allSourceIds = array_merge($allSourceIds, $idArray);
              } else {
                  $allSourceIds[] = $opmStatus->source_id;
              }
              $txQuery = "SELECT id, status FROM feev2_transaction ft WHERE ft.id IN (" . implode(',', $allSourceIds) . ")";
              $txStatus = $this->db->query($txQuery)->row();
              if($txStatus->status != 'SUCCESS') {
                $txData['reconcile_status'] = 'Incorrect-Transaction';
              }
            }
          } else {
            $noOfFailure++;
          }
        } else {
          $txData['traknpay_status'] = 'INITIATED';
          $noOfInitiated++;
        }
        array_push($status_arrays, $txData);
      }
      
      
      $table = '';
      $table .= '<h3>'.$this->settings->getSetting('school_name').'</h3>';
      if(empty($status_arrays)) {
        $table .= '<p>No Transactions</p>';
      } else {
        $table .= '<table border="1" cellpadding="6" cellspacing="0">

        <tr>
          <th colspan="4">Summary </th>
        </tr>
        <tr>
          <th>No of Transactions : '.count($trans).'</th>
          <th>No of Success : '.$noOfSuccess.'</th>
          <th>No of Initiated : '.$noOfInitiated.'</th>
          <th>No of Failure : '.$noOfFailure.'</th>
        </tr>

        <tr>
          <th colspan="2">Order Id </th>
          <th>Reconcile Status </th>
          <th>Traknpay Status</th>
        </tr>';

        foreach ($status_arrays as $key => $val) { 
          $table .= '<tr>
            <th colspan="2">'.$val['order_id'].'</th>';
            if ($val['reconcile_status'] =='Incorrect' || $val['reconcile_status'] =='Incorrect-Transaction') {
              $table .='<th style="color:red">'.$val['reconcile_status'].'</th>';
            }else{
              $table .='<th>'.$val['reconcile_status'].'</th>';
            }
          $table .='<th>'.$val['traknpay_status'].'</th>
            </tr>';
        }
        $table .= '</table><br>';
      }
      echo $table;
    }

    public function transportation_ETA_update(){
      $this->load->model('transportation_model');
      $journey_type = 'PICKING';
      $drop_typ = 'DROPPING';
      
      $date = date('d-m-Y', strtotime('-1 days')); 
      //$date = '10-01-2025'; 
      $journeys = $this->transportation_model->getDailyJourneys_email($journey_type, $date);
      $drop = $this->transportation_model->getDailyJourneys_email($drop_typ, $date);
      
      $table = '';
      
      $table .= '<h3>' . $this->settings->getSetting('school_name') . '</h3>';
      
      $count = 0;
      if (!empty($journeys)) {
          $i = 1;
          $color = "";
  
          $table .= '<table border="1" cellpadding="6" cellspacing="0">
          <tr>
              <th colspan="7">Transportation Report of Pick on Date: ' . $date . ' </th>
          </tr>
          <tr>
              <th>Sl no</th>
              <th>Journey</th>
              <th>Total No Stops</th>
              <th>ETA\'s</th>
              <th>Geo-Fence</th>
              <th>Journey Status</th>
              <th>ETA Analysis</th>
          </tr>'; 
  
          foreach ($journeys as $key => $val) {
              if ($val['status'] != 'Not Started' && $val['total_stops'] > $val['eta_reminder']) {
                  if ($val['total_stops'] == ($val['eta_reminder'] + 3)) {
                      $color = "orange";
                  } else {
                      $color = "red";  
                      $count++;  // Only count red journeys
                  }
              } else {
                  if ($val['total_stops'] == ($val['eta_reminder'] - 3)) {
                      $color = "orange";
                  } else {
                      $color = "green";
                  }
              }
  
              $table .= '<tr>
              <td>' . $i++ . '</td>
              <td>' . $val['journey_name'] . '</td>
              <td style="text-align:center">' . $val['total_stops'] . '</td>
              <td style="text-align:center">' . $val['eta_reminder'] . '</td>
              <td style="text-align:center">' . $val['geo_fence_reminder'] . '</td>
              <td style="text-align:center">' . ($val['status'] == "Not Started" ? '-' : $val['status']) . '</td>
              <td style="color:' . $color . '">Attention</td>
              </tr>';
          }
  
          $table .= '<tr><td style="text-align:center" colspan="7"><b>Total No of Journeys Needing Attention: ' . $count . '</b></td></tr>';
          $table .= '</table><br>';
      } else {
          $table .= '<p>Sorry! No Transportation Report Found</p>';
      }
      
      $count1 = 0;
      if (!empty($drop)) {
          $i1 = 1;
          $color1 = "";
  
          $table .= '<table border="1" cellpadding="6" cellspacing="0">
          <tr>
              <th colspan="7">Transportation Report of Drop on Date: ' . $date . ' </th>
          </tr>
          <tr>
              <th>Sl no</th>
              <th>Journey</th>
              <th>Total No Stops</th>
              <th>ETA\'s</th>
              <th>Geo-Fence</th>
              <th>Journey Status</th>
              <th>ETA Analysis</th>
          </tr>';
  
          foreach ($drop as $key => $val) {
              if ($val['status'] != 'Not Started' && $val['total_stops'] > $val['eta_reminder']) {
                  if ($val['total_stops'] == ($val['eta_reminder'] + 3)) {
                      $color1 = "orange";
                  } else {
                      $color1 = "red";  
                      $count1++;  // Only count red journeys
                  }
              } else {
                  if ($val['total_stops'] == ($val['eta_reminder'] - 3)) {
                      $color1 = "orange";
                  } else {
                      $color1 = "green";
                  }
              }
  
              $table .= '<tr>
              <td>' . $i1++ . '</td>
              <td>' . $val['journey_name'] . '</td>
              <td style="text-align:center">' . $val['total_stops'] . '</td>
              <td style="text-align:center">' . $val['eta_reminder'] . '</td>
              <td style="text-align:center">' . $val['geo_fence_reminder'] . '</td>
              <td style="text-align:center">' . ($val['status'] == "Not Started" ? '-' : $val['status']) . '</td>
              <td style="color:' . $color1 . '">Attention</td>
              </tr>';
          }
  
          $table .= '<tr><td style="text-align:center" colspan="7"><b>Total No of Journeys Needing Attention: ' . $count1 . '</b></td></tr>';
          $table .= '</table><br>';
      } else {
          $table .= '<p>Sorry! No Transportation Report Found</p>';
      }
      $SchoolName =  $this->settings->getSetting('school_name');
      $table .= '<table border="1" cellpadding="6" cellspacing="0">
          <tr>
              <th colspan="4">Summary</th>
          </tr>
          <tr>
              <th>School Name</th>
              <th>Pickup</th>
              <th>Drops</th>
              <th>Total Attention Required</th>
          </tr>';
          $table .= '<tr>';
          $table .= '<td>'.$SchoolName.'</td>';
          $table .= '<td>'.count($journeys).'</td>';
          $table .= '<td>'.count($drop).'</td>';
          $table .= '<td>'.($count1+$count).'</td>';
          $table .= '</tr></table>';
          echo $table;
    }

    public function fee_audit(){
      // $date = date("Y-m-d");
      $this->load->model('feesv2/reports_model');
      $fee_amount = $this->reports_model->get_total_fees_all_table();
      $payment_issue = $this->reports_model->get_payment_issue_studentwise();
      $payment_issue_ins = $this->reports_model->get_payment_issue_ins_studentwise();
      $payment_issue_comp = $this->reports_model->get_payment_issue_comp_studentwise();
      // $receipt = $this->reports_model->get_check_receipt_number();
      $transStatus = 'Issue Found';
      $transStatus_color  ='red';
      if (($fee_amount->transTotal == $fee_amount->transComp) && $fee_amount->transTotalCon == $fee_amount->transCompCon)  {
        $transStatus ='No Issue';
        $transStatus_color  ='';
      }

      $studentStatus = 'Issue Found';
      $studentStatus_color  ='red';
      if (($fee_amount->totalFee == $fee_amount->insAmount) && ($fee_amount->insAmount == $fee_amount->compAmount) && ($fee_amount->sch_con == $fee_amount->ins_conc) && ($fee_amount->ins_conc == $fee_amount->comp_con) )  {
        $studentStatus ='No Issue';
        $studentStatus_color  ='';
      }

      $student_trans_Status = 'Issue Found';
      $student_trans_Status_color  ='red';
      if (($fee_amount->totalFee == $fee_amount->transTotal) && ($fee_amount->transTotalCon == $fee_amount->sch_con) && ($fee_amount->trans_fine == $fee_amount->sch_fine))  {
        $student_trans_Status ='No Issue';
        $student_trans_Status_color  ='';
      }
      $schIssue = 'No Issue';
      $schIssue_color  ='';
      if (!empty($payment_issue)) {
        $schIssue = 'Issue Found';
        $schIssue_color  ='red';
      }
      $insIssue = 'No Issue';
      $insIssue_color  ='';
      if (!empty($payment_issue_ins)) {
        $insIssue = 'Issue Found';
        $insIssue_color_  ='red';
      }
      $compIssue = 'No Issue';
      $compIssue_color  ='';
      if (!empty($payment_issue_comp)) {
        $compIssue = 'Issue Found';
        $compIssue_color  ='red';
      }
      // $receiptIssue = 'No Issue';
      // $receiptIssue_color  ='';
      // if (!empty($receipt)) {
      //   $receiptIssue = 'Issue Found';
      //   $receiptIssue_color  ='red';
      // }
      $table = ''; 
      $table .= '
        <tr>
          <td>'.$this->settings->getSetting('school_name').'</td>
          <td style="color:'.$transStatus_color.'" >'.$transStatus.'</td>
          <td style="color:'.$studentStatus_color.'">'.$studentStatus.'</td>
          <td style="color:'.$student_trans_Status_color.'">'.$student_trans_Status.'</td>
          <td style="color:'.$schIssue_color.'">'.$schIssue.'</td>
          <td style="color:'.$insIssue_color.'">'.$insIssue.'</td>
          <td style="color:'.$compIssue_color.'">'.$compIssue.'</td>
        </tr>';
      echo $table;
    }

    public function fee_audit_log(){
      $this->load->model('feesv2/reports_model');
      $fee_amount = $this->reports_model->check_fees_audit_table();
      $table = '<h3>' . $this->settings->getSetting('school_name') . '</h3>';
      $table .= '<table border="1" cellpadding="6" cellspacing="0">';
      $table .= '<tr>';
      $table .= '<th>Type</th>';
      $table .= '<th>Fee Blueprint</th>';
      $table .= '<th>Admission No</th>';
      $table .= '<th>Student Name</th>';
      $table .= '</tr>';

      if (!empty($fee_amount)) {
          foreach ($fee_amount as $type => $mismatches) {
              if (!empty($mismatches)) {
                  foreach ($mismatches as $mismatch) {
                      $table .= '<tr>';
                      $table .= '<td>' . $type . '</td>';
                      $table .= '<td>' . (!empty($mismatch->blueprint_name) ? $mismatch->blueprint_name : '-') . '</td>';
                      $table .= '<td>' . (!empty($mismatch->admission_no) ? $mismatch->admission_no : '-') . '</td>';
                      $table .= '<td>' . (!empty($mismatch->student_name) ? $mismatch->student_name : '-') . '</td>';
                      $table .= '</tr>';
                  }
              } else {
                  $table .= '<tr>';
                  $table .= '<td>' . $type . '</td>';
                  $table .= '<td>-</td>';
                  $table .= '<td>-</td>';
                  $table .= '<td>-</td>';
                  $table .= '</tr>';
              }
          }
      } else {
          $table .= '<tr>';
          $table .= '<td colspan="4">No mismatches found</td>';
          $table .= '</tr>';
      }

      $table .= '</table>';
      echo $table;
    }

    public function remove_old_sessions() {
      $this->db->query("delete from ci_sessions where timestamp < unix_timestamp((date_sub(now(),interval 30 day)))");
      $this->db->query("optimize table ci_sessions");
    }

    public function fees_online_payment_success_check($selectedDate){
      $date = date('Y-m-d', strtotime($selectedDate));
      
      $query = "SELECT opm.id as opm_id, opm.source_id, ft.id as trans_id, student_id, fee_student_schedule_id, amount_paid, ft.status, paid_datetime
          FROM online_payment_master opm 
          JOIN feev2_transaction ft ON FIND_IN_SET(ft.id, REPLACE(REPLACE(opm.source_id, '[', ''), ']', ''))
          WHERE ft.fee_student_schedule_id NOT IN (
              SELECT fee_student_schedule_id 
              FROM feev2_transaction 
              WHERE date_format(paid_datetime, '%Y-%m-%d') >= '".$date."' 
              AND status = 'SUCCESS'
          ) 
          AND opm.tx_response_code != 0 
          AND date_format(init_date_time, '%Y-%m-%d') = '".$date."'
          AND opm.source_id IS NOT NULL 
          AND opm.source_id != '[]'";
      $onlile_trans = $this->db->query($query)->result();

      $responseArry = [];
      if (!empty($onlile_trans)) {
        foreach ($onlile_trans as $key => $val) {
          $result =  $this->payment->get_transaction_status($val->opm_id);
          if ($result->result == 'SUCCESS') {
            $decode = json_decode($result->data);
            if ($decode->data[0]->response_code == 0) {
              array_push($responseArry, $decode->data[0]);
            }
          }
        }
        $this->_auto_redrive_transaction($responseArry);
        echo "<pre>"; print_r($responseArry);
      }else{
        echo "Success transactions not found";
      }
      
    }

    public function online_fees_per_day_auto_check(){
      $date = date('Y-m-d');
      // $date = '2025-04-21';
      $query = "SELECT opm.id as opm_id, opm.source_id, ft.id as trans_id, student_id, fee_student_schedule_id, amount_paid, ft.status, paid_datetime
          FROM online_payment_master opm 
          JOIN feev2_transaction ft ON FIND_IN_SET(ft.id, REPLACE(REPLACE(opm.source_id, '[', ''), ']', ''))
          WHERE ft.fee_student_schedule_id NOT IN (
              SELECT fee_student_schedule_id 
              FROM feev2_transaction 
              WHERE date_format(paid_datetime, '%Y-%m-%d') >= '".$date."' 
              AND status = 'SUCCESS'
          ) 
          AND opm.tx_response_code != 0 
          AND date_format(init_date_time, '%Y-%m-%d') = '".$date."'
          AND opm.source_id IS NOT NULL 
          AND opm.source_id != '[]'";
      $onlile_trans = $this->db->query($query)->result();
      // $query = "select opm.id as opm_id, source_id, ft.id as trans_id, student_id, fee_student_schedule_id, amount_paid, ft.status, paid_datetime
      //         from online_payment_master opm join feev2_transaction ft on opm.source_id = ft.id where ft.fee_student_schedule_id not in (select fee_student_schedule_id from feev2_transaction where date_format(paid_datetime, '%Y-%m-%d') >= '".$date."' and status = 'SUCCESS') and opm.tx_response_code != 0 and  date_format(init_date_time, '%Y-%m-%d') = '".$date."'";
      // $onlile_trans = $this->db->query($query)->result();
      $responseArry = [];
      if (!empty($onlile_trans)) {
        foreach ($onlile_trans as $key => $val) {
          $result =  $this->payment->get_transaction_status($val->opm_id);
          if ($result->result == 'SUCCESS') {
            $decode = json_decode($result->data);
            if ($decode->data[0]->response_code == 0) {
              array_push($responseArry, $decode->data[0]);
            }
          }
        }
        $this->_auto_redrive_transaction($responseArry);
      }else{
        echo "Success transactions not found";
      }
      
    }

    private function _auto_redrive_transaction($responseArray){
      $this->load->model('feesv2/fees_collection_model');
      foreach ($responseArray as $key => $data) {
        $response = $this->payment->auto_redrive_transaction($data);
        if ($response) {
          if (strpos($response['source_id'], '[') === 0) {
            // If source_id is already in array format
            $sourceIds = json_decode($response['source_id'], true);
          } else {
              // If source_id is a single value, create an array with that value
            $sourceIds = [$response['source_id']];
          }
          foreach ($sourceIds as $key => $source_id) {
            $is_receipt_generated = $this->fees_collection_model->is_receipt_generated($source_id);
            if ($is_receipt_generated == false){
              $result = $this->fees_collection_model->update_redrive_trans_student_all_table($source_id);
              // echo "<pre>"; print_r($result); die();
              if ($result) {
                $this->fees_collection_model->create_pdf_template_for_fee_receipts($source_id, $response['transaction_id'], $response['transaction_date'], $response['transaction_time']);
              }
            }
          }
        }
      }
    }

    public function fees_application_online_payment_success_check(){
      //$date = date('Y-m-d'); 
      $date = date('Y-m-d', strtotime('-1 days'));
      $sql = "select opm.id as opm_id, opm.order_id from online_application_fee_payment_master opm join admission_forms af on opm.source_id = af.id where af.id not in (select admas.af_id from admission_status admas where admas.payment_status != 'SUCCESS') and opm.tx_response_code != 0 and date_format(af.created_on, '%Y-%m-%d') = '".$date."' and  af.receipt_number !='' or af.receipt_number is null ";
      //  echo "<pre>"; print_r($sql); die();

      $onlile_trans = $this->db->query($sql)->result();
     
      $responseArry = [];
      if (!empty($onlile_trans)) {
        foreach ($onlile_trans as $key => $val) {
          $result =  $this->payment_application->get_transaction_status($val->opm_id);
          //echo "<pre>"; print_r($result); die();
          if ($result->result == 'SUCCESS') {
            $decode = json_decode($result->data);
            if ($decode->data[0]->response_code == 0) {
              array_push($responseArry, $decode->data[0]);
            }
          }
        }
        //echo "<pre>"; print_r($responseArry); die();
        $this->_auto_redrive_application_transaction($responseArry);
        echo "<pre>"; print_r($responseArry);
      }else{
        echo "Success transactions not found";
      } 
    }

    private function _auto_redrive_application_transaction($responseArray){

      $this->load->model('Admission_model');
      foreach ($responseArray as $key => $data) {
        $response = $this->payment_application->auto_redrive_application_transaction($data);
        if ($response) {
          $isApplicationGenerated = $this->Admission_model->is_application_no_generated($response['source_id']);
          if ($isApplicationGenerated == false){
            
            $this->Admission_model->update_transcation_details($response['source_id'], 'SUCCESS','Application Amount Paid'); 
            $this->Admission_model->update_redrive_application_receipt($response['source_id']);

            if($this->settings->getSetting('show_admission_receipt_button')){
              $this->Admission_model->update_admission_application_receipt($response['source_id']);
            }

          }
        }
      }
    }

    public function calender_events_notification_for_parent(){
      $event_sms_message = $this->settings->getSetting('calender_events_notification_message');
      if (!empty($event_sms_message)) {
        $this->load->model('calenderevents_model');
        $date = date('Y-m-d', strtotime('+1 days'));
        $callenderEvents = $this->calenderevents_model->get_day_before_events_data($date);
        $school_name = $this->settings->getSetting('school_name');
        if (!empty($callenderEvents)) {
          $boards = [];
          foreach ($callenderEvents as $key => $val) {
            if (!in_array($val->board, $boards)) {
              if ($val->board !='100') {
                array_push($boards, $val->board);
              }
            }
            if ($val->applicable_to == 2 || $val->applicable_to == 3) {
              $students = $this->calenderevents_model->get_all_student_ids_for_callender_events($boards);
            }

            if ($val->applicable_to == 1 || $val->applicable_to == 3) {
              $staffs = $this->calenderevents_model->get_all_staff_ids_for_callender_events($boards);
            }
            
            $toDate = ' to '. $val->to_date;
            if ($val->to_date =='1970-01-01' || empty($val->to_date) ||  $val->to_date = null) {
              $toDate = '';
            }
            $notification_content = $event_sms_message;
            $notification_content = str_replace('%%fromdate_to_todate%%', $val->from_date.$toDate, $notification_content);
            switch ($val->event_type) {
              case '1':
              case '4':
                $eventType = 'Event';
                break;
              case '2':
              case '3':
                $eventType = 'Holiday';
                break;
              case '5':
              case '6':
                $eventType = 'Info';
                break;
              default:
                // code...
                break;
            }
            $notification_content = str_replace('%%event_type%%', $eventType, $notification_content);
            $notification_content = str_replace('%%event_name%%', $val->event_name, $notification_content);
            $notification_content = str_replace('%%school_name%%', $school_name, $notification_content);
            $input_array = array(
              'mode' => 'notification', 
              'title' => $school_name, 
              'message' => $notification_content,
              'source' => 'Callender Events',
              'student_url' => site_url('parent_controller/school_calendar'),
              'staff_url' => site_url('calender_events/calender_controller'),
              'visible' => 1,
              'send_to' => 'Both',
              
            );
            if (!empty($students)) {
              $input_array['student_ids'] = $students['student_ids'];
              $input_array['acad_year_id'] = $students['acad_year_id'];
            }
            if (!empty($staffs)) {
              $input_array['staff_ids'] = $staffs['staff_ids'];
              $input_array['acad_year_id'] = $staffs['acad_year_id'];
            }
            $this->load->helper('texting_helper');
            sendText($input_array, 'External');
          }
        }
      }
    }

    public function send_birthday_notification_sms(){
        $this->load->model('Birthday_Notifications_Model');
        // trigger_error("In Sending Birthday Wishes Notification/SMS");
        $birthday_sms_send = $this->settings->getSetting('birthday_sms_send');
        $smsMode = 'notification';
        $message_staff='';
        if($birthday_sms_send){
            $student_data= $this->Birthday_Notifications_Model->studentDataforBirthdayList();
            $data_staff = $this->Birthday_Notifications_Model->staffDataforBirthdayList();
            $message = $this->settings->getSetting('birthday_message');
            $bd_message_staff = $this->settings->getSetting('birthday_message_staff');
            $school_name = $this->settings->getSetting('school_name');
            $acad_year_id = $this->settings->getSetting('academic_year_id');
            $student_notification = array();
            $staff_notification = array();
            $mangement__staff_notification = array();
            $stdIds = array();
            if (!empty($student_data)) {
                foreach ($student_data as $key => $value) {
                    $message_staff.=$value->std_name.'('.$value->class_name.$value->section_name.'), ';
                    $temp=$message;
                    $temp=str_replace("%%student_name%%", $value->std_name, $temp);
                    // required parent name ?
                    // $temp=str_replace("%%parent_name%%", $value->parent_name, $temp);
                    $temp=str_replace("%%parent_name%%", '', $temp);
                    $student_notification[$value->student_id] = $temp;
                }
            }
            if(!empty($data_staff)) {
                foreach ($data_staff as $key => $value2) {
                    if($message_staff != '') {
                      $message_staff .= 'and ';
                    }
                    $message_staff .= $value2->staff_name.' (Staff), ';
                    $temp2=$bd_message_staff;
                    $temp2=str_replace("%%staff_name%%", $value2->staff_name, $temp2);
                    $staff_notification[$value2->staff_id] = $temp2;
                }
            }

            $input_array = array(
              'mode' => $smsMode, 
              'title' => $school_name, 
              'source' => 'Birthday SMS',
              'student_url' => site_url('parent_controller/texts'),
              'staff_url' => site_url('communication/texting/staff_texts'),
              'visible' => 1,
              'send_to' => 'Both',
              'acad_year_id' => $acad_year_id
            );
            if(!empty($student_notification)) {
              $input_array['student_id_messages'] = $student_notification;
            }
          if(!empty($staff_notification)) {
              $input_array['staff_id_messages'] = $staff_notification + $mangement__staff_notification;
          }
          $this->load->helper('texting_helper');
          sendUniqueText($input_array, 'External');
        }
    }

    private function _construct_student_email_birthday_data($student_data, $birthday_email_template, $school_name){
      $emailConstructArry = [];
      foreach ($student_data as $key => $value) {
        $emails = [
          'student' => $value->std_email,
          'father' => $value->f_email,
          'mother' => $value->m_email
        ];
        $membersEmail = [];
        $emailContent = [];
        $email_data = [];
        foreach ($emails as $role => $email) {
          $avatar_type = 0;

          switch ($role) {
              case 'student':
                  $avatar_type = 1;
                  $stakeholder_id = $value->student_id;
                  break;
              case 'father':
                  $avatar_type = 2;
                  $stakeholder_id = $value->father_id;
                  break;
              case 'mother':
                  $avatar_type = 2;
                  $stakeholder_id = $value->mother_id;
                  break;
          }

          $email_obj = new stdClass();
          $email_obj->stakeholder_id = $stakeholder_id;
          $email_obj->avatar_type = $avatar_type;
          $email_obj->email = $email;

          $email_data[] = $email_obj;

          if (!empty($email)) {
            // $membersEmail['memberEmail'][] = ['email' => $email];
            $membersEmail['memberEmail'][] = $email;
          }
        }
        $temp = clone $birthday_email_template;
        $temp->email_subject = str_replace('%%name%%', $value->std_name, $temp->email_subject);
        $temp->content = str_replace('%%school_name%%', $school_name, $temp->content);
        $temp->content = str_replace('%%name%%', $value->std_name, $temp->content);
        // Add the constructed email subject and content to the array
        $emailContent['content'] = [
            'email_subject' => $temp->email_subject,
            'email_content' => $temp->content,
            'registered_email' => $temp->registered_email,
            'receivers' => 'Parents and Students',
        ];
        $studentEmailInfo = array_merge($membersEmail, $emailContent, ['email_data' => $email_data]);
        $emailConstructArry[] = $studentEmailInfo;
      }
      return $emailConstructArry;
    }

    private function _construct_staff_email_birthday_data($data_staff, $birthday_email_template, $school_name){
      $emailConstructArry = [];
      foreach ($data_staff as $key => $value) {
        $emails = [
          'staff' => $value->stf_email,
        ];
        $membersEmail = [];
        $emailContent = [];
        $email_data = [];
        foreach ($emails as $role => $email) {
          $email_obj = new stdClass();
          $email_obj->stakeholder_id = $value->staff_id;
          $email_obj->avatar_type = $value->avatar_type;
          $email_obj->email = $email;

          $email_data[] = $email_obj;

          if (!empty($email)) {
            // $membersEmail['memberEmail'][] = ['email' => $email];
            $membersEmail['memberEmail'][] = $email;
          }
        }
        $temp = clone $birthday_email_template;
        $temp->email_subject = str_replace('%%name%%', $value->staff_name, $temp->email_subject);
        $temp->content = str_replace('%%name%%', $value->staff_name, $temp->content);
        // $temp->content = str_replace('%%school_name%%', $school_name, $temp->content);
        // Add the constructed email subject and content to the array
        $emailContent['content'] = [
            'email_subject' => $temp->email_subject,
            'email_content' => $temp->content,
            'registered_email' => $temp->registered_email,
            'receivers' => 'Staff',
        ];
        $staffEmailInfo = array_merge($membersEmail, $emailContent, ['email_data' => $email_data]);
        $emailConstructArry[] = $staffEmailInfo;
      }
      return $emailConstructArry;
    }

    private function _construct_members_email_data($members_data, $student_names, $staff_names, $birthday_info_members_email_template, $school_name){
      $emailConstructArry = [];
      $temp = clone $birthday_info_members_email_template;
      if (strpos($temp->members_email, ', ') != false) {
          $emails = explode(', ', $temp->members_email);
      } else {
          $emails = explode(',', $temp->members_email);
      }
      if(empty($student_names)){
        $temp->content = str_replace('%%participants%%', 'Staff', $temp->content);
        $staff_names = str_replace(', ', '<br>', $staff_names);
        $temp->content = str_replace('%%names%%', $staff_names, $temp->content);
      }else if(empty($staff_names)){
        $temp->content = str_replace('%%participants%%', 'Student(s)', $temp->content);
        $student_names = str_replace(', ', '<br>', $student_names);
        $temp->content = str_replace('%%names%%', $student_names, $temp->content);
      }else{
        $staff_names = str_replace(', ', '<br>', $staff_names);
        $student_names = str_replace(', ', '<br>', $student_names);
        $combined_names = 'Staff: <br>' . $staff_names . '.<br><br>Student(s): <br>' . $student_names;
        $temp->content = str_replace('%%participants%%', 'Staff and Student(s)', $temp->content);
        $temp->content = str_replace('%%names%%', $combined_names, $temp->content);
      }
      $temp->content = str_replace('%%school_name%%', $school_name, $temp->content);
      if (!empty($emails)) {
        foreach($emails as $email){
          // $membersEmail['memberEmail'][] = ['email' => $email];
          $membersEmail['memberEmail'][] = $email;
        }
      }
      $emailContent['content'] = [
          'email_subject' => $temp->email_subject,
          'email_content' => $temp->content,
          'registered_email' => $temp->registered_email,
          'receivers' => 'Members Only',
      ];
      $email_data = [];
      foreach($members_data as $members){
        $email_obj = new stdClass();
        $email_obj->stakeholder_id = $members->staff_id;
        $email_obj->avatar_type = $members->avatar_type;
        $email_obj->email = $members->stf_email;

        $email_data[] = $email_obj;
      }
      $staffEmailInfo = array_merge($membersEmail, $emailContent, ['email_data' => $email_data]);
      $emailConstructArry[] = $staffEmailInfo;
      // echo "<pre>";print_r($emailConstructArry);die();
      return $emailConstructArry;
    }

    public function send_birthday_email(){
      $this->load->model('Birthday_Notifications_Model');
      $this->load->helper('email_helper');

      $birthday_student_email_template = $this->Birthday_Notifications_Model->getBirthdayEmailTemplateForStudents();
      $birthday_staff_email_template = $this->Birthday_Notifications_Model->getBirthdayEmailTemplateForStaff();
      $birthday_info_members_email_template = $this->Birthday_Notifications_Model->getBirthdayInfoEmailTemplateForMember();
      $school_name = $this->settings->getSetting('school_name');
      $student_data = [];
      if(!empty($birthday_student_email_template)){
        $student_data= $this->Birthday_Notifications_Model->studentDataforBirthdayList();
      }
      
      $data_staff = [];
      if(!empty($birthday_staff_email_template)){
        $data_staff = $this->Birthday_Notifications_Model->staffDataforBirthdayList();        
      }
      $student_result = [];
      $student_names = '';
      if (!empty($student_data)) {
        $student_result = $this->_construct_student_email_birthday_data($student_data, $birthday_student_email_template,$school_name);
        $student_names = implode(', ', array_map(function($student) {
            return $student->std_name;
        }, $student_data));
        echo "<pre>";
        print_r('Students Data Collected<br>');
        print_r($student_names);
      }
      // trigger_error("student_result");
      // trigger_error(json_encode($student_result));
      $staff_result = [];
      $staff_names = [];
      if(!empty($data_staff)){
        $staff_result = $this->_construct_staff_email_birthday_data($data_staff, $birthday_staff_email_template, $school_name);
        $staff_names = implode(', ', array_map(function($staff) {
            return $staff->staff_name;
        }, $data_staff));
        echo "<pre>";
        print_r('Staffs Data Collected<br>');
        print_r($staff_names);
      }

      $members_data = [];
      if (!empty($birthday_info_members_email_template) && !empty($birthday_info_members_email_template->members_email) && (!empty($student_data) || !empty($data_staff))){
        $members_data = $this->Birthday_Notifications_Model->membersDataForBirthdayInfo($birthday_info_members_email_template->members_email);
      }

      $members_result = [];
      if(!empty($members_data)){
        $members_result = $this->_construct_members_email_data($members_data, $student_names, $staff_names, $birthday_info_members_email_template, $school_name);
        echo "<pre>";
        print_r('Members Data Collected<br>');
      }

      $merge = array_merge($student_result, $staff_result, $members_result);

      $acad_year_id = $this->settings->getSetting('academic_year_id');
      if(!empty($merge)){
        echo "<pre>";
        print_r('Sending and Storing Birthday Emails');
        foreach ($merge as $key => $value) {
          // echo "<pre>";print_r($value['memberEmail']);
          // $members = array_column(array_values(array_filter($value['memberEmail'])), 'email');
          $email_master_data = array(
            'subject' => $value['content']['email_subject'],
            'body' => $value['content']['email_content'],
            'source' => 'Birthday Email',
            'sent_by' => 1,
            'recievers' => $value['content']['receivers'],
            'from_email' => $value['content']['registered_email'],
            'files' => NULL,
            'acad_year_id' => $acad_year_id,
            'visible' => 1,
            'sender_list' => NULL,
            'sending_status' => 'Completed'
          );
          $this->load->model('communication/emails_model');
          $email_master_id = $this->emails_model->saveEmail($email_master_data);
          $this->emails_model->save_sending_email_data($value['email_data'], $email_master_id);
          $this->load->helper('email_helper');
          sendEmail($value['content']['email_content'], $value['content']['email_subject'], $email_master_id, $value['memberEmail'], $value['content']['registered_email'], []);
        }
        echo "<pre>";
        print_r('Emails Sent');
      }
    }

    public function fine_amount_update(){
      $this->load->model('feesv2/Fees_student_model');        
      $result = $this->Fees_student_model->update_fine_amount();
      if ($result) {
        // trigger_error('fine amount refresh triggered successfully');
      }else{
        trigger_error('fine amount refresh triggered un-successfully');
      }
    }

    public function get_staff_task_basket_open_details(){
      $this->load->model('stb/Staff_tasks_model');
      $result = $this->Staff_tasks_model->get_open_staff_task_basket_data();
      $school_name = $this->settings->getSetting('school_name');
      $acad_year_id = $this->settings->getSetting('academic_year_id');

      $staffTaskBasket_notification = array();

      if (!empty($result)) {
        foreach ($result as $key => $val) {
          $message = 'You have '.$val->staffTaskCount.' open tasks pending in your staff basket. Kindly address it timely.';
          $staffTaskBasket_notification[$val->staff_id] = $message;
        }
      }
      $input_array = array(
        'mode' => 'notification', 
        'title' => $school_name, 
        'source' => 'Tasks basket notification',
        'student_url' => '',
        'staff_url' => site_url('dashboard'),
        'visible' => 1,
        'send_to' => 'staff',
        'acad_year_id' => $acad_year_id,
        'staff_id_messages'=> $staffTaskBasket_notification
      );
      $this->load->helper('texting_helper');
      sendUniqueText($input_array, 'External');
    }


    public function suy_register_roots(){
      $data = (array) json_decode(file_get_contents('php://input'), TRUE);
      $insert_id = $this->suy_model->insert_suy_register_users($data);
      $success = 0;
      if ($insert_id) {
       $success = $insert_id;
      }
      echo json_encode($success);
    }

    public function get_suy_roots_progams_batch(){
      $programName = (array) json_decode(file_get_contents('php://input'), TRUE);
      $programName = 'Beginner';
      $result = $this->suy_model->get_suy_roots_prgram_batch_list($programName);
      $success = [];
      if ($result) {
        $success = ['status' => '1', 'message' => 'Successull', 'data' => $result];
      }else{ 
        $success = ['status' => '0', 'message' => 'error', 'data' =>[]];
      }
      echo json_encode($success);
    }

    //Statistics update
    public function calculate_save_statistics () {
      $acad_year_id = $this->settings->getSetting('academic_year_id');
      $this->load->model('msm/Msm_statistics_model', 'msms');

      //Student count
      $this->load->model('msm/student_analytics_model', 'sam');
      $student_count = $this->sam->get_student_summary($acad_year_id);
      $this->msms->save_statistics("student_count_obj", json_encode($student_count), 'json');

      //Staff attendance
      $this->load->model('msm/Staff_attendance_model', 'staff_att_model');
      $staff_att = $this->staff_att_model->get_staff_attendance_summary();
      $this->msms->save_statistics("staff_attendance_obj", json_encode($staff_att), 'json');

      $from_date = date('Y-m-d', strtotime('-7 days'));
			$yesterday = date('Y-m-d', strtotime('-1 days'));
		  $to_date =  date('Y-m-d');

      $this->load->model('msm_v3/Todays_feed_model', 'todays_feed_model');
      $fee_att = $this->todays_feed_model->get_fee_collection_tf($from_date, $yesterday, $to_date);
      $this->msms->save_statistics("todays_feed_fees_obj", json_encode($fee_att), 'json');

      $staff_att_todays_feed = $this->todays_feed_model->get_staff_attendance_tf($to_date);
      $this->msms->save_statistics("todays_feed_staff_att_obj", json_encode($staff_att_todays_feed), 'json');

      $leads_att = $this->todays_feed_model->leads_enquiry_tf($acad_year_id, $from_date, $yesterday, $to_date);
      $this->msms->save_statistics("todays_feed_leads_obj", json_encode($leads_att), 'json');

      $school_code = $this->settings->getSetting('school_short_name');
      $admission_att = $this->todays_feed_model->admission_application_tf($school_code, $acad_year_id, $from_date, $yesterday, $to_date);
      $this->msms->save_statistics("todays_feed_admission_obj", json_encode($admission_att), 'json');

      $parent_att = $this->todays_feed_model->get_parent_ticketing_tf($from_date, $yesterday, $to_date);
      $this->msms->save_statistics("todays_feed_parent_obj", json_encode($parent_att), 'json');

      $infirmary_att = $this->todays_feed_model->get_infirmary_count_tf($acad_year_id, $from_date, $yesterday, $to_date);
      $this->msms->save_statistics("todays_feed_infirmary_obj", json_encode($infirmary_att), 'json');

      $int_att = $this->todays_feed_model->get_internal_ticketing_activity_tf($from_date, $yesterday, $to_date);
      $this->msms->save_statistics("todays_feed_internal_obj", json_encode($int_att), 'json');

      $this->load->model('msm_v3/Overview_model', 'overview_model');
      $overview_att = $this->overview_model->get_msm_landing_page_data($acad_year_id, $school_code);
      $this->msms->save_statistics("overview_data", json_encode($overview_att), 'json');
    }

    public function fee_daily_transaction_email(){
      // $todaydate = date("Y-m-d");
      $this->load->model('feesv2/reports_model');
      $this->load->helper('fees_helper');
      $email_content =$this->reports_model->get_email_template_management_fee();
      if (empty($email_content) || empty($email_content->members_email)) {
        return false;
        exit();
      }
      
      $fee_amount = $this->reports_model->get_daily_track_fee_transaction();
      // $fee_management = $this->reports_model->get_fee_management_summary();
      $enquiries = $this->reports_model->get_daily_track_enquiries_datewise();
      $admissions = $this->reports_model->get_daily_track_admission_datewise();
      $schoolName = $this->settings->getSetting('school_name');
      $date = date('d M y', strtotime('-1 days'));
      $htmloutput = '';
      $htmloutput .= 'Dear Management Team, <br><br>'.$schoolName.' statistics for '.$date.' - ';
      $htmloutput .= '<br><br><b>Fees Collection</b><br>';
      $htmloutput .= '<br><table border="1" cellpadding="6" cellspacing="0">
      <tr>
        <th>Fee Head</th>
        <th>Total received</th>
        <th>Total daily collection</th>
        <th>Cash Payment</th>
        <th>Other Payment</th>
        <th>Online Payment</th>
        <th>Concession</th>
      </tr>';

      $htmloutput .= '<tr>';
      $htmloutput .= '<td>Fees Collection</td>';
      $htmloutput .= '<td><b>' . numberToCurrency_withoutSymbol(!empty($fee_amount) ? $fee_amount['totalReceipts'] : 0) . '</b></td>';
      $htmloutput .= '<td><b>' . numberToCurrency_withoutSymbol(!empty($fee_amount) ? $fee_amount['total_collection_daily'] : 0) . '</b></td>';
      $htmloutput .= '<td>' . numberToCurrency_withoutSymbol(!empty($fee_amount) ? $fee_amount['cash_collected_amount'] : 0) . '</td>';
      $htmloutput .= '<td>' . numberToCurrency_withoutSymbol(!empty($fee_amount) ? $fee_amount['other_collected_amount'] : 0) . '</td>';
      $htmloutput .= '<td>' . numberToCurrency_withoutSymbol(!empty($fee_amount) ? $fee_amount['online_collected_amount'] : 0) . '</td>';
      $htmloutput .= '<td>' . numberToCurrency_withoutSymbol(!empty($fee_amount) ? $fee_amount['total_concession'] : 0) . '</td>';
      $htmloutput .= '</tr>';

      $htmloutput .= '<tr>';
      $htmloutput .= '<td>Application Collection</td>';
      $htmloutput .= '<td><b>' . numberToCurrency_withoutSymbol(!empty($admissions) ? $admissions['totalReceipts'] : 0) . '</b></td>';
      $htmloutput .= '<td><b>' . numberToCurrency_withoutSymbol(!empty($admissions) ? $admissions['total_collection_daily'] : 0) . '</b></td>';
      $htmloutput .= '<td>' . numberToCurrency_withoutSymbol(!empty($admissions) ? $admissions['cash_collected_amount'] : 0) . '</td>';
      $htmloutput .= '<td>' . numberToCurrency_withoutSymbol(!empty($admissions) ? $admissions['other_collected_amount'] : 0) . '</td>';
      $htmloutput .= '<td>' . numberToCurrency_withoutSymbol(!empty($admissions) ? $admissions['online_collected_amount'] : 0) . '</td>';
      $htmloutput .= '<td>' . numberToCurrency_withoutSymbol(!empty($admissions) ? $admissions['total_concession'] : 0) . '</td>';
      $htmloutput .= '</tr>';

      $htmloutput .= '<tr>';
      $htmloutput .= '<td><b>Total</b></td>';
      $htmloutput .= '<td><b>' . numberToCurrency_withoutSymbol(
          (!empty($fee_amount) ? $fee_amount['totalReceipts'] : 0) + 
          (!empty($admissions) ? $admissions['totalReceipts'] : 0)
      ) . '</b></td>';
      $htmloutput .= '<td><b>' . numberToCurrency_withoutSymbol(
          (!empty($fee_amount) ? $fee_amount['total_collection_daily'] : 0) + 
          (!empty($admissions) ? $admissions['total_collection_daily'] : 0)
      ) . '</b></td>';
      $htmloutput .= '<td><b>' . numberToCurrency_withoutSymbol(
          (!empty($fee_amount) ? $fee_amount['cash_collected_amount'] : 0) + 
          (!empty($admissions) ? $admissions['cash_collected_amount'] : 0)
      ) . '</b></td>';
      $htmloutput .= '<td><b>' . numberToCurrency_withoutSymbol(
          (!empty($fee_amount) ? $fee_amount['other_collected_amount'] : 0) + 
          (!empty($admissions) ? $admissions['other_collected_amount'] : 0)
      ) . '</b></td>';
      $htmloutput .= '<td><b>' . numberToCurrency_withoutSymbol(
          (!empty($fee_amount) ? $fee_amount['online_collected_amount'] : 0) + 
          (!empty($admissions) ? $admissions['online_collected_amount'] : 0)
      ) . '</b></td>';
      $htmloutput .= '<td><b>' . numberToCurrency_withoutSymbol(
          (!empty($fee_amount) ? $fee_amount['total_concession'] : 0) + 
          (!empty($admissions) ? $admissions['total_concession'] : 0)
      ) . '</b></td>';
      $htmloutput .= '</tr>';
      $htmloutput .= '</table>';
      
      $enquiries_today = 0;
      if (!empty($enquiries)) {
        $enquiries_today = $enquiries->enquiries_today;
      }
      $admission_today = 0;
      if (!empty($admissions['totalReceipts'])) {
        $admission_today = $admissions['totalReceipts'];
      }

      $dailyTransactionsLink = site_url('feesv2/reports/day_books1');
      $managementSummaryLink = site_url('feesv2/reports_v2/management_summary');
      $EnquirySummaryLink = site_url('enquiry/enquiry_staff/enquiry_graphs');
      $htmloutput .= '<br><br>Enquiries received today: '.$enquiries_today.'<br>';
      $htmloutput .= 'Admission Forms received today: '.$admission_today.'<br><br>';
      $htmloutput .= '<b>Please visit the <a target="_blank" rel="noopener noreferrer"  href='.$dailyTransactionsLink.'>Daily Transactions</a>.</b><br>';
      $htmloutput .= '<b>Please visit the <a target="_blank" rel="noopener noreferrer"  href='.$managementSummaryLink.'>Fees Management Summary</a>.</b><br>';
      $htmloutput .= '<b>Please visit the <a target="_blank" rel="noopener noreferrer"  href='.$EnquirySummaryLink.'>Enquiry Analysis</a>.</b><br>';
      $htmloutput .= '<br>Thanks and Regards,<br>- NextElement Team';
      $content = $htmloutput;
      $memberSplit = explode(',', $email_content->members_email);
      $memberEmail = [];
      foreach ($memberSplit as $key => $val) {
          $memberEmail[] = $val;
      }
      $this->load->model('Birthday_Notifications_Model');
      $members_data = $this->Birthday_Notifications_Model->membersDataForBirthdayInfo($email_content->members_email);
      $email_data = [];
      if(!empty($members_data)){
        foreach ($members_data as $key => $val) {
            if(empty($val->stf_email))
                continue;

            $email_obj = new stdClass();
            $email_obj->stakeholder_id = $val->staff_id;
            $email_obj->avatar_type = $val->avatar_type;
            $email_obj->email = $val->stf_email;
            $email_data[] = $email_obj;
        }
      }
      $acad_year_id = $this->settings->getSetting('academic_year_id');
      $email_master_data = array(
        'subject' => $email_content->email_subject,
        'body' => 'Fees Managemeny Statistics',
        'source' => 'Fee Daily Transaction Statistics For Management',
        'sent_by' => 0,
        'recievers' => 'Staffs',
        'from_email' => $email_content->registered_email,
        'files' => NULL,
        'acad_year_id' => $acad_year_id,
        'visible' => 1,
        'sender_list' => NULL,
        'sending_status' => $email_content->members_email
      );
      $this->load->model('communication/emails_model');
      $email_master_id = $this->emails_model->saveEmail($email_master_data);
      if(!empty($email_data)){
        $this->emails_model->save_sending_email_data($email_data, $email_master_id);
      }
      $this->load->helper('email_helper');
      sendEmail($content, $email_content->email_subject, $email_master_id, $memberEmail, $email_content->registered_email, []);
    }

    function update_settlementdata_status_daily(){
      $this->load->library('payment');
      $result = $this->db->select('id, order_id, settlement_details_json')
      ->from('online_payment_master')
      ->where('tx_response_code',0)
      ->where('settlement_details_json is null')
      ->get()->result();
      foreach ($result as $key => $val) {
        $settlements = $this->payment->get_online_settlementdata_status($val->order_id);
        $settelmentArry = [];
        if(!empty($settlements->data)){
          foreach ($settlements->data as $key => $settlement) {
            if(!array_key_exists($settlement->transaction_id, $settelmentArry)) {
              $settelmentArry[$settlement->transaction_id]['settlement_split'] = array();
            }
            $settelmentArry[$settlement->transaction_id]['settlement_split'][] = 
            array(
              'settlement_id'=>$settlement->settlement_id,
              'bank_reference'=>$settlement->bank_reference,
              'settlement_datetime'=>date('d-M-Y',strtotime($settlement->settlement_datetime)),
              'transaction_date'=>date('d-M-Y',strtotime($settlement->transaction_date)),
              'settlement_account_name'=>$settlement->settlement_account_name,
              'settlement_account_number'=>$settlement->settlement_account_number,
              'settlement_ifsc_code'=>$settlement->settlement_ifsc_code,
              'settlement_bank_name'=>$settlement->settlement_bank_name,
              'settlement_bank_branch'=>$settlement->settlement_bank_branch,
              'amount_reimbursed'=>$settlement->amount_reimbursed,
            );
            $this->db->where('tx_id',$settlement->transaction_id);
            $this->db->update('online_payment_master', array('settlement_details_json'=>json_encode($settelmentArry)));
          }
        }
      }
    }

    public function auto_checkout_staff_attendance(){
      $is_staff_attendance_auto_checkout_disabled=$this->settings->getSetting("disable_staff_attendance_auto_checkout");

      if((int)$is_staff_attendance_auto_checkout_disabled==1){
        return;
      }
      
      $this->load->model('staff/Attendance_model');
      $this->Attendance_model->mark_previously_missed_checkout_automatic();
    }

  public function send_notifications_to_staff_not_checked_in(){
    $this->load->model('staff/Attendance_model');
    $this->Attendance_model->send_notifications_to_staff_not_checked_in();
  }

  public function send_notifications_to_staff_not_checked_out(){
    $this->load->model('staff/Attendance_model');
    $this->Attendance_model->send_notifications_to_staff_not_checked_out();
  }

    public function auto_challan_payments_generate(){
      $fromdate = date('Y-m-d', strtotime('-1 days'));
			$toDate = date('Y-m-d'); 
			echo $this->payment->get_get_challan_details($fromdate, $toDate);
    }


  public function updatePurchasePdfLink()
  {
    $this->load->model('procurement/Requisition_model_v2');
    $input = $this->input->post();
    $this->Requisition_model_v2->updatePurchasePdfLink($input['path'], $input['status']);
  }

  public function updateBOMPdfLink(){
    $this->load->model('procurement/Requisition_model_v2');
    $input = $this->input->post();
    $this->Requisition_model_v2->updateBOMPdfLink($input['path'], $input['status']);
  }

  public function call_automatic_send_calender_notifications(){
    $this->load->model("Calenderevents_model");
    $this->Calenderevents_model->automatic_send_calender_notifications();
  }

  public function updateRetrnSalesPdfLink() {
    // trigger_error('In Callback_controller for Fee PDF');
    // trigger_error(json_encode($_POST));
    $this->load->model('procurement/sales_model_v2');
    $input = $this->input->post();
    $this->sales_model_v2->updateReturnSalesPdfLink($input['path'], $input['status']);
          
}
  public function ticket_escalation(){
    $all_tickets = $this->Internalticketing_model->get_all_tickets_with_escalation_time();
    // echo '<pre>tickets';print_r($all_tickets);
    foreach($all_tickets as $val=>$ticket){
      if ($ticket->escalation_status == 'No Escalation'){
        $this->get_escalation_time_and_update_data($ticket->ticket_id, $ticket->created_on, $ticket->first_escalation_time, 'First Escalation');
      }else if($ticket->escalation_status == 'First Escalation'){
        $this->get_escalation_time_and_update_data($ticket->ticket_id, $ticket->created_on, $ticket->second_escalation_time, 'Second Escalation');
      }else if($ticket->escalation_status == 'Second Escalation'){
        $this->get_escalation_time_and_update_data($ticket->ticket_id, $ticket->created_on, $ticket->third_escalation_time, 'Third Escalation');
      }
    }

  }

  public function get_escalation_time_and_update_data($ticket_id, $created_on, $escalation_status_time, $status){
    $current_date_time = date('Y-m-d H:i:s');
    $current_date_time = new DateTime($current_date_time);
    $created_on = new DateTime($created_on);
    $schoolName = $this->settings->getSetting('school_short_name');
    if ($schoolName == 'demoschool1') {
      echo 'Demoschool1';
      $escalation_time_in_min = $escalation_status_time;
          } else {
      echo 'Other School';
      $escalation_time_in_min = $escalation_status_time * 60;
          }
    $time_difference = $current_date_time->diff($created_on);
    $total_minutes_difference = $time_difference->days * 24 * 60 + $time_difference->h * 60 + $time_difference->i;
    if ($total_minutes_difference >= $escalation_time_in_min){
      // print_r("Herer");die();
      $result = $this->Internalticketing_model->update_escalation_status_and_assignee($ticket_id, $status);
      if  ($result){
        $ticket_details = $this->Internalticketing_model->get_ticket_details_for_notification($ticket_id); 
        $assigned_staff_details = $this->Internalticketing_model->get_staff_name_and_email($ticket_id, 'assigned_to');
        // echo "<pre>";print_r($assigned_staff_details);die();
        $staff_ids = $this->Internalticketing_model->get_ticket_creator_by_id($ticket_id);
        if(!empty($staff_ids)) {
          $this->load->helper('texting_helper');
          $notify_array = array();
          $notify_array['staff_ids'] = $staff_ids;
          $notify_array['title'] = 'Escalated to next level';
          $notify_array['message'] = "Ticket $ticket_details->ticket_item_id created by you is escalated to $assigned_staff_details->staff_name";
          $notify_array['mode'] = 'notification';
          $notify_array['source'] = 'Internal Ticketing';
          sendText($notify_array, 'External');
        }
        $staff_ids = $this->Internalticketing_model->get_staff_by_department($ticket_id);
        if(!empty($staff_ids)) {
          $this->load->helper('texting_helper');
          $notify_array = array();
          $notify_array['staff_ids'] = $staff_ids;
          $notify_array['title'] = 'Escalated to next level';
          $notify_array['message'] = "Ticket $ticket_details->ticket_item_id is escalated to $assigned_staff_details->staff_name";
          $notify_array['mode'] = 'notification';
          $notify_array['source'] = 'Internal Ticketing';
          sendText($notify_array, 'External');
        }
      }
    }
    return 1; //True
  }

  public function updateSalesPdfLink_v2() {
    // trigger_error('In Callback_controller for Sales PDF');
    // trigger_error(json_encode($_POST));
    $this->load->model('sales/Sales_model_v2');
    $input = $this->input->post();
    $this->Sales_model_v2->updateSalesPdfLink_v2($input['path'], $input['status']);
}

public function online_admission_payment_transactions(){
  //$date = '2024-02-11';
  $date = date('Y-m-d', strtotime('-1 days'));

  $query = "select opm.id as opm_id, opm.order_id from online_application_fee_payment_master opm join admission_forms af on opm.source_id = af.id where af.id not in (select admas.af_id from admission_status admas where admas.payment_status = 'SUCCESS') and opm.tx_response_code != 0 and date_format(af.created_on, '%Y-%m-%d') >= '".$date."'";
  $trans = $this->db->query($query)->result();

  

  //echo "<pre>"; print_r($trans); die();

  $noOfSuccess = 0;
  $noOfFailure = 0;
  $noOfInitiated = 0;
  $status_arrays = [];
  foreach ($trans as $key => $val) {
    $txData=array(
      'transaction_date'=>$date,
      'order_id' => $val->order_id,
      'reconcile_status'=> 'Correct',
      'traknpay_status' => 'FAILED'
    );

    $status = $this->payment->get_transaction_details_all($val->order_id);
    //echo "<pre>"; print_r($status); die();
    $txData['reconcile_status'] = 'Correct';
    if(!isset($status->error)) {
      $oderId = $status->data[0]->order_id;
      $opmQuery ="select tx_response_code, tx_response_message, status, source_id from online_application_fee_payment_master opm where opm.order_id='".$oderId."'";
      $opmStatus = $this->db->query($opmQuery)->row();

      
      if($status->data[0]->response_code == 0) {
        $noOfSuccess++;
        $txData['traknpay_status'] = 'SUCCESS';
        if($opmStatus->tx_response_code != 0) {
          $txData['reconcile_status'] = 'Incorrect';
        } else {
          $txQuery = "select id, payment_status from admission_status as where as.id='".$opmStatus->source_id."'";
          $txStatus = $this->db->query($txQuery)->row();
          if($txStatus->payment_status != 'SUCCESS') {
            $txData['reconcile_status'] = 'Incorrect-Transaction';
          }
        }
      } else {
        $noOfFailure++;
      }
    } else {
      $txData['traknpay_status'] = 'INITIATED';
      $noOfInitiated++;
    }

    array_push($status_arrays, $txData);
  }
  
  
  $table = '';
  $table .= '<h3>'.$this->settings->getSetting('school_name').'</h3>';
  // echo "<pre>"; print_r($status_arrays); die();
  if(empty($status_arrays)) {
    $table .= '<p>No Transactions</p>';
  } else {
    $table .= '<table border="1" cellpadding="6" cellspacing="0">

    <tr>
      <th colspan="4">Summary </th>
    </tr>
    <tr>
      <th>No of Transactions : '.count($trans).'</th>
      <th>No of Success : '.$noOfSuccess.'</th>
      <th>No of Initiated : '.$noOfInitiated.'</th>
      <th>No of Failure : '.$noOfFailure.'</th>
    </tr>

    <tr>
      <th colspan="2">Order Id </th>
      <th>Reconcile Status </th>
      <th>Traknpay Status</th>
    </tr>';

    foreach ($status_arrays as $key => $val) { 
     $table .= '<tr>
        <th colspan="2">'.$val['order_id'].'</th>';
        if ($val['reconcile_status'] =='Incorrect' || $val['reconcile_status'] =='Incorrect-Transaction') {
          $table .='<th style="color:red">'.$val['reconcile_status'].'</th>';
        }else{
          $table .='<th>'.$val['reconcile_status'].'</th>';
        }
       
        $table .='<th>'.$val['traknpay_status'].'</th>
      </tr>';
    }
    $table .= '</table><br>';
  }
  echo $table;
}

  public function subject_attendance_notify_absent_students_to_parents() {
    // 1. We need all the classes to fetch students
    $this->load->model('class_section');
    $this->load->model('attendance_v2/Attendance_model');

    $classes = $this->class_section->getAllClassess();

    $status_message=[];
    if(empty($classes)){
      $status_message["status"] = "Error";
      $status_message["message"]="Classes not found!";

      echo json_encode($status_message); die();
    }
    
    $class_ids=[];
    foreach($classes as $key => $class){
      $class_ids[]=$class->id;
    }

    $requestDate = date('Y-m-d');
    // $requestDate = "2024-10-14";
    $action_type="absentees";

    $response=$this->Attendance_model->get_late_absentees_students(["requestDate"=>$requestDate,"classIds"=>$class_ids,"actionType"=>$action_type]);

    if(empty($response)){
      $status_message["status"] = "Success";
      $status_message["message"] = "Found no absent students for date " . date("d.m.Y", strtotime($requestDate)) . "!";

      echo json_encode($status_message); die();
    }

    // Construct message to for absent students
    $message_template=$this->settings->getSetting("subject_attendance_notify_late_absent_notification_template");

    if(empty($message_template)){
      $message_template="Your ward was %%student_name%% of %%class_section%% is %%action_type%% on %%date%% for the subject(s) %%subjects%%. Kindly ignore if you have already informed the school through email-%%school_name%% - NXTSMS";
    }

    // $communication_mode="notification";
    // $text_send_to = "Mother";
    // $text_send_to = "Both";
    // $text_send_to = "preferred";
    // $student_messages[$student_id]="preffered message";

    $student_messages = [];

    $school_name=$this->settings->getSetting("school_short_name");
    if(empty($school_name)){
      $school_name=$this->settings->getSetting("school_name");
    }

    foreach($response as $key => $student_info){
      $message = str_replace("%%student_name%%",$student_info->student_name,$message_template);
      $message = str_replace("%%class_section%%",$student_info->class_name.' '.$student_info->section_name,$message);
      $message = str_replace("%%action_type%%","Absent",$message);
      $message = str_replace("%%date%%",date("d.m.Y",strtotime($requestDate)),$message);
      $message = str_replace("%%subjects%%",$student_info->absent_subjects,$message);
      $message = str_replace("%%school_name%%",$school_name,$message);

      $student_messages[$student_info->student_id]=$message;
    }

    $communication_mode=$this->settings->getSetting("subject_attendance_automatic_text_mode_to_use");
    if($communication_mode!="sms"){
      $communication_mode = "notification";
    }
    
    $input = array();
    $input['source'] = 'Subject Attendance';
    $text_send_to = "";
    
    if($communication_mode=="notification"){
      $text_send_to = "Both";
    }else{
      $text_send_to = $this->settings->getSetting('text_send_to');
      if(empty($text_send_to)){
        $text_send_to = "Father";
      }
    }
    
    $input['mode'] = $communication_mode;
    $input['student_id_messages'] = $student_messages;
    $input['send_to'] = $text_send_to;

    $status = 0;
    $error = 0;
    $warning = 0;
    $res = sendUniqueText($input_array, 'External');

    if ($res['success'] != ''){
      $status = 1;
    }

    $error = $res['error'];
    $warning = $res['warning'];

    echo json_encode(['status' => $status, 'error' => $error, 'warning' => $warning]);
  }

  public function fees_audit_assinged_amount_more_than_collected_amount(){
    $query = "select fss.id, feev2_cohort_student_id, total_fee, (total_fee_paid + total_concession_amount + total_concession_amount_paid) as total_paid, total_fee_paid,   payment_status, total_concession_amount, total_concession_amount_paid , total_fine_amount, total_fine_amount_paid, concat(sa.first_name,'',ifnull(sa.last_name,'')) as student_name, sa.admission_no
    from feev2_student_schedule fss
    join feev2_cohort_student fcs on fss.feev2_cohort_student_id=fcs.id
    join student_admission sa on fcs.student_id = sa.id
    where payment_status ='PARTIAL' and total_fee < (total_fee_paid + total_concession_amount + total_concession_amount_paid)";
    $audit = $this->db->query($query)->result();
    $table = '';
    $table .= '<h3>'.$this->settings->getSetting('school_name').'</h3>';
    if(empty($audit)) {
      $table .= '<p>No Issue</p>';
    } else {
      $table .= '<table border="1" cellpadding="6" cellspacing="0">';

      foreach ($audit as $key => $val) { 
       $table .= '<tr>
          <td>'.$val->admission_no.'</td>
          <td>'.$val->student_name.'</td>
        </tr>';
      }
      $table .= '</table><br>';
    }
    echo $table;
  }

  public function admission_number_duplicate(){
    $query = "
          SELECT sa.id, sa.admission_no, CONCAT(sa.first_name, ' ', IFNULL(sa.last_name, '')) AS student_name, admission_acad_year_id, 
          (CASE WHEN sy.promotion_status = 4 OR sy.promotion_status = 5 THEN sy.promotion_status ELSE sa.admission_status END) AS admission_status, 
          CONCAT(c.class_name, IFNULL(cs.section_name, '')) AS classSection
          FROM student_admission sa
          JOIN student_year sy ON sa.id = sy.student_admission_id
          JOIN class c ON sy.class_id = c.id
          JOIN class_section cs ON sy.class_section_id = cs.id
          JOIN (
              SELECT admission_no
              FROM student_admission
              GROUP BY admission_no
              HAVING COUNT(*) > 1
          ) dup ON sa.admission_no = dup.admission_no
          ORDER BY sa.admission_no, sa.id;
      ";
    $audit = $this->db->query($query)->result();
    $table = '';
    $table .= '<h3>'.$this->settings->getSetting('school_name').'</h3>';
    if(empty($audit)) {
      $table .= '<p>No Issue</p>';
    } else {
      $table .= '<table border="1" cellpadding="6" cellspacing="0">';
      foreach ($audit as $key => $val) { 
        $acadYearJoining = $this->acad_year->getAcadYearById($val->admission_acad_year_id);
        $admission_status = '';
        if($val->admission_status == 1){
          $admission_status = 'Pending';
        }else if($val->admission_status == 2){
          $admission_status = 'Approved';
        }else if($val->admission_status == 3){
          $admission_status = 'Rjected';
        }else{
          $admission_status = 'Alumini';
        }

       $table .= '<tr>
          <td>'.$val->admission_no.'</td>
          <td>'.$val->student_name.'</td>
          <td>'.$acadYearJoining.'</td>
          <td>'.$admission_status.'</td>
          <td>'.$val->classSection.'</td>
        </tr>';
      }
      $table .= '</table><br>';
    }
    echo $table;
  }

  public function email_bounce_school_wise_list(){
    $from_date = date('d-m-Y', strtotime('-7 days'));
    $to_date = date('d-m-Y');
    $query = "SELECT COUNT(est.id) AS count, est.status
          FROM email_master em
          JOIN email_sent_to est ON em.id = est.email_master_id
          WHERE est.status IN ('Bounce-failed','Complaint-abuse','Bounce-','Complaint-')
            AND em.sent_on >= DATE_SUB(CURDATE(), INTERVAL 7 DAY)
          GROUP BY est.status";
    $email_audit = $this->db->query($query)->result();
    $table = '';
    $table .= '<h3>'.$this->settings->getSetting('school_name').' ('.$from_date.' to '.$to_date.')</h3>';
    if(empty($email_audit)) {
      $table .= '<p>No Issue</p>';
    } else {
      $table .= '<table border="1" cellpadding="6" cellspacing="0">';
      foreach ($email_audit as $key => $val) { 
       $table .= '<tr>
          <td>'.$val->status.'</td>
          <td>'.$val->count.'</td>
        </tr>';
      }
      $table .= '</table><br>';
    }
    echo $table;
  }

  public function autoApproveExceptionFaceAttendance(){
      $curDate = date('Y-m-d');
      $this->load->model('staff/Attendance_model');
      $exceptionAttendanceData = $this->Attendance_model->get_descriptors_for_auto_approval($curDate);

      if(empty($exceptionAttendanceData)){
        echo "No Exception Face Attendance Found for the Date: " . $curDate . "\n";
        die();
      }

      $matchedIdsWithStatus = [];

      foreach ($exceptionAttendanceData as $date => $records) {
          foreach ($records as $id => $record) {
              if (
                  empty($record['exception_descriptors']) ||
                  empty($record['face_descriptors']) ||
                  !is_array($record['face_descriptors'])
              ) {
                  continue;
              }

              $exceptionDescriptors = array_map('floatval', $record['exception_descriptors']);
              $eventType = $record['event_type'] ?? null;
              $matchFound = false;

              foreach ($record['face_descriptors'] as $faceDescriptor) {
                  if (
                      empty($faceDescriptor['descriptor']) ||
                      !is_array($faceDescriptor['descriptor'])
                  ) {
                      continue;
                  }

                  $knownDescriptor = array_map('floatval', $faceDescriptor['descriptor']);
                  $distance = $this->euclideanDistance($exceptionDescriptors, $knownDescriptor);

                  if ($distance <= 0.45) {
                      $matchedIdsWithStatus[] = (object) [
                          'id' => $id,
                          'status' => 4,
                          'type' => $eventType,
                          'descriptor' => $exceptionDescriptors
                      ];
                      $matchFound = true;
                      break;
                  }
              }

              if (!$matchFound) {
                  $matchedIdsWithStatus[] = (object) [
                      'id' => $id,
                      'status' => 0
                  ];
              }
          }
      }
      // Now pass to the model for updating
      $result = $this->Attendance_model->update_status_for_auto_approval($matchedIdsWithStatus);

      if ($result) {
          echo "Updated the Exception Face Attendance Statuses for the Date: $curDate\n";
      } else {
          echo "Failed to update the Exception Face Attendance Statuses for the Date: $curDate\n";
      }
  }

  private function euclideanDistance(array $a, array $b): float {
      $sum = 0.0;
      $count = min(count($a), count($b)); // ensure equal length

      for ($i = 0; $i < $count; $i++) {
          $sum += pow($a[$i] - $b[$i], 2);
      }

      return sqrt($sum);
  }
  
  public function updateGenertaedIdcardPathLink($pdf_id) {
      $this->load->model('idcards/Idcards_model');
      $input = $this->input->post();
      $this->Idcards_model->updateGenertaedIdcardPathLink($input['path'], $input['status'],$pdf_id);
  }

  public function getStaffAnniversaryData(){
    $this->load->model('Email_model');
    $email_template = $this->Email_model->getStaffAnniversaryEmailTemplate();
    if(empty($email_template)){
      echo "<pre>";
      print_r('No Email Template Found For Weekly Staff Anniversary Data');
      return;
    } else if(!empty($email_template) && empty($email_template->members_email)){
      echo "<pre>";
      print_r('No Members Found In Email Template For Weekly Staff Anniversary Data');
      return;
    } else {
      echo "<pre>";
      print_r('Templete Found For Weekly Staff Anniversary Data');
      $this->load->model('Birthday_Notifications_Model');
      $members_data = $this->Birthday_Notifications_Model->membersDataForBirthdayInfo($email_template->members_email);
      if(empty($members_data)){
        echo "<pre>";
        print_r("Members Data Not Found. Member Email(s) Must Belong To Staff Listed In The ERP.");
        return;
      }
      echo "<pre>";
      print_r('Members Data Found');
      $memberEmail = [];
      $email_data = [];
      foreach ($members_data as $key => $val) {
          if(empty($val->stf_email))
              continue;
          if(!empty($val->stf_email))
              $memberEmail[] = $val->stf_email;

          $email_obj = new stdClass();
          $email_obj->stakeholder_id = $val->staff_id;
          $email_obj->avatar_type = $val->avatar_type;
          $email_obj->email = $val->stf_email;
          $email_data[] = $email_obj;
      }
      $senderList = implode(',',$memberEmail);
      $acad_year_id = $this->settings->getSetting('academic_year_id');
      $this->load->model('Dashboard_model');
      $anniversary = $this->Dashboard_model->get_staff_completed_years();
      echo "<pre>";
      print_r('Weekly Staff Anniversary Data Collected');
      $start_date = $anniversary['week_start'];
      $end_date = $anniversary['week_end'];
      $table_html = '';
      if (!empty($anniversary['anniversary_data'])) {
          $table_html .= "<p>Please find below the list of staff members who have completed their 1st, 5th, or 10th year of service during the past week, from <strong>$start_date</strong> to <strong>$end_date</strong>.</p>";
          $table_html .= 'Here is the detailed information:<br>';
          $table_html .= '<table border="1" cellpadding="8" cellspacing="0" style="border-collapse:collapse;width:100%;">';
          $table_html .= '<thead>
              <tr style="background-color:#f2f2f2;">
                  <th>#</th>
                  <th>Staff Name</th>
                  <th>Employee Code</th>
                  <th>Joining Date</th>
                  <th>Completed On Date</th>
                  <th>Years Completed</th>
              </tr>
          </thead><tbody>';

          foreach ($anniversary['anniversary_data'] as $index => $staff) {
              $table_html .= '<tr>
                  <td>' . ($index + 1) . '</td>
                  <td>' . htmlspecialchars($staff->staff_name) . '</td>
                  <td>' . htmlspecialchars($staff->employee_code) . '</td>
                  <td>' . date('d-m-Y', strtotime($staff->joining_date)) . '</td>
                  <td>' . date('d-m-Y', strtotime($staff->completed_on_date)) . '</td>
                  <td>' . $staff->years_completed . '</td>
              </tr>';
          }

          $table_html .= '</tbody></table><br>';
          $table_html .= 'Please review their details accordingly.';
      } else {
          $table_html = "No staff anniversaries found during the past week (<b>$start_date</b> to <b>$end_date</b>).";
      }
      $replaceAnniversaryData = str_replace('%%anniversary_data%%', $table_html, $email_template->content);
      $email_master_data = array(
        'subject' => $email_template->email_subject,
        'body' => $replaceAnniversaryData,
        'source' => 'Weekly Staff Anniversary Data',
        'sent_by' => 0,
        'recievers' => "Staff",
        'from_email' => $email_template->registered_email,
        'files' => NULL,
        'acad_year_id' => $acad_year_id,
        'visible' => 1,
        'sender_list'=>implode(',',$memberEmail),
        'sending_status' => 'Completed'
      );
      $this->load->model('communication/emails_model');
      echo "<pre>";
      print_r('Storing Email Data');
      $email_master_id = $this->emails_model->saveEmail($email_master_data);
      $this->emails_model->save_sending_email_data($email_data, $email_master_id);
      $this->load->helper('email_helper');
      echo "<pre>";
      print_r('Email Sent!');
      sendEmail($replaceAnniversaryData, $email_template->email_subject, $email_master_id, $memberEmail, $email_template->registered_email, []);
    }
  }

  public function exit_staff(){
    // fetching the staffs whos last working date is crossed
    $sql="select id from staff_master where last_date_of_work <=current_timestamp() and status=2";
    $ids = $this->db->query($sql)->result();
    $staff_ids = array_map(function($obj) { return $obj->id; }, $ids);

    if(! empty ($staff_ids)){
        // Start DB transaction
        $this->db->trans_start();

        $result = $this->exit_staff_document_update($staff_ids);
        if ($result) {
            $this->delete_manage_group_assigned_staff($staff_ids);
            $this->store_staff_edit_history($staff_ids);
        }

        // Complete DB transaction
        $this->db->trans_complete();

        if ($this->db->trans_status() === FALSE || !$result) {
            // Transaction failed
            echo 0;
        } else {
            // All good
            echo 1;
        }
    } else {
        echo 0;
    }
  }

  private function store_staff_edit_history($staff_ids) {
    $staff_termination_old_data = $this->get_staff_termination_old_data($staff_ids);
    $insertData = [];

    if (!empty($staff_termination_old_data)) {
        $current_date = date('d-m-Y', strtotime($this->Kolkata_datetime()));
        $edited_on = $this->Kolkata_datetime();

        foreach ($staff_termination_old_data as $item) {
            $id = $item->id;
            unset($item->id);

            $old_data = json_encode($item);

            $new_data_array = [
                'staff_exit_date' => $current_date,
                'status' => "4"
            ];

            $insertData[] = [
                'staff_id' => $id,
                'old_data' => $old_data,
                'new_data' => json_encode($new_data_array),
                'edited_by' => "0",
                'edited_on' => $edited_on
            ];
        }

        if (!empty($insertData)) {
            $this->db->insert_batch('staff_edit_history', $insertData);
        }
    }
  }


  public function get_staff_termination_old_data($staff_ids){
    return $this->db->select("id,ifnull(last_date_of_work,'') as last_date_of_work,ifnull(resignation_date,'') as resignation_date,ifnull(exit_remarks,'') as exit_remarks,ifnull(resignation_letter_doc,'') as resignation_letter_doc,ifnull(exit_update_on,'') as exit_update_on,ifnull(exit_updated_by,'') as exit_updated_by")->from('staff_master')->where_in('id',$staff_ids)->get()->result();
  }

  private function exit_staff_document_update($staff_id) {
    $user_names= array();
    
    // fetching user_id of the staffs
    $user_id= $this->db->select("user_id")
      ->where_in('stakeholder_id', $staff_id)
      ->where('avatar_type', 4)
      ->get('avatar')->result();
      if(! empty ($user_id)) {
        $user_id_array = array_map(function($obj) { return $obj->user_id; }, $user_id);
        $user_names= $this->db->select('username')
        ->where_in('id', $user_id_array)
        ->get('users')->result();
        $user_names_array = array_map(function($obj) { return $obj->username; }, $user_names);
        
    } else {
      return 0;
    }

    // getting the unique_id of the ci_session of the staffs
    if (!empty($user_names_array)) {
      $conditions = [];
      foreach ($user_names_array as $user) {
          $conditions[] = "data LIKE '%" . $this->db->escape_like_str($user) . "%'";
      }
      
      $whereClause = implode(' OR ', $conditions);
      $sql = "SELECT id FROM ci_sessions cs WHERE $whereClause";
      
      $query = $this->db->query($sql);
    }

    $sessionIds = [];
    foreach ($query->result() as $key => $val) {
      array_push($sessionIds, $val->id);
    }
    $this->db->trans_start();
    
    // delete session of the staffs 
    if (!empty($sessionIds)) {
      // $this->db->where_in('id',$sessionIds);
      // $this->db->delete('ci_sessions');
    }

    // update users table of the staffs 
    if (!empty($user_id)) {
      // Extract user IDs from the array of objects
      $user_ids = array_map(function($obj) {
          return $obj->user_id;
      }, $user_id);
  
      // Perform the update
      $this->db->where_in('id', $user_ids);
      $this->db->update('users', array('token' => '', 'active' => 0));

    }
    
    // update status of the staff 
    $data = [
      'status' => 4,
      'exit_update_on' => $this->Kolkata_datetime()
    ];
    $this->db->where_in('id', $staff_id);
    $this->db->update('staff_master', $data);

    $this->db->trans_complete();

    return $this->db->trans_status() ? true : 0;
  }

  private function delete_manage_group_assigned_staff($staff_ids) {
    $group_json = $this->db->select('*')->get('texting_groups')->result();
    $updateArry = [];
    if(!empty($group_json)){
       foreach ($group_json as $val) {
        $group_data = [
            'students' => [],
            'class_section' => [],
            'staff' => []
        ];

        $jsonDecode = json_decode($val->group_json);
        $group_data['students'] = $jsonDecode->students;
        $group_data['class_section'] = $jsonDecode->class_section;
        $staff_ids_in_group = $jsonDecode->staff;

        foreach ($staff_ids_in_group as $staffId) {
            if (!in_array($staffId, (array) $staff_ids)) {
                $group_data['staff'][] = $staffId;
            }
        }

        $groupJson = json_encode($group_data);
        $updateArry[] = [
            'id' => $val->id,
            'group_json' => $groupJson
        ];
      }
    }
   
    if(!empty($updateArry)){
      $this->db->update_batch('texting_groups', $updateArry, 'id');
      return 1;
    }
    return 0;
  
  }

}


?>