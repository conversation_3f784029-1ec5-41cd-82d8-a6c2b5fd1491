<ul class="breadcrumb">
    <li><a href="<?php echo site_url('dashboard') ?>">Dashboard</a></li>
    <li><a href="<?php echo site_url('examination/Assessments/index');?>">Examination</a></li>
    <li><a href="<?php echo site_url('examination/Assessments/showAssessments/') . $classId;?>">Assessments</a></li>
    <li> Details</li>
</ul>

<hr>

<div class="col-md-12">
	<div class="card cd_border">
		<div class="card-header panel_heading_new_style_staff_border">
			<div class="row" style="margin: 0px;">
				<div class="d-flex justify-content-between" style="width:100%;">
					<h3 class="card-title panel_title_new_style_staff">
						<a class="back_anchor" href="<?php echo site_url('examination/Assessments/showAssessments/') . $classId;?>">
							<span class="fa fa-arrow-left"></span>
						</a> 
						Assessment Details for Grade <b><?= $className ?> : <?= $assObj->long_name ?></b>
					</h3>   
				</div>
			</div>
		</div>
        <div class="card-body">

            <div class="col-md-4">
                <a href="<?php echo site_url('examination/Assessments/editAssessment/'.$assObj->id.'/'.$classId) ?>">
                    <div class="widget widget-default widget-item-icon new_height animate__animated animate__fadeIn"> 
                        <div class="widget-item-left" style="width:52px;">
                            <span class="animate__animated animate__fadeIn">
                                <?php $this->load->view('svg_icons/edit.svg') ?>
                            </span>
                        </div>
                        <div class="widget-data" style="padding-left:78px;">
                            <div class="widget-title">Edit Assessment</div>
                            <div class="widget-sub-title">Change assessment details</div>
                        </div>
                    </div>
                </a>
            </div>

            <div class="col-md-4">
                <a href="#" onclick="checkIsFirst('<?= $assObj->subAdded ?>', '<?= $assObj->id ?>', '<?= $classId ?>')">
                    <div class="widget widget-default widget-item-icon new_height animate__animated animate__fadeIn"> 
                        <div class="widget-item-left" style="width:52px;">
                            <span class="animate__animated animate__fadeIn">
                                <?php $this->load->view('svg_icons/books.svg') ?>
                            </span>
                        </div>
                        <div class="widget-data" style="padding-left:78px;">
                            <div class="widget-title">Subjects</div>
                            <div class="widget-sub-title">Add/Remove subjects for this assessment</div>
                        </div>
                    </div>
                </a>
            </div>

            <?php 
                $disableSub = '';
                if($assObj->publish_status == 'Published'){
                    $status = 'Published';
                    $change = "Not Published";
                    $tag = "Un-Publish";
                    // $disableSub = ' disabled';
                } else {
                    $status = 'Not Published';
                    $change = "Published";
                    $tag = "Publish";
                }
                if($assObj->show_marks_to_parents == '1'){
                    $parent_status = 'Published';
                    $change_mark_status = "0";
                    $parent_tag = "Un-Publish";
                } else {
                    $parent_status = 'Not Published';
                    $change_mark_status = "1";
                    $parent_tag = "Publish";
                }
                $disable=' disabled';
                $disableP='cursor: pointer;';
                if($assObj->subAdded == '') {
                    $disable= '';
                    $change='';
                }
            ?>

            <?php //if ($permitReleaseToMarks) { ?>
                <!-- <div class="col-md-4">
                    <?php //$tag = $tag . ' PORTIONS'; ?>
                    <a href="#" id="publishBtn" data-placement='top' data-toggle="modal" data-target="#view-modal" data-id="<?php //echo $assObj->id.'_'.$classId.'_'.$change?>" data-original-title='<?php // echo $tag ?>'>
                        <div class="widget widget-default widget-item-icon new_height animate__animated animate__fadeIn"> 
                            <div class="widget-item-left" style="width:52px;">
                                <span class="animate__animated animate__fadeIn">
                                    <?php //$this->load->view('svg_icons/circular.svg') ?>
                                </span>
                            </div>
                            <div class="widget-data" style="padding-left:78px;">
                                <div class="widget-title"><?php //echo $tag ?></div>
                                <div class="widget-sub-title"><strong>Current Status: <?php // echo $status ?></strong></div>
                            </div>
                        </div>
                    </a>
                </div> -->
            <?php // } ?>

            <?php if ($permitPublishMarksToParents) { ?>
                <div class="col-md-4">
                    <?php $parent_tag = $parent_tag . ' MARKS'; ?>
                    <a href="#" onclick="choose_subjects_to_show_to_parents('<?php echo $assObj->id; ?>', '<?php echo $classId; ?>')">
                        <div class="widget widget-default widget-item-icon new_height animate__animated animate__fadeIn"> 
                            <div class="widget-item-left" style="width:52px;">
                                <span class="animate__animated animate__fadeIn">
                                    <?php $this->load->view('svg_icons/circular.svg') ?>
                                </span>
                            </div>
                            <div class="widget-data" style="padding-left:78px;">
                                <div class="widget-title"><?= $parent_tag ?></div>
                                <div class="widget-sub-title"><strong>Current Status: <?= $parent_status ?></strong></div>
                            </div>
                        </div>
                    </a>
                </div>
            <?php } ?>

            <?php if ($permitAccessControl) : ?>
                <div class="col-md-4">
                    <a href="<?php echo site_url('examination/Assessments/addPermissions/' . $assObj->id . '/' . $classId) ?>">
                        <div class="widget widget-default widget-item-icon new_height animate__animated animate__fadeIn"> 
                            <div class="widget-item-left" style="width:52px;">
                                <span class="animate__animated animate__fadeIn">
                                    <?php $this->load->view('svg_icons/aggregatereport.svg') ?>
                                </span>
                            </div>
                            <div class="widget-data" style="padding-left:78px;">
                                <div class="widget-title">Access Control</div>
                                <div class="widget-sub-title">Provide Access control to staff for entering marks</div>
                            </div>
                        </div>
                    </a>
                </div>

            <?php endif ?>

            <?php  if ($permitReleaseToMarks && ($assObj->marks_release_type == 'manual' || $assObj->marks_release_type == '')) { 
                // echo '<pre>'.$permitReleaseToMarks; die();
                $state = 1;
                $title = 'Release for marks entry';
                $releaseStatus = "Not Released";
                if($assObj->release_marks == 1) {
                    $state=0;
                    $title = 'Close marks entry';
                    $releaseStatus = "Released";
                }
                ?>

                <div class="col-md-4">
                    <a href="#" onclick="releaseMarks(<?= $state ?>, <?= $assObj->id ?>)" id="release_marks_anchor_id">
                        <div class="widget widget-default widget-item-icon new_height animate__animated animate__fadeIn"> 
                            <div class="widget-item-left" style="width:52px;">
                                <span class="animate__animated animate__fadeIn">
                                    <?php $this->load->view('svg_icons/provisionstaff.svg') ?>
                                </span>
                            </div>
                            <div class="widget-data" style="padding-left:78px;">
                                <div class="widget-title"><?= $title ?></div>
                                <div class="widget-sub-title"><strong>Current Status: <?= $releaseStatus ?> </strong></div>
                            </div>
                        </div>
                    </a>
                </div>
            <?php } // echo '<pre>'; print_r($assObj); die(); ?>




            <?php $need_to_update_marks_entry_status= 0; if ($permitReleaseToMarks && $assObj->marks_release_type == 'auto') {
                
                // if(isset($assObj->marks_entry_open_date) && $assObj->marks_entry_open_date != '1970-01-01' && isset($assObj->marks_entry_close_date) && $assObj->marks_entry_close_date != '1970-01-01') {
                //     $open= date('d-m-Y', strtotime($assObj->marks_entry_open_date));
                //     $close= date('d-m-Y', strtotime($assObj->marks_entry_close_date));
                //     if(date('d-m-Y') >= $open && date('d-m-Y') <= $close && $assObj->release_marks != 1) {
                //         $need_to_update_marks_entry_status= 1;
                //         echo "<button type='button' id='release_marks_anchor_id_2' style='display: none;' onclick='releaseMarks_2(1, $assObj->id)'></button type='button'>";
                //     }
                // }
                // $ = 1;
                $title = 'Marks Entry Settings';
                $open_date = "Not Set";
                $close_date = "Not Set";
                if($assObj->marks_entry_open_date) {
                    $open_date = $assObj->marks_entry_open_date_f1;
                }
                if($assObj->marks_entry_close_date) {
                    $close_date= $assObj->marks_entry_close_date_f1;
                }
                ?>

                <div class="col-md-4">
                    <a href="#" onclick="update_marks_entry_settings('<?= $assObj->marks_entry_open_date_f1; ?>', '<?= $assObj->marks_entry_close_date_f1; ?>', <?= $assObj->id ?>)">
                        <div class="widget widget-default widget-item-icon new_height animate__animated animate__fadeIn"> 
                            <div class="widget-item-left" style="width:52px;">
                                <span class="animate__animated animate__fadeIn">
                                    <?php $this->load->view('svg_icons/provisionstaff.svg') ?>
                                </span>
                            </div>
                            <div class="widget-data" style="padding-left:78px;">
                                <div class="widget-title"><?= $title ?></div>
                                <div class="widget-sub-title"><strong>Open Date:</strong> <?= $assObj->marks_entry_open_date_f1; ?>, <strong>Close Date:</strong> <?= $assObj->marks_entry_close_date_f1; ?></div>
                            </div>
                        </div>
                    </a>
                </div>
            <?php } ?>






            <div class="col-md-4">
                <a href="<?php echo site_url('examination/Assessments/getPortions/' . $assObj->id . '/' . $classId) ?>">
                    <div class="widget widget-default widget-item-icon new_height animate__animated animate__fadeIn"> 
                        <div class="widget-item-left" style="width:52px;">
                            <span class="animate__animated animate__fadeIn">
                                <?php $this->load->view('svg_icons/homework.svg') ?>
                            </span>
                        </div>
                        <div class="widget-data" style="padding-left:78px;">
                            <div class="widget-title">Portions</div>
                            <div class="widget-sub-title">Add/Edit portions for this assessment</div>
                        </div>
                    </div>
                </a>
            </div>

            <?php if ($this->authorization->isSuperAdmin()) { ?>
                <div class="col-md-4">
                    <a href="<?php echo site_url('examination/Assessments/hallticket/' . $assObj->id . '/' . $classId) ?>">
                        <div class="widget widget-default widget-item-icon new_height animate__animated animate__fadeIn"> 
                            <div class="widget-item-left" style="width:52px;">
                                <span class="animate__animated animate__fadeIn">
                                    <?php $this->load->view('svg_icons/homeworkreports.svg') ?>
                                </span>
                            </div>
                            <div class="widget-data" style="padding-left:78px;">
                                <div class="widget-title">Hallticket Template</div>
                                <div class="widget-sub-title">Add/Edit hallticket template for this assessment</div>
                            </div>
                        </div>
                    </a>
                </div>                
            <?php } ?>

            <div class="col-md-4">
                <a href="<?php echo site_url('examination/Assessments/halltickets/' . $assObj->id . '/' . $classId) ?>">
                    <div class="widget widget-default widget-item-icon new_height animate__animated animate__fadeIn"> 
                        <div class="widget-item-left" style="width:52px;">
                            <span class="animate__animated animate__fadeIn">
                                <?php $this->load->view('svg_icons/attendance.svg') ?>
                            </span>
                        </div>
                        <div class="widget-data" style="padding-left:78px;">
                            <div class="widget-title">Halltickets</div>
                            <div class="widget-sub-title">Student Halltickets</div>
                        </div>
                    </div>
                </a>
            </div>   

        </div>

	</div>
</div>


<div id="view-modal" class="modal fade" tabindex="-1" role="dialog" aria-labelledby="myModalLabel" aria-hidden="true" style="display: none;">
  <div class="modal-dialog" style="width:70%;margin: auto"> 
     <div class="modal-content">  
   
        <div class="modal-header"> 
           <h4 class="modal-title"> Assessment timetable </h4> 
           <button type="button" class="close" data-dismiss="modal" aria-hidden="true">×</button> 
        </div> 
            
        <div class="modal-body mobile" style="height:500px;overflow-y: scroll;">
           <div id="modal-loader" style="display: none; text-align: center;">
           <!-- ajax loader -->
           <img src="<?php echo base_url('assets/img/ajax-loader.gif');?>">
           </div>
           <div id="dynamic-content"></div>
        </div> 
        <div class="modal-footer">
            <input type="hidden" name="publishId" id="publishId">
            <button type="button" onclick="publish()" id="confirmP" class="btn btn-primary" data-dismiss="modal">Confirm</button>  
            <button type="button" class="btn btn-danger" data-dismiss="modal" style="margin-bottom: 3px">Close</button>  
        </div> 
                        
    </div> 
  </div>
</div>

<!-- choose subject modal -->
<div class="modal fade" id="resource_uploader" tabindex="-1" role="dialog" style="width:35%;margin:auto;top:auto" data-backdrop="static" aria-labelledby="resource-uploader-label" aria-hidden="true">
    <div class="modal-content modal-dialog" style="border-radius: 8px;">
      <div class="modal-header" style="border-bottom: 2px solid #ccc;">
        <h4 class="modal-title" id="modalHeader">Choose subjects to show marks to parents</h4>
        <button style="font-size: 32px;font-weight: bold;color: #e04b4a;opacity: 1;padding-top: .5rem;" type="button" class="close" data-dismiss="modal">&times;</button>
      </div>
       <form id="myform">
        <div class="modal-body">
            <div class="col-md-12" id="subject_selection_input">
                
            </div>
        </div>
        <div class="modal-footer">
            <button type="button" class="btn btn-danger" onclick="changeParentMarksPublishStatus(0)">Un-Publish Marks</button>
            <button type="button" class="btn btn-primary" onclick="changeParentMarksPublishStatus(1)">Publish Marks</button>
        </div>
        </form>
    </div>
  </div>


<script>
    // anish

    $(document).ready(() => {
        // if('<?php // echo $assObj->marks_release_type; ?>' == 'auto' && '<?php // echo $need_to_update_marks_entry_status; ?>') {
        //     $("#release_marks_anchor_id_2").click();
        // }

        $('#open_date, #close_date').datepicker({
            todayBtn: "linked",
            language: "it",
            autoclose: true,
            todayHighlight: true,
            format: 'dd-mm-yyyy',
            orientation: "top"
            // endDate: "today"
        });
    });

    function changeParentMarksPublishStatus(change_mark_status) {
        var sub_ids_arr_status1= [];
        var sub_ids_arr_status0= [];
        var computed_ids_arr_status1= [];
        var computed_ids_arr_status0= [];
        $(".ass_subjects").each(function() {
            if(this.checked) {
                sub_ids_arr_status1.push($(this).val());
            } else {
                sub_ids_arr_status0.push($(this).val());
            }
        });

        $(".comps").each(function() {
            if(this.checked) {
                computed_ids_arr_status1.push($(this).val());
            } else {
                computed_ids_arr_status0.push($(this).val());
            }
        });

        if(sub_ids_arr_status1.length == 0) {
            sub_ids_arr_status1= [0];
        }
        if(sub_ids_arr_status0 == 0) {
            sub_ids_arr_status0= [0];
        }
        
        var ass_id= $("#sub_ass_id").val();
        var class_id= $("#sub_class_id").val();

        $.ajax({
            url: '<?php echo site_url('examination/Assessments/changeParentMarksPublishStatusSubjectWise'); ?>',
            data: {
                    'ass_id': ass_id,
                    'class_id': class_id,
                    'change_mark_status': change_mark_status,
                    'sub_ids_arr_status1': sub_ids_arr_status1,
                    'sub_ids_arr_status0': sub_ids_arr_status0,
                    'computed_ids_arr_status0': computed_ids_arr_status0,
                    'computed_ids_arr_status1': computed_ids_arr_status1
                  },
            type: "post",
            success: function (data) {
                
                if(data == 1){
                    $(function(){
                        new PNotify({
                            title:'Success',
                            text: 'Successful',
                            type:'success',
                        });
                    });
                }else{
                     $(function(){
                        new PNotify({
                            title:'Error',
                            text: 'Something went wrong',
                            type:'error',
                        });
                    });
                }

                $("#resource_uploader").modal('hide');
                window.location='<?php echo site_url('examination/Assessments/showAssessmentsMorePage/') ?>'+ass_id+'/'+class_id;

               
            },
            error: function (err) {
                console.log(err);
            }
        });
    }

    function select_all_subs(current) {
        if(current.checked) {
            // Iterate each checkbox
            $(':checkbox').each(function() {
                this.checked = true;                        
            });
        } else {
            $(':checkbox').each(function() {
                this.checked = false;                       
            });
        }
    }

    function choose_subjects_to_show_to_parents(ass_id, class_id) {
            $.ajax({
                url: '<?php echo site_url('examination/assessments/get_subjects_assessment_wise'); ?>',
                data: {'ass_id': ass_id,'class_id':class_id},
                type: "post",
                success: function (data) {
                    var p_data = JSON.parse(data);
                    var data= p_data['result'];
                    var computed= p_data['computed'];
                    
                    var subjects= `
                                    <div class="form-group" style="">
                                        <input type="checkbox" id="select_all_sub" name="sub_all" value="all" onclick="select_all_subs(this)">
                                        <label for="sub_all" style="color: green;">Select All</label><br>
                                        <input type="hidden" id="sub_ass_id" value="${ass_id}">
                                        <input type="hidden" id="sub_class_id" value="${class_id}">
                                    </div>
                                `;
                    var checks= 'checked';
                    for(var index in data) {
                        var checks= '';
                        if(data[index].show_marks_to_parents == 1)
                            checks= 'checked';
                        subjects += `
                                        <div class="form-group">
                                            <input class="ass_subjects" type="checkbox" id="sub${data[index].id}" name="sub" value="${data[index].id}" ${checks}>
                                            <label for="sub${data[index].id}">${data[index].name}</label><br>
                                        </div>
                                    `;
                    }
                    for(var index2 in computed) {
                        var checks= '';
                        if(computed[index2].show_result_to_parents == 1)
                            checks= 'checked';
                        subjects += `
                                        <div class="form-group">
                                            <input class="comps" type="checkbox" id="comp${computed[index2].id}" name="comp" value="${computed[index2].id}" ${checks}>
                                            <label style="color: tan;" for="comp${computed[index2].id}">${computed[index2].name}</label><br>
                                        </div>
                                    `;
                    }
                    $("#subject_selection_input").html(subjects);
                },
                error: function (err) {
                    console.log(err);
                },
                complete: function() {
                    $("#resource_uploader").modal('show');
                }
            });
        
    }

    $(document).ready(function(){
        $(document).on('click', '#publishBtn', function(e){
            e.preventDefault();  
            var uid = $(this).data('id'); // get id of clicked row
            var arr = uid.split("_");
            $("#publishId").val(arr[0]+'_'+arr[2]);
            $('#dynamic-content').html(''); // leave this div blank
            $('#modal-loader').show();      // load ajax loader on button click
         
            $.ajax({
              url: "<?php echo site_url('examination/Assessments/publishAssessment');?>",
              data: {'assId': arr[0], 'classId': arr[1]},
              type: 'post',
            }).done(function(data){
                    var data = JSON.parse(data);
                    var empty = data.empty;
                    var data = data.html;
                    $('#dynamic-content').html(''); // blank before load.
                   $('#dynamic-content').html(data); // load here
                   $('#modal-loader').hide(); // hide loader
                   if(empty == 1) $("#confirmP").hide();
             })
             .fail(function(){
                  $('#dynamic-content').html('Something went wrong, Please try again...');
                  $('#modal-loader').hide();
             });
        });
    });

  function checkIsFirst(sub,assId,classId){
    if(sub == ''){
      bootbox.confirm({
        title: "Info",
        message: "Edit assessment will be locked once you add subjects. Do you want to continue?",
        className:'widthadjust',
        buttons: {
          confirm: {
            label: 'Yes',
            className: 'btn-success'
          },
          cancel: {
            label: 'No',
            className: 'btn-danger'
          }
        },
        callback: function (result) {
          if(result) {
            window.location='<?php echo site_url('examination/Assessments/addSubjects/') ?>'+assId+'/'+classId;
          }
        }
      });
    } else {
        window.location='<?php echo site_url('examination/Assessments/addSubjects/') ?>'+assId+'/'+classId;
    }
  }

  function publish(){
      var ids = $("#publishId").val();
      var idStat = ids.split("_");
      $.ajax({
          url: "<?php echo site_url('examination/Assessments/changePublishStatus');?>",
          data: {'id':idStat[0], 'status':idStat[1]},
          type: 'post',
          success: function(data) {
              if(data == -1){
                  bootbox.alert({
                      title: "Publishing Assessment",
                      message: "Date or Portions not added to one or more subjects. Please add before continuing.",
                      className:'widthadjust',
                      buttons: {
                          ok: {
                              label: 'Got it',
                              className: 'btn-success'
                          }
                      }
                  });
              } else if(data){
                  location.reload();
              }
          },
          error: function(err) {
              console.log(err);
          }
      }); 
  }

  function releaseMarks_2(status, assId) {
    $.ajax({
                  url: "<?php echo site_url('examination/Assessments/changeMarksReleaseStatus');?>",
                  data: {'assId':assId, 'status': status},
                  type: 'post',
                  success: function(data) {
                      location.reload();
                  },
                  error: function(err) {
                      console.log(err);
                  }
              });
  }

  function releaseMarks(changeTo, assId) {
      $msg = "You cannot make changes to subjects after releasing. Are you Sure?";
      if(changeTo == 0)
          $msg = "You are closing the marks entry. Are you Sure?";
      bootbox.confirm({
          title: 'Release Assessment For Marks Entry',
        message: $msg,
        className:'widthadjust',
        buttons: {
            confirm: {
                label: 'Yes',
                className: 'btn-success'
            },
            cancel: {
                label: 'No',
                className: 'btn-danger'
            }
        },
        callback: function (result) {
          if(result) {
              $.ajax({
                  url: "<?php echo site_url('examination/Assessments/changeMarksReleaseStatus');?>",
                  data: {'assId':assId, 'status':changeTo},
                  type: 'post',
                  success: function(data) {
                      location.reload();
                  },
                  error: function(err) {
                      console.log(err);
                  }
              });
          }
        }
      });
  }

  let is_auto= false;
  function update_marks_entry_settings(open, close, ass_id) { //mdal need to work
    is_auto= true;
    $("#ass_id1").val(ass_id);
    if(open.toString()) {
        $("#open_date, #open_date_validation").val(open.split(' ')[0]);
        $("#open_date_time, #open_date_time_validation").val(open.split(' ')[1]);
    }
    if(close.toString()) {
        $("#close_date, #close_date_validation").val(close.split(' ')[0]);
        $("#close_date_time, #close_date_time_validation").val(close.split(' ')[1]);
    }

    let prev_open_date= $("#open_date_validation").val();
    let prev_open_time= $("#open_date_time_validation").val();
   
    let prev_open_number= Number(`${prev_open_date.replaceAll('-', '')}${prev_open_time.replaceAll(':', '')}`);
    let now = new Date();
    let now2= moment(now).format('DDMMYYYYHHmm');
    let nowTimeNumber= Number(now2.toString());
    if(is_auto && prev_open_number <= nowTimeNumber) {
        $("input#open_date_time, input#open_date").css({
            'pointer-events': 'none',
            'opacity': '0.7'
        });
    }

    $("#time_boundation_modal").modal('show');
  }

  let show_info_alert= true;
  function show_info() {
    let prev_open_date= $("#open_date_validation").val();
    let prev_open_time= $("#open_date_time_validation").val();
    if(!prev_open_date || !prev_open_time) {
        return;
    }
    let prev_open_number= Number(`${prev_open_date.replaceAll('-', '')}${prev_open_time.replaceAll(':', '')}`);
    let now = new Date();
    let now2= moment(now).format('DDMMYYYYHHmm');
    let nowTimeNumber= Number(now2.toString());
    if(is_auto && prev_open_number <= nowTimeNumber && show_info_alert) {
        // showSweetAlert('Error', 'The open date cannot be edited because the marks entry period has already started.', 'error');

        Swal.fire({
            title: 'Not Editable',
            html: 'The open date cannot be edited because the marks entry period has already started. Are you sure you still want to edit?',
            icon: 'question',
            showCancelButton: true,
            confirmButtonText: 'Yes, let me edit!',
            confirmButtonColor: '#3085d6',
            cancelButtonText: 'No, cancel',
            cancelButtonColor: '#d33',
            reverseButtons: true,
            focusConfirm: false,
            focusCancel: true
            }).then((result) => {
                if (result.isConfirmed) {
                    $("input#open_date_time, input#open_date").css({
                        'pointer-events': 'auto',
                        'opacity': '1'
                    });
                    show_info_alert= false;
                } else {
                    $("input#open_date_time, input#open_date").css({
                        'pointer-events': 'none',
                        'opacity': '0.7'
                    });
                }
            });


    }
  }

  function convertTo24HourFormat(timeString) { 
        const [time, period] = timeString.split(' '); 
        const [hour, minute] = time.split(':'); 
        let formattedHour = parseInt(hour); 
    
        if (period === 'PM') { 
            formattedHour += 12; 
        } 
    
        return `${formattedHour}:${minute}`; 
    } 

  function update_time_boundation() {
    var open= $("#open_date").val();
    var close= $("#close_date").val();
    var ass_id= $("#ass_id1").val();

    var open_date_time= $("#open_date_time").val().toString();
    let time_open= '';
    if(open_date_time) {
        time_open= convertTo24HourFormat(open_date_time);
    }
    var close_date_time= $("#close_date_time").val().toString();
    let time_close= '';
    if(close_date_time) {
        time_close= convertTo24HourFormat(close_date_time);
    }

    let is_date_time_valid= validateMarksReleaseForm();

    if(open && close && is_date_time_valid) {
        $.ajax({
        url: '<?php echo site_url('examination/Assessments/update_time_boundation'); ?>',
        type: "post",
        data: {open, time_open, time_close, close, ass_id},
        success(data) {
            var p_data = JSON.parse(data);
            console.log(p_data);
            window.location.reload();
            
        }
    });
    }
  }

  function validateMarksReleaseForm() {
        // Check if auto release is selected
        if (is_auto) {
            // Validate that dates are provided
            if (!validateDatesProvided()) {
                return false;
            }
            
            // Validate that open date is before close date
            if (!validateOpenBeforeClose()) {
                return false;
            }
        }
        
        return true; // Form is valid
    }

    // Function to validate that dates are provided
   function validateDatesProvided() {
        const openDate = document.getElementById('open_date').value;
        const openTime = document.getElementById('open_date_time').value;
        const closeDate = document.getElementById('close_date').value;
        const closeTime = document.getElementById('close_date_time').value;
        
        if (!openDate || !openTime || !closeDate || !closeTime) {
            showSweetAlert('Error', 'All date and time fields are required for auto release', 'error');
            return false;
        }
        
        return true;
    }

    // Function to validate that open date is before close date
    function validateOpenBeforeClose() {
        const openDateTime = getDateTimeNumber('open_date', 'open_date_time');
        const closeDateTime = getDateTimeNumber('close_date', 'close_date_time');
        
        if (openDateTime >= closeDateTime) {
            showSweetAlert('Error', 'Marks entry open date/time must be before close date/time', 'error');
            return false;
        }
        
        return true;
    }

    // Helper function to combine date and time into a Date object
    function getDateTimeNumber(dateElementId, timeElementId) {
        const dateStr = document.getElementById(dateElementId).value;
        const timeStr = document.getElementById(timeElementId).value;

        if (!dateStr || !timeStr) return null;

        // Parse DD-MM-YYYY format correctly
        const [day, month, year] = dateStr.split('-');
        const formattedDate = `${year}${month.padStart(2, '0')}${day.padStart(2, '0')}`;
        
        // Remove all non-digit characters from time (handles : and any other separators)
        const formattedTime = timeStr.replace(/\D/g, '').padEnd(6, '0');
        
        // Combine and convert to number
        return Number(formattedDate + formattedTime);
    }

    // Function to show SweetAlert messages
    function showSweetAlert(title, text, icon) {
        Swal.fire({
            title: title,
            text: text,
            icon: icon,
            confirmButtonText: 'OK'
        });
    }
    

</script>
<script src="https://cdn.jsdelivr.net/npm/sweetalert2@11"></script>

<div class="modal fade" id="time_boundation_modal" tabindex="-1" role="dialog" style="width:50%;margin:auto;top:0%" data-backdrop="static" aria-labelledby="resource-uploader-label" aria-hidden="true">
    <div class="modal-content modal-dialog" style="border-radius: 8px;">
      <div class="modal-header" style="border-bottom: 2px solid #ccc;">
        <h4 class="modal-title" id="modalHeader">Update Marks Entry Settings</h4>
        <button style="font-size: 32px;font-weight: bold;color: #e04b4a;opacity: 1;padding-top: .5rem;" type="button" class="close" data-dismiss="modal">&times;</button>
      </div>
       <form id="myform">

       <input type="hidden" id="ass_id1">
        <div class="modal-body">
            <div class="col-md-12">
            <div class="form-group">
                    <label for="name" class="col-md-4 col-xs-12 control-label">Marks Entry Open Date<font color="red">*</font></label>
                    <div class="col-md-8 col-xs-12">
                        <div class="input-group" onclick="show_info()" id="open-time-div">
                            <span class="input-group-addon">
                                <span class="fa fa-calendar"></span>
                            </span>
                            <input style="display: none;" type="text" class="form-control" id="open_date_validation" placeholder="DD-MM-YYYY" name="open_date_validation">
                            <input style="display: none;" type="time" id="open_date_time_validation" name="open_date_time_validation" class="form-control col-md-6">

                            <input type="text" class="form-control" id="open_date" placeholder="DD-MM-YYYY" name="open_date" required="">
                            <input type="time" id="open_date_time" name="open_date_time" class="form-control col-md-6" required>
                        </div>
                        <div class="help-block">Enter marks entry open date</div>
                    </div>
                </div>

                <div class="form-group">
                    <label for="name" class="col-md-4 col-xs-12 control-label">Marks Entry Close Date<font color="red">*</font></label>
                    <div class="col-md-8 col-xs-12">
                        <div class="input-group">
                            <span class="input-group-addon">
                                <span class="fa fa-calendar"></span>
                            </span>
                            <input style="display: none;" type="text" class="form-control" id="close_date_validation" placeholder="DD-MM-YYYY" name="close_date_validation">
                            <input style="display: none;" type="time" name="close_date_time_validation" id="close_date_time_validation" class="form-control col-md-6">

                            <input type="text" class="form-control" id="close_date" placeholder="DD-MM-YYYY" name="close_date" required="">
                            <input type="time" name="close_date_time" id="close_date_time" class="form-control col-md-6" required>
                        </div>
                        <div class="help-block">Enter marks entry close date</div>
                    </div>
                </div>
            </div>
        </div>
        <div class="modal-footer">
            <!-- <div> <input type="reset" value="Clear Form" ></div> -->
            <button type="button" class="btn btn-primary" onclick="update_time_boundation()">Submit</button>
        </div>
        </form>
    </div>
  </div>

<style type="text/css">
  ul.panel-controls>li>a {
    border-radius: 50%;
}
.widthadjust{
  width:600px;
  margin:auto;
  }
</style>
