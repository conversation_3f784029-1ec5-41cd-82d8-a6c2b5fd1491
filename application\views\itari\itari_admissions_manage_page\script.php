<script src="https://cdnjs.cloudflare.com/ajax/libs/html2canvas/1.4.0/html2canvas.min.js"></script>
<script src="https://cdn.jsdelivr.net/npm/jspdf@2.4.0/dist/jspdf.umd.min.js"></script>
<script src="https://cdnjs.cloudflare.com/ajax/libs/html2pdf.js/0.10.1/html2pdf.bundle.min.js"></script>

<script>    

    $('#generate').on('click',function(){
        get_itari_enquiry_data_based_on_forms();
        document.getElementById("action-btn").style.display = "block";
        $('#message').hide();
    });

    function get_itari_enquiry_data_based_on_forms() {
        var range = $('#daterange').val();
        if(range == 7){
            var createdfrom_date = $("#created_from_date").val();
            var createdto_date = $('#created_to_date').val();
        }else{
            var range = $('#daterange').val().split('_');
            var createdfrom_date = range[0];
            var createdto_date = range[1];
        }
        var range1 = $('#daterange1').val();
        if(range1 == 7){
            var followupfrom_date = $("#followup_from_date").val();
            var followupto_date = $('#followup_to_date').val();
        }else{
            var range = $('#daterange1').val().split('_');
            var followupfrom_date = range[0];
            var followupto_date = range[1];
        }
        var follow_up_status = $('#follow_up_status').val();
        if (follow_up_status == null) {
            follow_up_status = '';
        }
        var fee_status = $('#fee_status').val();
        if (fee_status == null) {
            fee_status = '';
        }
        var grade = $('#grade').val();
        if (grade == null) {
            grade = '';
        }
        $.ajax({
            url: '<?= site_url('itari/Itari_Controller/get_itari_enquiry_data_based_on_forms'); ?>',
            type: 'POST',
            data: {'createdfrom_date': createdfrom_date,'createdto_date':createdto_date,'followupfrom_date':followupfrom_date,'followupto_date':followupto_date,'follow_up_status':follow_up_status,'grade':grade, 'fee_status':fee_status},
            success: function (data) {
                var data1 = $.parseJSON(data);
                $('.list-group-enquiry').html(construct_enquiry_data(data1)); 
                // $('#studentEnquiryDetails').html(construct_enquiry_template(data[0]));
                $(".datatable").DataTable({
                    order: [],
                    "dom": '<"wrapper"flipt>'
                });
                onclickgetEnquiryId();
                $('#followForm').css('display', 'none');
                // $('#feesForm').css('display', 'none');
            },
            error: function (err) {
                console.log(err);
            }
        });
    }

    function onclickgetEnquiryId() {
        var enquiryId = $('a.list-group-item.active').attr('id');
        if (typeof enquiryId === 'undefined') {
            $('.details-tab').css('display', 'none');
            $('.message').css('display', '');
            $('.message').html('<div class="no-data-display">No Data Found</div>');
            $('.list-group-enquiry').css('display', 'none');
            $('#detailsForm').hide();
            $('#followForm').hide();
            return false;
        }
        else{
            $('.message').css('display', 'none');
            $('.details-tab').css('display', '');
            $('.list-group-enquiry').css('display', '');
            $('#detailsForm').show();
            onclickgetId(enquiryId);
        }
        // $('#enquiry_id').val(enquiryId);
    }

    function changeDateRange(){
        var range = $('#daterange').val();
        if(range == 7)
            $("#custom_range").show();
        else
            $("#custom_range").hide();
    }
    
    function formatDate (dateObj) {
        if (!dateObj || dateObj == '00-00-0000') {
            return '-';
        }
        const day = dateObj.getDate().toString().padStart(2, '0');
        const month = (dateObj.getMonth() + 1).toString().padStart(2, '0');
        const year = dateObj.getFullYear();
        return `${day}-${month}-${year}`;
    }
    function construct_enquiry_template(data) {
        function addRowIfExists(label, value, id = '') {
            if (value) {
                return `<tr><td><strong>${label}</strong></td><td class="${id}">${value}</td></tr>`;
            }
            return '';
        }

        function formatDate(dateObj) {
            if (!dateObj || dateObj == '00-00-0000') {
                return '-';
            }
            const date = new Date(dateObj);
            const day = date.getDate();
            const month = date.getMonth() + 1;
            const year = date.getFullYear();
            return `${day.toString().padStart(2, '0')}-${month.toString().padStart(2, '0')}-${year}`;
        }

        let std = '';

        let status = '';
        switch (data.admission_status) {
            case "admit": status = "Admitted"; break;
            case "submitted": status = "Application Submitted, Fees not Paid"; break;
            case "draft": status = "Draft"; break;
            case "Application Fees Paid": status = "Application Submitted, Fees Paid to ITARI."; break;
            case "Moved To ERP": status = "Moved To ERP."; break;
            case "closed": status = "Application Closed"; break;
            case "pending": status = "Application Pending"; break;
        }

        std += '<h3 style="color:#04327F"><b>Current Status</b>: ' + status;
        if (data.admission_status === "Moved To ERP") {
            std += ' - <a id="student_url" href="<?php echo site_url("student/Student_controller/addMoreStudentInfo/"); ?>' + data.id + '" target="_blank" style="color: #04327F; text-decoration: underline;">Click Here to view Student</a>';
        }
        std += '</h3><br>';

        std += '<div style="text-align: center; margin-bottom: 20px;">';
        if (data.photo) {
            std += '<img src="' + data.photo + '" style="width: 20%;">';
        }
        std += '</div>';

        std += '<h3>Personal Details</h3>';
        std += '<table class="table table-bordered">';
        std += addRowIfExists('Student name', data.name, 'student_first_name');
        std += addRowIfExists('Gender', data.gender, 'gender');
        std += addRowIfExists('Date of Birth', data.dob ? formatDate(data.dob) : '-', 'dob');
        std += addRowIfExists('Mobile Number', data.phone_num, 'mobile_number');
        std += addRowIfExists('Email', data.email, 'email');

        let addressParts = [];
        if (data.permanent_address) addressParts.push(data.permanent_address);
        if (data.permanent_city) addressParts.push(data.permanent_city);
        if (data.permanent_state) addressParts.push(data.permanent_state);
        if (data.permanent_country) addressParts.push(data.permanent_country);
        let address = addressParts.join(', ');
        if (data.permanent_pincode) address += ' - ' + data.permanent_pincode;
        if (address.trim()) {
            std += addRowIfExists('Address', address, 'address');
        }

        if (data.course) {
            std += addRowIfExists('Program', data.course.toUpperCase(), 'grade');
        }

        let mode_of_study = 'Not Filled';
        if (data.mode_of_study === 1) mode_of_study = "In-Person";
        else if (data.mode_of_study === 2) mode_of_study = "Blended";
        else if (data.mode_of_study === 3) mode_of_study = "Hybrid";
        else if (data.mode_of_study === 4) mode_of_study = "Online";
        std += addRowIfExists('Mode of Study', mode_of_study, 'mode_of_study');

        if (data.criminal_reason) {
            std += addRowIfExists('Criminal Reason', data.criminal_reason, 'criminal_reason');
        }
        std += '</table>';

        std += '<h3>Academic Details</h3>';
        if (!data.academic || data.academic.length === 0) {
            std += '<h5 class="no-data-display">No data</h5>';
        } else {
            data.academic.forEach(acad => {
                std += '<table class="table table-bordered">';
                std += addRowIfExists('Degree name', acad.degree_name);
                std += addRowIfExists('College Name', acad.college_name);
                std += addRowIfExists('University Name', acad.university_name);
                if (acad.start_date && acad.end_date) {
                    const formattedstartDate = formatDate(acad.start_date);
                    const formattedendDate = formatDate(acad.end_date);
                    std += addRowIfExists('Period', `From: ${formattedstartDate} &nbsp;&nbsp; To: ${formattedendDate}`);
                }
                std += addRowIfExists('Subjects', acad.subjects);
                std += addRowIfExists('GPA/Percentage', acad.gpa);
                std += '</table>';
            });
        }

        std += '<h3>Employment Details</h3>';
        if (!data.employment || data.employment.length === 0) {
            std += '<h5 class="no-data-display">No data</h5>';
        } else {
            data.employment.forEach(emp => {
                std += '<table class="table table-bordered">';
                std += addRowIfExists('Experience', emp.experience);
                std += addRowIfExists('Name of Institution', emp.name_of_instituition);
                if (emp.start_date) {
                    const formattedStartDate = formatDate(emp.start_date);
                    let formattedEndDate = 'Present';
                    if (emp.end_date && emp.end_date !== '0000-00-00') {
                        formattedEndDate = formatDate(emp.end_date);
                    }
                    std += addRowIfExists('Date', `From: ${formattedStartDate} &nbsp;&nbsp; To: ${formattedEndDate}`);
                }
                std += addRowIfExists('Roles and Responsibility', emp.roles_resp);
                std += addRowIfExists('Address', emp.address);
                let type = emp.type;
                if (type === "cur") type = "Current Employer";
                else if (type === "pre") type = "Previous Employer";
                std += addRowIfExists('Type', type);
                std += '</table>';
            });
        }

        std += '<h3>References Details</h3>';
        if (!data.reference || data.reference.length === 0) {
            std += '<h5 class="no-data-display">No data</h5>';
        } else {
            data.reference.forEach(ref => {
                std += '<table class="table table-bordered">';
                std += addRowIfExists('Name', ref.name);
                std += addRowIfExists('Phone Number', ref.phone_num);
                std += addRowIfExists('Email', ref.email);
                std += addRowIfExists('Capacity', ref.capacity);
                std += '</table>';
            });
        }

        if (data.essay) {
            std += '<h3>Essay</h3>';
            std += '<a href="' + data.essay + '" target="_blank" class="btn btn-secondary" id="essay"> View <i class="fa fa-eye"></i></a>';
        }

        // Follow-up Details
        const follow_up_table = document.querySelector("#follow_up");
        let ftd = '<h3>Follow Up Details</h3>';
        ftd += '<table class="table table-bordered">';
        ftd += addRowIfExists('Student name', data.name, 'student_first_name');
        ftd += addRowIfExists('Mode of Study', mode_of_study, 'mode_of_study');
        if (data.created_date) {
            ftd += addRowIfExists('Date', data.created_date, 'created_date');
        }
        let followStatus = data.admission_status;
        if (followStatus === "pending") followStatus = "Follow Up Pending";
        else if (followStatus === "admit") followStatus = "Admitted";
        else if (followStatus === "closed") followStatus = "Closed";
        ftd += addRowIfExists('Status', followStatus, 'status');
        ftd += '</table>';
        follow_up_table.innerHTML = ftd;

        return std;
    }

    function construct_enquiry_data(enquirydata) {
        var status;
        var output='';
        var m=1;
        output += '<table class="table table-bordered datatable">';
        output +='<thead>';
        output += '<tr>';
        output += '<th>Enquiry Details</th>';
        output += '</tr>';
        output +='</thead>';
        output +='<tbody>';
        for(var i = 0; i < enquirydata.length; i++){
            if(enquirydata[i].admission_status == "submitted" ){
                status ="Application Submitted, Fees not Paid";
            }
            if(enquirydata[i].admission_status == "admit" ){
                status ="Admitted";
            }
            else if(enquirydata[i].admission_status == "draft"){
                status = "Draft";
            }
            else if(enquirydata[i].admission_status == "Application Fees Paid"){
                status = "Application Submitted, Fees Paid to ITARI.";
            }
            else if(enquirydata[i].admission_status == "closed"){
                status = 'Application Closed';
            }
            else if(enquirydata[i].admission_status == "pending"){
                status = 'Application Pending';
            }
            else if(enquirydata[i].admission_status == "Moved To ERP"){
                status = 'Moved To ERP';
            }
            if (m == 1) {
            var active = 'active';
            }else{
                var active = '';
            }
            var createdDate = enquirydata[i].created_on;
            var nextFollowupdate = enquirydata[i].next_follow_date;
            if (enquirydata[i].next_follow_date == null) {
                nextFollowupdate = 'No Follow-up date'
            }
            output += '<tr>';
            output += '<td><a id="' + enquirydata[i].id + '" onclick="onclickgetId(' + enquirydata[i].id + ')" class="list-group-item ' + active + '">\
                    <span style="font-weight: bold;">' + enquirydata[i].name + '</span>\
                    <span style="float: right; font-weight:bold">Initiation Date: ' + enquirydata[i].created_on + '</span>\
                    <p>\
                        <span><b>Mobile No: </b> ' + enquirydata[i].phone_num + '</span>' + 
                        (
                            enquirydata[i].fee_paid_date 
                            ? '<span style="float: right; font-weight:bold">Payment Date: ' + enquirydata[i].fee_paid_date + '</span>' 
                            : enquirydata[i].fees_collected_on 
                            ? '<span style="float: right; font-weight:bold">Payment Date: ' + enquirydata[i].fees_collected_on + '</span>'
                            : '<span style="float: right; font-weight:bold">Payment Date: - &nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;</span>'
                        ) + 
                    '</p>\
                    <p>\
                        <span><b>Status: </b>' + status + '</span>\
                    </p>\
                </a></td>';
            output += '</tr>';
            m++;
        }
        output +='</tbody>';
        output += '</table>';
        return output;
    }
    function onclickgetId(itari_admission_id){        
        $('.list-group-item').removeClass('active');
        $('#'+itari_admission_id).addClass('active');
        $('#success').hide();
        
        $('#studentEnquiryDetails').show();
        $('#followEnquiryDetails').hide();
        $('#feesEnquiryDetails').hide();

        $('#detailsForm').addClass('active');
        $('#followForm').removeClass('active');
        $('#feesForm').removeClass('active');

        $('#faAction1').addClass('fa-angle-down');
        $('#faAction2').removeClass('fa-angle-down');
        $('#faAction2').addClass('fa-angle-up');
        $('#faAction3').removeClass('fa-angle-down');
        $('#faAction3').addClass('fa-angle-up');
        $('#followup_action').val('');
        $('#loader').show();
        $('#opacity').css('opacity','0.5');
        $.ajax({
            url: '<?php echo site_url('itari/Itari_Controller/get_itari_details_by_Id_for_forms'); ?>',
            data: {'enquiry_id':itari_admission_id},
            type: "post",
            success: function (data) {
                var data = JSON.parse(data);
                $('#opacity').css('opacity','5');
                $('#loader').hide();
                $('#studentEnquiryDetails').html(construct_enquiry_template(data.follow_enquiry));
                $('#follow-history').html(construct_enquiry_history(data.follow_enquiry.followup));
                $('#feesEnquiryDetails').html(construct_fees_data(data.follow_enquiry.fees_data, data.follow_enquiry.course, data.follow_enquiry.fees_payment_mode));
                $('#enquiry_id').val(itari_admission_id); 
                $('#itari_admission_id').val(itari_admission_id); 
                if(data.follow_enquiry.fees_paid_status == "1"){
                    $('#followForm').css('display', '');
                }
            },
            error: function (err) {
                console.log(err);
            }
        });
    }

    function construct_enquiry_history(followup) {
        var his ='';
        if (followup.length == 0) {
            his +='<h4 class="no-data-display">Enquiry History Not Found</h4>';
        } else {
            his +='<h4>Follow-up History</h4>';
            his +='<table class="table table-bordered">';
            his +='<thead>';
            his +='<tr>';
            his +='<th>#</th>';
            his +='<th>Date</th>';
            his +='<th>Action</th>';
            his +='<th>Remarks</th>';
            his +='<th>Status</th>';
            his +='<th>Next follow-up date</th>';
            his +='</tr>';
            his +='</thead>';
            his +='<tbody>';
            
            for (var i = 0; i < followup.length; i++) {
            var fh = followup[i];
            his +='<tr>';
            his +='<td>'+(i+1)+'</td>';
        
            var dateString = fh.created_on.split(' ')[0];
            his +='<td>'+dateString+'</td>';
            his +='<td>'+fh.follow_up_type+'</td>';
        
            var status = fh.current_admission_status;
            if(status == "pending") {
                status = "Follow Up Pending";
            } else if(status == "admit") {
                status = "Admitted";
            } else if(status == "closed") {
                status = "Admission Closed";
            }
        
            if(fh.remarks) {
                his +='<td>'+fh.remarks+'</td>';
            } else {
                his +='<td>-</td>';
            }
        
            his +='<td>'+status+'</td>';
            his +='<td>'+fh.next_follow_date+'</td>';
            his +='</tr>';
            }
        
            his +='</tbody>';
            his +='</table>';
        }
        return his;
    }

    function actionTabs(e) {
        if (e == 0) {
            $('#studentEnquiryDetails').show();
            $('#followEnquiryDetails').hide();
            $('#feesEnquiryDetails').hide();
            $('#moveTo_Erp').hide();
            $('#detailsForm').addClass('active');
            $('#followForm').removeClass('active');
            $('#feesForm').removeClass('active');
            $('#moveToErp').removeClass('active');

            $('#faAction1').addClass('fa-angle-down');
            $('#faAction2').removeClass('fa-angle-down');
            $('#faAction2').addClass('fa-angle-up');
            $('#faAction3').removeClass('fa-angle-down');
            $('#faAction3').addClass('fa-angle-up');
            $('#faAction4').removeClass('fa-angle-down');
            $('#faAction4').addClass('fa-angle-up');
            
        }else if(e == 1){
            $('#studentEnquiryDetails').hide();
            $('#feesEnquiryDetails').hide();
            $('#moveTo_Erp').hide();
            $('#followEnquiryDetails').show();
            $('#followForm').addClass('active');
            $('#detailsForm').removeClass('active');
            $('#feesForm').removeClass('active');
            $('#moveToErp').removeClass('active');

            $('#faAction2').addClass('fa-angle-down');
            $('#faAction1').removeClass('fa-angle-down');
            $('#faAction1').addClass('fa-angle-up');
            $('#faAction3').removeClass('fa-angle-down');
            $('#faAction3').addClass('fa-angle-up');
            $('#faAction4').removeClass('fa-angle-down');
            $('#faAction4').addClass('fa-angle-up');
        } else if(e == 2){
            $('#feesEnquiryDetails').show();
            $('#followEnquiryDetails').hide();
            $('#studentEnquiryDetails').hide();
            $('#moveTo_Erp').hide();
            $('#feesForm').addClass('active');
            $('#followForm').removeClass('active');
            $('#detailsForm').removeClass('active');
            $('#moveToErp').removeClass('active');

            $('#faAction3').addClass('fa-angle-down');
            $('#faAction1').removeClass('fa-angle-down');
            $('#faAction1').addClass('fa-angle-up');
            $('#faAction2').removeClass('fa-angle-down');
            $('#faAction2').addClass('fa-angle-down');
            $('#faAction4').removeClass('fa-angle-down');
            $('#faAction4').addClass('fa-angle-up');
        }
        // } else{
        //     $('#studentEnquiryDetails').hide();
        //     $('#followEnquiryDetails').hide();
        //     $('#moveTo_Erp').hide();
        //     $('#fees_assign').show();
        //     $('#followForm').removeClass('active');
        //     $('#detailsForm').removeClass('active');
        //     $('#feesForm').addClass('active');
        //     $('#moveToErp').removeClass('active');

        //     $('#faAction4').addClass('fa-angle-down');
        //     $('#faAction1').removeClass('fa-angle-down');
        //     $('#faAction1').addClass('fa-angle-up');
        //     $('#faAction2').removeClass('fa-angle-down');
        //     $('#faAction2').addClass('fa-angle-up');
        //     $('#faAction3').removeClass('fa-angle-down');
        //     $('#faAction3').addClass('fa-angle-up');   
        // }
    }
    function submit_followUp() {
        var admission_id = $('#itari_admission_id').val();
        var $form = $('#follow-up-action');
        if ($form.parsley().validate()){
            var formData = new FormData($('#follow-up-action')[0]);
            formData.append('itari_admission_id', admission_id);
            $('#submitbutton').prop('disabled',true).html('Please wait..');
            $.ajax({
                url: '<?php echo site_url('itari/Itari_Controller/itari_admissions_follow_up'); ?>',
                type: 'post',
                data: formData,
                processData: false,
                contentType: false,
                success: function(data) {
                    var data = JSON.parse(data);
                    $('#submitbutton').prop('disabled',false).html('Submit');
                    if (data.status == true) {
                        $('#success').show('slow');
                        setTimeout(function() {
                            $("#success").hide('500');
                        }, 500);
                        $('#follow-history').html(construct_enquiry_history(data.followup));
                    } else {
                        $('#error').hide();
                        itari_enquiry_detailsbyId(enquiry_id);
                    }
                }
            });
        }
    } 
    function construct_fees_data(fees_data, course, payment_mode) {
        var std = '';
        var tx_dt_tm;
        if(fees_data.length > 0 && payment_mode == "Online"){
            std += '<h3>Fees Details</h3>';
            for(let i=0;i<fees_data.length;i++){
                if(fees_data[i].tx_dt_time == null){
                    tx_dt_tm = "-";
                }
                else{
                    tx_dt_tm = fees_data[i].tx_dt_time;
                }
                std += '<table class="table table-bordered">';
                std += '<tr>';
                std += '<td><strong>Mode</strong></td>';
                std += '<td class="" id="tx_id"><b>Online</b></td>';
                std += '</tr>';

                std += '<tr>';
                std += '<td>';
                std += '<strong>Date of Transaction</strong>';
                std += '</td>';
                std += '<td class="" id="date"><b>'+tx_dt_tm+'</b></td>';
                std += '</tr>';

                std += '<tr>';
                std += '<td style="width: 40%;">';
                std += '<span><strong>Order ID</strong></span>';
                std += '</td>';
                std += '<td class=" text-left" id="order_id">'+fees_data[i].order_id+'</td>';
                std += '</tr>';

                std += '<tr>';
                std += '<td>';
                std += '<strong>Amount</strong>';
                std += '</td>';
                std += '<td class="" id="amount">'+fees_data[i].amount+'</td>';
                std += '</tr>';

                std += '<tr>';
                std += '<td><strong>Transaction ID</strong></td>';
                std += '<td class="" id="tx_id">'+fees_data[i].tx_id+'</td>';
                std += '</tr>';

                std += '<tr>';
                std += '<td><strong>Status</strong></td>';
                std += '<td class="" id="status">'+fees_data[i].status+'</td>';
                std += '</tr>';

                std += '</table>';
            }
        }
        else if(payment_mode == "Offline"){
            std += '<h3>Fees Details</h3>';
            for(let i=0;i<fees_data.length;i++){
                std += '<table class="table table-bordered">';
                std += '<tr>';
                std += '<td><strong>Mode</strong></td>';
                std += '<td class="" id="tx_id"><b>Offline</b></td>';
                std += '</tr>';
                
                std += '<tr>';
                std += '<td>';
                std += '<strong>Date of Transaction</strong>';
                std += '</td>';
                std += '<td class="" id="date"><b>'+fees_data[i].tx_dt_time+'</b></td>';
                std += '</tr>';

                std += '<tr>';
                std += '<td style="width: 40%;">';
                std += '<span><strong>Fees Collected By</strong></span>';
                std += '</td>';
                std += '<td class=" text-left" id="order_id">'+fees_data[i].fees_collected+'</td>';
                std += '</tr>';

                if(fees_data[i].remarks){
                    std += '<tr>';
                    std += '<td><strong>Remarks</strong></td>';
                    std += '<td class="" id="tx_id">'+fees_data[i].remarks+'</td>';
                    std += '</tr>';
                }

                std += '<tr>';
                std += '<td><strong>Status</strong></td>';
                std += '<td class="" id="status">'+fees_data[i].status+'</td>';
                std += '</tr>';
                std += '</table>';
            }
        }
        else{
            std += '<h3 class="no-data-display">Fees Details Not Found</h3>';
            std += '<button class="btn btn-primary" id="offline_fees_btn" style="float: right" onclick = "collect_offline_fees(\'' + course + '\')">Collect Fees Offline</button>'
        }
        // follow_up_table.innerHTML = ftd
        return std
    }
    function collect_offline_fees(course){
        amount=0;
        if(course === "pgde")
            amount = 2000;
        else if(course === "ibtl")
            amount = 2000;
        else if(course === "cidtl")
            amount = 2000;
        else if(course === "pgpece")
            amount = 2000;
        else if(course === "maedrpl")
            amount = 2000;
        console.log(amount);
        document.getElementById('fees_amount').value = amount;
        $("#offline_fees_collection_modal").show();
    }

    $("#offline_fees_close").on('click', function(){
        $("#offline_fees_collection_modal").hide();
    });

    $("#offline_fees_submit").on('click', function(){
        var itari_admission_id = $('#enquiry_id').val();
        console.log(itari_admission_id);
        
        var remarks = $("#offline_remarks").val();
        console.log(remarks);
        
        $.ajax({
            url: '<?php echo site_url('itari/Itari_Controller/update_offline_fees'); ?>',
            data: {'itari_admission_id':itari_admission_id, 'remarks':remarks},
            type: "post",
            success: function (data) {
                if(data){
                    $("#offline_fees_collection_modal").hide();
                    onclickgetId(itari_admission_id);
                    $('#detailsForm').removeClass('active');
                    $('#followForm').removeClass('active');
                    $('#feesForm').addClass('active');
                    $('#feesEnquiryDetails').show();
                    $('#followEnquiryDetails').hide();
                    $('#studentEnquiryDetails').hide();
                    $('#faAction3').addClass('fa-angle-down');
                    $('#faAction1').removeClass('fa-angle-down');
                    $('#faAction1').addClass('fa-angle-up');
                    $('#faAction2').removeClass('fa-angle-down');
                    $('#faAction2').addClass('fa-angle-down');
                    $('#faAction4').removeClass('fa-angle-down');
                    $('#faAction4').addClass('fa-angle-up');
                }
            }
        })
    })

    function download_application_form() {
        var admissionId = $('#itari_admission_id').val();
        var button = $('#download_button');
        button.text('Checking...').css({'opacity': '0.6', 'pointer-events': 'none'});
        $.ajax({
            url: '<?php echo site_url('itari/Itari_Controller/check_application_form'); ?>',
            type: 'POST',
            data: {'admissionId': admissionId},
            success: function(data) {
                var successData = data.trim();
                if (successData == 1) {
                    button.text('Downloading...');
                    var downloadUrl = '<?php echo site_url('itari/Itari_Controller/download_application_form/'); ?>' + admissionId;
                    window.location.href = downloadUrl;

                    setTimeout(() => {
                        button.text('Download Details').css({'opacity': '1', 'pointer-events': 'auto'});
                    }, 2000);
                } else {
                    button.text('Generating...');
                    generate_application_form(admissionId);
                }
            }
        });
    }

    function generate_application_form(admissionId) {
        var button = $('#download_button');
        $.ajax({
            url: '<?php echo site_url('itari/Itari_Controller/generate_pdf_application_form'); ?>',
            type: 'POST',
            data: {'admissionId': admissionId},
            success: function(data) {
                button.text('Download Details').css({'opacity': '1', 'pointer-events': 'auto'});
                // setTimeout(() => {
                //     download_application_form();
                // }, 3000);
            }
        });
    }

    function download_essay(){
        var actionBtnDiv = document.getElementById('studentEnquiryDetails');        
        var studentName = actionBtnDiv.querySelector('#student_first_name').textContent;
        var essayUrl = actionBtnDiv.querySelector('#essay').getAttribute('href');

        fetch(essayUrl)
            .then(response => response.blob())
            .then(blob => {
            var link = document.createElement('a');
            link.href = URL.createObjectURL(blob);
            link.download = studentName+'_essay.pdf';
            link.click();
            URL.revokeObjectURL(link.href);
            })
            .catch(error => {
            console.error('Error downloading the essay:', error);
            });
    }

    function open_erp_modal() {
        var admission_id = $('#itari_admission_id').val();
        $('#classes').empty();
        $("#class_dropdown").val("");

        $.ajax({
            url: '<?php echo site_url('itari/Itari_Controller/get_classes'); ?>',
            type: 'POST',
            data: { 'itari_admission_id': admission_id },
            success: function(data) {
                var data = JSON.parse(data);
                console.log(data);

                let dropdown = '<label for="fees_amount">Select Class:</label>';
                dropdown += '<select id="class_dropdown" class="form-control" onchange="fetchSections(this.value)">';
                dropdown += '<option value="">Select Class</option>';

                data.forEach(function(item) {
                    dropdown += `<option value="${item.class_id}">${item.class_name}</option>`;
                });

                dropdown += '</select>';
                $('#classes').html(dropdown);
                $('#move_to_erp_modal').modal('show');
            }
        });
    }

    function fetchSections(classId) {
        if (classId === "") {
            $('#sections').html('');
            return;
        }

        $.ajax({
            url: '<?php echo site_url('itari/Itari_Controller/get_sections'); ?>',
            type: 'POST',
            data: { 'class_id': classId },
            success: function(data) {
                var response = JSON.parse(data);
                if (!response.length) {
                    $('#sections').html('');
                    return;
                }
                let sectionDropdown = '<label for="fees_amount">Select Section:</label><select id="section_dropdown" class="form-control">';
                sectionDropdown += '<option value="">Select Section</option>';

                response.forEach(function(item) {
                    sectionDropdown += `<option value="${item.section_id}">${item.section_name}</option>`;
                });

                sectionDropdown += '</select>';

                $('#sections').html(sectionDropdown);
            },
            error: function(error) {
                console.log("Error fetching sections", error);
            }
        });
    }

    $("#move_to_erp_close").on('click', function(){
        $("#move_to_erp_modal").hide();
    });

    $("#move_to_erp_submit").on('click', function () {
        let admissionNo = $("#admission_no").val().trim();
        let classId = $("#class_dropdown").val();
        let sectionDropdown = $("#section_dropdown");
        let sectionId = sectionDropdown.val();

        let $errorBox = $("#move_to_erp_error");
        $errorBox.addClass('d-none').text('');

        if (!admissionNo) {
            $errorBox.text("Admission Number is required.").removeClass('d-none');
            return;
        }
        if (!classId) {
            $errorBox.text("Please select a class.").removeClass('d-none');
            return;
        }
        if (sectionDropdown.length && sectionDropdown.find('option').length > 1) {
            if (!sectionId) {
                $errorBox.text("Please select a section.").removeClass('d-none');
                return;
            }
        }
        move_to_erp(admissionNo, classId, sectionId);
    });

    function move_to_erp(admission_no, class_id, section_id){
        var admission_id = $('#itari_admission_id').val();
        $.ajax({
            url: '<?php echo site_url('itari/Itari_Controller/move_to_erp'); ?>',
            type: 'POST',
            data: {'itari_admission_id': admission_id, 'admission_no': admission_no, 'class_id': class_id, 'section_id': section_id},
            success: function(data) {
                var data = JSON.parse(data);
                console.log(data.success);
                
                if(data.success == true){
                    $('#move_to_erp_modal').modal('hide');
                    onclickgetId(admission_id);
                }
                else{
                    let $errorBox = $("#move_to_erp_error");
                    $errorBox.addClass('d-none').text('');
                    $errorBox.text(data.message).removeClass('d-none');
                }
            }
        });
    }
</script>