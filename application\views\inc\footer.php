        <!-- MESSAGE BOX-->
        <div class="message-box animated fadeIn" data-sound="alert" id="mb-signout" style="z-index: 100000000;">
            <div class="mb-container">
                <div class="mb-middle">
                    <!--  <div class="mb-title"><span class="fa fa-sign-out"></span> Log <strong>Out</strong> ?</div> -->
                    <div class="mb-content">
                        <!-- <img width="100px" src="<?php //echo $this->config->item('s3_base_url') ?>/nextelement-common/door.png">           -->
                       <!--  <p>Press No if youwant to continue work. Press Yes to logout current user.</p> -->
                        <div class="pull-right text-center" >
                            <p style="color: #000; font-size: 18px; margin-bottom: 28px;">Are you sure you want to log out?</p>
                            <a  style="background-color: #6893CA;border-radius:4px;border-color: #6893CA;" class="btn btn-success btn-lg mb-control-close margin_mobile">No</a>
                            <a  style="border-radius:4px;" href="<?php echo site_url('auth/logout');?>" class="btn btn-secondary btn-lg margin_mobile">Yes</a>
                        </div>
                    </div>
                    <!-- <div class="mb-footer" style="padding: 0;">
                             
                       
                    </div> -->
                </div>
            </div>
        </div>

        <style type="text/css">
            .message-box .mb-container{
                background: #fff;
            }
            .x-navigation li.active > a{
                background: #00701a;
            }

            @media (max-width: 768px) {
                .message-box .mb-container .mb-middle{
                    width: 100%;
                    padding: 0;
                    margin-left: -25%;
                }
            }

            .counterFeesCollectionModel  .modal-content{
                width:20%;
                margin:auto;
            }
           

        </style>
        <!-- END MESSAGE BOX-->

        <script type="text/javascript">
            function add_scroller(container_id) {
                var container = document.getElementById(container_id);
                if(container.clientWidth < container.scrollWidth) {
                    $("#range-input").html(`<input type="range" class="form-control-range" id="formControlRange" oninput="scroll_report('${container_id}', this.value)" min="0" max="${container.scrollWidth - container.clientWidth}" value="0">`);
                } else {
                    $("#range-input").html('');
                }
            }

            function scroll_report(container_id, scrollTo) {
                var container = document.getElementById(container_id);
                var scroll_value = scrollTo - container.scrollLeft;
                container.scrollLeft += scroll_value;
            }
        </script>

        <script type="text/javascript" src="<?php echo base_url();?>assets/js/cookie_handler.js"></script>
        <!-- Animation CDN link-->
        <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/animate.css/4.1.1/animate.min.css" />
        <!-- Use this website for more animation styles https://animate.style/#documentation-->

        <!-- START THIS PAGE PLUGINS-->        
        <script type='text/javascript' src='<?php echo base_url();?>assets/js/plugins/icheck/icheck.min.js'></script>        
        <script type="text/javascript" src="<?php echo base_url();?>assets/js/plugins/mcustomscrollbar/jquery.mCustomScrollbar.min.js"></script>
       
        <script type='text/javascript' src='<?php echo base_url();?>assets/js/plugins/bootstrap/bootstrap-datepicker.js'></script>
        <script type="text/javascript" src="<?php echo base_url();?>assets/js/plugins/owl/owl.carousel.min.js"></script>
        <script type="text/javascript" src="<?php echo base_url();?>assets/js/plugins/moment.min.js"></script>

        <script type="text/javascript" src="<?php echo base_url();?>assets/js/plugins/bootstrap/bootstrap-select.js"></script>
        <script type="text/javascript" src="<?php echo base_url();?>assets/js/plugins/tagsinput/jquery.tagsinput.min.js"></script>
        <script type="text/javascript" src="<?php echo base_url();?>assets/js/plugins/bootstrap/bootbox.min.js"></script>
        <!-- END THIS PAGE PLUGINS-->
        
        <!--  For Datatables -->
       

        <script type="text/javascript" src="<?php echo base_url();?>assets/js/plugins/datatables/jquery.dataTables.min.js"></script>
        <script type="text/javascript" src="<?php echo base_url();?>assets/js/plugins/datatables/buttons.min.js"></script>
        <script type="text/javascript" src="<?php echo base_url();?>assets/js/plugins/datatables/buttons.flash.min.js"></script>
        <script type="text/javascript" src="<?php echo base_url();?>assets/js/plugins/datatables/jszip.min.js"></script>
        <script type="text/javascript" src="<?php echo base_url();?>assets/js/plugins/datatables/pdfmake.min.js"></script>
        <script type="text/javascript" src="<?php echo base_url();?>assets/js/plugins/datatables/vfs_fonts.js"></script>
        <script type="text/javascript" src="<?php echo base_url();?>assets/js/plugins/datatables/buttons.html5.min.js"></script>
        <script type="text/javascript" src="<?php echo base_url();?>assets/js/plugins/datatables/buttons.print.min.js"></script>
        <script type="text/javascript" src="https://cdn.datatables.net/buttons/2.4.2/js/buttons.colVis.min.js"></script>

        <script type="text/javascript" src="<?php echo base_url();?>assets/js/plugins/bootstrap/bootstrap-timepicker.min.js"></script>
        <script type="text/javascript" src="<?php echo base_url();?>assets/js/actions.js"></script>
        <script type="text/javascript" src="<?php echo base_url();?>assets/js/settings.js"></script>

        <script type="text/javascript" src="<?php echo base_url();?>assets/js/parsley.js"></script>
        <script type="text/javascript" src="<?php echo base_url();?>assets/js/bootstrap-datetimepicker.js"></script>
        <script type="text/javascript" src="<?php echo base_url('assets/js/common/multiselect.min.js');?>"></script>
        <!-- <script type='text/javascript' src='<?php echo base_url();?>assets/js/chung-timepicker.js'></script>  -->
        <script type='text/javascript' src='<?php echo base_url();?>assets/js/pnotify.js'></script>
        <script type='text/javascript' src='<?php echo base_url();?>assets/js/pnotify.buttons.js'></script> 
        
        <!--For Calendar-->
        <script type="text/javascript" src="<?php echo base_url();?>assets/js/monthly.js"></script>
        <!-- End For Calendar-->
        
        <script type="text/javascript">
        $('#demo-form').parsley();
        </script>

        <?php 

            if(isset($_SESSION['datatablescript'])) {
                $this->load->view('inc/datatable',$this->session->userdata('datatablescript'));
                unset($_SESSION['datatablescript']);
            }

        ?>

        <script type="text/javascript">
            var $form = $('#demo-form');
            $('.submitFormSingleClick').click (function () {
                // var TotalComponentAmount = $('#onkeyupTotalComponentAmount').val();
                var TotalComponentAmount = $('#tEnt_amount').html();
                var EnterAmount = $('#tAmount').val();
                if (TotalComponentAmount == EnterAmount) {
                    if ($form.parsley().validate()){
                      $(this).val('Please wait ...').attr('disabled','disabled');
                      $('#demo-form').submit(); 
                    }
                }else{
                    $('#payAmount').prop('disabled',true);
                    $('#error_message').html('We found a problem with network OR system being used. Try generating the fee receipt again with the same or using a different browser. <a href="<?php echo site_url('feesv2/fees_collection'); ?>"> Goto Fee collection </a>');
                    return false;
                }
                
            });

            var $form1 = $('#sales-form');
            $('.submitSalesFormSingleClick').click (function () {
                var student_id= $("#student_id").val();
                var sale_type= $("#sale_type").val()
              if(sale_type != 'existing' || student_id && Number(student_id) > 0) {
                if ($form1.parsley().validate()){
                  $(this).val('Please wait ...').attr('disabled','disabled');
                  $('#sales-form').submit(); 
                }
              } else {
                  alert("Unable to fetch the student's details. Please check with your administrator.");
                }
            });

            var $form2 = $('#fee_counter_collection_form');
            $('.fee_counter_collection_button').click (function () {
                var TotalComponentAmount = $('#tEnt_amount').html();
                var EnterAmount = $('#tAmount').val();
                var FinalcAmount = $('#cAmount').val();
                var ReceiptFeeDate = $('#fee_date').val();
                var paymentModes = $('#paymentModes option:selected').map(function() {
                     return $(this).text();
                }).get().join(', ');        
                bootbox.dialog({
                    title: "Confirm Payment Details",
                    message: `<div>
                        <p><strong>Receipt Date:</strong> ${ReceiptFeeDate}</p>
                        <p><strong>Amount:</strong> ${FinalcAmount}</p>
                        <p><strong>Payment Mode:</strong> ${paymentModes}</p>
                        </div>`,
                    className:"counterFeesCollectionModel",
                    buttons: {
                        cancel: {
                            label: 'Cancel',
                            className: 'btn-secondary',
                            callback: function () {
                                // Do nothing, just close
                            }
                        },
                        ok: {
                            label: 'Okay',
                            className: 'btn-success',
                            callback: function () {
                                if (TotalComponentAmount == EnterAmount) {
                                if ($form2.parsley().validate()){
                                    var form2 = $('#fee_counter_collection_form')[0];
                                    var formData1 = new FormData(form2);
                                $('#payAmount').val('Please wait ...').attr('disabled','disabled');    
                                $.ajax({
                                    url: '<?php echo site_url('feesv2/fees_collection/submit_feev1'); ?>',
                                    type: 'post',
                                    data: formData1,
                                    // async: false,
                                    processData: false,
                                    contentType: false,
                                    // cache : false,
                                    success: function(data) {
                                    var message = $.parseJSON(data);
                                    // console.log(data);
                                    if (message.transId !='') {
                                        $('#last_fee_trans_id').val(message.transId);
                                            window.location.href ='<?php echo site_url('feesv2/fees_collection/fee_recieptv1/') ?>'+message.transId
                                    }
                                    // $('#fee_submit_print_modal').modal('show');
                                    $('#fee_submit_after_message').html(message.message);                
                                    }
                                });
                                }
                            }else{
                                $('#payAmount').prop('disabled',true);
                                $('#error_message').html('We found a problem with network OR system being used. Try generating the fee receipt again with the same or using a different browser. <a href="<?php echo site_url('feesv2/fees_collection'); ?>"> Goto Fee collection </a>');
                                return false;
                            }
                            }
                        }
                    }
                });
            });


            function start_loader(){
                $('body').append('<div id="preloader"><div class="loader-holder"><div></div><div></div><div></div><div></div>')
            }
            function end_loader(){
                $('#preloader').fadeOut('fast', function() {
                    $(this).remove();
                })
            }
        </script>
    </body>
</html>