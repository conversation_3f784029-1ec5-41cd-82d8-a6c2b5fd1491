<?php

class Attendance extends C<PERSON>_Controller {

    function __construct() {
      parent::__construct();
      if (!$this->ion_auth->logged_in() || !$this->authorization->isModuleEnabled('STAFF_ATTENDANCE') || (int) $this->authorization->isAuthorized('STAFF_ATTENDANCE.MODULE') == 0) {
        redirect('auth/login', 'refresh');
      }

      $this->load->model('staff/Attendance_model', 'staff_attendance');
      $this->load->model('staff/Staff_attendance_model', 'staff_attendance_v2');
      $this->load->library('filemanager');
    }

    private function isFaceCheckinEnabled(){
      $faceCheckIn = "face_checkin";
      $faceAndLocationCheckIn="location_checkin_and_face_checkin";

      // check for location checkin permission
      if((int) $this->authorization->isAuthorized('STAFF_ATTENDANCE.STAFF_ATTENDANCE_USE_FACE_CHECKIN')==0) return 0;
      
      // check if current viewport is mobile an mobile uis enabled in staff_attendance_supported_platforms
      if ((int) json_decode($this->settings->getSetting("staff_attendance_supported_platforms")) == 0) return 0;

      if(!in_array("mobile_browser", json_decode($this->settings->getSetting("staff_attendance_supported_platforms"))) && !in_array("mobile_app", json_decode($this->settings->getSetting("staff_attendance_supported_platforms")))){
        return 0;
      }

      if ($this->settings->getSetting("staff_attendance_mode") != $faceCheckIn && $this->settings->getSetting("staff_attendance_mode") != $faceAndLocationCheckIn) return 0;

      return 1;
    }

    private function isLocationCheckInEnabled(){
      $locationCheckIn = "location_checkin";
      $locationAndLocationCheckIn = "location_checkin_and_face_checkin";

      // check for location checkin permission
      if((int) $this->authorization->isAuthorized('STAFF_ATTENDANCE.STAFF_ATTENDANCE_USE_LOCATION_CHECKIN')==0) return 0;

    // check if current viewport is mobile an mobile uis enabled in staff_attendance_supported_platforms
      if ((int) json_decode($this->settings->getSetting("staff_attendance_supported_platforms")) == 0) return 0;
      
      if(!in_array("mobile_browser", json_decode($this->settings->getSetting("staff_attendance_supported_platforms"))) && !in_array("mobile_app", json_decode($this->settings->getSetting("staff_attendance_supported_platforms")))){
        return 0;
      }

      if ($this->settings->getSetting("staff_attendance_mode") != $locationCheckIn && $this->settings->getSetting("staff_attendance_mode") != $locationAndLocationCheckIn) return 0;

      return 1;
    }

    public function index() {
      // check which type of attendance mode is enabled
      $isFaceCheckInEnabled = $this->isFaceCheckinEnabled();
      $isLocationCheckInEnabled=$this->isLocationCheckInEnabled();
      $defaultAttendance = !$isFaceCheckInEnabled && !$isLocationCheckInEnabled ? 1 : 0;

    $site_url = site_url();
	    $data['tiles'] = array(
	        [
	          'title' => 'Manage Attendance',
	          'sub_title' => 'Manage staff attendance',
	          'icon' => 'svg_icons/attendancetemplate.svg',
	          'url' => $site_url.'staff/attendance/manage_attendance',
	          'permission' => $this->authorization->isAuthorized('STAFF_ATTENDANCE.MANAGE_ATTENDANCE')
          ],
          [
	          'title' => 'My Attendance',
	          'sub_title' => 'Manage staff attendance',
	          'icon' => 'svg_icons/myattendance.svg',
	          'url' => $site_url.'staff/attendance/my_attendance_desktop',
	          'permission' => $isLocationCheckInEnabled || $defaultAttendance
	        ],
          [
	          'title' => 'Approve Regularize Attendance',
	          'sub_title' => 'Approve Regularize attendance',
	          'icon' => 'svg_icons/attendancetemplate.svg',
	          'url' => $site_url.'staff/attendance/regularize_leave',
	          // 'permission' => $this->authorization->isSuperAdmin()
	          'permission' => $this->authorization->isAuthorized('STAFF_ATTENDANCE.STAFF_REGULARIZE_LEAVE') && $this->settings->getSetting('enable_staff_regularize_attendance')
	        ],
          [
	          'title' => 'Register Face-ID',
	          'sub_title' => 'Register Face-ID',
	          'icon' => 'svg_icons/attendancetemplate.svg',
	          'url' => $site_url.'staff/attendance/register_face',
	          'permission' => $this->authorization->isAuthorized('STAFF_ATTENDANCE.STAFF_ATTENDANCE_USE_FACE_CHECKIN') && $isFaceCheckInEnabled
	        ],
          [
	          'title' => 'Approve Face-ID',
	          'sub_title' => 'Approve Face-ID',
	          'icon' => 'svg_icons/attendancetemplate.svg',
	          'url' => $site_url.'staff/attendance/approve_face_for_checkin',
	          // 'permission' => $this->authorization->isSuperAdmin()
	          'permission' =>$this->authorization->isAuthorized('STAFF_ATTENDANCE.STAFF_ATTENDANCE_APPROVE_FACE_CHECKIN') && $isFaceCheckInEnabled
            // 'permission' => ($this->authorization->isAuthorized('STAFF_ATTENDANCE.STAFF_ATTENDANCE_APPROVE_FACE_CHECKIN'))
	        ],
          [
	          'title' => 'Face-ID Check-In',
	          'sub_title' => 'Face-ID Check-In',
	          'icon' => 'svg_icons/attendancetemplate.svg',
	          'url' => $site_url.'staff/attendance/mobile_checkin_with_face',
	          'permission' => $isFaceCheckInEnabled
	        ],
          [
	          'title' => 'Exception Attendance Approval',
	          'sub_title' => 'Approval for Unsuccessful Staff Face Check-Ins/Outs',
	          'icon' => 'svg_icons/attendancetemplate.svg',
	          'url' => $site_url.'staff/attendance/exception_attendance_approval',
	          'permission' => $this->authorization->isAuthorized('STAFF_ATTENDANCE.STAFF_ATTENDANCE_EXCEPTION_ATTENDANCE_APPROVAL') && $isFaceCheckInEnabled
            // 'permission' => ($this->authorization->isAuthorized('STAFF_ATTENDANCE.STAFF_ATTENDANCE_EXCEPTION_ATTENDANCE_APPROVAL'))
	        ]

	    );
	    $data['tiles'] = checkTilePermissions($data['tiles']);

	    $data['report_tiles'] = array(
	        [
	          'title' => 'Day Wise Status Report',
	          'sub_title' => 'View staff attendance report',
	          'icon' => 'svg_icons/assessment.svg',
	          'url' => $site_url.'staff/attendance/day_wise_report',
	          'permission' => $this->authorization->isAuthorized('STAFF_ATTENDANCE.DAY_WISE_REPORT')
          ],
          [
	          'title' => 'Staff Attendance - Detailed Report',
	          'sub_title' => 'View staff attendance report',
	          'icon' => 'svg_icons/assessment.svg',
	          'url' => $site_url.'staff/attendance/staff_attendence_report',
	          'permission' => $this->authorization->isAuthorized('STAFF_ATTENDANCE.DAY_WISE_REPORT')
          ],
          [
	          'title' => 'Month Wise Summary Report',
	          'sub_title' => 'View monthly staff attendance report',
	          'icon' => 'svg_icons/monthwiseclassattendance.svg',
	          'url' => $site_url.'staff/attendance/month_wise_report',
	          'permission' => $this->authorization->isAuthorized('STAFF_ATTENDANCE.DAY_WISE_REPORT')
	        ],
          [
	          'title' => 'Late Report',
	          'sub_title' => 'View Staff coming in late',
	          'icon' => 'svg_icons/monthwiseclassattendance.svg',
	          'url' => $site_url.'staff/attendance/late_report',
	          'permission' => $this->authorization->isAuthorized('STAFF_ATTENDANCE.LATE_REPORT')
	        ],
          [
	          'title' => 'Individual Staff Attendance',
	          'sub_title' => 'View Individual staff attendance report',
	          'icon' => 'svg_icons/monthwiseclassattendance.svg',
	          'url' => $site_url.'staff/attendance/indv_staff_report',
	          'permission' => $this->authorization->isAuthorized('STAFF_ATTENDANCE.INDIVIDUAL_STAFF_REPORT')
	        ],
          [
	          'title' => 'Over-ride Report',
	          'sub_title' => 'View Over-ride Report',
	          'icon' => 'svg_icons/monthwiseclassattendance.svg',
	          'url' => $site_url.'staff/attendance/override_report',
	          'permission' => $this->authorization->isAuthorized('STAFF_ATTENDANCE.OVERRIDE_REPORT')
	        ],
          [
	          'title' => 'Check-in Report',
	          'sub_title' => 'View staff Check-in report',
	          'icon' => 'svg_icons/assessment.svg',
	          'url' => $site_url.'staff/attendance/check_in_report',
	          'permission' => $this->authorization->isAuthorized('STAFF_ATTENDANCE.CHECK_IN_REPORT')
          ],
	    );
	    $data['report_tiles'] = checkTilePermissions($data['report_tiles']);

      $data['admin_tiles'] = array(
          [
            'title' => 'Manage Shifts',
            'sub_title' => 'View/Add shifts data',
            'icon' => 'svg_icons/view.svg',
            'url' => $site_url.'staff/shifts',
            'permission' => $this->authorization->isAuthorized('STAFF_ATTENDANCE.CREATE_SHIFT')
          ],
          [
            'title' => 'Staff Shifts Mapping',
            'sub_title' => 'View/Add staff shifts data',
            'icon' => 'svg_icons/staff.svg',
            'url' => $site_url.'staff/shifts/staff_shifts',
            'permission' => $this->authorization->isAuthorized('STAFF_ATTENDANCE.ASSIGN_SHIFT')
          ],
          [
            'title' => 'Redrive Missing Attendance',
            'sub_title' => 'Redrive missing attendance',
            'icon' => 'svg_icons/staff.svg',
            'url' => $site_url.'staff/attendance/redrive_attendance',
            'permission' => $this->authorization->isAuthorized('STAFF_ATTENDANCE.REDRIVE_ATTENDANCE')
          ],
          [
	          'title' => 'Manage Geo-fences',
	          'sub_title' => 'Manage staff attendance',
	          'icon' => 'svg_icons/myattendance.svg',
	          'url' => $site_url.'staff/attendance/add_locations',
	          'permission' => $this->authorization->isAuthorized('STAFF_ATTENDANCE.MANAGE_GEOFENCE')
	        ],
          [
	          'title' => 'Rectify attendance tool',
	          'sub_title' => 'Rectify attendance tool',
	          'icon' => 'svg_icons/myattendance.svg',
	          'url' => $site_url.'staff/attendance/rectify_attendance',
	          'permission' => $this->authorization->isAuthorized('STAFF_ATTENDANCE.RECTIFY_ATTENDANCE_TOOL')
	        ],
          [
	          'title' => 'Remove Duplicate attendance',
	          'sub_title' => 'Removes duplicate biometric entries',
	          'icon' => 'svg_icons/myattendance.svg',
	          'url' => $site_url.'staff/attendance/dedupe_attendance',
	          'permission' => $this->authorization->isAuthorized('STAFF_ATTENDANCE.DEDUPE_ATTENDANCE_TOOL')
	        ]
      );
      $data['admin_tiles'] = checkTilePermissions($data['admin_tiles']);
      if ($this->mobile_detect->isTablet()) {
        $data['main_content'] = 'staff/attendance_v2/index_tablet';
      }else if($this->mobile_detect->isMobile()){
        $data['main_content'] = 'staff/attendance_v2/index_mobile';
      }else{
        $data['main_content'] = 'staff/attendance_v2/index';    	
      }
      $this->load->view('inc/template', $data);
    }

    public function dedupe_attendance() {
      $data['attendance_objs'] = $this->staff_attendance_v2->dedupe_attendance();

      redirect('staff/attendance');
    }

    public function rectify_attendance() {
      $data['attendance_objs'] = $this->staff_attendance_v2->get_rectify_attendance_records();

      $data['main_content'] = 'staff/attendance_v2/rectify_attendance';
      $this->load->view('inc/template', $data);
    }

    public function redrive_attendance() {
      $data['records'] = $this->staff_attendance->get_missing_attendance();
      $data['main_content'] = 'staff/attendance_v2/redrive_attendance';
      $this->load->view('inc/template', $data);
    }

    public function update_redrive_attendance() {
      $staffCode = $_POST['staff_code'];
      $log_data = $this->staff_attendance->get_log_data($staffCode);

      foreach ($log_data as $log) {
        $att_data = array(
          'staff_code' => $staffCode,
          'punch_time' => $log['punch_time']
        );
        $status = $this->staff_attendance_v2->update_biomtric_data($log['id'], $att_data, true);
      }

      if($status) {
        $this->session->set_flashdata('flashSuccess', 'Succefully updated.');
      } else {
        $this->session->set_flashdata('flashError', 'Something went wrong');
      }
      echo json_encode($status);
    }

    public function get_staff_attendance_redrive_missing_count_details(){
      $missing_count_Details = $this->staff_attendance_v2->get_staff_attendance_redrive_missing_count_details($_POST);
      echo json_encode($missing_count_Details);
    }

    public function update_rectify_attendance() {
      $attendance_id = $_POST['attendance_id'];

      echo $this->staff_attendance_v2->update_rectify_attendance($attendance_id);
    }

    public function getAttendanceTransactions() {
      $attendance_id = $_POST['attendance_id'];
      $transactions = $this->staff_attendance->getAttendanceTransactions($attendance_id);
      echo json_encode($transactions);
    }

    public function getAttendanceCheckoutStatus(){
    if(!isset($_POST['attendanceId'])){
        echo json_encode([]);
        return;
      }else{
        $attendance_id = $_POST['attendanceId'];
      }

      $status = $this->staff_attendance->getAttendanceCheckoutStatus($attendance_id);
      echo json_encode($status);
    }

    public function late_report () {
      $data['staff_types'] = $this->settings->getSetting("staff_type");

      $data['main_content'] = 'staff/attendance_v2/late_report'; 
	    $this->load->view('inc/template', $data);
    }

    public function indv_staff_report($staff_id=-1) {
      $data['staff_id']=$staff_id;
      $data['staff_list'] = $this->staff_attendance->getStaffData();

      $data['main_content'] = 'staff/attendance_v2/individual_staff_report'; 
	    $this->load->view('inc/template', $data);
    }

    public function get_staff_by_status(){
      $staff_status_type = $_POST['staff_status_type'];
      $staff_list = $this->staff_attendance->getStaffData('all',$staff_status_type);
      echo json_encode($staff_list);
    }

    public function get_indv_staff_attendance() {
      $from_date = date('Y-m-d', strtotime($_POST['from_date']));
      $to_date = date('Y-m-d', strtotime($_POST['to_date']));
      $selected_staff_id = $_POST['selected_staff_id'];
      $staff_status_type = $_POST['staff_status_type'];
      $staff_ids = $this->_getStaffIdsByPrivilege();
      $staff = $this->staff_attendance->get_indv_staff_attendance($from_date, $to_date, $selected_staff_id,$staff_status_type);
      echo json_encode($staff);
    }

    public function addAttendance() {
      // echo "<pre>"; print_r($_POST); die();
      $input = $_POST;
      $input['source'] = 'Staff Login';
      $input['staff_id'] = $this->authorization->getAvatarStakeHolderId();
      $att_time = date('d-M h:i a');

      $curDate=date("Y-m-d");

      //Check if check-in is already done for this date if it is a 'Check-in'
      if ($input['event_type'] == 'Check-in') {
        if ($this->staff_attendance->is_already_checked_in($input['staff_id'], $curDate) == '1') {
          echo -100;
          return;
        }
      }

      //Auto-checkin previous dates
      if($input['event_type'] == 'Check-in') {
        $is_staff_attendance_auto_checkout_disabled=$this->settings->getSetting("disable_staff_attendance_auto_checkout");
        if((int)$is_staff_attendance_auto_checkout_disabled==0){
          $this->staff_attendance->mark_previously_missed_checkout($input['staff_id']);
        }
      }

      //Save attendance
      $attendance_id = $this->staff_attendance->saveAttendanceTransaction($input);

      //Send notification
      if($attendance_id) {
        //send notification to attendance admin and reporting manager
        $this->load->model('role');
        $admins = $this->role->getStaffListByPrivilege('STAFF_ATTENDANCE', 'ADMIN');
        $rep_manager_id = $this->staff_attendance->getReportingManager($input['staff_id']);
        $staff_ids = $admins;
        if($rep_manager_id) {
          $staff_ids[] = $rep_manager_id;
        }

        if(!empty($staff_ids)) {
          $this->load->helper('texting_helper');
          $staff_data = $this->staff_attendance->getStaffName($input['staff_id']);
          $event = $input['event_type'];
          $staff_name = $staff_data->staff_name;
          $input_arr = array();
          $input_arr['staff_ids'] = $staff_ids;
          $input_arr['mode'] = 'notification';
          $input_arr['source'] = 'Attendance';
          $input_arr['message'] = "Attendance: $event by $staff_name";
          $response = sendText($input_arr);
        }
      }
      echo $attendance_id;
    }

    public function checkPositionOutside() {
      $latitude = $_POST['latitude'];
      $longitude = $_POST['longitude'];
      $staffShiftId = $_POST['staffShiftId'];
      $data = $this->staff_attendance->checkPositionOutside($latitude, $longitude, $staffShiftId);
      echo json_encode($data);
    }

    public function manage_attendance() {
      if($this->authorization->isAuthorized('STAFF_ATTENDANCE.MANAGE_ATTENDANCE')!=1){
        redirect('staff/attendance');
      }

      $this->load->model('staff/Staff_leave', 'staff_leave');
      $leave_year = $this->staff_leave->getLeaveYear();
      if ($leave_year) {
        $data['leave_v2_year_id'] = $leave_year->id;
      } else {
        $data['leave_v2_year_id'] = 0;
      }

      $data["is_three_level_approve_enabled"]=(int)$this->settings->getSetting("enable_multi_level_leave_approver_mode");
      $data['staff_types'] = $this->settings->getSetting("staff_type");
      $data['staff_using'] = $this->authorization->getAvatarStakeHolderId();
      if ($this->mobile_detect->isTablet()) {
        $data['main_content'] = 'staff/attendance_v2/manage_attendance_tablet';
      }else if($this->mobile_detect->isMobile()){
        $data['main_content'] = 'staff/attendance_v2/manage_attendance_mobile';
      }else{
        $data['main_content'] = 'staff/attendance_v2/manage_attendance';  	
      }
      $this->load->view('inc/template', $data);
    }

    public function applyLeave() {
      $this->load->model('staff/Staff_leave', 'staff_leave');
      $leave_year = $this->staff_leave->getLeaveYear();
      $_POST["enable_leave_auto_approval_by_admin"] = $this->staff_leave->getStaffLeaveAutoApprovalPermissionForAdmin($_POST["leave_category"]);
      $input = $_POST;
      $input['leave_v2_year_id'] = $leave_year->id;
      $data = array('status' => 0);

      $is_multi_level_leave_enabled = $this->settings->getSetting("enable_multi_level_leave_approver_mode");
      if($is_multi_level_leave_enabled){
        $insert = $this->staff_leave->save_leave_appication_3level($input);
      }else{
        $insert = $this->staff_leave->save_leave_appication($input);
      }

      if ($insert) {
          //send notification to attendance admin and reporting manager
          $this->load->model('role');
          $staff_id = $input['staff_id'];
          $admins = $this->role->getStaffListByPrivilege('STAFF_ATTENDANCE', 'ADMIN');
          $rep_manager_id = $this->staff_attendance->getReportingManager($staff_id);
          $staff_ids = $admins;
          if($rep_manager_id) {
            $staff_ids[] = $rep_manager_id;
          }

          $this->load->helper('texting_helper');
          if(!empty($staff_ids)) {
              $staff = $this->staff_leave->getStaffData($staff_id);
              $staffName = $staff->staffName;
              $from_date = $input['from_date'];
              $to_date = $input['to_date'];
              $title = 'Leave application';
              $message = $staffName . ' applied leave - ';
              if($from_date == $to_date) {
                  $message .= 'On '.$from_date;
              } else {
                  $message .= 'From '.$from_date.' to '.$to_date;
              }
              $input_arr = array();
              $input_arr['staff_ids'] = $staff_ids;
              $input_arr['mode'] = 'notification';
              $input_arr['source'] = 'Staff Leave';
              $input_arr['message'] = $message;
              $response = sendText($input_arr);
          }

          $author = $this->authorization->isAuthorized('LEAVE.STAFF_LEAVE_ADMIN');
          if($author == 1) {
              $message = 'Leave applied and approved.';
              if($from_date == $to_date) {
                  $message .= 'On '.$from_date;
              } else {
                  $message .= 'From '.$from_date.' to '.$to_date;
              }
              $input_arr = array();
              $input_arr['staff_ids'] = [$staff_id];
              $input_arr['mode'] = 'notification';
              $input_arr['source'] = 'Staff Leave';
              $input_arr['message'] = $message;
              sendText($input_arr);
          }

          $date = date('Y-m-d', strtotime($input['from_date']));
          $staff_ids = [$input['staff_id']];
          $staff = $this->staff_attendance->getStaffAttendanceByDate($date, $staff_ids);
          $data['staff'] = $staff[0];
          $data['status'] = 1;
      }

      echo json_encode($data);
    }

    public function getStaffAttendanceByDate() {
      $date = date('Y-m-d', strtotime($_POST['date']));
      $staff_type = $_POST['staff_type'];
      $staff_ids = $this->_getStaffIdsByPrivilege();
      $staff = $this->staff_attendance->getStaffAttendanceByDate($date, $staff_ids, $staff_type);
      echo json_encode($staff);
    }

    public function getStaffAttendanceDataByStaffId() {
      $staff_id = $_POST['staff_id'];
      $date = date('Y-m-d', strtotime($_POST['date']));
      $staff = $this->staff_attendance->getStaffAttendanceByDate($date, [$staff_id]);
      echo json_encode($staff[0]);
    }

    public function getLateReport() {
      $from_date = date('Y-m-d', strtotime($_POST['from_date']));
      $to_date = date('Y-m-d', strtotime($_POST['to_date']));

      $selected_staff_type = $_POST['selected_staff_type'];
      $staff_status_type = $_POST['staff_status_type'];

      $late_data = $this->staff_attendance->getLateData($from_date, $to_date, $selected_staff_type,$staff_status_type);
      echo json_encode($late_data);
    }

    public function getOverrideReport() {
      $from_date = date('Y-m-d', strtotime($_POST['from_date']));
      $to_date = date('Y-m-d', strtotime($_POST['to_date']));

      $override_data = $this->staff_attendance->getOverrideData($from_date, $to_date,$_POST["staff_type"]);
      echo json_encode($override_data);
    }

    public function override_report() {
      $data['staff_types'] = $this->settings->getSetting("staff_type");

      $data['main_content'] = 'staff/attendance_v2/override_report'; 
	    $this->load->view('inc/template', $data);
    }

    public function changeStaffAttendanceStatus() {
      $attendance_id = $this->staff_attendance->saveStaffAttendanceStatus();
      $data = array(
        'attendance_id' => $attendance_id
      );
      if($attendance_id) {
        $date = date('Y-m-d', strtotime($_POST['date']));
        $result=$this->staff_attendance->set_staff_attendance_id_in_regularize_leaves($attendance_id,$date);
        $staff_ids = [$_POST['staff_id']];
        $staff_type = $_POST['staff_type'];
        $staff = $this->staff_attendance->getStaffAttendanceByDate($date, $staff_ids, $staff_type);
        $data['staff'] = $staff[0];
      }

      echo json_encode($data);
    }

    public function updateRegularizeLeave(){
      $respond=$this->staff_attendance->updateRegularizeLeave($_POST);
      
      if($respond>=0) {
        //send notification to leave admin and reporting manager
        $this->load->model('role');
        $admins = $this->role->getStaffListByPrivilege('STAFF_ATTENDANCE', 'ADMIN');

        $staff_id = $this->authorization->getAvatarStakeHolderId();

        $rep_manager_id = $this->staff_attendance->getReportingManager($_POST['staff_id']);
        $staff_ids = $admins;
        if($rep_manager_id) {
            $staff_ids[] = $rep_manager_id;
        }
        $staff_ids[]=$_POST['staff_id'];

        // trigger_error(json_encode($staff_ids));
        // $staff_ids[]=0;
            
        if(!empty($staff_ids)) {
          if($respond==1){
            $leave_status="Approved";
          }else{
            $leave_status="Rejeceted";
          }
            // $this->load->library('sms_sender');
          $this->load->helper('texting_helper');
          // $this->load->helper('sms_v2_helper');
          $this->load->model('communication/texting_model');
          $staff_data = $this->staff_attendance->getStaffName($_POST['staff_id']);
          $staff_name = $staff_data->staff_name;
          $event = "Regularize request ".$leave_status." for ".$staff_name;
          $input_arr = array();
          $input_arr['staff_ids'] = $staff_ids;
          $input_arr['mode'] = 'notification';
          $input_arr['source'] = 'Staff Attendance';
          $input_arr['message'] = "Staff Attendance: $event";
          $response = sendText($input_arr);
        }
      }
      echo $respond;
    }

    public function approveAttendance() {
      // echo "<pre>"; print_r($_POST); die();
      $staff_ids = $_POST['staff_ids'];
      $status = $this->staff_attendance->approveAttendance();
      $data = array(
        'status' => $status
      );
      if($status) {
        $date = date('Y-m-d', strtotime($_POST['date']));
        $staff_ids = $_POST['staff_ids'];
        $data['staff'] = $this->staff_attendance->getStaffAttendanceByDate($date, $staff_ids);
      }
      echo json_encode($data);
    }

    public function staff_attendance_history() {
      $today = date('Y-m-d');
      $from_date = date('Y-m-d', strtotime($today.' -7 days'));
      // $to_date = date('Y-m-d', strtotime($today.' -1 days'));
      $to_date = $today;
      $staff_id = $this->authorization->getAvatarStakeHolderId();
      $rep_manager_id = $this->staff_attendance->getReportingManager($staff_id);
      $rep_manager_name=$this->staff_attendance->getReportingManagerNameByStaffId($rep_manager_id);
      $data['reporting_manager_available'] = 0;
      $data['rep_manager_name']='';
      if(!empty($rep_manager_name)){
        $data['reporting_manager_available']=1;
        $data['rep_manager_name']=$rep_manager_name->first_name.' '.$rep_manager_name->last_name;
      }

      $months = array();
      $year_month = date('Y-m');
      //echo '<pre>'; print_r($year_month); 
      $year_month6 = date('Y-m', strtotime($today.' -5 months'));
      //echo '<pre>'; print_r($year_month6); 
      $month = date('F', strtotime($year_month6));
      //echo '<pre>'; print_r($month); 
      for($i=0;$i<6;$i++){
        $months[$year_month6] = $month;
        $year_month6 = date('Y-m', strtotime($year_month6.' +1 months'));
        $month = date('F', strtotime($year_month6));
      }
      $data['months'] = $months;

      // check whether the user can login or not
      $checkInType=$this->settings->getSetting("staff_attendance_mode");
      $data['checkin_disabled'] = $this->isStaffAttendanceCheckInDisabled($checkInType);

      if ($this->mobile_detect->isTablet()){
        $data['main_content'] = 'staff/self_attendance/previous_attendance_tablet';         
       }else if($this->mobile_detect->isMobile()) { 
      	$data['main_content'] = 'staff/self_attendance/previous_attendance_mobile';
      } else {
      	$data['main_content'] = 'staff/self_attendance/previous_attendance_desktop';
      }
      $this->load->view('inc/template', $data);
    }

    public function changeMonth(){
      $staff_id = $this->authorization->getAvatarStakeHolderId();
      $today =  date('Y-m');
      $year_month = $_POST['year_month'];
      $from_date = $year_month."-01";
      $to_date = $year_month."-". date('t', strtotime($from_date));
      if($today==$year_month){
        $to_date = date('Y-m-d');
      }

      // check if staff shifts exists for requested month
      $isStaffshiftsExists= $this->staff_attendance->isStaffshiftsExistsForGivenDuration($from_date, $to_date, [$staff_id]);
      if($isStaffshiftsExists==0){
        echo json_encode([]);
        return;
      }

      $staff_status_type = 2;
      $data['attendance'] = $this->staff_attendance->getMyAttendanceReport($from_date, $to_date, [$staff_id], [], $staff_status_type);
      foreach ($data['attendance'] as $i => $att) {
        if($att->shift_exists == 0) {
          unset($data['attendance'][$i]);
        }
        $isRequestExists=$this->staff_attendance->regularizeLeaveApplicationExists(["date"=>$att->date,"staff_id"=>$att->staff_id,"shift_id"=>$att->shift_id]);

        if ($isRequestExists) {
          $att->leave_resolved=$isRequestExists->leave_resolved;
          $att->approved_reason=$isRequestExists->approved_reason;  
        } else {
          $att->leave_resolved=null;
          $att->approved_reason=null;
        }
      }
      echo json_encode($data['attendance']);
    }

    public function regularizeLeaveApplication(){
      $data=$this->staff_attendance->regularizeLeaveApplication($_POST);

        if($data) {
          //send notification to leave admin and reporting manager
          $this->load->model('role');
          $admins = $this->role->getStaffListByPrivilege('STAFF_ATTENDANCE', 'ADMIN');
          
          $staff_id = $this->authorization->getAvatarStakeHolderId();
          $rep_manager_id = $this->staff_attendance->getReportingManager($staff_id);
          $staff_ids = $admins;
          if($rep_manager_id) {
              $staff_ids[] = $rep_manager_id;
          }
          $staff_ids[]=$staff_id;

          // trigger_error(json_encode($staff_ids));

          // echo "<pre>"; print_r($staff_ids); die();
              
          if(!empty($staff_ids)) {
              // $this->load->library('sms_sender');
              $this->load->helper('texting_helper');
              // $this->load->helper('sms_v2_helper');
              $this->load->model('communication/texting_model');
              $staff_data = $this->staff_attendance->getStaffName($staff_id);
              $event = "Regularize request sent";
              $staff_name = $staff_data->staff_name;
              $input_arr = array();
              $input_arr['staff_ids'] = $staff_ids;
              $input_arr['mode'] = 'notification';
              $input_arr['source'] = 'Staff Attendance';
              $input_arr['message'] = "Staff Attendance: $event by $staff_name";
              $response = sendText($input_arr);
          }
        }
      echo $data;
    }

    public function regularizeLeaveApplicationExists(){
      $result=$this->staff_attendance->regularizeLeaveApplicationExists($_POST);
      echo json_encode($result);
    }

    public function getAllRegularizeLeave(){
      $result=$this->staff_attendance->getAllRegularizeLeave($_POST);
      echo json_encode($result);
    }

    public function day_wise_report() {
      $data['staff_types'] = $this->settings->getSetting("staff_type");

      if ($this->mobile_detect->isTablet()) {
        $data['main_content'] = 'staff/attendance_v2/day_wise_report_tablet';
      }else if($this->mobile_detect->isMobile()){
        $data['main_content'] = 'staff/attendance_v2/day_wise_report_mobile';
      }else{
        $data['main_content'] = 'staff/attendance_v2/day_wise_report';     	
      }
	    $this->load->view('inc/template', $data);
    }

    public function staff_attendence_report(){
      $data['staff_types'] = $this->settings->getSetting("staff_type");
      $data['main_content'] = 'staff/attendance_v2/staff_attendence_report';  
      $this->load->view('inc/template_fee', $data); 
    }

    public function check_in_report(){
      $data['staff_types'] = $this->settings->getSetting("staff_type");
      $data['main_content'] = 'staff/attendance_v2/check_in_report';     
      $this->load->view('inc/template', $data);	
    }

    private function _getDatesByRange($from_date, $to_date, $format="d-m-Y") {
      $dates = [];
      $from = $from_date;
      while($from != $to_date) {
        $dates[] = array(
          'format1' => date('d-m', strtotime($from)),
          'format2' => date('d M Y', strtotime($from)),
          'format3' => date('D, d-m', strtotime($from))
        );
        $from = date('Y-m-d', strtotime("$from +1 days"));
      }
      $dates[] = array(
        'format1' => date('d-m', strtotime($from)),
        'format2' => date('d M Y', strtotime($from)),
        'format3' => date('D, d-m', strtotime($from))
      );
      return $dates;
    }

    private function _getStaffIdsByPrivilege($staff_type = 'all',$staff_status_type=2) {
      $staff_ids = [];

      $reporting_staff_id = $this->authorization->getAvatarStakeHolderId();
      if($this->authorization->isAuthorized('STAFF_ATTENDANCE.ADMIN')) {
        $reporting_staff_id = 'all';
      }

      $ids = $this->staff_attendance->getSatffReporters($reporting_staff_id, $staff_type,$staff_status_type);

      return $ids;
    }

    public function getDayWiseAttendance() {
    	$from_date = date('Y-m-d', strtotime($_POST['from_date']));
    	$to_date = date('Y-m-d', strtotime($_POST['to_date']));

      $currentDate = date('Y-m-d');
      if(strtotime($from_date)==strtotime($to_date) && strtotime($from_date)==strtotime($currentDate)){
        return 0;
      }

      if(strtotime($to_date)==strtotime($currentDate)){
        $to_date = date('Y-m-d', strtotime("-1 days"));
      }

      $selected_staff_type = $_POST['selected_staff_type'];
      $show_late_only = $_POST['show_late_only'];
      $staff_status_type = $_POST['staff_status_type'];

      // Check if we need chunked processing based on date range
      $days_diff = round((strtotime($to_date) - strtotime($from_date))/86400);
      $chunk_threshold = 30; // 1 month threshold - aggressive chunking for large datasets

      if($days_diff > $chunk_threshold) {
        // Use chunked processing for large date ranges
        $this->getDayWiseAttendanceChunked($from_date, $to_date, $selected_staff_type, $show_late_only, $staff_status_type);
        return;
      }

      // Original processing for smaller date ranges
      $data['dates'] = $this->_getDatesByRange($from_date, $to_date);

      $staff_ids = $this->_getStaffIdsByPrivilege($selected_staff_type,$staff_status_type);
      if(empty($staff_ids)){
        echo json_encode([]);
        return;
      }

      $attendance = $this->staff_attendance->getAttendanceReportByDateV2($from_date, $to_date, $staff_ids, $_POST["selected_staff_type"], $staff_status_type);

      $staff_attendance = $this->_processAttendanceData($attendance);

      $data['attendance'] = [];
      foreach ($staff_attendance as $key => $value) {
        $data['attendance'][] = $value;
      }

      if ($show_late_only === '1') {
        $data['attendance'] = $this->_filterLateAttendance($data['attendance']);
      }

      $data["all_active_leave_categories"]=$this->staff_attendance->get_all_active_leave_categories();
      echo json_encode($data);
    }

    /**
     * Get day-wise attendance data using chunked processing for large date ranges
     */
    public function getDayWiseAttendanceChunked($from_date, $to_date, $selected_staff_type, $show_late_only, $staff_status_type) {
      // Create date chunks (15-day periods for smaller chunks)
      $date_chunks = $this->_createDateChunks($from_date, $to_date, 15);

      // Get all staff IDs
      $staff_ids = $this->_getStaffIdsByPrivilege($selected_staff_type, $staff_status_type);
      if(empty($staff_ids)){
        echo json_encode(['error' => 'No staff found', 'chunks' => []]);
        return;
      }

      // Create staff chunks (process 25 staff at a time for smaller chunks)
      $staff_chunks = array_chunk($staff_ids, 25);

      $response = [
        'chunked_processing' => true,
        'total_date_chunks' => count($date_chunks),
        'total_staff_chunks' => count($staff_chunks),
        'date_chunks' => $date_chunks,
        'staff_chunks' => $staff_chunks,
        'dates' => $this->_getDatesByRange($from_date, $to_date),
        'all_active_leave_categories' => $this->staff_attendance->get_all_active_leave_categories(),
        'filters' => [
          'selected_staff_type' => $selected_staff_type,
          'show_late_only' => $show_late_only,
          'staff_status_type' => $staff_status_type
        ]
      ];

      echo json_encode($response);
    }

    /**
     * Process a single chunk of attendance data
     */
    public function processAttendanceChunk() {
      $chunk_from_date = $_POST['chunk_from_date'];
      $chunk_to_date = $_POST['chunk_to_date'];
      $staff_ids = $_POST['staff_ids'];
      $selected_staff_type = $_POST['selected_staff_type'];
      $staff_status_type = $_POST['staff_status_type'];
      $chunk_index = $_POST['chunk_index'] ?? 0;
      $total_chunks = $_POST['total_chunks'] ?? 1;

      try {
        // Get attendance data for this chunk
        $attendance = $this->staff_attendance->getAttendanceReportByDateV2(
          $chunk_from_date,
          $chunk_to_date,
          $staff_ids,
          $selected_staff_type,
          $staff_status_type
        );

        // Process the attendance data
        $staff_attendance = $this->_processAttendanceData($attendance);

        $chunk_data = [];
        foreach ($staff_attendance as $key => $value) {
          $chunk_data[] = $value;
        }

        echo json_encode([
          'success' => true,
          'chunk_index' => $chunk_index,
          'total_chunks' => $total_chunks,
          'attendance_data' => $chunk_data,
          'processed_staff_count' => count($chunk_data),
          'date_range' => $chunk_from_date . ' to ' . $chunk_to_date
        ]);

      } catch (Exception $e) {
        echo json_encode([
          'success' => false,
          'error' => $e->getMessage(),
          'chunk_index' => $chunk_index
        ]);
      }
    }

    /**
     * Create date chunks for processing large date ranges
     */
    private function _createDateChunks($from_date, $to_date, $chunk_size_days = 15) {
      $chunks = [];
      $current_from = $from_date;

      while (strtotime($current_from) <= strtotime($to_date)) {
        $current_to = date('Y-m-d', strtotime($current_from . " +{$chunk_size_days} days"));

        // Don't exceed the original to_date
        if (strtotime($current_to) > strtotime($to_date)) {
          $current_to = $to_date;
        }

        $chunks[] = [
          'from_date' => $current_from,
          'to_date' => $current_to
        ];

        // Move to next chunk
        $current_from = date('Y-m-d', strtotime($current_to . " +1 day"));
      }

      return $chunks;
    }

    /**
     * Process attendance data and calculate totals for each staff member
     */
    private function _processAttendanceData($attendance) {
      $staff_attendance = [];

      foreach ($attendance as $i => $att) {
        if(!array_key_exists($att->staff_id, $staff_attendance)) {
          // Initialize staff record
          $staff_attendance[$att->staff_id] = array();
          $staff_attendance[$att->staff_id]['employee_code'] = $att->employee_code;
          $staff_attendance[$att->staff_id]['staff_name'] = $att->staff_name;
          $staff_attendance[$att->staff_id]['email'] = $att->email;
          $staff_attendance[$att->staff_id]['staff_id'] = $att->staff_id;
          $staff_attendance[$att->staff_id]['picture_url'] = $att->picture_url;
          $staff_attendance[$att->staff_id]['total_leave_taken_for_duration'] = $att->total_leave_taken_for_duration;
          $staff_attendance[$att->staff_id][$att->date] = $att;
          $staff_attendance[$att->staff_id]["is_today"] = $att->is_today;

          // Initialize counters
          $staff_attendance[$att->staff_id]["totalPresentDays"] = 0;
          $staff_attendance[$att->staff_id]["totalWorkingDays"] = 0;
          $staff_attendance[$att->staff_id]["totalabsentDays"] = 0;
          $staff_attendance[$att->staff_id]["totalweekoff"] = 0;
          $staff_attendance[$att->staff_id]["totalhoiday"] = 0;
          $staff_attendance[$att->staff_id]["totalleavestaken"] = 0;
          $staff_attendance[$att->staff_id]["total_work_duration"] = 0;
          $staff_attendance[$att->staff_id]["total_ot"] = 0;
          $staff_attendance[$att->staff_id]["total_lateby"] = 0;
          $staff_attendance[$att->staff_id]["total_late_count"] = 0;
          $staff_attendance[$att->staff_id]["total_earlyby"] = 0;
          $staff_attendance[$att->staff_id]["total_duration"] = 0;
        } else {
          $staff_attendance[$att->staff_id][$att->date] = $att;
        }

        // Calculate totals
        if($att->shift_exists==1 && $att->status!="H" && $att->status!="WO")
          $staff_attendance[$att->staff_id]["totalWorkingDays"]++;

        if($att->status=="P"){
          $staff_attendance[$att->staff_id]["totalPresentDays"]++;
        }

        if(explode(" ",$att->status)[0]=="HD"){
          $staff_attendance[$att->staff_id]["totalPresentDays"] += 0.5;
        }

        if($att->status=="AB") $staff_attendance[$att->staff_id]["totalabsentDays"]++;
        if($att->status=="WO") $staff_attendance[$att->staff_id]["totalweekoff"]++;
        if($att->status=="H") $staff_attendance[$att->staff_id]["totalhoiday"]++;
        if($att->leave_count != 0) $staff_attendance[$att->staff_id]["totalleavestaken"] += $att->leave_count;

        $staff_attendance[$att->staff_id]["total_work_duration"] += $att->duration;
        $staff_attendance[$att->staff_id]["total_ot"] += $att->ot;
        $staff_attendance[$att->staff_id]["total_lateby"] += $att->late_by;
        $staff_attendance[$att->staff_id]["total_late_count"] += (int)$att->is_late;
        $staff_attendance[$att->staff_id]["total_earlyby"] += $att->early_by;
        $staff_attendance[$att->staff_id]["total_duration"] = $staff_attendance[$att->staff_id]["total_work_duration"] + $staff_attendance[$att->staff_id]["total_ot"];
      }

      return $staff_attendance;
    }

    /**
     * Filter attendance data to show only late staff
     */
    private function _filterLateAttendance($attendance_data) {
      $late_attendance = [];
      foreach ($attendance_data as $staff_data) {
        foreach ($staff_data as $staff_item) {
          if (isset($staff_item->is_late) && $staff_item->is_late == 1) {
            $late_attendance[] = $staff_data;
            break; // Only add once per staff member
          }
        }
      }
      return $late_attendance;
    }

    public function get_staff_ids(){

    $selected_staff_type = $_POST['selected_staff_type'];
    $staff_status_type = $_POST['staff_status_type'];

    $from_date = date('Y-m-d', strtotime($_POST['from_date']));
    $to_date = date('Y-m-d', strtotime($_POST['to_date']));

    $staff_ids = $this->staff_attendance->get_staff_ids($selected_staff_type, $staff_status_type);
    
    if(!empty($staff_ids)){
      $data['dates'] = $this->_getDatesByRange($from_date, $to_date);
    }

    $data['staff_ids']=$staff_ids;
    echo json_encode($data);
  }
    public function getStaffAttendanceChanges() {
      $attendance_id = $_POST['attendance_id'];
      $transactions = $this->staff_attendance->getStaffAttendanceHistory($attendance_id);
      echo json_encode($transactions);
    }

     public function getStaffAttendanceData() {
      $attendanceData = $this->staff_attendance->getStaffAttendanceData($_POST['attendanceId']);
      echo json_encode($attendanceData);
    }

    public function editStaffAttendanceTime() {
      $edit_response = $this->staff_attendance->editStaffAttendanceTime($_POST);
      echo $edit_response;
    }

    public function getCheckinDetails() {
      $staff_id = $this->authorization->getAvatarStakeHolderId();
      $date = date('Y-m-d');
      $shift = $this->staff_attendance->getStaffShiftData($staff_id, $date);
      $data = array(
        'status' => 0
      );
      if(empty($shift)) {
        $data['status'] = 0;
      } else if($shift->type == 1){
        $att_data = $this->staff_attendance->getShiftAttendance($staff_id, $shift->id);
        // echo '<pre>'; print_r($att_data); die();
        if(empty($att_data)) {
          $data['status'] = -1;
        } else {
          $data['status'] = 1;
          $data['att'] = $att_data;
        }
      } else {
        $data['status'] = $shift->type;//2:week-off, 3:Holiday
      }

      echo json_encode($data);
    }

    public function month_wise_report() {
      $data['staff_types'] = $this->settings->getSetting("staff_type");

      if ($this->mobile_detect->isTablet()) {
        $data['main_content'] = 'staff/attendance_v2/month_wise_report_tablet';
      }else if($this->mobile_detect->isMobile()){
        $data['main_content'] = 'staff/attendance_v2/month_wise_report_mobile';
      }else{
        $data['main_content'] = 'staff/attendance_v2/month_wise_report';
      }
	    $this->load->view('inc/template', $data);
    }

    public function getMonthWiseAttendance() {
      // echo '<pre>';print_r($_POST);die();

    	$from_date = date('Y-m-d',strtotime('01-'.$_POST['from_date']));
      $to = $_POST['to_date'];

      $staff_status_type = $_POST['staff_status_type'];

      $to = "01-".$to;

      $year_month = date('Y-m', strtotime($to));
      $to = $year_month."-". date('t', strtotime($to));
    	$to_date = date('Y-m-d', strtotime($to));
      $today = date('Y-m-d');

      // 1. Check if from and to date have the same months
      // 2. If yes -> then check if 'Today' is the current day of the month else do nothing
      // 3. If yes then return [] else do nothing

      if(date('m', strtotime($from_date)) === date('m', strtotime($to_date)) && date("Y-m-01", strtotime($from_date)) == $today){
        echo json_encode([]);
        return;
      }else if($today < $to_date){
        $to_date = date('Y-m-d', strtotime($today.' -1 days'));
      }

      // Check if we need chunked processing based on date range
      $days_diff = round((strtotime($to_date) - strtotime($from_date))/86400);
      $chunk_threshold = 30; // 1 month threshold - aggressive chunking for large datasets

      if($days_diff > $chunk_threshold) {
        // Use chunked processing for large date ranges
        $this->getMonthWiseAttendanceChunked($from_date, $to_date, $staff_status_type, $_POST["staff_type"]);
        return;
      }

      $date1 = $from_date;
      $date2 = $to_date;
      $ts1 = strtotime($date1);
      $ts2 = strtotime($date2);
      $year1 = date('Y', $ts1);
      $year2 = date('Y', $ts2);
      $month1 = date('m', $ts1);
      $month2 = date('m', $ts2);
      $count = (($year2 - $year1) * 12) + ($month2 - $month1)+1;

    	$staff = $this->staff_attendance->getStaffData("all",$staff_status_type);

    	$staff_names = array();
    	foreach ($staff as $stf) {
    		$staff_names[$stf->id] = $stf->staff_name;
    	}

      // $staff_ids = $this->_getStaffIdsByPrivilege($_POST['selected_staff_type']);
      $staff_ids = $this->_getStaffIdsByPrivilege('all',$staff_status_type);
      if(empty($staff_ids)){
        echo json_encode([]);
        return;
      }

      $attendance = $this->staff_attendance->getAttendanceReportByDateV2($from_date, $to_date, $staff_ids,$_POST["staff_type"],$staff_status_type);

      // Process the month-wise data
      $processed_data = $this->_processMonthWiseData($attendance, $from_date, $to_date, $count, $staff_names);
      echo json_encode($processed_data);
    }

    /**
     * Get month-wise attendance data using chunked processing for large date ranges
     */
    public function getMonthWiseAttendanceChunked($from_date, $to_date, $staff_status_type, $staff_type) {
      // Create date chunks (15-day periods for smaller chunks)
      $date_chunks = $this->_createDateChunks($from_date, $to_date, 15);

      // Get all staff IDs
      $staff_ids = $this->_getStaffIdsByPrivilege('all', $staff_status_type);
      if(empty($staff_ids)){
        echo json_encode(['error' => 'No staff found', 'chunks' => []]);
        return;
      }

      // Create staff chunks (process 25 staff at a time for smaller chunks)
      $staff_chunks = array_chunk($staff_ids, 25);

      // Calculate month count for dates
      $ts1 = strtotime($from_date);
      $ts2 = strtotime($to_date);
      $year1 = date('Y', $ts1);
      $year2 = date('Y', $ts2);
      $month1 = date('m', $ts1);
      $month2 = date('m', $ts2);
      $count = (($year2 - $year1) * 12) + ($month2 - $month1)+1;

      $response = [
        'chunked_processing' => true,
        'total_date_chunks' => count($date_chunks),
        'total_staff_chunks' => count($staff_chunks),
        'date_chunks' => $date_chunks,
        'staff_chunks' => $staff_chunks,
        'month_count' => $count,
        'from_date' => $from_date,
        'to_date' => $to_date,
        'filters' => [
          'staff_status_type' => $staff_status_type,
          'staff_type' => $staff_type
        ]
      ];

      echo json_encode($response);
    }

    /**
     * Process a single chunk of month-wise attendance data
     */
    public function processMonthWiseAttendanceChunk() {
      $chunk_from_date = $_POST['chunk_from_date'];
      $chunk_to_date = $_POST['chunk_to_date'];
      $staff_ids = $_POST['staff_ids'];
      $staff_status_type = $_POST['staff_status_type'];
      $staff_type = $_POST['staff_type'];
      $chunk_index = $_POST['chunk_index'] ?? 0;
      $total_chunks = $_POST['total_chunks'] ?? 1;

      try {
        // Get attendance data for this chunk
        $attendance = $this->staff_attendance->getAttendanceReportByDateV2(
          $chunk_from_date,
          $chunk_to_date,
          $staff_ids,
          $staff_type,
          $staff_status_type
        );

        // Get staff names for this chunk
        $staff = $this->staff_attendance->getStaffData("all", $staff_status_type);
        $staff_names = array();
        foreach ($staff as $stf) {
          if(in_array($stf->id, $staff_ids)) {
            $staff_names[$stf->id] = $stf->staff_name;
          }
        }

        echo json_encode([
          'success' => true,
          'chunk_index' => $chunk_index,
          'total_chunks' => $total_chunks,
          'attendance_data' => $attendance,
          'staff_names' => $staff_names,
          'processed_staff_count' => count($staff_ids),
          'date_range' => $chunk_from_date . ' to ' . $chunk_to_date
        ]);

      } catch (Exception $e) {
        echo json_encode([
          'success' => false,
          'error' => $e->getMessage(),
          'chunk_index' => $chunk_index
        ]);
      }
    }

    /**
     * Process month-wise attendance data
     */
    private function _processMonthWiseData($attendance, $from_date, $to_date, $count, $staff_names) {
      $staff_attendance = [];
      $today = date('Y-m-d');

      $m_y = date('M-Y',strtotime($from_date));
      $s_d = date('Y-m-d',strtotime('01-'.$m_y));
      $e_d = date('Y-m', strtotime($s_d));
      $e_d = $e_d."-". date('t', strtotime($e_d));

      $dates = array();

      for($j=0;$j<$count;$j++){
        if($today<$e_d){
          $e_d = date('Y-m-d', strtotime($today.' -1 days'));
        }
        $diff = date('d', strtotime($e_d));
        $dates[$j]['format']=$m_y;
        $dates[$j]['days']=$diff;
        $m_y = date('d-m-Y', strtotime($s_d.' +1 months'));
        $m_y = date('M-Y', strtotime($m_y));
        $s_d = date('Y-m-d',strtotime('01-'.$m_y));
        $e_d = date('Y-m', strtotime($s_d));
        $e_d = $e_d."-". date('t', strtotime($e_d));
      }

      $data['dates'] = $dates;

      $m_y = date('M-Y',strtotime($from_date));
      $s_d = date('Y-m-d',strtotime('01-'.$m_y));
      $e_d = date('Y-m', strtotime($s_d));
      $e_d = $e_d."-". date('t', strtotime($e_d));

      for($j=1;$j<=$count;$j++){

        if($today<$e_d){
          $e_d = date('Y-m-d', strtotime($today.' -1 days'));
        }
        foreach ($attendance as $i => $att) {

          if(!array_key_exists($att->staff_id, $staff_attendance)) {
            $staff_attendance[$att->staff_id] = array();
            $staff_attendance[$att->staff_id]['employee_code'] = $att->employee_code;
            $staff_attendance[$att->staff_id]['staff_name'] = $staff_names[$att->staff_id];
            $staff_attendance[$att->staff_id]['staff_id'] = $att->staff_id;
            $staff_attendance[$att->staff_id]['staff_type'] = $att->staff_type;
            $staff_attendance[$att->staff_id]['staff_department'] = $att->staff_department_name;
            $staff_attendance[$att->staff_id]['joining_date'] = $att->joining_date;
            $staff_attendance[$att->staff_id]['last_date_of_work'] = $att->last_date_of_work;
            $staff_attendance[$att->staff_id]['staff_status'] = $att->staff_status;
          }
          if(!array_key_exists($m_y, $staff_attendance[$att->staff_id])){
            $staff_attendance[$att->staff_id][$m_y]['shift_exists'] = 0;
            $staff_attendance[$att->staff_id][$m_y]['P'] = 0;
            $staff_attendance[$att->staff_id][$m_y]['AB'] = 0;
            $staff_attendance[$att->staff_id][$m_y]['HD'] = 0;
            $staff_attendance[$att->staff_id][$m_y]['WO'] = 0;
            $staff_attendance[$att->staff_id][$m_y]['H'] = 0;
            $staff_attendance[$att->staff_id][$m_y]['total_working_days'] = 0;
          }

          if(($att->status=='P' or $att->status=='FD') and $s_d<=date('Y-m-d', strtotime($att->date)) and $e_d>=date('Y-m-d', strtotime($att->date)) and $att->type == 1){
            $staff_attendance[$att->staff_id][$m_y]['P'] += 1;
          }
          elseif((strpos($att->status, 'HD') !== false) and $s_d<=date('Y-m-d', strtotime($att->date)) and $e_d>=date('Y-m-d', strtotime($att->date))){
            $staff_attendance[$att->staff_id][$m_y]['HD'] += 1;
          }
          elseif($att->status=='H' and $s_d<=date('Y-m-d', strtotime($att->date)) and $e_d>=date('Y-m-d', strtotime($att->date))){
            $staff_attendance[$att->staff_id][$m_y]['H'] += 1;
          }
          elseif($att->status=='WO' and $s_d<=date('Y-m-d', strtotime($att->date)) and $e_d>=date('Y-m-d', strtotime($att->date))){
            $staff_attendance[$att->staff_id][$m_y]['WO'] += 1;
          }
          elseif((strpos($att->status, 'AB') !== false) and $s_d<=date('Y-m-d', strtotime($att->date)) and $e_d>=date('Y-m-d', strtotime($att->date))){
            $staff_attendance[$att->staff_id][$m_y]['AB'] += 1;
          }
          if($att->shift_exists == 1 and $s_d<=date('Y-m-d', strtotime($att->date)) and $e_d>=date('Y-m-d', strtotime($att->date))){
            $staff_attendance[$att->staff_id][$m_y]['shift_exists'] = 1;
          }

          if ($att->shift_exists == 1 and $s_d <= date('Y-m-d', strtotime($att->date)) and $e_d >= date('Y-m-d', strtotime($att->date)) and $att->type==1) {
            $staff_attendance[$att->staff_id][$m_y]['total_working_days'] += 1;
          }

        }
        $m_y = date('d-m-Y', strtotime($s_d.' +1 months'));
        $m_y = date('M-Y', strtotime($m_y));
        $s_d = date('Y-m-d',strtotime('01-'.$m_y));
        $e_d = date('Y-m', strtotime($s_d));
        $e_d = $e_d."-". date('t', strtotime($e_d));
      }

      $data['attendance'] = [];
      foreach ($staff_attendance as $key => $value) {
        $data['attendance'][] = $value;
      }

      return $data;
    }

    public function regularize_leave(){
      if ($this->mobile_detect->isTablet()) {
        $data['main_content'] = 'staff/self_attendance/regularize_leave';
      }else if($this->mobile_detect->isMobile()){
        $data['main_content'] = 'staff/self_attendance/regularize_leave_mobile';
      }else{
        $data['main_content'] = 'staff/self_attendance/regularize_leave';     	
      }

      // $data['main_content'] = 'staff/self_attendance/regularize_leave';     	
	    $this->load->view('inc/template', $data);
    }

    public function register_face(){
      $staff_id = $this->authorization->getAvatarStakeHolderId();
      $data['face_exist'] = $this->staff_attendance->get_staff_face_if_exist($staff_id);
      $data['approved'] = $this->staff_attendance->check_approval_status($staff_id);
      if ($data['face_exist'] > 0){
        $res = $this->staff_attendance->get_staff_faces($staff_id);
        $photos = [];
        $approved = 0;
        $remarks = '';
        foreach ($res as $key => $photo) {
          if ($key === 'is_approved') {
              $data['is_approved'] = $photo;
          } else if($key === 'remarks') {
              $data['remarks'] = $photo;
          } else {
            $photos[$key] = $this->filemanager->getFilePath(json_decode($photo));
          }
        } 
        $data['photos'] = $photos;
      } else {
        $data['photos'] = [];
        $data['is_approved'] = 0;
        $data['remarks'] = '';
      }
      if ($this->mobile_detect->isTablet()) {
        $data['main_content'] = 'staff/self_attendance/tablet_face_checkin';
      }else if($this->mobile_detect->isMobile()){
        $data['main_content'] = 'staff/self_attendance/register_face_mobile';
      }else{
        $data['main_content'] = 'staff/self_attendance/desktop_face_checkin';     	
      }
	    $this->load->view('inc/template', $data);
    }

    public function store_faces_for_registration()  {
      $descriptor = json_decode($_POST['descriptors']);
      $uploadedImages = [];
      
      // Loop through each uploaded file in $_FILES
      foreach ($_FILES as $key => $file) {
          if (strpos($key, 'image_') !== false) { // Check if it's an image file
              $photo_url = $this->s3FileUpload($file, 'staff_attendance_face_recognition');
              $uploadedImages[$key] = $photo_url;
          }
      }

      $fileNames = array_map(function($image) {
          return $image['file_name'];
      }, $uploadedImages);

      if (empty($descriptor) || empty($fileNames) || count($descriptor) != 4 || count($fileNames) != 4) {
          echo 0;
          return;
      }

      // Check each sub-array in $descriptor for null values
      foreach ($descriptor as $desc) {
          if (!is_array($desc) || in_array(null, $desc, true)) {
              echo 0;
              return;
          }
      }

      // Check each value in $fileNames for null
      foreach ($fileNames as $fileName) {
          if ($fileName === null || $fileName === '') {
              echo 0;
              return;
          }
      }
      $status = $this->staff_attendance->register_face($descriptor, $fileNames);
      // echo "<pre>";print_r($status);die();
      if($status){
        echo 1;
        return;
      } else {
        echo 0;
        return;
      }
    }

    private function s3FileUpload($file, $folder_name = 'profile')
    {
      if ($file['tmp_name'] == '' || $file['name'] == '') {
        return ['status' => 'empty', 'file_name' => ''];
      }
      return $this->filemanager->uploadFile($file['tmp_name'], $file['name'], $folder_name);
    }

    public function approve_face_for_checkin(){
      $data['title'] = 'Approve Face-ID';
      if ($this->mobile_detect->isTablet()) {
        $data['main_content'] = 'staff/self_attendance/tablet_approve_neg.php';
      }else if($this->mobile_detect->isMobile()){
        $data['main_content'] = 'staff/self_attendance/mobile_checkin_neg';
      }else{
        $data['main_content'] = 'staff/self_attendance/approve_face_for_checkin';     	
      }
      $data['approve_details'] = $this->staff_attendance->get_photo();
	    $this->load->view('inc/template', $data);
    }

    public function exception_attendance_approval(){
      $data['title'] = 'Exception Attendance Approval';
      if ($this->mobile_detect->isTablet()) {
        $data['main_content'] = 'staff/self_attendance/tablet_approve_neg';
      }else if($this->mobile_detect->isMobile()){
        $data['main_content'] = 'staff/self_attendance/mobile_checkin_neg';
      }else{
        $data['main_content'] = 'staff/self_attendance/exception_attendance_approval';     	
      }
	    $this->load->view('inc/template', $data);
    }

    function get_exception_attendance(){
      $from_date = $this->input->post('from_date');
      $to_date = $this->input->post('to_date');
      $exception_staff_details = $this->staff_attendance->get_exception_attendance($from_date, $to_date);
      echo json_encode($exception_staff_details);
    }

    function get_exception_attendance_details(){
      $date = $this->input->post('date');
      $type = $this->input->post('type');
      $exception_att_details = $this->staff_attendance->get_exception_attendance_details($date, $type);

      if($exception_att_details == '0' || empty($exception_att_details)){
        echo 0;
        return;
      }
      if($exception_att_details != '' || $exception_att_details != null || !empty($exception_att_details)){
        foreach($exception_att_details as $key => $val){
          if (!empty($val->check_in_out_photo)) {
              $val->check_in_out_photo = $this->filemanager->getFilePath(json_decode($val->check_in_out_photo));
          }
          if (!empty($val->reference_face_id)) {
              $val->reference_face_id = $this->filemanager->getFilePath(json_decode($val->reference_face_id));
          }
          $exception_att_details[$key] = $val;
        }
      }
      // echo "<pre>";print_r($exception_att_details);die();
      echo json_encode($exception_att_details);
    }

    public function get_descriptors_for_auto_approval(){
      $date = $this->input->post('date');

      echo json_encode($this->staff_attendance->get_descriptors_for_auto_approval($date));
    }

    public function update_status_for_auto_approval() {
      // Get the input data
      $ids_array = $this->input->post('ids_array');
      
      // Decode the JSON array
      $status = json_decode($ids_array);
  
      // Call the model and send the response back
      $result = $this->staff_attendance->update_status_for_auto_approval($status);
      echo json_encode(['success' => $result]);
    }  

    public function handle_exception_attendance(){
      // echo "<pre>";print_r($_POST);die();
      $att_trans_id = $this->input->post('att_trans_id');
      $action = $this->input->post('action');
      $type = $this->input->post('type');
      $useFaceID = $this->input->post('useFaceID');
      $staff_id = $this->input->post('staff_id');
      $status = $this->staff_attendance->handle_exception_attendance($att_trans_id, $action, $type, $useFaceID, $staff_id);
      if (is_numeric($status)) {
          echo $status; 
      } else {
          echo json_encode(['message' => $status]);
      }
    }

    public function action_taken_for_face_checkin(){
      // echo "<pre>";print_r($_POST);die();
      $row_id = $_POST['row_id'];
      $action = $_POST['action'];
      $remarks = $_POST['remarks'];
      // $this->send_status_notification_for_staff(98, $action);
      $data = $this->staff_attendance->action_taken_for_face_checkin($row_id, $action, $remarks);

      if($data){
        $this->send_status_notification_for_staff($data, $action);
        echo 1;
        return;
      } else {
        echo 0;
        return;
      }
    }

    private function send_status_notification_for_staff($data, $action){
      $staff_details = $this->staff_attendance->get_staff_details_for_notification($data);
      if($staff_details){
        // echo "<pre>";print_r($staff_details);die();
        $receivers = [(string) $staff_details->staff_id];
        $school_name = $this->settings->getSetting('school_name');
        $this->load->model('communication/texting_model', 'texting_model');
        if ($action == 'approve') {
            $notification = "Dear staff member, your Face-ID has been approved. Please check your school app for more details. Thank you!";
        } else if ($action == 'reject') {
            $notification = "Dear staff member, your Face-ID has been rejected. Please check your school app for more details. Thank you!";
        } else {
            $notification = "Dear staff member, you have a new Face-ID status notification. Please check your school app for more details. Thank you!";
        }
        $credits = $this->texting_model->_calculateCredits($notification, 0);
        $text_master = array(
          'title' => 'Staff Attendance',
          'message' => $notification,
          'sent_by' => $this->authorization->getAvatarId(),
          'reciever' => 'Staff',
          'acad_year_id' => $this->acad_year->getAcadYearId(),
          'source' => 'Staff Attendance',
          'text_count' => 0,
          'visible' => 1,
          'mode' => 'notification',
          'sms_credits' => $credits,
          'is_unicode' => 0,
          'sender_list' => NULL,
          'sending_status' => 'Initiated'
        );
        // echo "<pre>";print_r($text_master);die();
        $texting_master_id = $this->texting_model->save_texts($text_master);

        $url = site_url('staff/attendance/register_face');
        $textingData = [];
        $textingData[] = array(
          'texting_master_id' => $texting_master_id,
          'stakeholder_id' => $staff_details->staff_id,
          'mobile_no' => $staff_details->staff_mobile_no,
          'mode' => 1,
          'status' => ($staff_details->tokenState == 0 )? 'No Token' : 'Sent',
          'avatar_type' => 4,
          'is_read' => 0,
          'user_id' => $staff_details->user_id,
          'token' => $staff_details->user_token,
        );
        // echo "<pre>";print_r($textingData);die();
        $token_data = $this->texting_model->save_notifications($textingData);
        $this->load->helper('notification_helper');
        $notification_status = commonNotifications($token_data, $school_name, $notification, $url);
      }
    }

    public function mobile_checkin_with_face(){
    // check whether the user can login or not
      $data['checkin_disabled'] = $this->isStaffAttendanceCheckInDisabled("face_checkin");

      $this->load->model('staff/shifts_model');
      $staff_id = $this->authorization->getAvatarStakeHolderId();
      $data['staffId'] = $staff_id;
      $data['staff_name'] = $this->staff_attendance->get_staff_name_by_staff_id($staff_id);
      $rep_manager_id = $this->staff_attendance->getReportingManager($staff_id);
      $rep_manager_name=$this->staff_attendance->getReportingManagerNameByStaffId($rep_manager_id);
      $data['face_exist'] = $this->staff_attendance->get_staff_face_if_exist($staff_id);
      $data['is_approved'] = $this->staff_attendance->check_approval_status($staff_id);

      $data['reporting_manager_available'] = 0;
      $data['rep_manager_name']='';
      if(!empty($rep_manager_name)){
        $data['reporting_manager_available']=1;
        $data['rep_manager_name']=$rep_manager_name->first_name.' '.$rep_manager_name->last_name;
      }
      $date = date('Y-m-d');
      $data['shifts'] = $this->shifts_model->getShiftsByDate($staff_id, $date);
      $data['is_on_leave'] = $this->staff_attendance->check_is_on_leave($staff_id, $date);
      $months = array();
      $year_month = date('Y-m');
      $today = date('Y-m-d'); 
      $year_month6 = date('Y-m', strtotime("-5 months", strtotime($today)));
      $month = date('F', strtotime($year_month6));
      for($i=0;$i<6;$i++){
        $months[$year_month6] = $month;
        $year_month6 = date('Y-m', strtotime($year_month6.' +1 months'));
        $month = date('F', strtotime($year_month6));
      }

      $data['months'] = $months;
      $data['descriptors'] = json_encode($this->staff_attendance->get_stored_faces_for_checkin());
      if ($this->mobile_detect->isTablet()) {
        $data['main_content'] = 'staff/self_attendance/tablet_face_checkin';
      }else if($this->mobile_detect->isMobile()){
        $data['main_content'] = 'staff/self_attendance/mobile_face_checkin';
      }else{
        $data['main_content'] = 'staff/self_attendance/desktop_face_checkin';     	
      }
      // echo "<pre>";print_r($data);die();
	    $this->load->view('inc/template', $data);
    }

    public function get_stored_faces_for_checkin(){
      echo json_encode($this->staff_attendance->get_stored_faces_for_checkin());
    }

    private function isStaffAttendanceCheckInDisabled($checkinType){
      if($checkinType=="location_checkin" || $checkinType=="face_checkin"){
        $locationAndFaceCheckin="location_checkin_and_face_checkin";
        // Steps
        // 1. If the user has no checkin priviledge, if not then return 1 -> Disabled
        // 2. If any one of the config namely->staff_attendance_mode and staff_attendance_supported_platforms is disabled then return 1 -> Disabled
        // 3. Else 
          //    Get all the supported platform, and check if the current platform is not found then, return 1
          //    else if platform is found, then check if the given attendance type => 'location_check_in' or 'face_checkin' is found or not, if not then return 1 -> Disabled
          //    else return 0 'Not disabled' i.e they can checkin the staff attendance

        if($checkinType=="face_checkin"){
          $checkInPermissiom = (int)$this->authorization->isAuthorized('STAFF_ATTENDANCE.STAFF_ATTENDANCE_USE_FACE_CHECKIN');
        }else {
          $checkInPermissiom = (int)$this->authorization->isAuthorized('STAFF_ATTENDANCE.STAFF_ATTENDANCE_USE_LOCATION_CHECKIN');
        }
        
        if($checkInPermissiom==0) return 1; //1-> Disable checkin/checkout

        if((int)json_decode($this->settings->getSetting("staff_attendance_supported_platforms")) == 0) return 1;

        $staffAttendanceMode=$this->settings->getSetting("staff_attendance_mode");

        if ($staffAttendanceMode != $checkinType && $staffAttendanceMode != $locationAndFaceCheckin) {
          return 1;
        }

        $staffAttendanceSupportedPlatforms = $this->settings->getSetting("staff_attendance_supported_platforms");
        $isViewportEnabled=0;
      

        if($checkinType=="face_checkin"){
          // for checkin we have to only check the mobile viewport
          // checking respective viewports
          if ($this->mobile_detect->isMobile()) {
            // is mobile supported
            $isViewportEnabled = in_array("mobile_browser", json_decode($staffAttendanceSupportedPlatforms)) || in_array("mobile_app", json_decode($staffAttendanceSupportedPlatforms));
          }
        }else{
          if ($this->mobile_detect->isTablet()) {
          // considering tablet in mobile category
          // is mobile supported
          $isViewportEnabled = in_array("mobile_browser", json_decode($staffAttendanceSupportedPlatforms)) || in_array("mobile_app", json_decode($staffAttendanceSupportedPlatforms));
          } else if ($this->mobile_detect->isMobile()) {
            // is mobile supported
            $isViewportEnabled = in_array("mobile_browser", json_decode($staffAttendanceSupportedPlatforms)) || in_array("mobile_app", json_decode($staffAttendanceSupportedPlatforms));
          } else {
            $isViewportEnabled = in_array("desktop", json_decode($staffAttendanceSupportedPlatforms));
          }
        }

        if($isViewportEnabled==0) return 1;

        return 0; // staff attendance check in is enabled
      } else {
        return 1;
      }
    }
    public function my_attendance_desktop(){
      // check whether the user can login or not
      $data['checkin_disabled'] = $this->isStaffAttendanceCheckInDisabled("location_checkin");

      // echo $data['checkin_disabled']; die();

      $this->load->model('staff/shifts_model');
      $staff_id = $this->authorization->getAvatarStakeHolderId();
      $rep_manager_id = $this->staff_attendance->getReportingManager($staff_id);
      $rep_manager_name=$this->staff_attendance->getReportingManagerNameByStaffId($rep_manager_id);

      $data['reporting_manager_available'] = 0;
      $data['rep_manager_name']='';
      if(!empty($rep_manager_name)){
        $data['reporting_manager_available']=1;
        $data['rep_manager_name']=$rep_manager_name->first_name.' '.$rep_manager_name->last_name;
      }
      $date = date('Y-m-d');
      $data['shifts'] = $this->shifts_model->getShiftsByDate($staff_id, $date);
      $data['is_on_leave'] = $this->staff_attendance->check_is_on_leave($staff_id, $date);
      
      $months = array();
      $year_month = date('Y-m');
      $today = date('Y-m-d'); 
      $year_month6 = date('Y-m', strtotime("-5 months", strtotime($today)));
      $month = date('F', strtotime($year_month6));
      for($i=0;$i<6;$i++){
        $months[$year_month6] = $month;
        $year_month6 = date('Y-m', strtotime($year_month6.' +1 months'));
        $month = date('F', strtotime($year_month6));
      }

      $data['months'] = $months;
      if ($this->mobile_detect->isTablet()) {
        $data['main_content'] = 'staff/self_attendance/my_attendance_tablet';
      }else if($this->mobile_detect->isMobile()){
        $data['main_content'] = 'staff/self_attendance/my_attendance';
      }else{
        $data['main_content'] = 'staff/self_attendance/my_attendance_desktop';     	
      }
	    $this->load->view('inc/template', $data);
    }

    public function add_locations(){
      $data['main_content'] = 'staff/self_attendance/add_locations';
	    $this->load->view('inc/template', $data);
    }

    public function add_new_staff_locations(){
      return $this->staff_attendance->add_new_staff_locations($_POST);
    }

    public function get_staff_attendance_locations(){
      $staff_attendance_locations=$this->staff_attendance->get_staff_attendance_locations();
      echo json_encode($staff_attendance_locations);
  }

  public function get_Staff_AttendanceData() {
    $from_date = date('Y-m-d', strtotime($_POST['from_date']));
    $to_date = date('Y-m-d', strtotime($_POST['to_date']));
    $selected_staff_type = $_POST['selected_staff_type'];

    $staff_status_type = $_POST['staff_status_type'];
    // $attendence_type = $_POST['attendence_type'];
    $data['dates'] = $this->_getDatesByRange($from_date, $to_date);
    // $staff = $this->staff_attendance->getStaffData($selected_staff_type);
    
    // $staff_names = array();
    // foreach ($staff as $stf) {
    //   $staff_names[$stf->id] = $stf->staff_name;
    // }

    $staff_ids = $this->_getStaffIdsByPrivilege($selected_staff_type,$staff_status_type);
    // $attendance = $this->staff_attendance->getAttendanceReportByDate($from_date, $to_date, $staff_ids);
    $attendance = $this->staff_attendance->getAttendanceReportByDateForCheckInReport($from_date, $to_date, $staff_ids, $selected_staff_type,$staff_status_type);

    $data['attendance'] = $attendance;

    // echo "<pre>"; print_r($data); die();
  
    // $staff_attendance = [];
    // foreach ($attendance as $i => $att) {
    //   if(!array_key_exists($att->staff_id, $staff_attendance)) {
    //     $staff_attendance[$att->staff_id] = array();
    //     $staff_attendance[$att->staff_id]['employee_code'] = $att->employee_code;
    //     $staff_attendance[$att->staff_id]['staff_name'] = $att->staff_name;
    //     $staff_attendance[$att->staff_id]['staff_id'] = $att->staff_id;
    //     $staff_attendance[$att->staff_id]['email'] = $att->email;
    //     $staff_attendance[$att->staff_id][$att->date] = $att;
    //   }

    //   $staff_attendance[$att->staff_id][$att->date] = $att;
    // }
  
    // $data['attendance'] = [];
    // foreach ($staff_attendance as $key => $value) {
    //   $data['attendance'][] = $value;
    // }

    echo json_encode($data);
  }

  public function delete_geofence(){
    echo $this->staff_attendance->delete_geofence($_POST);
  }

  public function edit_geofence(){
    echo $this->staff_attendance->edit_geofence($_POST);
  }

  public function getLeaveRegularizeReasons(){
    $data=$this->staff_attendance->getLeaveRegularizeReasons();
    echo json_encode($data);
  }

  public function update_mass_staff_attendance_status(){
    $response = $this->staff_attendance->update_mass_staff_attendance_status($_POST);
    echo json_encode($response);
  }

  public function get_checked_in_out_photo(){
    $staff_id = $this->authorization->getAvatarStakeHolderId();
    $event_type = $this->input->post('type');
    $attendance_id = $this->input->post('attendance_id');
    if($attendance_id == '' || $attendance_id <= 0 || $attendance_id == null){
      echo 0;
      return;
    }
    $result = '';
    if($event_type == 'checkIn'){
      $result = $this->staff_attendance->get_staff_cheched_in_photo($attendance_id, $staff_id);
    } else if($event_type == 'checkOut') {
      $result = $this->staff_attendance->get_staff_cheched_out_photo($attendance_id, $staff_id);
    } else {
      echo 0;
      return;
    }

    if($result != '' || $result != 0){
      $url = $this->filemanager->getFilePath(json_decode($result));
      echo json_encode($url);
    } else {
      echo 0;
      return;
    }
  }

  public function addAttendance_mobile_checkin() {

      if(!isset($_POST['input'])){
          echo json_encode(["error" => "Invalid input."]);
          return;
      }
      $input = json_decode($_POST['input'], true);

      if (!$input) {
          echo json_encode(["error" => "Invalid JSON in input."]);
          return;
      }
      
      if (!isset($_FILES['image']) || $_FILES['image']['error'] !== UPLOAD_ERR_OK) {
          echo json_encode(["error" => "Image upload failed."]);
          return;
      }
      
      $photo_url = $this->s3FileUpload($_FILES['image'], 'staff_attendance_face_recognition');
      if (!$photo_url || !isset($photo_url["file_name"])) {
          echo json_encode(["error" => "Image upload returned an invalid response."]);
          return;
      }
      
      $input['photo_url'] = $photo_url["file_name"];
      $input['source'] = 'Face-ID';
      $input['staff_id'] = $this->authorization->getAvatarStakeHolderId();
      if(!isset($_POST['tries'])){
          echo json_encode(["error" => "Invalid tries."]);
          return;
      }
      $input['tries'] = $_POST['tries'];

      // $input['descriptor'] = null;
      // if ($input['tries'] == '3' && isset($_POST['descriptors']) && empty($_POST['descriptors'])) {
      //     echo json_encode(["error" => "Descriptors not found."]);
      //     return;
      // } else {
      //   $data_array = json_decode($_POST['descriptors'], true);
      //   if (!is_array($data_array)) {
      //       echo json_encode(["error" => "Invalid JSON in descriptors."]);
      //       return;
      //   } else {
      //     $input['descriptor'] = array_values($data_array);
      //   }
      // }

      if (isset($_POST['descriptors']) && !empty($_POST['descriptors'])) {
          $data_array = json_decode($_POST['descriptors'], true);
          if (!$data_array) {
              echo json_encode(["error" => "Invalid JSON in descriptors."]);
              return;
          }
          $input['descriptor'] = array_values($data_array);
      } else {
          $input['descriptor'] = null;
      }

      $input['attempts_details_json'] = isset($_POST['attempts_details']) && !empty($_POST['attempts_details']) 
          ? $_POST['attempts_details'] 
          : null;
      
      $att_time = date('d-M h:i a');
      $curDate = date("Y-m-d");
      
      if (!isset($input['event_type']) || !in_array($input['event_type'], ['Check-in', 'Check-out'])) {
          echo json_encode(["error" => "Invalid event_type."]);
          return;
      }
      if ($input['event_type'] == 'Check-in') {
          if ($this->staff_attendance->is_already_checked_in($input['staff_id'], $curDate) == '1') {
              echo json_encode(["error" => "Already checked in."]);
              return;
          }
      }
      
      if ($input['event_type'] == 'Check-in') {
          $is_staff_attendance_auto_checkout_disabled = $this->settings->getSetting("disable_staff_attendance_auto_checkout");
          if ((int)$is_staff_attendance_auto_checkout_disabled === 0) {
              $this->staff_attendance->mark_previously_missed_checkout($input['staff_id']);
          }
      }

      //Save attendance
      $attendance_id = $this->staff_attendance->saveAttendanceTransaction($input);

      //Send notification
      if($attendance_id) {
        //send notification to attendance admin and reporting manager
        $this->load->model('role');
        $admins = $this->role->getStaffListByPrivilege('STAFF_ATTENDANCE', 'ADMIN');
        $rep_manager_id = $this->staff_attendance->getReportingManager($input['staff_id']);
        $staff_ids = $admins;
        if($rep_manager_id) {
          $staff_ids[] = $rep_manager_id;
        }

        if(!empty($staff_ids)) {
          if($_POST['tries'] == 3){
            $this->load->helper('texting_helper');
            $staff_data = $this->staff_attendance->getStaffName($input['staff_id']);
            $event = $input['event_type'];
            $staff_name = $staff_data->staff_name;
            $input_arr = array();
            $input_arr['staff_ids'] = $staff_ids;
            $input_arr['mode'] = 'notification';
            $input_arr['source'] = 'Attendance';
            $input_arr['message'] = "Attendance: Staff $staff_name $event On 3rd Attempt, Subjected For Approval.";
            $response = sendText($input_arr);
          }
          else{
            $this->load->helper('texting_helper');
            $staff_data = $this->staff_attendance->getStaffName($input['staff_id']);
            $event = $input['event_type'];
            $staff_name = $staff_data->staff_name;
            $input_arr = array();
            $input_arr['staff_ids'] = $staff_ids;
            $input_arr['mode'] = 'notification';
            $input_arr['source'] = 'Attendance';
            $input_arr['message'] = "Attendance: $event by $staff_name";
            $response = sendText($input_arr);
          }
        }
      }
      echo $attendance_id;
    }

}