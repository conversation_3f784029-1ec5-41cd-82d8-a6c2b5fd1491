<ul class="breadcrumb">
  <li><a href="<?php echo site_url('avatars');?>">Dashboard</a></li>
  <li><a href="<?php echo site_url('feesv2/fees_dashboard');?>">Fee Dashboard</a></li>
  <li>Assign Fees</li>
</ul>

<div class="col-md-12">
  <div class="card cd_border">

    <div class="card-header panel_heading_new_style_staff_border">
      <div class="row" style="margin: 0px">
        <div class="d-flex justify-content-between" style="width:100%;">
          <h3 class="card-title panel_title_new_style_staff">
          <a class="back_anchor" href="<?php echo site_url('feesv2/fees_dashboard');?>">
            <span class="fa fa-arrow-left"></span>
          </a> Assign Fees </h3>
          <div class="col-md-8 d-flex align-items-center justify-content-end" id="exportButtons">
            <ul class="panel-controls">
              <label style="font-weight: 600;"  class="checkbox-inline"><input style="width:20px;height: 16px;" type="checkbox" name="enable_rte_students" id="enable_rte_students"> Enable RTE Students</label>
            </ul>

            <form enctype="multipart/form-data" id="assign-form" action="<?php echo site_url('feesv2/fees_student_v2/fee_assign_to_student') ?>" class="form-horizontal" data-parsley-validate method="post">
              <ul class="panel-controls">
                <div class="form-group">
                  <label id="ftype" class="control-label col-md-5">Fee type</label>
                  <div class="col-md-7">
                    <select name="blueprint_id" onchange="this.form.submit();" id="blueprint" class="form-control">
                      <?php foreach ($fee_types as $key => $type) { ?>
                        <option <?php if($type->id == $selectedBP) echo 'selected' ?> value="<?php echo $type->id ?>"><?php echo $type->name ?></option>
                      <?php } ?>
                    </select>
                  </div>
                </div>
              </ul>
            </form>

            <ul class="panel-controls">
              <form enctype="multipart/form-data" id="std_assign-form" action="<?php echo site_url('feesv2/fees_student_v2/view_fee_assign_view') ?>" class="form-horizontal" data-parsley-validate method="post">
                <input type="hidden" name="blueprint_id" value="<?php echo $selectedBP ?>">
              </form>  
              <a class="btn btn-warning" onclick="view_assing_history()" href="javascript:void(0)">View details</a>
            </ul>

           

          </div>

        </div>
      </div>
    </div>

    <div class="card-body">
      <div class="col-md-3">
        <label for="filter">Select Filter</label>
        <select class="form-control" id="filter" name="filter">
          <option value="fee_type">Fee Type</option>
          <option value="custom">Custom</option>
        </select>
      </div>

      <div class="row" id="feeTypeShow">
        <?php if ($display_filters === FALSE) { ?>
        <h4>Fee Structure applies to all</h4>
        <?php } else { ?>
          <?php if (isset($showAcadJoining)) : ?>
            <div class="col-md-3">
              <label for="acadJoiningId"><?= $acadJoiningLabel ?></label>
              <select class="form-control" id="acadJoiningId" name="<?= $acadColumnName ?>">
                <?php foreach ($acadJoiningOptions as $key => $va) : ?>
                  <option value="<?= $key ?>"><?= $va ?></option>
                <?php endforeach ?>
              </select>
            </div>
          <?php endif ?>
          <?php if (isset($showRTE)) : ?>
            <div class="col-md-3">
              <label for="rteType"><?= $rteLabel ?></label>
              <select class="form-control" id="rteType" name="<?= $rteColumnName ?>">
                <?php foreach ($rteOptions as $key => $va) : ?>
                  <option value="<?= $key ?>"><?= $va ?></option>
                <?php endforeach ?>
              </select>
            </div>
          <?php endif ?>
          <?php if (isset($showquota)) : ?>
            <div class="col-md-3">
              <label for="quotaType"><?= $quotaLabel ?></label>
              <select class="form-control" id="quotaType" name="<?= $quotaColumnName ?>">
                <?php foreach ($quotaOptions as $key => $va) : ?>
                  <option value="<?= $key ?>"><?= $va ?></option>
                <?php endforeach ?>
              </select>
            </div>
          <?php endif ?>

        <?php if (isset($showClass)) : ?>
          <div class="col-md-3">
            <label for="classId"><?= $classLabel ?></label>
            <select class="form-control" id="classId" name="<?= $classColumnName ?>">
              <?php foreach ($classData as $key => $va) : ?>
                <option value="<?= $va->id ?>"><?= $va->class_name ?></option>
              <?php endforeach ?>
            </select>
          </div>
        <?php endif ?>
        <?php if (isset($showMedium)) : ?>
          <div class="col-md-3">
          <label for="medium"><?= $mediumLabel ?></label>
          <select class="form-control" id="medium" name="<?= $mediumColumnName ?>">
            <?php foreach ($mediumOptions as $key => $va) : ?>
              <option value="<?= $key ?>"><?= $va ?></option>
            <?php endforeach ?>
          </select>
          </div>
        <?php endif ?>
        <?php if (isset($showCategory)) : ?>
          <div class="col-md-3">
            <label for="category"><?= $categoryLabel ?></label>
            <select class="form-control" id="category" name="<?= $categoryColumnName ?>">
              <?php foreach ($categoryOptions as $key => $va) : ?>
                <option value="<?= $key ?>"><?= $va ?></option>
              <?php endforeach ?>
            </select>
          </div>
        <?php endif ?>
        <?php if (isset($showAdmissionType)) : ?>
          <div class="col-md-3">
            <label for="category"><?= $admissionTypeLabel ?></label>
            <select class="form-control" id="admissionType" name="<?= $admissionTypeColumnName ?>">
              <?php foreach ($admissionTypeOptions as $key => $va) : ?>
                <option value="<?= $key ?>"><?= $va ?></option>
              <?php endforeach ?>
            </select>
          </div>
        <?php endif ?>
        <?php if (isset($showBoards)) : ?>
          <div class="col-md-3">
            <label for="boards"><?= $boardsLabel ?></label>
            <select class="form-control" id="boards" name="<?= $boardsColumnName ?>">
              <?php foreach ($boardList as $key => $va) : ?>
                <option value="<?= $key ?>"><?= $va ?></option>
              <?php endforeach ?>
            </select>
          </div>
        <?php endif ?>
        <?php if (isset($showBoarding)) : ?>
          <div class="col-md-3">
            <label for="boarding"><?= $boardingLabel ?></label>
            <select class="form-control" id="boarding" name="<?= $boardingColumnName ?>">
              <?php foreach ($boardingOptions as $key => $va) : ?>
                <option value="<?= $key ?>"><?= $va ?></option>
              <?php endforeach ?>
            </select>
          </div>
        <?php endif ?>
        <?php if (isset($showStaff)) : ?>
          <div class="col-md-3">
            <label for="staff"><?= $staffLabel ?></label>
            <select class="form-control" id="staff" name="<?= $staffColumnName ?>">
              <?php foreach ($staffOptions as $key => $va) : ?>
                <option value="<?= $va ?>"><?= $key ?></option>
              <?php endforeach ?>
            </select>
          </div>
        <?php endif ?>
        <?php if (isset($showClassType)) : ?>
          <div class="col-md-3">
            <label for="classType"><?= $classTypeLabel ?></label>
            <select class="form-control" id="classType" name="<?= $classTypeName ?>">
              <?php foreach ($classTypeOptions as $key => $va) : ?>
                <option value="<?= $key ?>"><?= $va ?></option>
              <?php endforeach ?>
            </select>
          </div>
        <?php endif ?>
        <?php if (isset($showSibling)) : ?>
          <div class="col-md-3">
            <label for="sibling"><?= $siblingLabel ?></label>
            <select class="form-control" id="sibling" name="<?= $siblingColumnName ?>">
              <?php foreach ($siblingOptions as $key => $va) : ?>
                <option value="<?= $va ?>"><?= $key ?></option>
              <?php endforeach ?>
            </select>
          </div>
        <?php endif ?>
        <?php if (isset($showTransport)) : ?>
          <div class="col-md-3">
            <label for="sibling"><?= $transportLabel ?></label>
            <select class="form-control" id="transport" name="<?= $transportColumnName ?>">
              <?php foreach ($transportOptions as $key => $va) : ?>
                <option value="<?= $va ?>"><?= $key ?></option>
              <?php endforeach ?>
            </select>
          </div>
        <?php endif ?>
        <?php if (isset($showTransportKm)) : ?>
          <div class="col-md-3">
            <label for="sibling"><?= $TransportKmLabel ?></label>
            <select class="form-control" id="transport_km" name="<?= $TransportKmName ?>">
              <?php foreach ($TransportKmOptions as $key => $va) : ?>
                <option value="<?= $va->kilometer ?>"><?= $va->kilometer ?></option>
              <?php endforeach ?>
            </select>
          </div>
        <?php endif ?>
        <?php if (isset($showTransportStop)) : ?>
          <div class="col-md-3">
            <label for="sibling"><?= $TransportStopLabel ?></label>
            <select class="form-control" id="transstop" name="<?= $TransportStopName ?>">
              <?php foreach ($TransportStopOptions as $key => $va) : ?>
                <option value="<?= $va->name ?>"><?= $va->name ?></option>
              <?php endforeach ?>
            </select>
          </div>
        <?php endif ?>
        <?php if (isset($showTransportPM)) : ?>
          <div class="col-md-3">
            <label for="sibling"><?= $TransportPMLabel ?></label>
            <select class="form-control" id="transport_pic_mode" name="<?= $TransportPMName ?>">
              <?php foreach ($TransportPMOptions as $key => $va) : ?>
                <option value="<?= $key ?>"><?= $va ?></option>
              <?php endforeach ?>
            </select>
          </div>
        <?php endif ?>
        <?php if (isset($showGenderPM)) : ?>
          <div class="col-md-3">
            <label for="gender"><?= $GenderPMLabel ?></label>
            <select class="form-control" id="gender" name="<?= $GenderPMName ?>">
              <?php foreach ($GenderPMOptions as $key => $va) : ?>
                <option value="<?= $key ?>"><?= $va ?></option>
              <?php endforeach ?>
            </select>
          </div>
        <?php endif ?>
        <?php if (isset($showPhysicalDisabilityPM)) : ?>
          <div class="col-md-3">
          <label for="PhysicalDisability"><?= $PhysicalDisabilityPMLabel ?></label>
          <select class="form-control" id="PhysicalDisability" name="<?= $PhysicalDisabilityPMName ?>">
            <?php foreach ($PhysicalDisabilityPMOptions as $key => $va) : ?>
              <option value="<?= $key ?>"><?= $va ?></option>
            <?php endforeach ?>
          </select>
          </div>
        <?php endif ?>
        <?php if (isset($showIsLifeTimeFeePM)) : ?>
          <div class="col-md-3">
          <label for="IsLifeTimeFee"><?= $IsLifeTimeFeePMLabel ?></label>
          <select class="form-control" id="IsLifeTimeFee" name="<?= $IsLifeTimeFeePMName ?>">
            <?php foreach ($IsLifeTimeFeePMOptions as $key => $va) : ?>
              <option value="<?= $key ?>"><?= $va ?></option>
            <?php endforeach ?>
          </select>
          </div>
        <?php endif ?>
        <?php if (isset($showcombination)) : ?>
          <div class="col-md-3">
          <label for="classId"><?= $combinationLabel ?></label>
          <select class="form-control" id="combination" name="<?= $combinationColumnName ?>">
            <?php foreach ($combinationData as $key => $va) : ?>
              <option value="<?= $va->combination ?>"><?= $va->combination ?></option>
            <?php endforeach ?>
          </select>
          </div>
        <?php endif ?>

         <?php if (isset($showattempt)) : ?>
          <div class="col-md-3">
            <label for="attemptType"><?= $attemptLabel ?></label>
            <select class="form-control" id="attemptType" name="<?= $attemptColumnName ?>">
              <?php foreach ($attemptOptions as $key => $va) : ?>
                <option value="<?= $key ?>"><?= $va ?></option>
              <?php endforeach ?>
            </select>
          </div>
        <?php endif ?>
      <?php } ?>

      <div class="col-md-1 d-flex align-items-end">
        <button type="button" id="get" class="btn btn-primary" style="border-radius: .45rem;" onclick="getData()" >Search</button>
      </div>

      </div>
      <div class="row" id="customShow" style="display: none" >
        <div class="col-md-3">
          <label class="col-md-12" for="gradeView" style="font-size: 14px;">Class</label>
          <div class="col-md-12">
             <?php 
              $array = array();
              $array[0] = 'Select Class';
              foreach ($classList as $key => $cl) {
                  $array[$cl->classId] = $cl->className;
              }
              echo form_dropdown("classSectionId", $array, '', "id='classSectionId' multiple title='Select Class' class='form-control select' ");
            ?>
          </div>
        </div>

        <div class="col-md-3">
          <label class="col-md-12" for="gradeView">Student Name</label>
          <div class="col-md-12">
           <div class="row">
            <div class="col-md-10">
              <input id="stdName1" autocomplete="off" placeholder="Search by Student Name" class="form-control input-md" name="stdName1"> 
            </div>
            <div class="col-md-2">
              <input type="button" value="Get" id="getByStdName" class="input-md btn btn-primary">
            </div>
          </div>
          </div>
        </div>

        <div class="col-md-3">
          <label class="col-md-12" for="gradeView">Admission No.</label>
          <div class="col-md-12">
           <div class="row">
            <div class="col-md-10">
              <input id="admission_no" autocomplete="off" placeholder="Search by Admission No" class="form-control input-md" name="admission_no">
            </div>
            <div class="col-md-2">
              <input type="button" value="Get" id="getByAdmissionNo" class="input-md btn  btn-primary">
            </div>
          </div>
          </div>
        </div>
      </div>


      </div>
      
      <div class="card-body">
        <div class="col-12 text-center loading-icon" style="display: none;">
          <i class="fa fa-spinner fa-spin" style="font-size: 40px;"></i>
        </div>
        <div class="text-center"><div style="display: none;" class="progress" id="progress"><div id="progress-ind" class="progress-bar progress-bar-striped progress-bar-animated" role="progressbar" ariavaluenow="50" aria-valuemin="0" aria-valuemax="100" style="width: 50%"></div></div></div>
        <div class="text-right mb-3">
          <!-- <input type="button" onclick="fee_assinged()" id="assingButton"  class="btn btn-primary" value="Assign">
          <input type="button" onclick="fee_status_update_parent('Publish', 'publish_status', 'PUBLISHED')" id="publishButton" class="btn btn-warning status_click_Publish" value="Publish">
          <input type="button" onclick="fee_status_update_parent('UnPublish', 'publish_status', 'NOT_PUBLISHED')" id="un_publishButton"  class="btn btn-info status_click_UnPublish" value="Un-Publish">
          <input type="button" onclick="fee_status_update_parent('Online', 'online_payment', 'PUBLISHED')" id="onlineButton"  class="btn btn-danger status_click_Online" value="Online">
          <input type="button" onclick="fee_status_update_parent('Offline', 'online_payment', 'NOT_PUBLISHED')"  id="offlineButton" class="btn btn-primary status_click_Offline" value="Offline">
 -->
        </div>
        <div class="stdudentData">

          <h5>Select filter to get Student list</h5>
        </div>
        
      </div>
  </div>
</div>

<script type="text/javascript">

function view_assing_history() {
  $('#std_assign-form').submit();
}


$('#filter').on('change',function(){
  $('.stdudentData').html('');
  var filter = $('#filter').val();
  if (filter == 'fee_type') {
    // getData();
    $('#feeTypeShow').show();
    $('#customShow').hide();
  }else{
    $('#stdName1').val('');
    $('#admission_no').val('');
    $('#customShow').show();
    $('#feeTypeShow').hide();
  }
});

  var total_students = 0;
  var completed = 0;
  function getData() {
    $('.stdudentData').html('');
    total_students = 0;
    completed = 0;
    var acadJoiningId = $('#acadJoiningId').val();
    var rteType = $('#rteType').val();
    var quotaType = $('#quotaType').val();
    var classId = $('#classId').val();
    var medium =  $('#medium').val();
    var category = $('#category').val();
    var admissionType = $('#admissionType').val();
    var boards = $('#boards').val();
    var boarding = $('#boarding').val();
    var staff = $('#staff').val();
    var classType = $('#classType').val();
    var sibling = $('#sibling').val();
    var transport = $('#transport').val();
    var transport_km = $('#transport_km').val();
    var transstop = $('#transstop').val();
    var transport_pic_mode = $('#transport_pic_mode').val();
    var gender = $('#gender').val();
    var PhysicalDisability = $('#PhysicalDisability').val();
    var IsLifeTimeFee = $('#IsLifeTimeFee').val();
    var combination = $('#combination').val();
    var attemptType = $('#attemptType').val();
    if (sibling == undefined) {
      sibling ='';
    }
    if (transport == undefined) {
      transport ='';
    }
    if (transport_km == undefined) {
      transport_km ='';
    }
    if (transstop == undefined) {
      transstop ='';
    }
    if (transport_pic_mode == undefined) {
      transport_pic_mode ='';
    }
    if (gender == undefined) {
      gender ='';
    }
    if (PhysicalDisability == undefined) {
      PhysicalDisability ='';
    }
    if (IsLifeTimeFee == undefined) {
      IsLifeTimeFee ='';
    }
    if (combination == undefined) {
      combination ='';
    }
    if (attemptType == undefined) {
      attemptType ='';
    }
    if (acadJoiningId == undefined) {
      acadJoiningId ='';
    }
    if (rteType == undefined) {
      rteType ='';
    }
    if (quotaType == undefined) {
      quotaType ='';
    }
    if (classId == undefined) {
      classId ='';
    }
    if (medium == undefined) {
      medium ='';

    }if (category == undefined) {
      category ='';
    }
    if (admissionType == undefined) {
      admissionType ='';
    }
    if (boards == undefined) {
      boards ='';
    }
    if (boarding == undefined) {
      boarding ='';
    }
    if (staff == undefined) {
      staff ='';
    }
    if (classType == undefined) {
      classType ='';
    }
    $('.loading-icon').show();
    $.ajax({
      url: '<?php echo site_url('feesv2/fees_student_v2/get_fee_assing_student_ids'); ?>',
      type: 'post',
      data: {'acadJoiningId':acadJoiningId,'rteType':rteType,'quotaType':quotaType,'classId':classId,'medium':medium,'category':category,'admissionType':admissionType,'boards':boards,'boarding':boarding,'staff':staff,'classType':classType,'sibling':sibling,'transport':transport,'transport_km':transport_km,'transstop':transstop,'transport_pic_mode':transport_pic_mode,'gender':gender, 'PhysicalDisability':PhysicalDisability, 'IsLifeTimeFee':IsLifeTimeFee, 'combination':combination,'attemptType':attemptType},
      success: function(data) {
        $('.loading-icon').hide();
         var data = JSON.parse(data);
         if(data.length == 0){
          $("#progress").hide();

          $('.stdudentData').html('<h5>No Data Found</h5>');
          return false;
         }
        var students = data;
        studentIds = students;
        total_students = parseInt(100* (studentIds.length - 2)) + parseInt(studentIds[studentIds.length - 1].length);
        var progress = document.getElementById('progress-ind');
        progress.style.width = (completed/total_students)*100+'%';
        $("#progress").show();
        report_index(0);
      }
    });
  }

$('#classSectionId').on('change',function(){
  var seletced = $(this).val();
  if (seletced != null) {
    class_section_wise_student_data(seletced);
  }else{
    $('.stdudentData').html('');
  }
});
function class_section_wise_student_data(classSectionId){
  if(classSectionId) {
    $.ajax({
      url: '<?php echo site_url('feesv2/fees_student_v2/class_section_wise_student_data'); ?>',
      type: 'post',
      data: {'classSectionId':classSectionId},
      success: function(data) {
        $('.loading-icon').hide();
         var data = JSON.parse(data);
         if(data.length == 0){
          $("#progress").hide();

          $('.stdudentData').html('<h5>No Data Found</h5>');
          return false;
         }
        var students = data;
        studentIds = students;
        total_students = parseInt(100* (studentIds.length - 2)) + parseInt(studentIds[studentIds.length - 1].length);
        var progress = document.getElementById('progress-ind');
        progress.style.width = (completed/total_students)*100+'%';
        $("#progress").show();
        report_index(0);
      }
    });
  }
 }

$("#getByAdmissionNo").click(function(){
  getByAdmission();
});

function getByAdmission() {
  var admin_no = $("#admission_no").val();
  if(admin_no) {
    $.ajax({
      url: '<?php echo site_url('feesv2/fees_student_v2/get_admission_student_data'); ?>',
      type: 'post',
      data: {'admin_no':admin_no},
      success: function(data) {
        var data = JSON.parse(data);
        if(data.length == 0){
          $("#progress").hide();

          $('.stdudentData').html('<h5>No Data Found</h5>');
          return false;
         }
        var students = data;
        studentIds = students;
        total_students = parseInt(100* (studentIds.length - 2)) + parseInt(studentIds[studentIds.length - 1].length);
        var progress = document.getElementById('progress-ind');
        progress.style.width = (completed/total_students)*100+'%';
        $("#progress").show();
        report_index(0);
      }
    });
  }
}

  $("#stdName1").keydown(function(e) {
    if(e.keyCode == 13) {
      getByStdName();
    }
  });

  $("#getByStdName").click(function (){
    getByStdName();
  });

  function getByStdName() {
    var stdName = $("#stdName1").val();
    $.ajax({
        url: '<?php echo site_url('feesv2/fees_student_v2/search_student_fee'); ?>',
        type: 'post',
        data: {'stdName':stdName},
        success: function(data) {
          var data = JSON.parse(data);
          if(data.length == 0){
          $("#progress").hide();

          $('.stdudentData').html('<h5>No Data Found</h5>');
          return false;
         }
          var students = data;
          studentIds = students;
          total_students = parseInt(100* (studentIds.length - 2)) + parseInt(studentIds[studentIds.length - 1].length);
          var progress = document.getElementById('progress-ind');
          progress.style.width = (completed/total_students)*100+'%';
          $("#progress").show();
          report_index(0);
        }
    });
  }

  function report_index(index) {
    if(index < studentIds.length) {
      get_fee_report(index);
    }else{
      $("#progress").hide();
      $('.friendNameDisplay').show();
    }
  }
  function get_fee_report(index) {
    var student_ids = studentIds[index];
    var blueprint = $('#blueprint').val();
    $.ajax({
      url: '<?php echo site_url('feesv2/fees_student_v2/get_fee_amount_by_std'); ?>',
      type: 'post',
      data: {'student_ids':student_ids,'blueprint':blueprint},
      success: function(data) {
        var data = JSON.parse(data);
        if(data.length == 0){
          $("#progress").hide();
          $('.stdudentData').html('<h5>No Data Found</h5>');
          return false;
         }
        var stdData = data.stdArry
        var cohortsFilter = data.cohorts
        if (index == 0) {
          constructFeeHeader(stdData);
        }
         completed += Object.keys(stdData).length;
        var progress = document.getElementById('progress-ind');
        progress.style.width = (completed/total_students)*100+'%';

        prepare_student_table(stdData, index, cohortsFilter);

      }
    });

  }

  function constructFeeHeader(stdData) {
    var assingCount = 0;
    for(var len =0; len < stdData.length; len++){
      if (stdData[len].assigned_status != 1) {
         assingCount++;
      }
    }
    var fr = '<?php echo json_encode($friendly_name) ?>';
    var html ='';
    html +='<table id="fee_students" class="table table-bordered">';
    html +='<thead>';
    html +='<tr>';
    // html +='<th>#</th>';
    html +='<th rowspan="2" >Student Name</th>';
    html +='<th rowspan="2" >Admission No</th>';
    html +='<th rowspan="2" >Class Name</th>';
    html +='<th rowspan="2"><select class="form-control" onchange="cohort_friendly_name_data()" name="friendly_name" id="friendly_name">';
    html +='<option value="">Select Fee Filter</option>';
     var frdata = JSON.parse(fr);
    for(var m=0; m < frdata.length; m++){
      html +='<option value="'+frdata[m].id+'">'+frdata[m].friendly_name+'</option>';
    }
    html +='</select> <a onclick="add_custom_fees()" class="btn btn-info btn-sm" style="float: left;margin-top: 0.5rem;">Add Custom</a></th>';
    html +='<th rowspan="2" >Fee Amount</th>';

    html +='<th><input type="button" onclick="fee_assinged()" id="assingButton" disabled="true"  class="btn btn-primary" value="Assign"></th>';

    html +='<th><input type="button" onclick="fee_status_update_parent(\'Publish\', \'publish_status\', \'PUBLISHED\')" id="publishButton" disabled="true" class="btn btn-warning status_click_Publish" value="Publish"></th>';

    html +='<th><input type="button" onclick="fee_status_update_parent(\'UnPublish\', \'publish_status\', \'NOT_PUBLISHED\')" id="un_publishButton"  class="btn btn-info status_click_UnPublish" disabled="true" value="Un-Publish"></th>';

    html +='<th> <input type="button" onclick="fee_status_update_parent(\'Online\', \'online_payment\', \'PUBLISHED\')" id="onlineButton"  class="btn btn-danger status_click_Online" disabled="true" value="Online"></th>';

    html +='<th> <input type="button" onclick="fee_status_update_parent(\'Offline\', \'online_payment\', \'NOT_PUBLISHED\')"  id="offlineButton" class="btn btn-primary status_click_Offline" disabled="true" value="Offline"></th>';

    html +='</tr>';
    html +='<tr>';
    html +='<th>Assign <input type="checkbox" ) '+((assingCount == 0)?'disabled':'')+'  name="selectAll" value="assign" onclick="check_all(this, value)" id="assignAll" class="check"></th>';
    html +='<th>Publish <input type="checkbox" name="selectAll" value="publish" onclick="check_all(this, value)" id="publishAll" class="check"></th>';
    html +='<th>Un-Publish <input type="checkbox" name="selectAll" value="un-publish" onclick="check_all(this, value)" id="un_publishAll" class="check"></th>';
    html +='<th>Online <input type="checkbox" name="selectAll" value="online" onclick="check_all(this, value)" id="onlineAll" class="check"></th>';
    html +='<th>Offline <input type="checkbox" name="selectAll" value="offline" onclick="check_all(this, value)" id="offlineAll" class="check"></th>';
    html +='</tr>';

    html +='</thead>';
    html +='</table>';
    $('.stdudentData').html(html);
  }
  
  function prepare_student_table(std, index, cohortsFilter) {

    var isCheckedEnabledRTE = 0;
    if ($('#enable_rte_students').is(':checked')) {
      isCheckedEnabledRTE = 1;
    }    
    var srNo = index * 100;

    var html ='';
    var j = 0;
    html +='<tbody>';
    for(var i in std){
      var bg = '';
      var lableRTE = '';
      if (std[i].is_rte == 1) {
        bg = 'red';
        lableRTE = 'RTE';
      }

      if (std[i].is_rte == 3) {
        bg = 'red';
        lableRTE = 'Scholar-ship';
      }
     
      var assignedDisabled = '';
      if (std[i].assigned_status == 1) {
        assignedDisabled = 'disabled';
      }
      var filterDisabled= ''
      if (std[i].cohort_id == '') {
        filterDisabled = 'disabled';
      }
      html += '<tr style="color:'+bg+'">';
      // html += "<td>" + (j+1+srNo) + "</td>";
      html += "<td>" +std[i].student_name + "</td>";
      html += "<td>" + std[i].admission_no + "</td>";
      html += "<td>" + std[i].className + "</td>";
      html += "<td>";
      html += '<select class="form-control fee_filter" '+assignedDisabled+' name="fee_filter" onchange="filterwise_get_amount('+std[i].student_id+')" id="feeFilter_'+std[i].student_id+'">';
      html += '<option  value="">Select Filter</option>';
      for(var k=0; k<cohortsFilter.length; k++){
        var selected = '';
        if (std[i].cohort_id == cohortsFilter[k].id) {
          var selected = 'selected';
        }
        html += '<option '+selected+' value="'+cohortsFilter[k].id+'">'+cohortsFilter[k].friendly_name+'</option>';
      }
      html += '</select>';
      html += "</td>";
      html += "<td class='total_fee_amount' id=fee_total_"+std[i].student_id+" >"+std[i].total_fee+"</td>";
      if (std[i].fee_collect_status == 1) {
        html += "<td>Fee Started</td>";
       }else if(std[i].fee_collect_status == 2){
        html += "<td><a class='btn btn-danger reset_"+std[i].student_id+"' onclick='resetFeeStrucutre("+std[i].student_id+","+std[i].cohot_student_id+")''>Reset Structure</a></td>";
      }else{
        if (isCheckedEnabledRTE == 0 && (std[i].is_rte == 1 || std[i].is_rte == 3)) {
          html += "<td>"+lableRTE+"</td>";
        }else{
          html += "<td><input type='checkbox' "+filterDisabled+" id='assignStd_"+std[i].student_id+"' value="+std[i].student_id+" name='assign_fees[]' onclick='check_assignindividual()' class='assignCheck'></td>";
        }
      }
      
      if (std[i].assigned_status == 1 && std[i].publish_status == 0) {
        html += "<td><input type='checkbox'  onclick='check_publishIndividual()' name='publish_fees' value="+std[i].cohot_student_id+'_'+std[i].student_id+" class='publishCheck'></td>";
      }else if(std[i].assigned_status == 0){
         html += "<td>Fee Not Assigned</td>";
      }else{
        html += "<td>Published</td>";
      }

      if (std[i].publish_status == 1) {
        html += "<td><input type='checkbox'  onclick='check_un_publishIndividual()' name='publish_fees' value="+std[i].cohot_student_id+'_'+std[i].student_id+" class='un_publishCheck'></td>";
      }else if(std[i].assigned_status == 0){
         html += "<td>Fee Not Assigned</td>";
      }else{
        html += "<td>Not Published</td>";
      }

      if (std[i].assigned_status == 1 &&  std[i].online_payment == 0) {
        html += "<td><input type='checkbox' onclick='check_onlineIndividual()' name='online_fees' value="+std[i].cohot_student_id+'_'+std[i].student_id+" class='onlineCheck'></td>";
      }else if(std[i].assigned_status == 0) {
        html += "<td>Fee Not Assigned</td>";
      }else{
        html += "<td>Online</td>";
      }

      if (std[i].online_payment == 1) {
        html += "<td><input type='checkbox' onclick='check_offlineIndividual()' name='ofline_fees' value="+std[i].cohot_student_id+'_'+std[i].student_id+" class='offlineCheck'></td>";
      }else if(std[i].assigned_status == 0){
         html += "<td>Fee Not Assigned</td>";
      }else{
        html += "<td>Offline</td>";
      }

      html += "</tr>";
    }
    html +='</tbody>';
    
    html +='</table>';
    $('#fee_students').append(html);
    index++;
    report_index(index);
}

function add_custom_fees() {
 var blueprintId = $('#blueprint').val();
  assign_custom_fees(blueprintId);
}
function cohort_friendly_name_data() {
  var friendly_name = $('#friendly_name').val();
  $.ajax({
    url: '<?php echo site_url('feesv2/fees_student_v2/serach_student_fee_strucutre'); ?>',
    type: 'post',
    data: {'friendly_name':friendly_name},
    success: function(data) {
      var fee = JSON.parse(data);
      output ='<option value='+fee.id+' >'+fee.friendly_name+'</option>';

      $('.fee_filter:not([disabled])').html(output);

      if (fee.total_fee ==0) {
        $('.assignCheck').attr('disabled','disabled').prop('checked',false);
      }else{
        $(".total_fee_amount").html(fee.total_fee);
        $('.assignCheck').removeAttr('disabled');
      }
    }
 });
}

function filterwise_get_amount(stdId) {
  var friendly_name = $('#feeFilter_'+stdId).val();
  if (friendly_name == '') {
    $("#fee_total_"+stdId).html('Select Filter');
    $('#assignStd_'+stdId).attr('disabled','disabled').prop('checked',false);
    return false;
  }
   $('#assignStd_'+stdId).removeAttr('disabled');
  $.ajax({
    url: '<?php echo site_url('feesv2/fees_student_v2/serach_student_fee_strucutre'); ?>',
    type: 'post',
    data: {'friendly_name':friendly_name},
    success: function(data) {
      var fee = JSON.parse(data);
      console.log(fee);
      if (fee.total_fee ==0) {
        $('#assignStd_'+stdId).attr('disabled','disabled').prop('checked',false);
        $("#fee_total_"+stdId).html(fee.total_fee);
      }else{
        $("#fee_total_"+stdId).html(fee.total_fee);
        $('#assignStd_'+stdId).removeAttr('disabled');
      }
      
    }
  });
}


function check_assignindividual() {
  if ($("input[class='assignCheck']:checked").length > 0) {
      $('#publishAll').prop('disabled',true);
      $('#un_publishAll').prop('disabled',true);
      $('#onlineAll').prop('disabled',true);
      $('#offlineAll').prop('disabled',true);

      $("input[class='publishCheck']").prop('disabled',true);
      $("input[class='un_publishCheck']").prop('disabled',true);
      $("input[class='onlineCheck']").prop('disabled',true);
      $("input[class='offlineCheck']").prop('disabled',true);

  }else{
      $('#publishAll').prop('disabled',false);
      $('#un_publishAll').prop('disabled',false);
      $('#onlineAll').prop('disabled',false);
      $('#offlineAll').prop('disabled',false);
      $("input[class='publishCheck']").prop('disabled',false);
      $("input[class='un_publishCheck']").prop('disabled',false);
       $("input[class='onlineCheck']").prop('disabled',false);
       $("input[class='offlineCheck']").prop('disabled',false);
  }
  $('#assingButton').prop('disabled', $("input[class='assignCheck']:checked").length == 0);
}

function check_publishIndividual() {
   if ($("input[class='publishCheck']:checked").length > 0) {
    $('#assignAll').prop('disabled',true);
    $('#un_publishAll').prop('disabled',true);
    $('#onlineAll').prop('disabled',true);
    $('#offlineAll').prop('disabled',true);
    $("input[class='assignCheck']").prop('disabled',true);
    $("input[class='un_publishCheck']").prop('disabled',true);
    $("input[class='onlineCheck']").prop('disabled',true);
    $("input[class='offlineCheck']").prop('disabled',true);

  }else{
    $('#assignAll').prop('disabled',false);
    $('#un_publishAll').prop('disabled',false);
    $('#onlineAll').prop('disabled',false);
    $('#offlineAll').prop('disabled',false);
    $("input[class='assignCheck']").prop('disabled',false);
    $("input[class='un_publishCheck']").prop('disabled',false);
    $("input[class='onlineCheck']").prop('disabled',false);
    $("input[class='offlineCheck']").prop('disabled',false);
  }
   $('#publishButton').prop('disabled', $("input[class='publishCheck']:checked").length == 0);
}

function check_un_publishIndividual() {
   if ($("input[class='un_publishCheck']:checked").length > 0) {

    $('#publishAll').prop('disabled',true);
    $('#assignAll').prop('disabled',true);
    $('#onlineAll').prop('disabled',true);
    $('#offlineAll').prop('disabled',true);

    $("input[class='publishCheck']").prop('disabled',true);
    $("input[class='assignCheck']").prop('disabled',true);
    $("input[class='onlineCheck']").prop('disabled',true);
    $("input[class='offlineCheck']").prop('disabled',true);

  }else{
    $('#publishAll').prop('disabled',false);
    $('#assignAll').prop('disabled',false);
    $('#onlineAll').prop('disabled',false);
    $('#offlineAll').prop('disabled',false);
    $("input[class='publishCheck']").prop('disabled',false);
    $("input[class='assignCheck']").prop('disabled',false);
    $("input[class='onlineCheck']").prop('disabled',false);
    $("input[class='offlineCheck']").prop('disabled',false);
  }
  $('#un_publishButton').prop('disabled', $("input[class='un_publishCheck']:checked").length == 0);
}

function check_onlineIndividual() {
   if ($("input[class='onlineCheck']:checked").length > 0) {
      $('#publishAll').prop('disabled',true);
      $('#un_publishAll').prop('disabled',true);
      $('#assignAll').prop('disabled',true);
      $('#offlineAll').prop('disabled',true);

      $("input[class='publishCheck']").prop('disabled',true);
      $("input[class='un_publishCheck']").prop('disabled',true);
      $("input[class='assignCheck']").prop('disabled',true);
      $("input[class='offlineCheck']").prop('disabled',true);

  }else{
      $('#publishAll').prop('disabled',false);
      $('#un_publishAll').prop('disabled',false);
      $('#assignAll').prop('disabled',false);
      $('#offlineAll').prop('disabled',false);
      $("input[class='publishCheck']").prop('disabled',false);
      $("input[class='un_publishCheck']").prop('disabled',false);
      $("input[class='assignCheck']").prop('disabled',false);
      $("input[class='offlineCheck']").prop('disabled',false);
  }
  $('#onlineButton').prop('disabled', $("input[class='onlineCheck']:checked").length == 0);
}


function check_offlineIndividual() {
   if ($("input[class='offlineCheck']:checked").length > 0) {
      $('#publishAll').prop('disabled',true);
      $('#un_publishAll').prop('disabled',true);
      $('#assignAll').prop('disabled',true);
      $('#onlineAll').prop('disabled',true);
      $("input[class='publishCheck']").prop('disabled',true);
      $("input[class='un_publishCheck']").prop('disabled',true);
      $("input[class='assignCheck']").prop('disabled',true);
      $("input[class='onlineCheck']").prop('disabled',true);

  }else{
      $('#publishAll').prop('disabled',false);
      $('#un_publishAll').prop('disabled',false);
      $('#assignAll').prop('disabled',false);
      $('#onlineAll').prop('disabled',false);
      $("input[class='publishCheck']").prop('disabled',false);
      $("input[class='un_publishCheck']").prop('disabled',false);
      $("input[class='assignCheck']").prop('disabled',false);
      $("input[class='onlineCheck']").prop('disabled',false);
  }
  $('#offlineButton').prop('disabled', $("input[class='offlineCheck']:checked").length == 0);
}

  function check_all(check, value){
    if (value == 'assign') {
      if($(check).is(':checked')) {
        $('.assignCheck').not(':disabled').prop('checked',true);
        $('#publishAll').prop('disabled',true);
        $('#un_publishAll').prop('disabled',true);
        $('#onlineAll').prop('disabled',true);
        $('#offlineAll').prop('disabled',true);

        $('#assingButton').prop('disabled',false);
        $('#publishButton').prop('disabled',true);
        $('#un_publishButton').prop('disabled',true);
        $('#onlineButton').prop('disabled',true);
        $('#offlineButton').prop('disabled',true);

        $("input[class='publishCheck']").prop('disabled',true);
        $("input[class='un_publishCheck']").prop('disabled',true);
        $("input[class='onlineCheck']").prop('disabled',true);
        $("input[class='offlineCheck']").prop('disabled',true);

      }else{
        $('.assignCheck').prop('checked',false);
        $('#assignAll').prop('disabled',false);
        $('#publishAll').prop('disabled',false);
        $('#un_publishAll').prop('disabled',false);
        $('#onlineAll').prop('disabled',false);
        $('#offlineAll').prop('disabled',false);

        $('#assingButton').prop('disabled',true);
        $('#publishButton').prop('disabled',true);
        $('#un_publishButton').prop('disabled',true);
        $('#onlineButton').prop('disabled',true);
        $('#onlineButton').prop('disabled',true);
        $('#offlineButton').prop('disabled',true);

        $("input[class='publishCheck']").prop('disabled',false);
        $("input[class='un_publishCheck']").prop('disabled',false);
        $("input[class='onlineCheck']").prop('disabled',false);
        $("input[class='offlineCheck']").prop('disabled',false);
      }
    }

    if (value == 'publish') {
      if($(check).is(':checked')) {
        $('.publishCheck').prop('checked',true);

        $('#assignAll').prop('disabled',true);
        $('#onlineAll').prop('disabled',true);
        $('#offlineAll').prop('disabled',true);
        $('#un_publishAll').prop('disabled',true);

        $('#assingButton').prop('disabled',true);
        $('#publishButton').prop('disabled',false);
        $('#un_publishButton').prop('disabled',true);
        $('#onlineButton').prop('disabled',true);
        $('#offlineButton').prop('disabled',true);

        $("input[class='assignCheck']").prop('disabled',true);
        $("input[class='un_publishCheck']").prop('disabled',true);
        $("input[class='onlineCheck']").prop('disabled',true);
        $("input[class='offlineCheck']").prop('disabled',true);

      }else{
        $('.publishCheck').prop('checked',false);

        $('#assignAll').prop('disabled',false);
        $('#publishAll').prop('disabled',false);
        $('#un_publishAll').prop('disabled',false);
        $('#onlineAll').prop('disabled',false);
        $('#offlineAll').prop('disabled',false);

        $('#assingButton').prop('disabled',true);
        $('#publishButton').prop('disabled',true);
        $('#un_publishButton').prop('disabled',true);
        $('#onlineButton').prop('disabled',true);
        $('#offlineButton').prop('disabled',true);

        $("input[class='assignCheck']").prop('disabled',false);
        $("input[class='un_publishCheck']").prop('disabled',false);
        $("input[class='onlineCheck']").prop('disabled',false);
        $("input[class='offlineCheck']").prop('disabled',false);

      }
    }

    if (value == 'online') {
      if($(check).is(':checked')) {
        $('.onlineCheck').prop('checked',true);
        $('#assignAll').prop('disabled',true);
        $('#publishAll').prop('disabled',true);
        $('#offlineAll').prop('disabled',true);
        $('#assingButton').prop('disabled',true);
        $('#publishButton').prop('disabled',true);
        $('#onlineButton').prop('disabled',false);
        $('#un_publishAll').prop('disabled',true);
        $('#un_publishButton').prop('disabled',true);
        $('#offlineButton').prop('disabled',true);

        $("input[class='publishCheck']").prop('disabled',true);
        $("input[class='un_publishCheck']").prop('disabled',true);
        $("input[class='assignCheck']").prop('disabled',true);
        $("input[class='offlineCheck']").prop('disabled',true);

      }else{
        $('.onlineCheck').prop('checked',false);
        $('#un_publishAll').prop('disabled',false);
        $('#assignAll').prop('disabled',false);
        $('#publishAll').prop('disabled',false);
        $('#onlineAll').prop('disabled',false);
        $('#offlineAll').prop('disabled',false);
        $('#assingButton').prop('disabled',true);
        $('#publishButton').prop('disabled',true);
        $('#onlineButton').prop('disabled',true);
        $('#un_publishButton').prop('disabled',true);
        $('#offlineButton').prop('disabled',true);

        $("input[class='publishCheck']").prop('disabled',false);
        $("input[class='un_publishCheck']").prop('disabled',false);
        $("input[class='assignCheck']").prop('disabled',false);
        $("input[class='offlineCheck']").prop('disabled',false);
      }
    }

    if (value == 'offline') {
      if($(check).is(':checked')) {
        $('.offlineCheck').prop('checked',true);
        $('#assignAll').prop('disabled',true);
        $('#publishAll').prop('disabled',true);
        $('#onlineAll').prop('disabled',true);
        // $('#offlineAll').prop('disabled',true);
        $('#assingButton').prop('disabled',true);
        $('#publishButton').prop('disabled',true);
        $('#onlineButton').prop('disabled',true);
        $('#un_publishAll').prop('disabled',true);
        $('#un_publishButton').prop('disabled',true);
        $('#offlineButton').prop('disabled',false);

        $("input[class='publishCheck']").prop('disabled',true);
        $("input[class='un_publishCheck']").prop('disabled',true);
        $("input[class='assignCheck']").prop('disabled',true);
        $("input[class='onlineCheck']").prop('disabled',true);

      }else{
        $('.offlineCheck').prop('checked',false);
        $('#un_publishAll').prop('disabled',false);
        $('#assignAll').prop('disabled',false);
        $('#publishAll').prop('disabled',false);
        $('#onlineAll').prop('disabled',false);
        $('#offlineAll').prop('disabled',false);
        $('#assingButton').prop('disabled',true);
        $('#publishButton').prop('disabled',true);
        $('#onlineButton').prop('disabled',true);
        $('#un_publishButton').prop('disabled',true);
        $('#offlineButton').prop('disabled',true);

        $("input[class='publishCheck']").prop('disabled',false);
        $("input[class='un_publishCheck']").prop('disabled',false);
        $("input[class='assignCheck']").prop('disabled',false);
        $("input[class='onlineCheck']").prop('disabled',false);

      }
    }

    if (value == 'un-publish') {
      if($(check).is(':checked')) {
        $('.un_publishCheck').prop('checked',true);
        $('#assignAll').prop('disabled',true);
        $('#publishAll').prop('disabled',true);
        $('#offlineAll').prop('disabled',true);
        $('#assingButton').prop('disabled',true);
        $('#publishButton').prop('disabled',true);
        $('#onlineButton').prop('disabled',true);
        $('#un_publishButton').prop('disabled',false);
        $('#offlineButton').prop('disabled',true);
        $('#onlineAll').prop('disabled',true);

        $("input[class='publishCheck']").prop('disabled',true);
        $("input[class='assignCheck']").prop('disabled',true);
        $("input[class='onlineCheck']").prop('disabled',true);
        $("input[class='offlineCheck']").prop('disabled',true);

      }else{
        $('.un_publishCheck').prop('checked',false);
        $('.offlineCheck').prop('checked',false);
        $('#un_publishAll').prop('disabled',false);
        $('#assignAll').prop('disabled',false);
        $('#publishAll').prop('disabled',false);
        $('#onlineAll').prop('disabled',false);
        $('#offlineAll').prop('disabled',false);
        $('#assingButton').prop('disabled',true);
        $('#publishButton').prop('disabled',true);
        $('#onlineButton').prop('disabled',true);
        $('#un_publishButton').prop('disabled',true);
        $('#offlineButton').prop('disabled',true);

        $("input[class='publishCheck']").prop('disabled',false);
        $("input[class='assignCheck']").prop('disabled',false);
        $("input[class='onlineCheck']").prop('disabled',false);
        $("input[class='offlineCheck']").prop('disabled',false);

      }
    }
  }

function resetFeeStrucutre(stdId, cohort_student_id) {
  var blueprintId = $('#blueprint').val();
  $('.reset_'+stdId).attr('disabled','disabled').html('Please wait..')
  $.ajax({
    url: '<?php echo site_url('feesv2/fees_student_v2/reset_fee_structure'); ?>',
    type: 'post',
    data: {'cohort_student_id':cohort_student_id,'stdId':stdId,'blueprintId':blueprintId},
    success: function(data) {
      if (data == 1) {
        $('.reset_'+stdId).attr('disabled','disabled').html('Please wait..')
      }
    }
  });
}

  function fee_assinged() {
    var blueprintId = $('#blueprint').val();
    // var cohort_id = $('.fee_filter').val();   
    var stdIds = [];
    $('.assignCheck:checked').each(function(){
      stdIds.push($(this).val());
    });
    if (stdIds.length <= 0) {
      return false;
    }
    $('#assingButton').val('Please wait...').prop('disabled',true);

    var stdCohorts = {};
    for(var std in stdIds){
      var cohort_id = $('#feeFilter_'+stdIds[std]).val();
      stdCohorts[stdIds[std]] = cohort_id;
    }
    $.ajax({
      url: '<?php echo site_url('feesv2/fees_student_v2/assign_fee_v3_strucutre'); ?>',
      type: 'post',
      data: {'blueprintId':blueprintId,'stdCohorts':stdCohorts},
      success: function(data) {
        console.log(data);
        if (data == 1) {
          $(function(){
            new PNotify({
              title: 'Info',
              text:  'Assigned Successfully',
              type: 'info',
            });
          });
          location.reload();
        }
      }
    });
  }

function fee_status_update_parent(onclickname, column, value) {
  var filtertype = $('#filter').val();
  var cohortPublihsIds = [];
  var student_ids = [];
   if (onclickname == 'Publish') {
    $('.publishCheck:checked').each(function(){
      var split = $(this).val().split('_');
      cohortPublihsIds.push(split[0]);
      student_ids.push(split[1]);
    });

   }else if(onclickname == 'UnPublish'){
    $('.un_publishCheck:checked').each(function(){
      var split = $(this).val().split('_');
      cohortPublihsIds.push(split[0]);
      student_ids.push(split[1]);
    });

   }else if(onclickname == 'Online'){
    $('.onlineCheck:checked').each(function(){
      var split = $(this).val().split('_');
      cohortPublihsIds.push(split[0]);
      student_ids.push(split[1]);
    });

   }else if(onclickname == 'Offline'){
    $('.offlineCheck:checked').each(function(){
      var split = $(this).val().split('_');
      cohortPublihsIds.push(split[0]);
      student_ids.push(split[1]);
    });
   }
  if (cohortPublihsIds.length <= 0) {
    return false;
  }
  $('.status_click_'+onclickname).val('Please wait...');
  var blueprintId = $('#blueprint').val();

  $.ajax({
    url: '<?php echo site_url('feesv2/fees_student_v2/fee_update_cohort_student_status'); ?>',
    type: 'post',
    data: {'cohortPublihsIds':cohortPublihsIds, 'column':column,'value':value,'blueprintId':blueprintId,'student_ids':student_ids,'onclickname':onclickname},
    success: function(data) {
      if (data == 1) {
        $(function(){
          new PNotify({
            title: 'Info',
            text:  'Assigned Successfully',
            type: 'info',
          });
        });
        if (filtertype =='custom') {
          var selectedClassSection  = $('#classSectionId').val();
          class_section_wise_student_data(selectedClassSection);
          // getByAdmission();
          // getByStdName();
        }else{
          getData();
        }
        // location.reload();
      }
    }
  });

}

// Custom

function assign_custom_fees(blueprint_id) {
  $('#fsiId').html('');
  $('#blueprint_component').html('');
  $('#friendly_name_custom').val('');
  $('#custom_fee_modal').modal('hide');
  $.ajax({
    url: '<?php echo site_url('feesv2/fees_student_v2/fee_blueprint_component_details'); ?>',
    type: 'post',
    data: {'blueprint_id':blueprint_id},
    success: function(data) {
      var insType = JSON.parse(data);
      var options = "<option value=''>Select Installment</option>";
      for(var i in insType){
        options += '<option value="'+i+'">'+insType[i]+'</option>';
      }
      console.log(options);
      $('#fsiId').html(options);
      $('#custom_fee_modal').modal('show');
    }
  });
}

</script>


<div class="modal fade" id="custom_fee_modal" role="dialog" style="padding-right: 4px;">
  <div class="modal-dialog">
    <div class="modal-content" style="width:48%;margin: auto;border-radius: .75rem; margin-top:6%">
      <div class="modal-header" style="border-top-left-radius: .75rem;border-top-right-radius: .75rem;">
        <h4 class="modal-title">Add Custom Fees for <span id="blueprintName"></span></h4>
        <button type="button" class="close" data-dismiss="modal"><i class="fa fa-times" aria-hidden="true" style="color: #d80403;font-size: 21px;"></i></button>
      </div>
      <div class="modal-body">
        <form enctype="multipart/form-data" method="post" data-parsley-validate=""  class="form-horizontal" id="custom_fee_form" novalidate="">
          <div class="card-body px-0">

            <div class="form-group">
              <label class="control-label col-md-3">Installment Type <font color="red">*</font></label>
              <div class="col-md-9 pl-0">
                <select class="form-control" onchange="installmenType_comp()" name="installments_type" id="fsiId">
                  
                </select>
              </div>
            </div>
            <hr>
            <div class="form-group">
              <div class="col-md-9 col-md-offset-3 pl-0" style="overflow: scroll;">
                <div id="blueprint_component"></div>
              </div>
            </div>

            <div class="form-group">
             <label class="control-label col-md-3">Friendly Name <font color="red">*</font></label>
              <div class="col-md-9 pl-0">
                <input type="text" name="friendly_name_custom" id="friendly_name_custom" class="form-control">
              </div>
            </div>

            <div class="form-group">
              <a style="width: 10rem;" type="button" class="btn btn-primary pull-right" id="submit_custom_fee" onclick="submit_custom_fee()">Submit</a>
              <button class="btn btn-danger mr-1 pull-right" style="width: 10rem;" data-dismiss="modal">Cancel</button>
            </div>                   
          </div>
        </form>
      </div> 
    </div>
  </div>
</div>

<script type="text/javascript">
  function installmenType_comp() {
    var fsiId = $('#fsiId').val();
    var blueprint_id = $('#blueprint').val();
    $.ajax({
      url:'<?php echo site_url('feesv2/fees_cohorts/get_installment_wise_component') ?>',
      type:'post',
      data : {'fsiId':fsiId, 'blueprint_id':blueprint_id},
      success : function(data){
      var data = JSON.parse(data);
        $('#blueprint_component').html(fee_component_construct_table(data));
      }
    });
  }

   function fee_component_construct_table(component) {
    var html = '';
    var ins = 1;
    var t=1;
    for (var i = 0; i < component.length; i++) {
      html +='<table class="table table-bordered">';
      html +='<thead>';
      html += '<h3>'+component[i].installment_name+'</h3>';
      html +='</thead>';
      html +='<tbody>';
      var comp = 1;
      for (var j = 0; j < component[i].comp.length; j++) {
        html +='<tr>';
        html +='<td>'+component[i].comp[j].component_name+'</td><input type="hidden" name="feev2_blueprint_components_id[]" value="'+component[i].feev2_blueprint_installment_types_id+'_'+component[i].feev2_installment_id+'_'+component[i].comp[j].feev2_blueprint_component_id+'">';
        html +='<td><input type="text" name="comp_amount[]" id="clonedata'+ins+'_'+comp+'" autocomplete="off" onkeyup="installment_total_count('+component.length+', '+component[i].comp.length+')" class="form-control"></td>';
        html +='</tr>';
        comp++;
      }
      
      html +='</tbody>';
      html +='<tfoot>';
      html +='<tr>';
      html +='<th>Total</th>';
      html +='<th id="total'+t+'" class="total"></th>';
      html +='</tr>';
      html +='</tfoot>';
      html +='</table>';
      ins++;
      t++;
    }
    html +='<table class="table table-bordered">';
    html +='<tr>';
    html += '<th width="40%">Grand Total</th>';
    html += '<th><input type="text" readonly name="total_fee" id="grandtotal" class="form-control"></th>';
    html +='</tr>';
    html +='</table>';
    return html;
  }

  function installment_total_count(inslenth, complenth) {

    for (var i = 1; i <= inslenth; i++) {
      var total = 0;
      for (var c = 1; c <= complenth; c++) {
        $('#clonedata'+i+'_'+c).each(function() {
            total += +this.value;
        });
      }
      $('#total'+i).html(total);
    }

    var grandtotal = 0;
     $('.total').each(function() {
       grandtotal += parseInt( $(this).html());
    });
    $('#grandtotal').val(grandtotal);
  }

  function submit_custom_fee() {
    var fsiId = $('#fsiId').val();
    var friendly_name_custom = $('#friendly_name_custom').val();
    var grandtotal = $('#grandtotal').val();
    if (fsiId == '') {
      alert('Please Select Installment Type');
      return false;
    }
   
    if (grandtotal == '' && grandtotal == '0') {
      alert('Amount not entered');
      return false;
    }
    if (friendly_name_custom == '') {
      alert('Please Enter Friendly name');
      return false;
    }
    var form = $('#custom_fee_form')[0];
    var formData = new FormData(form);
    formData.append('blueprint_id', $("#blueprint").val());
    // $('#submit_custom_fee').val('Please wait ...').attr('disabled','disabled');
    $.ajax({
      url: '<?php echo site_url('feesv2/fees_student_v2/submit_custom_fee_structure'); ?>',
      type: 'post',
      data: formData,
      // async: false,
      processData: false,
      contentType: false,
      cache : false,
      success: function(data) {
        $('#fsiId').html('');
        $('#custom_fee_modal').modal('hide');
        $('#friendly_name').val('');
        custom_assign_return_cohort_friendly_name_data(data)
      }
    });
  }

  function custom_assign_return_cohort_friendly_name_data(friendly_name) {
    if (friendly_name == 'custom') {
      var blueprintId = $('#blueprint').val();
      assign_custom_fees(blueprintId);
      return false;
    }
    $.ajax({
      url: '<?php echo site_url('feesv2/fees_student_v2/serach_student_fee_strucutre'); ?>',
      type: 'post',
      data: {'friendly_name':friendly_name},
      success: function(data) {
        var fee = JSON.parse(data);
        output ='<option value='+fee.id+' >'+fee.friendly_name+'</option>';
        $('.fee_filter:not([disabled])').html(output);
        if (fee.total_fee ==0) {
          $('.assignCheck').attr('disabled','disabled').prop('checked',false);
        }else{
          $(".total_fee_amount").html(fee.total_fee);
          $('.assignCheck').removeAttr('disabled');
        }
      }
   });
  }
</script>