<?php
defined('BASEPATH') OR exit('No direct script access allowed');

class Question_bank extends CI_Controller {

    public function __construct() {
        parent::__construct();
        $this->load->model('academics/Question_bank_model');
    }

    public function index() {
        $data['classes'] = $this->Question_bank_model->getClassesForLms();
        $data['teachers'] = $this->Question_bank_model->getTeachers();
        $data['main_content'] = 'academics/question_bank/list';
        $this->load->view('inc/template', $data);
    }

    public function create() {
        $data['classes'] = $this->Question_bank_model->getClassesForLms();
        $data['learningOutcomes'] = $this->Question_bank_model->getLearningOutcomes();
        $data['main_content'] = 'academics/question_bank/create';
        $this->load->view('inc/template', $data);
    }

    public function check_duplicate() {
        $result = $this->Question_bank_model->check_question_duplicate();
        echo json_encode($result);
    }

    public function get_subjects_list(){
        $result = $this->Question_bank_model->get_subjects_list();
        echo json_encode($result);
    }

    public function get_subject_lessons(){
        $result=$this->Question_bank_model->get_subject_lessons($_POST);
        echo json_encode($result);
    }

    public function get_lesson_topics(){
        $result = $this->Question_bank_model->get_lesson_topics();
        echo json_encode($result);
    }

    public function get_questions_list() {
        $page = $this->input->post('page') ?: 1;
        $per_page = $this->input->post('per_page') ?: 25;
        $filters = $this->input->post();

        $result = $this->Question_bank_model->get_questions_list($page, $per_page, $filters);
        echo json_encode($result);
    }

    public function view($question_id) {
        $data['question'] = $this->Question_bank_model->get_question_details($question_id);
        if (!$data['question']) {
            show_404();
        }
        $data['main_content'] = 'academics/question_bank/view';
        $this->load->view('inc/template', $data);
    }

    public function edit($question_id) {
        $data['question'] = $this->Question_bank_model->get_question_details($question_id);
        if (!$data['question']) {
            show_404();
        }
        $data['classes'] = $this->Question_bank_model->getClassesForLms();
        $data['learningOutcomes'] = $this->Question_bank_model->getLearningOutcomes();
        $data['main_content'] = 'academics/question_bank/edit';
        $this->load->view('inc/template', $data);
    }

    public function duplicate($question_id) {
        $original_question = $this->Question_bank_model->get_question_details($question_id);
        if (!$original_question) {
            show_404();
        }

        $data['question'] = $original_question;
        $data['is_duplicate'] = true;
        $data['classes'] = $this->Question_bank_model->getClassesForLms();
        $data['learningOutcomes'] = $this->Question_bank_model->getLearningOutcomes();
        $data['main_content'] = 'academics/question_bank/index';
        $this->load->view('inc/template', $data);
    }

    public function delete($question_id) {
        $result = $this->Question_bank_model->delete_question($question_id);
        echo json_encode($result);
    }

    public function save() {
        try {
            // Collect form data
            $question_data = $this->collect_question_data();

            // Validate data
            $validation_result = $this->validate_question_data($question_data);
            if (!$validation_result['success']) {
                echo json_encode($validation_result);
                return;
            }

            // Save question
            $question_id = $this->Question_bank_model->save_question($question_data);

            if ($question_id) {
                echo json_encode([
                    'success' => true,
                    'message' => 'Question saved successfully',
                    'question_id' => $question_id
                ]);
            } else {
                echo json_encode([
                    'success' => false,
                    'message' => 'Failed to save question'
                ]);
            }
        } catch (Exception $e) {
            echo json_encode([
                'success' => false,
                'message' => 'Error: ' . $e->getMessage()
            ]);
        }
    }

    private function collect_question_data() {
        // Debug: Log all POST data
        log_message('debug', 'POST data received: ' . print_r($this->input->post(), true));
        
        $data = [
            'question_text' => $this->input->post('question_text'),
            'question_type' => $this->input->post('question_type'),
            'question_image' => $this->input->post('question_image'),
            'passage_text' => $this->input->post('passage_text'),
            'class_master_id' => $this->input->post('class_id'),
            'subject_master_id' => $this->input->post('subject_master_id'),
            'lesson_name' => $this->input->post('lesson_name'),
            'topic_name' => $this->input->post('topic_name'),
            'learning_outcome_id' => $this->input->post('learning_outcome'),
            'difficulty_level' => $this->input->post('difficulty_level'),
            'max_marks' => $this->input->post('max_marks') ? $this->input->post('max_marks') : 1,
            'negative_marks' => $this->input->post('negative_marks') ? $this->input->post('negative_marks') : 0,
            'status' => $this->input->post('action') == 'submit' ? 'pending_review' : 'draft',
            'blooms' => $this->input->post('blooms'),
            'skills' => $this->input->post('skills'),
            'tags' => $this->input->post('tags'),
            // 'reusable_classes' => $this->input->post('reusable_classes'),
            'hints' => $this->input->post('hints'),
            // Structured answer data
            'mcq_data' => $this->input->post('mcq_data'),
            'comprehension_data' => $this->input->post('comprehension_data'),
            'match_data' => $this->input->post('match_data'),
            'fill_blanks_data' => $this->input->post('fill_blanks_data'),
            'blank_answer' => $this->input->post('blank_answer'), // For backward compatibility
            'tf_data' => $this->input->post('tf_data'),
            'short_answer_data' => $this->input->post('short_answer_data'),
            'long_answer_data' => $this->input->post('long_answer_data'),
            'custom_answer_data' => $this->input->post('custom_answer_data'),
            // Legacy fields for backward compatibility
            'tf_answer' => $this->input->post('tf_answer'),
            'short_answers' => $this->input->post('short_answer'),
            'long_answers' => $this->input->post('long_answer'),
            'custom_answer' => $this->input->post('custom_answer')
        ];

        // Debug: Log collected data
        log_message('debug', 'Collected question data: ' . print_r($data, true));

        return $data;
    }

    private function validate_question_data($data) {
        $errors = [];

        if (empty($data['question_text'])) {
            $errors[] = 'Question text is required';
        }

        if (empty($data['question_type'])) {
            $errors[] = 'Question type is required';
        }

        if (empty($data['class_master_id'])) {
            $errors[] = 'Class is required';
        }

        if (empty($data['subject_master_id'])) {
            $errors[] = 'Subject is required';
        }

        if (!empty($errors)) {
            return [
                'success' => false,
                'message' => 'Validation failed',
                'errors' => $errors
            ];
        }

        return ['success' => true];
    }
}