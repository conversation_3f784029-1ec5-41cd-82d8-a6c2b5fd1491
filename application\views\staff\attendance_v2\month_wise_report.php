<ul class="breadcrumb">
  <li><a href="<?php echo site_url('dashboard');?>">Dashboard</a></li>
  <li><a href="<?php echo site_url('staff/attendance');?>">Staff Attendance</a></li>
  <li>Month wise summary report</li>
</ul>

<div class="col-md-12">
  	<div class="card cd_border">
	    <div class="card-header panel_heading_new_style_staff_border">
	      <div class="row" style="margin: 0px;">
	        <div class="d-flex justify-content-between" style="width:100%;">
	          <h3 class="card-title panel_title_new_style_staff">
	            <a class="back_anchor" href="<?php echo site_url('staff/attendance'); ?>">
	              <span class="fa fa-arrow-left"></span>
	            </a> 
	            Month wise summary report
	          </h3>   
	        </div>
	      </div>
	    </div>
    	<div class="card-body pt-1">
	      	<div class="row mb-5">
		        <div class="col-md-3 form-group">
		          	<label for="fromdateId" class="control-label">From Month</label>
		          	<div class="input-group date" id="start_date_picker"> 
		                <input value="<?php echo date('m-Y'); ?>" input placeholder="From Month" required="" type="text"  class="form-control" id="fromdateId" name="from_date" >
		                <span class="input-group-addon">
		                    <span class="glyphicon glyphicon-calendar"></span>
		                </span>
		            </div>
		        </div>
		        <div class="col-md-3 form-group">
		          	<label for="todateId" class="control-label">To Month</label>
		          	<div class="input-group date" id="end_date_picker"> 
		               	<input required=""  autocomplete="off" type="text" input placeholder="From Month" value="<?php echo date('m-Y'); ?>" class="form-control " id="todateId" name="to_date">
		                <span class="input-group-addon">
		                	<span class="glyphicon glyphicon-calendar"></span>
		                </span>
		            </div>
		        </div>

				<div class="col-md-3 form-group">
                    <label class="control-label">Staff Status Type</label>
                    <select class="form-control" name="staff_status_type" id="staff_status_type">
                        	<option value="all">All</option>
                            <option value='2'>Approved</option>
                            <option value='4'>Resigned</option>
                    </select>
                </div>

				<div class="col-md-3 form-group">
                    <label class="control-label">Staff Type</label>
                    <select title="All" class="form-control select" name="staff_type[]" id="staff_type" onChange="hideDataArea()" multiple>
                        <!-- <option value="all">All</option> -->
						<?php if(!empty($staff_types)){
							foreach($staff_types as $key => $val){
								echo "<option value='$key'>$val</option>";
							}
						}else{
							echo "<option value='2'>found no staff types</option>";
						} ?>
                    </select>
                </div>

		        <div class="col-md-3 form-group pt-3">
		          	<button class="btn btn-primary mt-3" onclick="getAttendanceData()">Get Report</button>
		        </div>
	      	</div>
			
			<div id="attendance-data">
				<div style="color:red;text-align:center;
				color: black;
				border: 2px solid #fffafa;
				text-align: center;
				border-radius: 6px;
				position: relative;
				margin-left: 14px;
				padding: 10px;
				font-size: 14px;
				margin-top: 14px;
				background: #ebf3ff;
				">
					Click Get Report to fetch details!
				</div>
			</div>
    	</div>
  	</div>
	  
</div>


<style>
  /* styles over here */
  .modal {
    overflow-y:auto;
  }
  
  .modal-dialog{
    margin: 4% auto;
    width: 80%;
  }
  
  .modal-header{
    position:relative;
  }

  .close{
    font-size: 34px;
    color: red;
    position: absolute;
    right: 10px;
  }

  tr:hover{
    background: #F1EFEF;
  }

  .row_background_color
  {
    background:#7f848780;
  }

  .dt-buttons{
    font-size: 14px;
    background:"red";
  }

  td>a>i{
		text-decoration: none;
		font-size: 16px;
		color: #191818;
		padding: 2px 5px;
	}

	.dataTables_wrapper .dt-buttons {
		float: right;
	}

	.dataTables_filter input {
		background-color: #f2f2f2;
		border: 1px solid #ccc;
		border-radius: 4px;
		margin-right: 5px;
	}
  
	.dataTables_wrapper .dataTables_filter {
		float: right;
		text-align: left;
		width: unset;
	}
</style>

<script src="https://cdnjs.cloudflare.com/ajax/libs/xlsx/0.16.9/xlsx.full.min.js" integrity="sha512-wBcFatf7yQavHQWtf4ZEjvtVz4XkYISO96hzvejfh18tn3OrJ3sPBppH0B6q/1SHB4OKHaNNUKqOmsiTGlOM/g==" crossorigin="anonymous"></script>
<script src="https://cdnjs.cloudflare.com/ajax/libs/FileSaver.js/2.0.0/FileSaver.min.js" integrity="sha512-csNcFYJniKjJxRWRV1R7fvnXrycHP6qDR21mgz1ZP55xY5d+aHLfo9/FcGDQLfn2IfngbAHd8LdfsagcCqgTcQ==" crossorigin="anonymous"></script>
<script type="text/javascript">
var headers = [];
var json_data = [];
$(document).ready(function() {

	var maxAttendenceDate = new Date();
  maxAttendenceDate.setDate(maxAttendenceDate.getDate());
  var minAttendenceDate = new Date();
  minAttendenceDate.setFullYear( minAttendenceDate.getFullYear() - 2);

	$('#start_date_picker,#end_date_picker').datetimepicker({
      viewMode: 'months',
      format: 'MM-YYYY',
      maxDate: maxAttendenceDate,
      minDate: minAttendenceDate
  });

});

function hideDataArea(){
	$("#attendance-data").html('');
}

function constructHeader(dates) {
	headers = ['Sl', 'Staff', 'Joining', 'Exit','Status'];
	var html = '';
	html += '<thead>';
	html += '<tr>';
	html += '<th rowspan="2" width="3%">#</th>';
	html += '<th rowspan="2" width="15%">Staff Code</th>';
	html += '<th rowspan="2" width="15%">Staff</th>';
	html += '<th rowspan="2" width="10%">Joining Date</th>';
	html += '<th rowspan="2" width="10%">Exit date</th>';
	html += '<th rowspan="2" width="7%">Status</th>';
	html += '<th rowspan="2" width="7%">Staff Type</th>';
	html += '<th rowspan="2" width="7%">Department</th>';
	var count = 0
	for(var i in dates) {
		count += 1;
		headers.push(dates[i].format);
		html += '<th colspan="5" style="text-align: center;">'+dates[i].format+'</th>';
	}
	html += '</tr>';
	html += '<tr>';
	for(j=1;j<=count;j++){
		
		html += '<th>Working Days</th>';
		html += '<th>Present FD</th>';
		html += '<th>Present HD</th>';
		html += '<th>Absent</th>';
		html += '<th>Percentage</th>';
		
	}
	html += '</tr>';
	
	html += '</thead>';
	return html;
}

function constructReport(dates, attendance) {
	json_data = [];
	var html = '<tbody>';
	var j = 1;

	
	for(var i in attendance) {
	
		html += `<tr data-toggle='tooltip' data-original-title='${attendance[i].staff_name}'>`;
		html += '<td>'+(j++)+'</td>';
		html += '<td>'+attendance[i].employee_code+'</td>';
		html += '<td>'+attendance[i].staff_name+'</td>';
		html += '<td>'+attendance[i].joining_date+'</td>';
		html += '<td>'+attendance[i].last_date_of_work+'</td>';
		html += '<td>'+attendance[i].staff_status+'</td>';
		html += `<td>${attendance[i].staff_type || "NA"}</td>`;
		html += `<td>${attendance[i].staff_department || "NA"}</td>`;
		var month_attendance = attendance[i];
		for(var k in dates) {
			// Check if month data exists and has shift_exists property
			if(month_attendance[dates[k].format] && month_attendance[dates[k].format]['shift_exists'] == 1) {
				// var working_days = dates[k].days - month_attendance[dates[k].format]['WO'] - month_attendance[dates[k].format]['H'];
				var working_days=month_attendance[dates[k].format]['total_working_days'] || 0;
				var presents = +(month_attendance[dates[k].format]['P'] || 0) + (+(month_attendance[dates[k].format]['HD'] || 0) * 0.5);
				var percentage = working_days > 0 ? ((presents/working_days*100)).toFixed(2) : 0;
				html += '<td>'+(working_days)+'</td>';
				html += '<td>'+(month_attendance[dates[k].format]['P'] || 0)+'</td>';
				html += '<td>'+(month_attendance[dates[k].format]['HD'] || 0)+'</td>';
				html += '<td>'+(month_attendance[dates[k].format]['AB'] || 0)+'</td>';
				if(isNaN(percentage) || percentage == 0){
					html += '<td>0.00 %</td>';
				}else{
					html += '<td>'+percentage+' %</td>';
				}
			} else {
				html += '<td>-</td>';
				html += '<td>-</td>';
				html += '<td>-</td>';
				html += '<td>-</td>';
				html += '<td>-</td>';
			}
		}
		html += '</tr>';
	}
	
	html += '</tbody>';
	return html;
}

// function printProfile(){
//       var restorepage = document.body.innerHTML;
//       var printcontent = document.getElementById('month_wise_report').innerHTML;
//       document.body.innerHTML = printcontent;
//       window.print();
//       document.body.innerHTML = restorepage;
//   }

function getAttendanceData() {
	 const msg = `
            <div style="color:red;text-align:center;
              color: black;
              border: 2px solid #fffafa;
              text-align: center;
              border-radius: 6px;
              position: relative;
              margin-left: 14px;
              padding: 10px;
              font-size: 14px;
              margin-top: 14px;
			  background: #ebf3ff;
			  ">
                Loading...
            </div>`;

	$("#attendance-data").html(msg);

	var from_date = $("#fromdateId").val();
	var to_date = $("#todateId").val();
	var staff_status_type = $("#staff_status_type").val();
	var staff_type = $("#staff_type").val();

	$.ajax({
        url: '<?php echo site_url('staff/attendance/getMonthWiseAttendance'); ?>',
        type: 'post',
        data: {'from_date':from_date, 'to_date': to_date,'staff_status_type':staff_status_type,"staff_type":staff_type},
        success: function(data) {
            if(data != 0) {
                var response = JSON.parse(data);

                // Check if chunked processing is needed
                if(response.chunked_processing) {
                    processChunkedMonthWiseData(response);
                } else {
                    // Original processing for smaller date ranges
                    if(!Object.entries(response)?.length){
                        let msg = `
                        <div style="color:red;text-align:center;
                            color: black;
                            border: 2px solid #fffafa;
                            text-align: center;
                            border-radius: 6px;
                            position: relative;
                            margin-left: 14px;
                            padding: 10px;
                            font-size: 14px;
                            margin-top: 14px;
                            background: #ebf3ff;">
                            No Data to show
                            </div>
                        `;
                        return $("#attendance-data").html(msg);
                    }

                    displayMonthWiseReport(response.dates, response.attendance);
                }
            } else {
                let msg = `
                <div style="color:red;text-align:center;
                    color: black;
                    border: 2px solid #fffafa;
                    text-align: center;
                    border-radius: 6px;
                    position: relative;
                    margin-left: 14px;
                    padding: 10px;
                    font-size: 14px;
                    margin-top: 14px;
                    background: #ebf3ff;">
                    No Data to show
                    </div>
                `;
                $("#attendance-data").html(msg);
            }
        },
        error: function (err) {
		    console.log(err);
		}
    });
}

// Global variables for chunked processing
var chunkedMonthWiseData = [];
var chunkedMonthWiseDates = [];
var chunkedMonthWiseFilters = {};

function processChunkedMonthWiseData(response) {
	chunkedMonthWiseDates = [];
	chunkedMonthWiseFilters = response.filters;
	chunkedMonthWiseData = [];

	var totalChunks = response.total_date_chunks * response.total_staff_chunks;
	var processedChunks = 0;

	// Show progress indicator
	showChunkedMonthWiseProgress(0, totalChunks);

	// Process each date chunk with each staff chunk
	var chunkPromises = [];

	for(var dateChunkIndex = 0; dateChunkIndex < response.date_chunks.length; dateChunkIndex++) {
		for(var staffChunkIndex = 0; staffChunkIndex < response.staff_chunks.length; staffChunkIndex++) {
			var dateChunk = response.date_chunks[dateChunkIndex];
			var staffChunk = response.staff_chunks[staffChunkIndex];

			var chunkPromise = processMonthWiseAttendanceChunk(
				dateChunk.from_date,
				dateChunk.to_date,
				staffChunk,
				chunkedMonthWiseFilters.staff_status_type,
				chunkedMonthWiseFilters.staff_type,
				processedChunks,
				totalChunks,
				response.from_date,
				response.to_date,
				response.month_count
			).then(function(chunkData) {
				processedChunks++;
				updateChunkedMonthWiseProgress(processedChunks, totalChunks);

				if(chunkData && chunkData.attendance_data) {
					// Merge chunk data with existing data
					mergeMonthWiseChunkData(chunkData.attendance_data, chunkData.staff_names);
				}

				// If all chunks processed, display final result
				if(processedChunks >= totalChunks) {
					finalizeChunkedMonthWiseReport(response.from_date, response.to_date, response.month_count);
				}
			});

			chunkPromises.push(chunkPromise);
		}
	}
}

function processMonthWiseAttendanceChunk(chunk_from_date, chunk_to_date, staff_ids, staff_status_type, staff_type, chunk_index, total_chunks, original_from_date, original_to_date, month_count) {
	return new Promise(function(resolve, reject) {
		$.ajax({
			url: '<?php echo site_url('staff/attendance/processMonthWiseAttendanceChunk'); ?>',
			type: 'post',
			data: {
				'chunk_from_date': chunk_from_date,
				'chunk_to_date': chunk_to_date,
				'staff_ids': staff_ids,
				'staff_status_type': staff_status_type,
				'staff_type': staff_type,
				'chunk_index': chunk_index,
				'total_chunks': total_chunks
			},
			success: function(data) {
				try {
					var chunkResponse = JSON.parse(data);
					if(chunkResponse.success) {
						resolve(chunkResponse);
					} else {
						console.error('Month-wise chunk processing failed:', chunkResponse.error);
						resolve(null);
					}
				} catch(e) {
					console.error('Error parsing month-wise chunk response:', e);
					resolve(null);
				}
			},
			error: function(err) {
				console.error('Month-wise chunk request failed:', err);
				resolve(null);
			}
		});
	});
}

function mergeMonthWiseChunkData(chunkAttendanceData, chunkStaffNames) {
	// Store chunk data for later processing
	chunkedMonthWiseData.push({
		attendance: chunkAttendanceData,
		staff_names: chunkStaffNames
	});
}

function showChunkedMonthWiseProgress(processed, total) {
	var progressHtml = `
		<div class="text-center" id="chunked-monthwise-progress">
			<div class="mb-3">
				<i class="fa fa-spinner fa-spin" style="font-size: 2rem;"></i>
			</div>
			<div class="progress" style="height: 25px;">
				<div class="progress-bar progress-bar-striped progress-bar-animated"
					 role="progressbar" style="width: 0%" id="chunk-monthwise-progress-bar">
					0%
				</div>
			</div>
			<p class="mt-2">
				<small>Processed <span id="processed-monthwise-chunks">0</span> of <span id="total-monthwise-chunks">${total}</span> chunks</small>
			</p>
		</div>
	`;
	$("#attendance-data").html(progressHtml);
}

function updateChunkedMonthWiseProgress(processed, total) {
	var percentage = Math.round((processed / total) * 100);
	$("#chunk-monthwise-progress-bar").css('width', percentage + '%').text(percentage + '%');
	$("#processed-monthwise-chunks").text(processed);
}

function finalizeChunkedMonthWiseReport(from_date, to_date, month_count) {
	// Process all collected chunk data to create final month-wise report
	var finalAttendanceData = processChunkedMonthWiseAttendance(chunkedMonthWiseData, from_date, to_date, month_count);

	if(finalAttendanceData.attendance.length == 0) {
		let msg = `
		<div style="color:red;text-align:center;
			color: black;
			border: 2px solid #fffafa;
			text-align: center;
			border-radius: 6px;
			position: relative;
			margin-left: 14px;
			padding: 10px;
			font-size: 14px;
			margin-top: 14px;
			background: #ebf3ff;">
			No Data to show
			</div>
		`;
		$("#attendance-data").html(msg);
	} else {
		displayMonthWiseReport(finalAttendanceData.dates, finalAttendanceData.attendance);
	}
}

function displayMonthWiseReport(dates, attendance) {
	// Sort attendance data by staff name for consistent ordering
	attendance.sort(function(a, b) {
		var nameA = (a.staff_name || '').toLowerCase();
		var nameB = (b.staff_name || '').toLowerCase();
		return nameA.localeCompare(nameB);
	});

	var html = '<div class="pull-right">';
	html += '</div>';
	html +='<div id="month_wise_report" class="table-responsive">';
	html +='<table id="monthly_report" class="table table-bordered table-striped" style="width: 100%;white-space: nowrap;">';
	html += constructHeader(dates);
	html += constructReport(dates, attendance);
	html +='</table>';
	html +='</div>';

	$("#attendance-data").html(html);

	const reportName=`staff_attendance_month_wise_summary_report_${new Date().toLocaleString('default', { month: 'short' })+" "+new Date().getDate()+" "+new Date().getFullYear()}_${new Date().getHours()+""+new Date().getMinutes()}.xls`;

	$('#monthly_report').DataTable( {
		"language": {
			"search": "",
			"searchPlaceholder": "Enter Search..."
		},
		"lengthMenu": [ [10, 25, 50, -1], [10, 25, 50, "All"] ],
		"pageLength": 10,
		dom: 'lBfrtip',
		buttons: [
			{
			extend: 'excelHtml5',
			text: 'Excel',
			filename: reportName,
			className: 'btn btn-info'
			},
			{
			extend: 'csvHtml5',
			text: 'CSV',
			filename: reportName,
			className: 'btn btn-info'
			},
			{
			extend: 'pdfHtml5',
			text: 'PDF',
			filename: reportName,
			className: 'btn btn-info'
			}
		]
	});
}

function processChunkedMonthWiseAttendance(chunkedData, from_date, to_date, month_count) {
	// Combine all attendance data from chunks
	var allAttendanceData = [];
	var allStaffNames = {};

	chunkedData.forEach(function(chunk) {
		allAttendanceData = allAttendanceData.concat(chunk.attendance);
		Object.assign(allStaffNames, chunk.staff_names);
	});

	// Process the combined data to create month-wise summary - using same logic as tablet/mobile
	var staff_attendance = [];
	var dates = [];
	var currentDate = new Date(from_date);

	// Create dates array
	for(var j = 0; j < month_count; j++){
		var monthStr = currentDate.toLocaleDateString('en-US', { month: 'short', year: 'numeric' }).replace(' ', '-');
		var daysInMonth = new Date(currentDate.getFullYear(), currentDate.getMonth() + 1, 0).getDate();

		dates[j] = {
			'format': monthStr,
			'days': daysInMonth
		};

		currentDate.setMonth(currentDate.getMonth() + 1);
	}

	// Process attendance data - single pass like tablet/mobile
	allAttendanceData.forEach(function(att) {
		if(!staff_attendance[att.staff_id]) {
			staff_attendance[att.staff_id] = {
				'employee_code': att.employee_code,
				'staff_name': allStaffNames[att.staff_id] || att.staff_name,
				'staff_id': att.staff_id,
				'staff_type': att.staff_type,
				'staff_department': att.staff_department_name,
				'joining_date': att.joining_date,
				'last_date_of_work': att.last_date_of_work,
				'staff_status': att.staff_status
			};
		}

		// Use same month key logic as tablet/mobile
		var attDate = new Date(att.date);
		var monthKey = attDate.toLocaleDateString('en-US', { month: 'short', year: 'numeric' }).replace(' ', '-');

		if(!staff_attendance[att.staff_id][monthKey]){
			staff_attendance[att.staff_id][monthKey] = {
				'shift_exists': 0, 'P': 0, 'AB': 0, 'HD': 0, 'WO': 0, 'H': 0, 'total_working_days': 0
			};
		}

		// Add null checks for att properties - same logic as tablet/mobile
		if((att.status == 'P' || att.status == 'FD') && (att.type == 1 || att.type === undefined)){
			staff_attendance[att.staff_id][monthKey]['P'] += 1;
		}
		else if(att.status && att.status.indexOf('HD') !== -1){
			staff_attendance[att.staff_id][monthKey]['HD'] += 1;
		}
		else if(att.status == 'H'){
			staff_attendance[att.staff_id][monthKey]['H'] += 1;
		}
		else if(att.status == 'WO'){
			staff_attendance[att.staff_id][monthKey]['WO'] += 1;
		}
		else if(att.status && att.status.indexOf('AB') !== -1){
			staff_attendance[att.staff_id][monthKey]['AB'] += 1;
		}

		if(att.shift_exists == 1 || att.shift_exists === undefined){
			staff_attendance[att.staff_id][monthKey]['shift_exists'] = 1;
		}

		if ((att.shift_exists == 1 || att.shift_exists === undefined) && (att.type == 1 || att.type === undefined)) {
			staff_attendance[att.staff_id][monthKey]['total_working_days'] += 1;
		}
	});

	// Convert to array format
	var attendanceArray = [];
	Object.keys(staff_attendance).forEach(function(key) {
		attendanceArray.push(staff_attendance[key]);
	});

	// Sort attendance data by staff name for consistent ordering
	attendanceArray.sort(function(a, b) {
		var nameA = (a.staff_name || '').toLowerCase();
		var nameB = (b.staff_name || '').toLowerCase();
		return nameA.localeCompare(nameB);
	});

	return {
		dates: dates,
		attendance: attendanceArray
	};
}

// Helper functions removed - now using same logic as tablet/mobile versions
// function exportToExcel()
//     {
//         var htmls = "";
//         var uri = 'data:application/vnd.ms-excel;base64,';
//         var template = '<html xmlns:o="urn:schemas-microsoft-com:office:office" xmlns:x="urn:schemas-microsoft-com:office:excel" xmlns="http://www.w3.org/TR/REC-html40"><head><!--[if gte mso 9]><xml><x:ExcelWorkbook><x:ExcelWorksheets><x:ExcelWorksheet><x:Name>{worksheet}</x:Name><x:WorksheetOptions><x:DisplayGridlines/></x:WorksheetOptions></x:ExcelWorksheet></x:ExcelWorksheets></x:ExcelWorkbook></xml><![endif]--><meta http-equiv="content-type" content="text/plain; charset=UTF-8"/></head><body><table>{table}</table></body></html>';
//         var base64 = function(s)
//         {
//             return window.btoa(unescape(encodeURIComponent(s)))
//         };

//         var format = function(s, c)
//         {
//             return s.replace(/{(\w+)}/g, function(m, p)
//             {
//                 return c[p];
//             })
//         };

//         var mainTable = $("#monthly_report").html();
//         htmls = mainTable;
//         var ctx = {
//             worksheet : 'Spreadsheet',
//             table : htmls
//         }
//         var link = document.createElement("a");
//         link.download = `staff_attendance_monthly_report_${new Date().toLocaleDateString()}_${new Date().getHours()}:${new Date().getMinutes()}.xls`;
//         link.href = uri + base64(format(template, ctx));
//         link.click();
//     }


</script>