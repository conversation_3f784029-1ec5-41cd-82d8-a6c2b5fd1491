<?php
require_once  APPPATH . '/third_party/aws/aws-autoloader.php';
use Aws\S3\S3Client;
use Aws\S3\Exception\S3Exception;

class Resources extends CI_Controller {

  function __construct() {
    parent::__construct();
    if (!$this->ion_auth->logged_in()) {
        redirect('auth/login', 'refresh');
    }
    $this->load->library('filemanager');
    $this->load->model('academics/resource_model');
  }

  public function index() {
      $data['main_content'] = 'academics/resources/index';
      $this->load->view('inc/template', $data);
  }

  public function addResource() {
    $data['classList'] = $this->resource_model->getAllClasses();
      // echo "<pre>";print_r($data);die();


    // $data['grades'] = $this->resource_model->get_existing_grades();
    // $data['subjects'] = $this->resource_model->get_existing_subjects();
    // $data['aws'] = $this->get_chunk_upload_secret();
    // $this->load->library('aws_library');
		// $data['aws'] = $this->aws_library->getSignatureData();
    $data['main_content'] = 'academics/resources/add_resource';
    $this->load->view('inc/template', $data);
  }

  public function getSubjects(){
    $grade = $_POST['grade'];
		$semester_id = $_POST['semester_id'];

    $data['subjectsList'] = $this->resource_model->getSubjectsList($grade, $semester_id);
    echo json_encode($data);
  }

  public function getStaff(){
    $data['staff'] = $this->resource_model->getAllNames();
    echo json_encode($data);
  }

  public function updateResourceData() {
    $input = $_POST;
    // echo "<pre>";print_r($input);die();
    $value =  $_POST['value'];
    $resource_id =  $_POST['id'];
    // $resource_id =  $_POST['id'];
    $this->resource_model->updateResourceData($resource_id,  $value);
    // echo "<pre>";print_r($input);die();

    echo $value;
  }

  public function updateDescription(){
    $input = $_POST;
    // echo "<pre>";print_r($input);die();
    $value =  $_POST['value'];
    $resource_id =  $_POST['id'];
    // $resource_id =  $_POST['id'];
    $this->resource_model->updateDescription($resource_id,  $value);
    // echo "<pre>";print_r($input);die();
    echo $value;
  }

  
  public function updateResourceTypeData() {
    $input = $_POST;
    $value =  $_POST['value'];
    $resource_id =  $_POST['id'];
    // $resource_id =  $_POST['id'];
    $this->resource_model->updateResourceTypeData($resource_id,  $value);
    // echo "<pre>";print_r($input);die();

    echo $value;
  }

  private function __s3FileUpload($file) {
    if($file['tmp_name'] == '' || $file['name'] == '') {
      return ['status' => 'empty', 'file_name' => ''];
    }        
    return $this->filemanager->uploadFile($file['tmp_name'],$file['name'],'resources');
  }
  
  public function submit_resource(){
    $location = $_POST['location'];
    $name = $_POST['name'];
    $resource_name =  str_replace(array('\'', '"'), '', $name); 
    $resource_type = $_POST['resource_type'];
    $description = $_POST['description'];
    if($resource_type=='Video Link'){
      //For playing youtube videos as embedded, watch should be replaced with embed
      $video_link = explode("&", $_POST['resource_file'])[0];
      $loc = str_replace("watch?v=","embed/", $video_link);
    }else if($resource_type=='Hyper Link'){
      $loc = $_POST['resource_file'];
    }else{
      $prefix = $this->filemanager->getFilePath('');
      $loc = str_replace($prefix, "", $location);
    }
    $result = $this->resource_model->insertResource($loc, $resource_name);
    echo json_encode($result);
  }

  public function save_resource_data() {
    $resource_type = $_POST['resource_type'];
    $location = '';
    if(in_array($resource_type, ['Video Link', 'Hyper Link'])) {
      $location = $_POST['resource_link'];
      if($resource_type == 'Video Link') {
        //For playing youtube videos as embedded, watch should be replaced with embed
        $video_link = explode("&", $location)[0];
        $location = str_replace("watch?v=","embed/", $video_link);
      }
    } else if($resource_type === 'Vimeo') {
      $location = $_POST['vimeo_id'];
    } else {
        $location = $_POST['resource_file'];
    }

    $status = 0;
    if($location != '') {
      $status = $this->resource_model->save_resource_data($location);
    }
    echo $status;
  }

  public function viewResourcesPage(){
    $data['grades'] = $this->resource_model->getAllClasses();
    $data['resourceTypes'] = $this->resource_model->getResourceTypes();
    $data['is_semester_scheme'] = $this->settings->getSetting("is_semester_scheme");

    if ($data['is_semester_scheme'] == '1')
      $data['semester_list'] = $this->resource_model->getSemesterList();

    $input = $this->input->post();
    // $grade = $_POST['grade'];
    // $resource_type = $_POST['resource_type'];
    // $data['selected_grade'] = $_POST['grade'];
    // $data['selected_type'] = $resource_type;

    // $subject = $_POST['subject'];
    $resource_config = $this->settings->getSetting('resources');

    if (($resource_config)) {

      $data['resource_size'] = $resource_config->resource_size;
    } else {
      // echo 'here';
      $data['resource_size'] = "5MB";
    }
    // echo "<pre>";print_r($data );die();


    // $data['aws'] = $this->get_chunk_upload_secret();
    // $this->load->library('aws_library');
		// $data['aws'] = $this->aws_library->getSignatureData();
    // echo "<pre>"; print_r($data); die();
    $data['base_url'] = $this->filemanager->getFilePath('');
    $is_admin = $this->authorization->isAuthorized('ELIBRARY.ADMIN');
    $data['is_resource_admin'] = 0;
    if($is_admin != '') {
      $data['is_resource_admin'] = $is_admin;
    }
    if ($this->mobile_detect->isTablet()) {
      $data['main_content']   = 'academics/resources/view_resources_tablet';
    }else if($this->mobile_detect->isMobile()){
      $data['main_content']    = 'academics/resources/view_resources_mobile';
    }else{
      $data['main_content']    = 'academics/resources/view_resources';        
    }
    // $data['main_content'] = 'academics/resources/view_resources';
    $this->load->view('inc/template', $data);
  }

  public function viewAllResourcesByGrade(){
    $grade = $_POST['grade'];
    $data['base_url'] = $this->filemanager->getFilePath('');
    $data['viewResources'] = $this->resource_model->getAllResourcesByGrade($grade);
    echo json_encode($data);
  }

  public function getSemestersForClass() {
    $data['semester_list'] = $this->resource_model->getSemestersForClass($_POST['grade']);
    echo json_encode($data);
  }

  public function getResources() {
    $data['base_url'] = $this->filemanager->getFilePath('');
    $data['viewResources'] = $this->resource_model->filterResources($_POST);

    foreach ($data['viewResources'] as &$res) {
      $res->signed_url = $this->filemanager->getSignedUrlWithExpiry($res->resource_file, '+30 minutes');
    }
    echo json_encode($data);
  }

  public function viewResourcesByGradeSubject(){
    $grade = $_POST['grade'];
    $subject = $_POST['subject'];

    $data['base_url'] = $this->filemanager->getFilePath('');
    $data['viewResources'] = $this->resource_model->getResourcesByGradeSubject($grade, $subject);
    echo json_encode($data);
  }

  public function viewResourcesByGradeCreatedBy(){
    $grade = $_POST['grade'];
    $created_by = $_POST['created_by'];
    $data['base_url'] = $this->filemanager->getFilePath('');
    $data['viewResources'] = $this->resource_model->viewResourcesByGradeCreatedBy($grade, $created_by);
    echo json_encode($data);
  }

  public function viewResourcesByGradeResourceType(){
    $grade = $_POST['grade'];
    $resourceType = $_POST['resourceType'];
    // $created_by = $_POST['created_by'];
    $data['base_url'] = $this->filemanager->getFilePath('');
    $data['viewResources'] = $this->resource_model->viewResourcesByGradeResourceType($grade, $resourceType);
    echo json_encode($data);
  }

  public function viewResourcesByGradeResourceTypeCreatedBy(){
    $grade = $_POST['grade'];
    $resourceType = $_POST['resourceType'];
    $created_by = $_POST['created_by'];
    $data['base_url'] = $this->filemanager->getFilePath('');
    $data['viewResources'] = $this->resource_model->viewResourcesByGradeResourceTypeCreatedBy($grade, $resourceType, $created_by);
    echo json_encode($data);
  }

  public function viewResourcesByGradeSubjectCreatedBy(){
    $grade = $_POST['grade'];
    $subject = $_POST['subject'];
    $created_by = $_POST['created_by'];
    $data['base_url'] = $this->filemanager->getFilePath('');
    $data['viewResources'] = $this->resource_model->viewResourcesByGradeSubjectCreatedBy($grade, $subject, $created_by);
    echo json_encode($data);
  }

  public function viewResourcesByGradeSubjectResourceType(){
    $grade = $_POST['grade'];
    $subject = $_POST['subject'];
    $resourceType = $_POST['resourceType'];
    $data['base_url'] = $this->filemanager->getFilePath('');
    $data['viewResources'] = $this->resource_model->viewResourcesByGradeSubjectResourceType($grade, $subject, $resourceType);
    echo json_encode($data);
  }

  public function viewResourcesByGradeSubjectTypeCreatedBy(){
    $grade = $_POST['grade'];
    $subject = $_POST['subject'];
    $resourceType = $_POST['resourceType'];
    $created_by = $_POST['created_by'];
    $data['base_url'] = $this->filemanager->getFilePath('');
    $data['viewResources'] = $this->resource_model->viewResourcesByGradeSubjectTypeCreatedBy($grade, $subject, $created_by,  $resourceType);
    echo json_encode($data);

  }

  public function getSubjectsByGrade(){
    $grade = $_POST['grade'];
    $data['subjectData'] = $this->resource_model->getSubjectsByGrade($grade);
    echo json_encode($data);
  }

  private function get_chunk_upload_secret() {
    $this->config->load('s3');
    $bucket = $this->config->item('s3_bucket');
    $accessKeyId = $this->config->item('access_key');
    $this->load->library('aws_library');
    $general = $this->aws_library->getPolicyAndSignature('*');
    $pdf = $this->aws_library->getPolicyAndSignature('application/pdf');
    return array('access' => $accessKeyId, 'signature' => $general['signature'], 'pdf_signature' => $pdf['signature'], 'policy' => $general['policy'], 'pdf_policy' => $pdf['policy'], 'bucket' => $bucket, 'subdomain' => CONFIG_ENV['main_folder'], 'short_date' => $general['short_date'], 'iso_date' => $general['iso_date'], 'pdf_short_date' => $pdf['short_date'], 'pdf_iso_date' => $pdf['iso_date'], 'region' => $general['region']);


    $secret = $this->config->item('secret_key');

    // prepare policy
    $policy = base64_encode(json_encode(array(
      // ISO 8601 - date('c'); generates uncompatible date, so better do it manually
      'expiration' => date('Y-m-d\TH:i:s.000\Z', strtotime('+1 day')),  
      'conditions' => array(
        array('bucket' => $bucket),
        array('acl' => 'public-read'),
        array('starts-with', '$key', ''),
        // for demo purposes we are accepting only images
        // array('starts-with', '$Content-Type', 'application/pdf, *'),
        array('starts-with', '$Content-Type', '*'),
        // Plupload internally adds name field, so we need to mention it here
        array('starts-with', '$name', ''),  
        array('success_action_status' => '201'),
        array('starts-with', '$chunk', ''),
        array('starts-with', '$chunks', ''),
        array('starts-with', '$Filename', ''),
      )
    )));

    // prepare policy for pdf
    $pdf_policy = base64_encode(json_encode(array(
      // ISO 8601 - date('c'); generates uncompatible date, so better do it manually
      'expiration' => date('Y-m-d\TH:i:s.000\Z', strtotime('+1 day')),  
      'conditions' => array(
        array('bucket' => $bucket),
        array('acl' => 'public-read'),
        array('starts-with', '$key', ''),
        // for demo purposes we are accepting only images
        array('starts-with', '$Content-Type', 'application/pdf'),
        // Plupload internally adds name field, so we need to mention it here
        array('starts-with', '$name', ''),  
        array('success_action_status' => '201'),
        array('starts-with', '$chunk', ''),
        array('starts-with', '$chunks', ''),
        array('starts-with', '$Filename', ''),
      )
    )));

    // sign policy
    $signature = base64_encode(hash_hmac('sha1', $policy, $secret, true));
    $pdf_signature = base64_encode(hash_hmac('sha1', $pdf_policy, $secret, true));
    return array('access' => $accessKeyId, 'signature' => $signature, 'pdf_signature' => $pdf_signature, 'policy' => $policy, 'pdf_policy' => $pdf_policy, 'bucket' => $bucket, 'subdomain' => CONFIG_ENV['main_folder']);
  }

  public function updateResourceStatusToInactive() {
    $resourceId = $_POST['resourceId'];
       
    $result = $this->resource_model->updateResourceStatusToInactive($resourceId);
    if ($result)
      $this->session->set_flashdata('flashSuccess', 'Successfully Updated.');
    else
      $this->session->set_flashdata('flashError', 'Something went wrong.');
    echo json_encode($result);
  }

  public function updateResourceStatusToActive() {
    $resourceId = $_POST['resourceId'];
    $result = $this->resource_model->updateResourceStatusToActive($resourceId);
    if ($result)
        $this->session->set_flashdata('flashSuccess', 'Successfully Updated.');
    else
        $this->session->set_flashdata('flashError', 'Something went wrong.');
    echo json_encode($result);
  }

  // public function getResourcesById(){
  //   $resource_id = $_POST['resource_id'];
  //   $resource = $this->resource_model->getResourcesById($resource_id);
  //   $data = array(); 
  //   if(sizeof($resource)!=0) {
  //     foreach ($resource as $key =>$value) {
  //       if($value->resource_file!=''){
  //         array_push($data, array('id'=>$value->id,'name' => $value->name,'type' => $value->resource_type, 'path' => $this->filemanager->getFilePath($value->resource_file)));
  //       }
  //       else{
  //         array_push($data, array('id'=>$value->id,'name' => $value->name,'type' => $value->resource_type, 'path' => 'No Path'));

  //       }
  //     }
  //   }
  //   echo json_encode($data);
  // }

  public function getResourceToPlay(){
    $resource_obj = $this->resource_model->getResourceToPlay();
    $data['resource'] = $this->filemanager->getSignedUrlWithExpiry($resource_obj->resource_file, '+5 minutes');
    echo json_encode($data);
  }

  public function getYouTubeVideo(){
    $resource_obj = $this->resource_model->getResourceToPlay();
    $data['resource'] = $this->filemanager->getSignedUrlWithExpiry($resource_obj->resource_file, '+5 minutes');
    echo json_encode($data);
  }


  public function downloadResource1($name){
    $parts = explode("/", $name); 
    $file_name = $parts[count($parts) - 1];
    $this->load->helper('download');
    force_download($file_name, $data, TRUE);
  }

  public function downloadResource($id){ 
    $file_link = $this->resource_model->download_resource($id);
    $fileName = $this->resource_model->getResourceName($id);
			
   	
    $link = $file_link->resource_file;
    $file = explode("/", $link);
    // $file_name = 'task'.($index+1);
    $file_name = $fileName->name;

    $fname = $file_name .'.'.explode(".", $file[count($file)-1])[1];
    // echo '<pre>'; print_r($fname); die();
    $url = $this->filemanager->getFilePath($link);
    $data = file_get_contents($url);
    $this->load->helper('download');
    force_download($fname, $data, TRUE);
    $this->load->library('user_agent');
    redirect($this->agent->referrer());
  }

  public function downloadSignedURLResource($id){ 
    //Get Signed URL
    $resource_obj = $this->resource_model->getResourceObject($id);
    $signed_resource = $this->filemanager->getSignedUrlWithExpiry($resource_obj->resource_file, '+5 minutes');

    //Get actual file name
    $file = explode("/", $resource_obj->resource_file);
    $fname = $resource_obj->name .'.'.explode(".", $file[count($file)-1])[1];

    //Download the file
    $data = file_get_contents($signed_resource);
    $this->load->helper('download');
    force_download($fname, $data, TRUE);
    $this->load->library('user_agent');
    redirect($this->agent->referrer());
  }
}
