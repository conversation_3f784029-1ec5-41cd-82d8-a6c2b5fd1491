<?php

/**
 * Description of Student_menu
 *
 * <AUTHOR>
 */
class Academics_menu extends CI_Controller {

  function __construct() {
    parent::__construct();
    if (!$this->ion_auth->logged_in()) {
      redirect('auth/login', 'refresh');
    }
    $this->load->model('academics/lessonPlan_model', 'lessonplan_model');
    
    // if (!$this->authorization->isAuthorized('ACADEMICS.MODULE')) {
    //   redirect('dashboard', 'refresh');
    // }
  }

  private function getlmsTilePermission($staffId,$staffType){
    return $this->lessonplan_model->getlmsTilePermission($staffId, $staffType);
  }

  function index() {
    $data['permit_student_tasks'] = $this->authorization->isAuthorized('STUDENT_TASKS.MODULE') && $this->authorization->isModuleEnabled('STUDENT_TASKS');
    $data['permit_question_bank'] = $this->authorization->isAuthorized('QUESTION_BANK.MODULE') && $this->authorization->isModuleEnabled('QUESTION_BANK');
    $data['permit_lesson_plan'] = $this->authorization->isAuthorized('LESSON_PLAN.MODULE') && $this->authorization->isModuleEnabled('LESSON_PLAN');
    $data['permit_elibrary'] = $this->authorization->isAuthorized('ELIBRARY.MODULE') && $this->authorization->isModuleEnabled('ELIBRARY');

    $staffId= $this->authorization->getAvatarStakeHolderId();
    // bring primary permission
    $lms_primary_permission=$this->getlmsTilePermission($staffId,"primary");
    // bring assistant permission
    $lms_assistant_permission = $this->getlmsTilePermission($staffId, "assistant");
    // bring section permission
    $lms_section_permission = $this->getlmsTilePermission($staffId, "section");

    $site_url = site_url();
    $data['academic_tiles'] = array(
      [
        'title' => 'E-Library',
        'sub_title' => 'Add and View Resources',
        'icon' => 'svg_icons/books.svg',
        'url' => $site_url.'academics/resources/viewResourcesPage',
        'permission' => $this->authorization->isAuthorized('ELIBRARY.MODULE') && $this->authorization->isModuleEnabled('ELIBRARY')
      ],
      [
        'title' => $this->settings->getSetting('student_task_module_name') != null ? $this->settings->getSetting('student_task_module_name') : 'Student Task',
        'sub_title' => 'Add and View Student Tasks',
        'icon' => 'svg_icons/homework.svg',
        'url' => $site_url.'student_tasks/tasks/dashboard',
        'permission' => $this->authorization->isAuthorized('STUDENT_TASKS.MODULE') && $this->authorization->isModuleEnabled('STUDENT_TASKS')
      ],
    );

    $data['lesson_plan_tiles'] = array(
      [
        'title' => 'Manage Syllabus',
        'sub_title' => 'Manage Subjects, Lessons, Topics',
        'icon' => 'svg_icons/class.svg',
        'url' => $site_url.'academics/ManageSubjects/index',
        'permission' => $this->authorization->isAuthorized('LESSON_PLAN.MODULE') && $this->authorization->isAuthorized('LESSON_PLAN.MANAGE_SYLLABUS') && $this->authorization->isModuleEnabled('LESSON_PLAN')
      ],
      [
        'title' => 'Plan Syllabus Schedule',
        'sub_title' => 'Manage and plan your sessions',
        'icon' => 'svg_icons/classwise.svg',
        'url' => $site_url.'academics/lesson_plan/manage_session',
        'permission' => $this->authorization->isAuthorized('LESSON_PLAN.MODULE') && $this->authorization->isAuthorized('LESSON_PLAN.PLAN_SYLLABUS_SCHEDULE') && $this->authorization->isModuleEnabled('LESSON_PLAN')
      ],
      [
        'title' => 'Design Session',
        'sub_title' => 'Create and plan your sessions',
        'icon' => 'svg_icons/classwise.svg',
        'url' => $site_url.'academics/lesson_plan/design_session',
        'permission' => $this->authorization->isAuthorized('LESSON_PLAN.MODULE') && $this->authorization->isAuthorized('LESSON_PLAN.DESIGN_SESSION') && $this->authorization->isModuleEnabled('LESSON_PLAN')
      ],
      [
        'title' => 'Check-in/out Session',
        'sub_title' => 'Check-in/out of sessions',
        'icon' => 'svg_icons/logout.svg',
        'url' => $site_url . 'academics/lesson_plan/checkinout',
        'permission' => $this->authorization->isAuthorized('LESSON_PLAN.MODULE') && $this->authorization->isAuthorized('LESSON_PLAN.CHECK_IN_OUT_SESSION') && $this->authorization->isModuleEnabled('LESSON_PLAN')
      ],
      // [
      //   'title' => 'Session Plan (old)',
      //   'sub_title' => 'Create and plan your sessions',
      //   'icon' => 'svg_icons/classwise.svg',
      //   'url' => $site_url.'academics/lesson_plan/session_plan_status',
      //   'permission' => $this->authorization->isAuthorized('LESSON_PLAN.MODULE') && $this->authorization->isModuleEnabled('LESSON_PLAN')
      // ],
      // [
      //   'title' => 'Manage Syllabus Schedule',
      //   'sub_title' => 'Plan program of work for the year',
      //   'icon' => 'svg_icons/timetable.svg',
      //   'url' => $site_url.'academics/lesson_plan/program_of_work',
      //   'permission' => $this->authorization->isSuperAdmin()
      // ],
    );

    $data['lesson_plan_admin_tiles'] = array (
      [
        'title' => 'Manage Skills',
        'sub_title' => 'Add and View Skills',
        'icon' => 'svg_icons/usermanagement.svg',
        'url' => $site_url.'academics/skills/index',
        'permission' => $this->authorization->isAuthorized('LESSON_PLAN.MODULE') && $this->authorization->isModuleEnabled('LESSON_PLAN')
      ],
      [
        'title' => 'Manage Objectives',
        'sub_title' => 'Add and View Objectives',
        'icon' => 'svg_icons/attendance.svg',
        'url' => $site_url.'academics/objectives/index',
        'permission' => $this->authorization->isAuthorized('LESSON_PLAN.MODULE') && $this->authorization->isModuleEnabled('LESSON_PLAN')
      ],
      [
        'title' => 'Manage Assessment Types',
        'sub_title' => 'Manage Assessment Type',
        'icon' => 'svg_icons/assessmentnew.svg',
        'url' => $site_url.'academics/lesson_plan/assessment_types',
        'permission' => $this->authorization->isAuthorized('LESSON_PLAN.MODULE') && $this->authorization->isModuleEnabled('LESSON_PLAN')
      ],
      [
        'title' => 'Manage Reference Books',
        'sub_title' => 'Add/View Reference Books',
        'icon' => 'svg_icons/books.svg',
        'url' => $site_url.'academics/lesson_plan/reference_books_view',
        'permission' => $this->authorization->isAuthorized('LESSON_PLAN.MODULE') && $this->authorization->isModuleEnabled('LESSON_PLAN')
      ],
      [
        'title' => 'Manage Schedules',
        'sub_title' => 'Schedule',
        'icon' => 'svg_icons/classwise.svg',
        'url' => $site_url.'academics/lesson_plan/schedule_master',
        'permission' =>$this->authorization->isAuthorized('LESSON_PLAN.MODULE') && $this->authorization->isModuleEnabled('LESSON_PLAN')
      ]
      // [
      //   'title' => 'Manage Schemes',
      //   'sub_title' => 'Add and View Schemes',
      //   'icon' => 'svg_icons/reportcard.svg',
      //   'url' => $site_url.'academics/lesson_plan/addScheme',
      //   'permission' => $this->authorization->isAuthorized('LESSON_PLAN.MODULE') && $this->authorization->isModuleEnabled('LESSON_PLAN')
      // ]
    );

    $data['lesson_plan_reports_tiles'] = array(
      // [
      //   'title' => 'Sessions in Progress',
      //   'sub_title' => 'View in-progress sessions and provide leadership feedback',
      //   'icon' => 'svg_icons/attendance.svg',
      //   'url' => $site_url.'academics/lesson_plan/session_progress_report',
      //   'permission' => $this->authorization->isSuperAdmin()
      // ],
      // [
      //   'title' => 'School academic progress',
      //   'sub_title' => 'Summary of progress across all grades and sections.',
      //   'icon' => 'svg_icons/school.svg',
      //   'url' => $site_url.'academics/lesson_plan/school_academic_report',
      //   'permission' => $this->authorization->isSuperAdmin()
      // ],
      // [
      //   'title' => 'Grade academic progress',
      //   'sub_title' => 'Summary of progress across all sections of a grade',
      //   'icon' => 'svg_icons/exam.svg',
      //   'url' => $site_url.'academics/lesson_plan/grade_academic_report',
      //   'permission' => $this->authorization->isSuperAdmin()
      // ],
      // [
      //   'title' => 'Section-wise academic progress',
      //   'sub_title' => 'Summary of progress across sections',
      //   'icon' => 'svg_icons/classandsection.svg',
      //   'url' => $site_url.'academics/lesson_plan/section_wise_academic_report',
      //   'permission' => $this->authorization->isSuperAdmin()
      // ],
      // [
      //   'title' => 'Staff-wise progress',
      //   'sub_title' => 'Check-in/out of sessions',
      //   'icon' => 'svg_icons/staff.svg',
      //   'url' => $site_url.'academics/lesson_plan/staff_wise_report',
      //   'permission' => $this->authorization->isSuperAdmin()
      // ],
      // [
      //   'title' => 'Feedback report',
      //   'sub_title' => 'Feedback analysis',
      //   'icon' => 'svg_icons/assessment.svg',
      //   'url' => $site_url.'academics/lesson_plan/feedback_report',
      //   'permission' => $this->authorization->isSuperAdmin()
      // ],
      // [
      //   'title' => 'Lesson Plan Status',
      //   'sub_title' => 'Lesson Plan Progress Status',
      //   'icon' => 'svg_icons/assessment.svg',
      //   'url' => $site_url.'academics/lesson_plan/lesson_plan_status',
      //   'permission' => $this->authorization->isAuthorized('LESSON_PLAN.MODULE') && $this->authorization->isModuleEnabled('LESSON_PLAN')
      // ],
      [
        'title' => 'Track Section-wise Progress',
        'sub_title' => 'Session Tracking',
        'icon' => 'svg_icons/assessment.svg',
        'url' => $site_url.'academics/lesson_plan/session_tracking',
        'permission' => $this->authorization->isAuthorized('LESSON_PLAN.MODULE') && $this->authorization->isModuleEnabled('LESSON_PLAN')
      ]
    );

    $data['question_bank_tiles'] = array (
      [
        'title' => 'Question Bank (Beta)',
        'sub_title' => 'Add and View Questions',
        'icon' => 'svg_icons/faq.svg',
        'url' => $site_url.'academics/quiz/questions',
        'permission' => $this->authorization->isAuthorized('QUESTION_BANK.MODULE') && $this->authorization->isModuleEnabled('QUESTION_BANK')
      ],
      [
        'title' => 'Question Bank V2',
        'sub_title' => 'Add and View Questions',
        'icon' => 'svg_icons/faq.svg',
        'url' => $site_url.'academics/question_bank/index',
        'permission' => $this->authorization->isSuperAdmin()
      ],
      [
        'title' => 'Create Test',
        'sub_title' => 'Add and View Test',
        'icon' => 'svg_icons/add.svg',
        'url' => $site_url.'academics/quiz',
        'permission' => $this->authorization->isAuthorized('QUESTION_BANK.MODULE') && $this->authorization->isModuleEnabled('QUESTION_BANK')
      ]
    );

   
    
    $data['academic_tiles'] = checkTilePermissions($data['academic_tiles']);
    $data['lesson_plan_tiles'] = checkTilePermissions($data['lesson_plan_tiles']);
    $data['lesson_plan_admin_tiles'] = checkTilePermissions($data['lesson_plan_admin_tiles']);
    $data['lesson_plan_reports_tiles'] = checkTilePermissions($data['lesson_plan_reports_tiles']);
    $data['question_bank_tiles'] = checkTilePermissions($data['question_bank_tiles']);

    if ($this->mobile_detect->isTablet()) {
       $data['main_content']   = 'academics/menu/index_tablet.php';
    }else if($this->mobile_detect->isMobile()){
      $data['main_content']    = 'academics/menu/index_mobile.php';
    }else{
      $data['main_content']    = 'academics/menu/index.php';        
    }

    $this->load->view('inc/template', $data);
  }
}