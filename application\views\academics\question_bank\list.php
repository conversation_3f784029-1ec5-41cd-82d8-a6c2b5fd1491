<ul class="breadcrumb">
    <li><a href="<?php echo site_url('dashboard');?>">Dashboard</a></li>
    <li><a href="<?php echo site_url('academics/academics_menu/index');?>">Academics</a></li>
    <li>Question Bank V2</li>
</ul>

<div class="col-md-12">
    <div class="panel panel-default new-panel-style_3">
        <div class="card-header panel_heading_new_style_staff_border">
            <div class="row" style="margin: 0px;">
                <div class="col-md-8 pl-0">
                    <h3 class="card-title panel_title_new_style_staff">
                        <a class="back_anchor" href="<?php echo site_url('academics/academics_menu/index'); ?>">
                            <span class="fa fa-arrow-left"></span>
                        </a>
                        Question Bank V2
                    </h3>
                </div>
                <div class="col-md-4">
                    <a target="_blank" href="<?php echo site_url('academics/question_bank/create'); ?>" title="Add New Question(s)" class="btn btn-primary pull-right">
                        <i class="fa fa-plus"></i> Add New Question
                    </a>
                </div>
            </div>
        </div>

        <div class="card-body pt-0">
            <!-- Action Buttons -->
            <div class="row mb-3 d-none">
                <div class="col-12">
                    <div class="d-flex justify-content-between align-items-center">
                        <div class="btn-group" role="group">
                            <a target="_blank" href="<?php echo site_url('academics/question_bank/create'); ?>" class="btn btn-primary">
                                <i class="fa fa-plus"></i> Add New Question
                            </a>
                            <button type="button" class="btn btn-outline-secondary" id="bulkActionsBtn" disabled title="Bulk Actions">
                                <i class="fa fa-tasks"></i> Bulk Actions
                            </button>
                            <button type="button" class="btn btn-outline-info" id="exportBtn" title="Export">
                                <i class="fa fa-download"></i> Export
                            </button>
                        </div>
                        <div class="btn-group d-none" role="group">
                            <button type="button" class="btn btn-outline-secondary" id="viewModeGrid" title="Grid View">
                                <i class="fa fa-th"></i>
                            </button>
                            <button type="button" class="btn btn-secondary" id="viewModeList" title="List View">
                                <i class="fa fa-list"></i>
                            </button>
                        </div>
                    </div>
                </div>
            </div>

            <!-- Filters Card -->
            <div class="card card-outline card-primary mb-3">
                <div class="card-header">
                    <h3 class="card-title">
                        <i class="fa fa-filter"></i> Filters
                    </h3>
                    <div class="card-tools d-none">
                        <button type="button" class="btn btn-tool" data-card-widget="collapse">
                            <i class="fa fa-minus"></i>
                        </button>
                        <button type="button" class="btn btn-sm btn-outline-secondary" id="clearFilters">
                            <i class="fa fa-times"></i> Clear All
                        </button>
                    </div>
                </div>
                <div class="card-body">
                    <form id="filterForm">
                        <div class="row g-3">
                            <!-- Basic Filters -->
                            <div class="col-md-3">
                                <label class="form-label fw-bold">Class</label>
                                <select class="form-control select-enhanced" name="class_id" id="filter_class_id">
                                    <option value="">All Classes</option>
                                    <?php if(!empty($classes)) { ?>
                                        <?php foreach($classes as $class) { ?>
                                            <option value="<?php echo $class->class_master_id; ?>"><?php echo $class->class_name; ?></option>
                                        <?php } ?>
                                    <?php } ?>
                                </select>
                            </div>
                            <div class="col-md-3">
                                <label class="form-label fw-bold">Subject</label>
                                <select class="form-control select-enhanced" name="subject_id" id="filter_subject_id">
                                    <option value="">All Subjects</option>
                                </select>
                            </div>
                            <div class="col-md-3">
                                <label class="form-label fw-bold">Question Type</label>
                                <select class="form-control select-enhanced" name="question_type" id="filter_question_type">
                                    <option value="">All Types</option>
                                    <option value="MCQ">Multiple Choice</option>
                                    <option value="Short Answer">Short Answer</option>
                                    <option value="Long Answer">Long Answer</option>
                                    <option value="Fill in the Blanks">Fill in the Blanks</option>
                                    <option value="True/False">True / False</option>
                                    <option value="Match">Match</option>
                                    <option value="Comprehension">Comprehension</option>
                                    <option value="Custom">Custom</option>
                                </select>
                            </div>
                            <div class="col-md-3">
                                <label class="form-label fw-bold">Difficulty Level</label>
                                <select class="form-control select-enhanced" name="difficulty_level" id="filter_difficulty_level">
                                    <option value="">All Levels</option>
                                    <option value="Easy">Easy</option>
                                    <option value="Medium">Medium</option>
                                    <option value="Hard">Hard</option>
                                </select>
                            </div>
                        </div>
                        
                        <div class="row g-3 mt-2">
                            <!-- Advanced Filters -->
                            <div class="col-md-3">
                                <label class="form-label fw-bold">Status</label>
                                <select class="form-control select-enhanced" name="status" id="filter_status">
                                    <option value="">All Status</option>
                                    <option value="draft">Draft</option>
                                    <option value="pending_review">Pending Review</option>
                                    <option value="approved">Approved</option>
                                    <option value="rejected">Rejected</option>
                                    <option value="archived">Archived</option>
                                </select>
                            </div>
                            <div class="col-md-3">
                                <label class="form-label fw-bold">Created By</label>
                                <select class="form-control select-enhanced" name="created_by" id="filter_created_by">
                                    <option value="">All Authors</option>
                                    <option value="me">My Questions</option>
                                    <?php if(!empty($teachers)) { ?>
                                        <?php foreach($teachers as $teacher) { ?>
                                            <option value="<?php echo $teacher->id; ?>"><?php echo $teacher->staff_name; ?></option>
                                        <?php } ?>
                                    <?php } ?>
                                </select>
                            </div>
                            <div class="col-md-3">
                                <label class="form-label fw-bold">Date Range</label>
                                <input type="date" class="form-control" name="date_from" id="filter_date_from" placeholder="From Date">
                            </div>
                            <div class="col-md-3">
                                <label class="form-label fw-bold">&nbsp;</label>
                                <input type="date" class="form-control" name="date_to" id="filter_date_to" placeholder="To Date">
                            </div>
                        </div>
                        
                        <div class="row g-3 mt-2">
                            <!-- Search and Tags -->
                            <div class="col-md-6">
                                <label class="form-label fw-bold">Search Questions</label>
                                <div class="input-group">
                                    <input type="text" class="form-control" name="search" id="filter_search" placeholder="Search in question text, tags, or content...">
                                    <button class="btn btn-outline-secondary" type="button" id="searchBtn" title="Search Question">
                                        <i class="fa fa-search mr-0"></i>
                                    </button>
                                </div>
                            </div>
                            <div class="col-md-3">
                                <label class="form-label fw-bold">Marks Range</label>
                                <div class="input-group">
                                    <input type="number" class="form-control" name="marks_min" id="filter_marks_min" placeholder="Min" step="0.5">
                                    <span class="input-group-text">to</span>
                                    <input type="number" class="form-control" name="marks_max" id="filter_marks_max" placeholder="Max" step="0.5">
                                </div>
                            </div>
                            <div class="col-md-3">
                                <label class="form-label fw-bold">Sort By</label>
                                <select class="form-control select-enhanced" name="sort_by" id="filter_sort_by">
                                    <option value="created_at_desc">Newest First</option>
                                    <option value="created_at_asc">Oldest First</option>
                                    <option value="question_text_asc">Question A-Z</option>
                                    <option value="question_text_desc">Question Z-A</option>
                                    <option value="difficulty_asc">Difficulty: Easy to Hard</option>
                                    <option value="difficulty_desc">Difficulty: Hard to Easy</option>
                                    <option value="usage_count_desc">Most Used</option>
                                    <option value="marks_desc">Highest Marks</option>
                                </select>
                            </div>
                        </div>
                        
                        <div class="row mt-3">
                            <div class="col-12">
                                <button type="button" class="btn btn-primary pull-right ml-2" id="applyFilters" title="Apply Filters">
                                    <i class="fa fa-filter"></i> Apply Filters
                                </button>
                                <button type="button" class="btn btn-secondary pull-right" id="resetFilters" title="Reset Filters">
                                    <i class="fa fa-refresh"></i> Reset
                                </button>
                                <div class="float-end">
                                    <small class="text-muted">
                                        <span id="totalQuestions">0</span> questions found
                                        <span id="filterStatus" class="ms-2"></span>
                                    </small>
                                </div>
                            </div>
                        </div>
                    </form>
                </div>
            </div>

            <!-- Questions List -->
            <div class="card">
                <div class="card-header">
                    <h3 class="card-title">
                        <i class="fa fa-list"></i> Questions
                    </h3>
                    <div class="card-tools">
                        <div class="input-group input-group-sm" style="width: 200px;">
                            <select class="form-control" id="perPageSelect">
                                <option value="10">10 per page</option>
                                <option value="25" selected>25 per page</option>
                                <option value="50">50 per page</option>
                                <option value="100">100 per page</option>
                            </select>
                        </div>
                    </div>
                </div>
                <div class="card-body p-0">
                    <!-- Loading State -->
                    <div id="loadingState" class="text-center p-4" style="display: none;">
                        <i class="fa fa-spinner fa-spin fa-2x text-primary"></i>
                        <p class="mt-2 text-muted">Loading questions...</p>
                    </div>
                    
                    <!-- Questions Container -->
                    <div id="questionsContainer">
                        <!-- Questions will be loaded here via AJAX -->
                    </div>
                    
                    <!-- Empty State -->
                    <div id="emptyState" class="text-center p-5" style="display: none;">
                        <i class="fa fa-question-circle fa-3x text-muted mb-3"></i>
                        <h4 class="text-muted">No Questions Found</h4>
                        <p class="text-muted">Try adjusting your filters or create a new question.</p>
                        <a href="<?php echo site_url('academics/question_bank/create'); ?>" class="btn btn-primary">
                            <i class="fa fa-plus"></i> Add First Question
                        </a>
                    </div>
                </div>
                
                <!-- Pagination -->
                <div class="card-footer">
                    <div class="row align-items-center">
                        <div class="col-md-6">
                            <div id="paginationInfo" class="text-muted">
                                <!-- Pagination info will be inserted here -->
                            </div>
                        </div>
                        <div class="col-md-6">
                            <nav aria-label="Questions pagination">
                                <ul class="pagination pagination-sm justify-content-end mb-0" id="paginationControls">
                                    <!-- Pagination controls will be inserted here -->
                                </ul>
                            </nav>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </section>
</div>

<!-- Bulk Actions Modal -->
<div class="modal fade" id="bulkActionsModal" tabindex="-1" aria-labelledby="bulkActionsModalLabel" aria-hidden="true">
    <div class="modal-dialog">
        <div class="modal-content">
            <div class="modal-header">
                <h5 class="modal-title" id="bulkActionsModalLabel">Bulk Actions</h5>
                <button type="button" class="btn-close" data-bs-dismiss="modal" aria-label="Close"></button>
            </div>
            <div class="modal-body">
                <p>Select an action to perform on <span id="selectedCount">0</span> selected questions:</p>
                <div class="list-group">
                    <button type="button" class="list-group-item list-group-item-action" data-action="approve">
                        <i class="fa fa-check text-success"></i> Approve Selected
                    </button>
                    <button type="button" class="list-group-item list-group-item-action" data-action="reject">
                        <i class="fa fa-times text-danger"></i> Reject Selected
                    </button>
                    <button type="button" class="list-group-item list-group-item-action" data-action="archive">
                        <i class="fa fa-archive text-warning"></i> Archive Selected
                    </button>
                    <button type="button" class="list-group-item list-group-item-action" data-action="delete">
                        <i class="fa fa-trash text-danger"></i> Delete Selected
                    </button>
                    <button type="button" class="list-group-item list-group-item-action" data-action="export">
                        <i class="fa fa-download text-info"></i> Export Selected
                    </button>
                </div>
            </div>
            <div class="modal-footer">
                <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">Cancel</button>
            </div>
        </div>
    </div>
</div>

<style>
/* Custom Styles for Question Bank List */
.select-enhanced {
    background-image: url("data:image/svg+xml,%3csvg xmlns='http://www.w3.org/2000/svg' viewBox='0 0 16 16'%3e%3cpath fill='none' stroke='%********' stroke-linecap='round' stroke-linejoin='round' stroke-width='2' d='m1 6 7 7 7-7'/%3e%3c/svg%3e");
    background-repeat: no-repeat;
    background-position: right 0.75rem center;
    background-size: 16px 12px;
    padding-right: 2.5rem;
    -webkit-appearance: none;
    -moz-appearance: none;
    appearance: none;
}

.question-card {
    border: 1px solid #e9ecef;
    border-radius: 8px;
    margin-bottom: 15px;
    transition: all 0.3s ease;
}

.question-card:hover {
    border-color: #007bff;
    box-shadow: 0 2px 8px rgba(0, 123, 255, 0.15);
}

.question-card.selected {
    border-color: #007bff;
    background-color: #f8f9ff;
}

.question-type-badge {
    font-size: 0.75rem;
    padding: 0.25rem 0.5rem;
}

.difficulty-badge {
    font-size: 0.75rem;
    padding: 0.25rem 0.5rem;
}

.status-badge {
    font-size: 0.75rem;
    padding: 0.25rem 0.5rem;
}

.question-actions {
    opacity: 0;
    transition: opacity 0.3s ease;
}

.question-card:hover .question-actions {
    opacity: 1;
}

.question-stats {
    font-size: 0.875rem;
    color: #6c757d;
}

.filter-tag {
    display: inline-block;
    background-color: #e9ecef;
    color: #495057;
    padding: 0.25rem 0.5rem;
    border-radius: 0.375rem;
    font-size: 0.75rem;
    margin-right: 0.5rem;
    margin-bottom: 0.25rem;
}

.filter-tag .remove-filter {
    margin-left: 0.25rem;
    cursor: pointer;
    color: #6c757d;
}

.filter-tag .remove-filter:hover {
    color: #dc3545;
}

@media (max-width: 768px) {
    .btn-group {
        flex-direction: column;
        width: 100%;
    }

    .btn-group .btn {
        border-radius: 0.375rem !important;
        margin-bottom: 0.25rem;
    }

    .card-tools {
        margin-top: 1rem;
    }
}
</style>

<script src="https://cdn.jsdelivr.net/npm/sweetalert2@11"></script>
<script>
// Question Bank List Management
$(document).ready(function() {
    // Global variables
    let currentPage = 1;
    let perPage = 25;
    let totalQuestions = 0;
    let selectedQuestions = [];
    let currentFilters = {};
    let viewMode = 'list'; // 'list' or 'grid'
    let lastLoadedQuestions = [];

    // Initialize the page
    initializePage();

    function initializePage() {
        // Load initial questions
        loadQuestions('onLoad');

        // Setup event listeners
        setupEventListeners();

        // Setup filter dependencies
        setupFilterDependencies();

        // Initialize tooltips
        $('[data-toggle="tooltip"]').tooltip();
    }

    function setupEventListeners() {
        // Filter form submission
        $('#applyFilters').on('click', function(e) {
            e.preventDefault();
            currentPage = 1;
            applyFilters();
        });

        // Reset filters
        $('#resetFilters, #clearFilters').on('click', function() {
            resetFilters();
        });

        // Search functionality
        $('#searchBtn').on('click', function() {
            currentPage = 1;
            applyFilters();
        });

        $('#filter_search').on('keypress', function(e) {
            if (e.which === 13) { // Enter key
                currentPage = 1;
                applyFilters();
            }
        });

        // Per page change
        $('#perPageSelect').on('change', function() {
            perPage = parseInt($(this).val());
            currentPage = 1;
            loadQuestions();
        });

        // View mode toggle
        $('#viewModeList').on('click', function() {
            setViewMode('list');
        });

        $('#viewModeGrid').on('click', function() {
            setViewMode('grid');
        });

        // Bulk actions
        $('#bulkActionsBtn').on('click', function() {
            if (selectedQuestions.length > 0) {
                $('#selectedCount').text(selectedQuestions.length);
                $('#bulkActionsModal').modal('show');
            }
        });

        // Bulk action handlers
        $('#bulkActionsModal').on('click', '[data-action]', function() {
            const action = $(this).data('action');
            performBulkAction(action);
        });

        // Export functionality
        $('#exportBtn').on('click', function() {
            exportQuestions();
        });
    }

    function setupFilterDependencies() {
        // Class change loads subjects
        $('#filter_class_id').on('change', function() {
            const classId = $(this).val();
            loadSubjects(classId);
        });
    }

    function loadSubjects(classId) {
        if (!classId) {
            $('#filter_subject_id').html('<option value="">All Subjects</option>');
            return;
        }

        $('#filter_subject_id').html('<option value="">Loading...</option>');

        $.ajax({
            url: '<?php echo site_url('academics/question_bank/get_subjects_list'); ?>',
            type: 'POST',
            data: { class_master_id: classId },
            success: function(response) {
                try {
                    const subjects = JSON.parse(response);
                    let options = '<option value="">All Subjects</option>';

                    subjects.forEach(function(subject) {
                        options += `<option value="${subject.id}">${subject.subject_name}</option>`;
                    });

                    $('#filter_subject_id').html(options);
                } catch(e) {
                    console.error('Error parsing subjects:', e);
                    $('#filter_subject_id').html('<option value="">Error loading subjects</option>');
                }
            },
            error: function() {
                $('#filter_subject_id').html('<option value="">Error loading subjects</option>');
            }
        });
    }

    function applyFilters() {
        // Collect filter values
        currentFilters = {
            class_id: $('#filter_class_id').val(),
            subject_id: $('#filter_subject_id').val(),
            question_type: $('#filter_question_type').val(),
            difficulty_level: $('#filter_difficulty_level').val(),
            status: $('#filter_status').val(),
            created_by: $('#filter_created_by').val(),
            date_from: $('#filter_date_from').val(),
            date_to: $('#filter_date_to').val(),
            search: $('#filter_search').val(),
            marks_min: $('#filter_marks_min').val(),
            marks_max: $('#filter_marks_max').val(),
            sort_by: $('#filter_sort_by').val()
        };

        // Remove empty filters
        Object.keys(currentFilters).forEach(key => {
            if (!currentFilters[key]) {
                delete currentFilters[key];
            }
        });

        // Update filter status display
        updateFilterStatus();

        // Load questions with filters
        loadQuestions();
    }

    function resetFilters() {
        $('#filterForm')[0].reset();
        currentFilters = {};
        currentPage = 1;
        updateFilterStatus();
        loadQuestions('Reset');
    }

    function updateFilterStatus() {
        const filterCount = Object.keys(currentFilters).length;
        if (filterCount > 0) {
            $('#filterStatus').html(`<span class="badge badge-info">${filterCount} filter(s) applied</span>`);
        } else {
            $('#filterStatus').html('');
        }
    }

    function loadQuestions(callFrom = 'applyFilters') {
        showLoading(true);

        const requestData = {
            page: currentPage,
            per_page: perPage,
            view_mode: viewMode,
            ...currentFilters
        };
        if(callFrom != 'applyFilters'){
            requestData.sort_by = 'created_at_desc'
        }

        $.ajax({
            url: '<?php echo site_url('academics/question_bank/get_questions_list'); ?>',
            type: 'POST',
            data: requestData,
            success: function(response) {
                try {
                    const data = JSON.parse(response);

                    if (data.success) {
                        displayQuestions(data.questions);
                        updatePagination(data.pagination);
                        totalQuestions = data.pagination ? data.pagination.total : data.questions.length;
                        updatePaginationInfo(data.pagination);

                        if (data.questions.length === 0) {
                            showEmptyState(true);
                        } else {
                            showEmptyState(false);
                        }
                    } else {
                        showError(data.message || 'Error loading questions');
                    }
                } catch(e) {
                    console.error('Error parsing response:', e);
                    showError('Error loading questions');
                }
            },
            error: function(xhr, status, error) {
                console.error('AJAX Error:', error);
                showError('Failed to load questions. Please try again.');
            },
            complete: function() {
                showLoading(false);
            }
        });
    }

    function displayQuestions(questions) {
        let html = '';

        if (viewMode === 'list') {
            html = generateListView(questions);
        } else {
            html = generateGridView(questions);
        }

        $('#questionsContainer').html(html);

        // Event listeners are already set up via onclick attributes in the HTML
    }

    function generateListView(questions) {
        let html = '<div class="table-responsive"><table class="table table-hover">';
        html += `
            <thead>
                <tr>
                    <th width="40">
                        <input type="checkbox" id="selectAll" onchange="toggleSelectAll(this)">
                    </th>
                    <th>Question</th>
                    <th width="120">Type</th>
                    <th width="100">Difficulty</th>
                    <th width="80">Marks</th>
                    <th width="100">Status</th>
                    <th width="120">Created</th>
                    <th width="120">Actions</th>
                </tr>
            </thead>
            <tbody>
        `;

        questions.forEach(function(question) {
            html += generateQuestionRow(question);
        });

        html += '</tbody></table></div>';
        return html;
    }

    function generateQuestionRow(question) {
        const questionText = question.question_text.length > 100 ?
            question.question_text.substring(0, 100) + '...' :
            question.question_text;

        const statusClass = getStatusClass(question.status);
        const difficultyClass = getDifficultyClass(question.difficulty_level);

        return `
            <tr class="question-row" data-question-id="${question.id}">
                <td>
                    <input type="checkbox" class="question-checkbox" value="${question.id}" onchange="updateBulkActionsButton()">
                </td>
                <td>
                    <div class="question-preview">
                        <div class="fw-bold">${questionText}</div>
                        <small class="text-muted">
                            ${question.class_name} - ${question.subject_name}
                            ${question.topic_name ? ' - ' + question.topic_name : ''}
                        </small>
                    </div>
                </td>
                <td>
                    <span class="badge badge-secondary question-type-badge">${question.question_type}</span>
                </td>
                <td>
                    <span class="badge ${difficultyClass} difficulty-badge">${question.difficulty_level || 'N/A'}</span>
                </td>
                <td>
                    <span class="fw-bold">${question.max_marks}</span>
                    ${question.negative_marks > 0 ? `<br><small class="text-danger">-${question.negative_marks}</small>` : ''}
                </td>
                <td>
                    <span class="badge ${statusClass} status-badge">${formatStatus(question.status)}</span>
                </td>
                <td>
                    <small>
                        ${formatDate(question.created_at)}<br>
                        <span class="text-muted">by ${question.created_by_name}</span>
                    </small>
                </td>
                <td>
                    <div class="btn-group btn-group-sm">
                        <button type="button" class="btn btn-outline-primary btn-sm" onclick="viewQuestion(${question.id})" title="View">
                            <i class="fa fa-eye mr-0"></i>
                        </button>
                        <button type="button" class="btn btn-outline-secondary btn-sm d-none" onclick="editQuestion(${question.id})" title="Edit">
                            <i class="fa fa-edit mr-0"></i>
                        </button>
                        <button type="button" class="btn btn-outline-info btn-sm d-none" onclick="duplicateQuestion(${question.id})" title="Duplicate">
                            <i class="fa fa-copy mr-0"></i>
                        </button>
                        <button type="button" class="btn btn-outline-danger btn-sm" onclick="deleteQuestion(${question.id})" title="Delete">
                            <i class="fa fa-trash-o mr-0"></i>
                        </button>
                    </div>
                </td>
            </tr>
        `;
    }

    // Utility functions
    function getStatusClass(status) {
        const statusClasses = {
            'draft': 'badge-secondary',
            'pending_review': 'badge-warning',
            'approved': 'badge-success',
            'rejected': 'badge-danger',
            'archived': 'badge-dark'
        };
        return statusClasses[status] || 'badge-secondary';
    }

    function getDifficultyClass(difficulty) {
        const difficultyClasses = {
            'Easy': 'badge-success',
            'Medium': 'badge-warning',
            'Hard': 'badge-danger'
        };
        return difficultyClasses[difficulty] || 'badge-secondary';
    }

    function formatStatus(status) {
        return status.replace('_', ' ').replace(/\b\w/g, l => l.toUpperCase());
    }

    function formatDate(dateString) {
        const date = new Date(dateString);
        return date.toLocaleDateString() + '<br>' + date.toLocaleTimeString([], {hour: '2-digit', minute:'2-digit'});
    }

    function showLoading(show) {
        if (show) {
            $('#loadingState').show();
            $('#questionsContainer').hide();
        } else {
            $('#loadingState').hide();
            $('#questionsContainer').show();
        }
    }

    function showEmptyState(show) {
        if (show) {
            $('#emptyState').show();
            $('#questionsContainer').hide();
        } else {
            $('#emptyState').hide();
            $('#questionsContainer').show();
        }
    }

    function showError(message) {
        Swal.fire({
            icon: 'error',
            title: 'Error',
            text: message
        });
    }
});

// Global functions for question actions
function viewQuestion(questionId) {
    window.open('<?php echo site_url('academics/question_bank/view/'); ?>' + questionId, '_blank');
}

function editQuestion(questionId) {
    window.location.href = '<?php echo site_url('academics/question_bank/edit/'); ?>' + questionId;
}

function duplicateQuestion(questionId) {
    Swal.fire({
        title: 'Duplicate Question?',
        text: 'This will create a copy of the question that you can modify.',
        icon: 'question',
        showCancelButton: true,
        confirmButtonText: 'Yes, duplicate it!'
    }).then((result) => {
        if (result.isConfirmed) {
            window.location.href = '<?php echo site_url('academics/question_bank/duplicate/'); ?>' + questionId;
        }
    });
}

function deleteQuestion(questionId) {
    Swal.fire({
        title: 'Delete Question?',
        text: 'This action cannot be undone!',
        icon: 'warning',
        showCancelButton: true,
        confirmButtonColor: '#d33',
        confirmButtonText: 'Yes, delete it!'
    }).then((result) => {
        if (result.isConfirmed) {
            // Perform delete action
            $.ajax({
                url: '<?php echo site_url('academics/question_bank/delete/'); ?>' + questionId,
                type: 'POST',
                success: function(response) {
                    const data = JSON.parse(response);
                    if (data.success) {
                        Swal.fire('Deleted!', 'Question has been deleted.', 'success');
                        loadQuestions(); // Reload the list
                    } else {
                        Swal.fire('Error!', data.message, 'error');
                    }
                },
                error: function() {
                    Swal.fire('Error!', 'Failed to delete question.', 'error');
                }
            });
        }
    });
}

// Missing function implementations
function generateGridView(questions) {
    let html = '<div class="row">';

    questions.forEach(function(question) {
        const questionText = question.question_text.length > 150 ?
            question.question_text.substring(0, 150) + '...' :
            question.question_text;

        const statusClass = getStatusClass(question.status);
        const difficultyClass = getDifficultyClass(question.difficulty_level);

        html += `
            <div class="col-md-6 col-lg-4 mb-3">
                <div class="card h-100">
                    <div class="card-header d-flex justify-content-between align-items-center">
                        <span class="badge ${statusClass}">${formatStatus(question.status)}</span>
                        <span class="badge ${difficultyClass}">${question.difficulty_level || 'N/A'}</span>
                    </div>
                    <div class="card-body">
                        <h6 class="card-title">${question.question_type}</h6>
                        <p class="card-text">${questionText}</p>
                        <small class="text-muted">
                            ${question.class_name} - ${question.subject_name}
                            ${question.topic_name ? ' - ' + question.topic_name : ''}
                        </small>
                    </div>
                    <div class="card-footer">
                        <div class="d-flex justify-content-between align-items-center">
                            <small class="text-muted">
                                ${formatDate(question.created_at)}<br>
                                by ${question.created_by_name}
                            </small>
                            <div class="btn-group btn-group-sm">
                                <button type="button" class="btn btn-outline-primary btn-sm" onclick="viewQuestion(${question.id})" title="View">
                                    <i class="fa fa-eye"></i>
                                </button>
                                <button type="button" class="btn btn-outline-secondary btn-sm" onclick="editQuestion(${question.id})" title="Edit">
                                    <i class="fa fa-edit"></i>
                                </button>
                                <button type="button" class="btn btn-outline-info btn-sm" onclick="duplicateQuestion(${question.id})" title="Duplicate">
                                    <i class="fa fa-copy"></i>
                                </button>
                                <button type="button" class="btn btn-outline-danger btn-sm" onclick="deleteQuestion(${question.id})" title="Delete">
                                    <i class="fa fa-trash"></i>
                                </button>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        `;
    });

    html += '</div>';
    return html;
}

function setViewMode(mode) {
    viewMode = mode;

    // Update button states
    if (mode === 'list') {
        $('#viewModeList').addClass('active');
        $('#viewModeGrid').removeClass('active');
    } else {
        $('#viewModeGrid').addClass('active');
        $('#viewModeList').removeClass('active');
    }
    console.log(lastLoadedQuestions);
    // Reload questions with new view mode
    if (typeof lastLoadedQuestions !== 'undefined' && lastLoadedQuestions.length > 0) {
        displayQuestions(lastLoadedQuestions);
    }
}

function performBulkAction(action) {
    if (selectedQuestions.length === 0) {
        Swal.fire('Warning', 'No questions selected', 'warning');
        return;
    }

    switch(action) {
        case 'delete':
            Swal.fire({
                title: 'Delete Selected Questions?',
                text: `This will delete ${selectedQuestions.length} question(s). This action cannot be undone.`,
                icon: 'warning',
                showCancelButton: true,
                confirmButtonColor: '#d33',
                cancelButtonColor: '#3085d6',
                confirmButtonText: 'Yes, delete them!'
            }).then((result) => {
                if (result.isConfirmed) {
                    // TODO: Implement bulk delete
                    console.log('Bulk delete:', selectedQuestions);
                    Swal.fire('Success', 'Questions deleted successfully', 'success');
                    $('#bulkActionsModal').modal('hide');
                    loadQuestions();
                }
            });
            break;
        case 'export':
            exportQuestions(selectedQuestions);
            break;
        default:
            console.log('Unknown bulk action:', action);
    }
}

function exportQuestions(questionIds = null) {
    // TODO: Implement export functionality
    const exportData = questionIds || selectedQuestions;
    console.log('Exporting questions:', exportData);

    Swal.fire({
        title: 'Export Questions',
        text: 'Export functionality will be implemented soon.',
        icon: 'info'
    });
}

function updatePagination(pagination) {
    if (!pagination) return;

    const paginationControls = $('#paginationControls');
    paginationControls.empty();

    // Previous button
    if (pagination.has_prev) {
        paginationControls.append(`
            <li class="page-item">
                <a class="page-link" href="#" onclick="goToPage(${pagination.current_page - 1})" aria-label="Previous">
                    <span aria-hidden="true">&laquo;</span>
                </a>
            </li>
        `);
    } else {
        paginationControls.append(`
            <li class="page-item disabled">
                <span class="page-link">&laquo;</span>
            </li>
        `);
    }

    // Page numbers
    const startPage = Math.max(1, pagination.current_page - 2);
    const endPage = Math.min(pagination.total_pages, pagination.current_page + 2);

    if (startPage > 1) {
        paginationControls.append(`<li class="page-item"><a class="page-link" href="#" onclick="goToPage(1)">1</a></li>`);
        if (startPage > 2) {
            paginationControls.append(`<li class="page-item disabled"><span class="page-link">...</span></li>`);
        }
    }

    for (let i = startPage; i <= endPage; i++) {
        const activeClass = i === pagination.current_page ? 'active' : '';
        paginationControls.append(`
            <li class="page-item ${activeClass}">
                <a class="page-link" href="#" onclick="goToPage(${i})">${i}</a>
            </li>
        `);
    }

    if (endPage < pagination.total_pages) {
        if (endPage < pagination.total_pages - 1) {
            paginationControls.append(`<li class="page-item disabled"><span class="page-link">...</span></li>`);
        }
        paginationControls.append(`<li class="page-item"><a class="page-link" href="#" onclick="goToPage(${pagination.total_pages})">${pagination.total_pages}</a></li>`);
    }

    // Next button
    if (pagination.has_next) {
        paginationControls.append(`
            <li class="page-item">
                <a class="page-link" href="#" onclick="goToPage(${pagination.current_page + 1})" aria-label="Next">
                    <span aria-hidden="true">&raquo;</span>
                </a>
            </li>
        `);
    } else {
        paginationControls.append(`
            <li class="page-item disabled">
                <span class="page-link">&raquo;</span>
            </li>
        `);
    }
}

function updatePaginationInfo(pagination) {
    if (!pagination) return;

    const start = ((pagination.current_page - 1) * pagination.per_page) + 1;
    const end = Math.min(pagination.current_page * pagination.per_page, pagination.total);

    $('#paginationInfo').html(`
        Showing ${start} to ${end} of ${pagination.total} questions
    `);
}

function goToPage(page) {
    if (page !== currentPage) {
        currentPage = page;
        loadQuestions();
    }
}

function toggleSelectAll(checkbox) {
    const questionCheckboxes = document.querySelectorAll('.question-checkbox');
    questionCheckboxes.forEach(cb => {
        cb.checked = checkbox.checked;
        if (checkbox.checked) {
            if (!selectedQuestions.includes(cb.value)) {
                selectedQuestions.push(cb.value);
            }
        } else {
            const index = selectedQuestions.indexOf(cb.value);
            if (index > -1) {
                selectedQuestions.splice(index, 1);
            }
        }
    });
    updateBulkActionsButton();
}

function updateBulkActionsButton() {
    // Update selectedQuestions array
    selectedQuestions = [];
    document.querySelectorAll('.question-checkbox:checked').forEach(cb => {
        selectedQuestions.push(cb.value);
    });

    // Update bulk actions button
    const bulkBtn = document.getElementById('bulkActionsBtn');
    if (selectedQuestions.length > 0) {
        bulkBtn.disabled = false;
        bulkBtn.innerHTML = `<i class="fa fa-tasks"></i> Bulk Actions (${selectedQuestions.length})`;
    } else {
        bulkBtn.disabled = true;
        bulkBtn.innerHTML = '<i class="fa fa-tasks"></i> Bulk Actions';
    }

    // Update select all checkbox
    const selectAllCheckbox = document.getElementById('selectAll');
    const totalCheckboxes = document.querySelectorAll('.question-checkbox').length;
    const checkedCheckboxes = document.querySelectorAll('.question-checkbox:checked').length;

    if (checkedCheckboxes === 0) {
        selectAllCheckbox.indeterminate = false;
        selectAllCheckbox.checked = false;
    } else if (checkedCheckboxes === totalCheckboxes) {
        selectAllCheckbox.indeterminate = false;
        selectAllCheckbox.checked = true;
    } else {
        selectAllCheckbox.indeterminate = true;
    }
}

// Store last loaded questions for view mode switching (declared globally above)
// Override displayQuestions to store the data
const originalDisplayQuestions = window.displayQuestions;
window.displayQuestions = function(questions) {
    lastLoadedQuestions = questions;
    return originalDisplayQuestions(questions);
};

</script>
