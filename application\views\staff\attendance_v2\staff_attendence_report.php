<ul class="breadcrumb">
  <li><a href="<?php echo site_url('dashboard');?>">Dashboard</a></li>
  <li><a href="<?php echo site_url('staff/attendance');?>">Staff Attendance</a></li>
  <li>Staff attendance - detailed report</li>
</ul>

<div class="col-md-12">
  	<div class="panel panel-default new-panel-style_3">
	    <div class="panel-heading panel_heading_new_style_staff_border">
            <div class="row" style="margin: 0px;">
                <div class="d-flex justify-content-between" style="width:100%;">
                    <div class="col-md-8">
                        <h3 class="panel-title card-title panel_title_new_style_staff">
                            <a class="back_anchor" href="<?php echo site_url('staff/attendance'); ?>">
                            <span class="fa fa-arrow-left"></span>
                            </a> 
                            Staff attendance - detailed report
                        </h3>
                    </div>
                    <div class="col-md-4 d-flex align-items-center justify-content-end">
                        <ul class="panel-controls" id="exportIcon" >
                          <button id="stu_print" class="btn btn-danger" onclick="print_visitor()"><span class="glyphicon glyphicon-print" aria-hidden="true"></span> Print</button>
                          <a style="margin-left:3px;" onclick="exportToExcel()" class="btn btn-primary"><span class="fa fa-file-text-o"></span> Export</a>
                        </ul>
                    </div> 
                </div>
            </div>
	    </div>
        <div class="panel-body pt-1">
			<div class="row" style="margin: 0px;">
				<div class="col-md-2 form-group">
					<label for="fromdateId" class="control-label">Attendance range</label>
					<div id="reportrange" class="dtrange" style="width: 100%">                                            
						<span></span>
						<input type="hidden" id="from_date">
						<input type="hidden" id="to_date">
					</div>
				</div>

				<div class="col-md-2 form-group">
					<label class="control-label">Staff Type</label>
					<select class="form-control" name="selected_staff_type" id="selected_staff_type">
						<option value="all">All</option>
						<?php foreach ($staff_types as $key=>$val) {
								echo "<option value='$key'>$val</option>";
							}
						?>
					</select>
				</div>

                <div class="col-md-2 form-group">
                    <label class="control-label">Staff Status Type</label>
                    <select class="form-control" name="staff_status_type" id="staff_status_type">
                            <option value="all">All</option>
                            <option value='2' selected>Approved</option>
                            <option value='4'>Resigned</option>
                    </select>
                </div>

				<div class="col-md-3 form-group pt-3">
					<button class="btn btn-primary mt-3" style="margin-top:20px" onclick="getAttendanceData()">Get Report</button>
				</div>

				<!-- Search functionality for large datasets -->
				<!-- <div class="col-md-12 form-group" id="staff-search-container" style="display:none;">
					<div class="input-group">
						<input type="text" class="form-control" id="staff-search" placeholder="Search staff by name or employee code..." onkeyup="searchStaff()">
						<div class="input-group-append">
							<button class="btn btn-outline-secondary" type="button" onclick="clearSearch()">Clear</button>
						</div>
					</div>
					<small class="text-muted">Found <span id="search-results-count">0</span> matching staff members</small>
				</div> -->
			</div>
			<div id="attendance-data">
			
			</div>
		</div>
  	</div>
</div>

<script src="https://cdnjs.cloudflare.com/ajax/libs/xlsx/0.16.9/xlsx.full.min.js" integrity="sha512-wBcFatf7yQavHQWtf4ZEjvtVz4XkYISO96hzvejfh18tn3OrJ3sPBppH0B6q/1SHB4OKHaNNUKqOmsiTGlOM/g==" crossorigin="anonymous"></script>
<script src="https://cdnjs.cloudflare.com/ajax/libs/FileSaver.js/2.0.0/FileSaver.min.js" integrity="sha512-csNcFYJniKjJxRWRV1R7fvnXrycHP6qDR21mgz1ZP55xY5d+aHLfo9/FcGDQLfn2IfngbAHd8LdfsagcCqgTcQ==" crossorigin="anonymous"></script>
<script type="text/javascript" src="<?php echo base_url('assets/js/plugins/moment.min.js') ?>"></script>
<script type="text/javascript" src="<?php echo base_url('assets/js/plugins/daterangepicker/daterangepicker.js') ?>"></script>

<script type="text/javascript">
$(document).ready(function() {
	$("#reportrange").daterangepicker({
		ranges: {
			'Today': [moment(), moment()],
			'Yesterday': [moment().subtract(1, 'days'), moment().subtract(1, 'days')],
			'Last 7 Days': [moment().subtract(6, 'days'), moment()],
			'Last 30 Days': [moment().subtract(29, 'days'), moment()],
			// 'This Month': [moment().startOf('month'), moment()],
			// 'Last Month': [moment().subtract(1, 'month').startOf('month'), moment().subtract(1, 'month').endOf('month')],
		},
        dateLimit: {
		'months': 1,
		'days': 0
		},
		opens: 'right',
		buttonClasses: ['btn btn-default'],
		applyClass: 'btn-small btn-primary',
		cancelClass: 'btn-small',
		format: 'DD-MM-YYYY',
		separator: ' to ',
		startDate: moment().subtract(6, 'days'),
		endDate: moment()            
		},function(start, end) {
		$('#reportrange span').html(start.format('MMM D, YYYY') + ' - ' + end.format('MMM D, YYYY'));
		$('#from_date').val(start.format('DD-MM-YYYY'));
		$('#to_date').val(end.format('DD-MM-YYYY'));
	});

	$("#reportrange span").html(moment().subtract(6, 'days').format('MMM D, YYYY') + ' - ' + moment().format('MMM D, YYYY'));
	$('#from_date').val(moment().subtract(6, 'days').format('DD-MM-YYYY'));
	$('#to_date').val(moment().format('DD-MM-YYYY'));
});

function hideDataArea(){
    $("#attendance-data").html('');
    // $("#staff-search-container").hide();
}

function getAttendanceData() {
	$("#attendance-data").html('<div class="text-center"><i class="fa fa-spinner fa-spin" style="font-size: 3rem;"></i></div>');
	var from_date = $("#from_date").val();
	var to_date = $("#to_date").val();
	var selected_staff_type = $("#selected_staff_type").val();
	var staff_status_type = $("#staff_status_type").val();
	var show_late_only = 'all';

	$.ajax({
        url: '<?php echo site_url('staff/attendance/getDayWiseAttendance'); ?>',
        type: 'post',
        data: {'from_date':from_date, 'to_date': to_date, 'selected_staff_type': selected_staff_type, 'show_late_only': show_late_only,'staff_status_type':staff_status_type},
        success: function(data) {
            if(data != 0) {
                var response = JSON.parse(data);

                // Check if chunked processing is needed
                if(response.chunked_processing) {
                    processChunkedStaffData(response);
                } else {
                    // Original processing for smaller date ranges
                    var dates = response.dates;
                    var attendance = response.attendance;
                    if(response?.length==0 || attendance.length == 0){
                        $("#attendance-data").html('<h3 class="no-data-display">No Data Available</h3>')
                    }else{
                        $("#attendance-data").html(construct_staff_attendence(dates,attendance));

                        // Show search functionality for large datasets
                        if(attendance.length > 20) {
                            // $("#staff-search-container").show();
                            updateSearchCount();
                        }
                    }
                }
            } else {
                $("#attendance-data").html('<h3 class="no-data-display">No Data Available</h3>')
                // $("#staff-search-container").hide();
            }
        },
        error: function (err) {
		    console.log(err);
		}
    });
}

// Global variables for chunked processing
var chunkedStaffAttendanceData = [];
var chunkedStaffDates = [];
var chunkedStaffFilters = {};

function processChunkedStaffData(response) {
	chunkedStaffDates = response.dates;
	chunkedStaffFilters = response.filters;
	chunkedStaffAttendanceData = [];

	var totalChunks = response.total_date_chunks * response.total_staff_chunks;
	var processedChunks = 0;

	// Show progress indicator
	showChunkedStaffProgress(0, totalChunks);

	// Process each date chunk with each staff chunk
	var chunkPromises = [];

	for(var dateChunkIndex = 0; dateChunkIndex < response.date_chunks.length; dateChunkIndex++) {
		for(var staffChunkIndex = 0; staffChunkIndex < response.staff_chunks.length; staffChunkIndex++) {
			var dateChunk = response.date_chunks[dateChunkIndex];
			var staffChunk = response.staff_chunks[staffChunkIndex];

			var chunkPromise = processStaffAttendanceChunk(
				dateChunk.from_date,
				dateChunk.to_date,
				staffChunk,
				chunkedStaffFilters.selected_staff_type,
				chunkedStaffFilters.staff_status_type,
				processedChunks,
				totalChunks
			).then(function(chunkData) {
				processedChunks++;
				updateChunkedStaffProgress(processedChunks, totalChunks);

				if(chunkData && chunkData.attendance_data) {
					// Merge chunk data with existing data
					mergeStaffChunkData(chunkData.attendance_data);
				}

				// If all chunks processed, display final result
				if(processedChunks >= totalChunks) {
					finalizeChunkedStaffReport();
				}
			});

			chunkPromises.push(chunkPromise);
		}
	}
}

function processStaffAttendanceChunk(chunk_from_date, chunk_to_date, staff_ids, selected_staff_type, staff_status_type, chunk_index, total_chunks) {
	return new Promise(function(resolve, reject) {
		$.ajax({
			url: '<?php echo site_url('staff/attendance/processAttendanceChunk'); ?>',
			type: 'post',
			data: {
				'chunk_from_date': chunk_from_date,
				'chunk_to_date': chunk_to_date,
				'staff_ids': staff_ids,
				'selected_staff_type': selected_staff_type,
				'staff_status_type': staff_status_type,
				'chunk_index': chunk_index,
				'total_chunks': total_chunks
			},
			success: function(data) {
				try {
					var chunkResponse = JSON.parse(data);
					if(chunkResponse.success) {
						resolve(chunkResponse);
					} else {
						console.error('Chunk processing failed:', chunkResponse.error);
						resolve(null);
					}
				} catch(e) {
					console.error('Error parsing chunk response:', e);
					resolve(null);
				}
			},
			error: function(err) {
				console.error('Chunk request failed:', err);
				resolve(null);
			}
		});
	});
}

function mergeStaffChunkData(chunkAttendanceData) {
	// Merge attendance data by staff_id
	chunkAttendanceData.forEach(function(staffData) {
		var existingStaffIndex = chunkedStaffAttendanceData.findIndex(function(existing) {
			return existing.staff_id === staffData.staff_id;
		});

		if(existingStaffIndex >= 0) {
			// Merge attendance records for existing staff
			var existingStaff = chunkedStaffAttendanceData[existingStaffIndex];

			// Merge daily attendance records
			Object.keys(staffData).forEach(function(key) {
				if(key !== 'staff_id' && key !== 'staff_name' && key !== 'employee_code' &&
				   key !== 'email' && key !== 'picture_url' && !key.startsWith('total')) {
					existingStaff[key] = staffData[key];
				}
			});

			// Update totals by adding values
			['totalPresentDays', 'totalWorkingDays', 'totalabsentDays', 'totalweekoff',
			 'totalhoiday', 'totalleavestaken', 'total_work_duration', 'total_ot',
			 'total_lateby', 'total_late_count', 'total_earlyby', 'total_duration'].forEach(function(totalKey) {
				if(staffData[totalKey] !== undefined) {
					existingStaff[totalKey] = (existingStaff[totalKey] || 0) + (staffData[totalKey] || 0);
				}
			});
		} else {
			// Add new staff data
			chunkedStaffAttendanceData.push(staffData);
		}
	});
}

function showChunkedStaffProgress(processed, total) {
	var progressHtml = `
		<div class="text-center" id="chunked-progress">
			<div class="mb-3">
				<i class="fa fa-spinner fa-spin" style="font-size: 2rem;"></i>
			</div>
			<div class="progress" style="height: 25px;">
				<div class="progress-bar progress-bar-striped progress-bar-animated active"
					 role="progressbar" style="width: 0%; background-color: #007bff; animation: progress-bar-stripes 1s linear infinite;" id="chunk-progress-bar">
					0%
				</div>
			</div>
			<p class="mt-2">
				<small>Processed <span id="processed-chunks">0</span> of <span id="total-chunks">${total}</span> chunks</small>
			</p>
		</div>
	`;
	$("#attendance-data").html(progressHtml);
}

function updateChunkedStaffProgress(processed, total) {
	var percentage = Math.round((processed / total) * 100);
	$("#chunk-progress-bar").css('width', percentage + '%').text(percentage + '%');
	$("#processed-chunks").text(processed);
}

function finalizeChunkedStaffReport() {
	// Display the final report
	if(chunkedStaffAttendanceData.length == 0){
		$("#attendance-data").html('<h3 class="no-data-display">No Data Available</h3>')
		// $("#staff-search-container").hide();
	} else {
		$("#attendance-data").html(construct_staff_attendence(chunkedStaffDates, chunkedStaffAttendanceData));

		// Show search functionality for large datasets
		if(chunkedStaffAttendanceData.length > 20) {
			// $("#staff-search-container").show();
			updateSearchCount();
		}
	}
}

// Search functionality
function searchStaff() {
	var searchTerm = $("#staff-search").val().toLowerCase();
	var matchCount = 0;

	$(".staff-attendance-block").each(function() {
		var staffName = $(this).data('staff-name');
		var employeeCode = $(this).find('h5 b').text().toLowerCase();

		if(searchTerm === '' || staffName.includes(searchTerm) || employeeCode.includes(searchTerm)) {
			$(this).show();
			matchCount++;
		} else {
			$(this).hide();
		}
	});

	$("#search-results-count").text(matchCount);
}

function clearSearch() {
	$("#staff-search").val('');
	$(".staff-attendance-block").show();
	updateSearchCount();
}

function updateSearchCount() {
	var totalCount = $(".staff-attendance-block").length;
	$("#search-results-count").text(totalCount);
}

function construct_staff_attendence(dates,attendance){
    var html = '';
    var colors = {
		AB : '#ff8b90',
		HD : '#6aa54d',
		P:"#6aa54d",
		OOD:"#6aa54d",
		WO:"#a6aab5",
		H: "#a6aab5"
	};
    var j=1;
    for(var i in attendance){
        var picture_url = '';
        if (attendance[i].picture_url == null || attendance[i].picture_url == '') {
                picture_url = '<?php echo base_url('assets/img/sample_boy_image.png')?>';
            } else {
                picture_url = attendance[i].picture_url;
            }
            var style= '';
            if(j%3 == 0){
                style = 'page-break-after:always';
            }

        html += `<div style="${style};margin-top:30px;" class="staff-attendance-block" data-staff-name="${attendance[i].staff_name.toLowerCase()}">
        <div class="col-md-3">
            <h5 ><b>${j++} . ${attendance[i].employee_code==="NA" && "" || attendance[i].employee_code} ${attendance[i].employee_code!=="NA" && " - " || ""} ${attendance[i].staff_name}</b></h5>
        </div>
        <div class="col-md-6">
            <p><b>Total Work Duration : </b>`+calculateTime(attendance[i].total_work_duration)+ `&nbsp;<b>Total OT : </b>`+calculateTime(attendance[i].total_ot)+` &nbsp;<b>Present : </b> ${attendance[i].totalPresentDays} &nbsp; <b>Absent : </b>${attendance[i].totalabsentDays} &nbsp; <b>Weekly off : </b>${attendance[i].totalweekoff} &nbsp; <b>Holiday : </b>${attendance[i].totalhoiday} &nbsp;  <b>Leaves Taken : </b>${attendance[i].totalleavestaken} &nbsp;  <b>Late By : </b>`+calculateTime(attendance[i].total_lateby)+` &nbsp;  <b>Early By : </b>`+calculateTime(attendance[i].total_earlyby)+`&nbsp;</p>
        </div>
        <div class="col-md-12 table-responsive" id="staff_data_${attendance[i].staff_id}">
            <table id="att-table" class="table table-bordered">
            <tr>
            <th>Date</th>`;
            for(var k in dates) {
             html += `<th >${dates[k].format3}</th>`;
            }
            html +=`</tr>
            <tr>
            <th>Shift</th>`;
            for(var k in dates) {
                if(dates[k].format2 in attendance[i]) {
                    html += '<td>'+attendance[i][dates[k].format2].name+'</td>';
                }else{
                    html += `<td>-</td>`;
                }
            }
            html += `</tr>
            <tr>
            <th>Status</th>`;
            for(var k in dates) {
                if(dates[k].format2 in attendance[i]) {
                    var status = attendance[i][dates[k].format2].status;
                    var leaveStatus="";

                    if(attendance[i][dates[k].format2].leave_information && Object.keys(attendance[i][dates[k].format2].leave_information).length){
                        leaveStatus=`(${attendance[i][dates[k].format2].leave_information?.all_taken_leaves_with_status_for_single_day})`;
                    }

                    const attStatus=status=="AB" && leaveStatus.length>0 ? leaveStatus : `${status} ${leaveStatus}`;

                    html += `<td style="background-color:${colors[attStatus.trim()]}">${attStatus} ${attendance[i][dates[k].format2].is_late==1 ? " (L)" : "" }</td>`;
                }else{
                    html += `<td>-</td>`;
                }
            }
            html += `</tr>
            <tr>
            <th>In</th>`;
            for(var k in dates) {
                if(dates[k].format2 in attendance[i]) {
                    html += `<td style>${attendance[i][dates[k].format2].first_check_in_time}</td>`;
                }else{
                    html += `<td>-</td>`;
                }
            }
            html += `</tr>
            <tr>
            <th>Out</th>`;
            for(var k in dates) {
                if(dates[k].format2 in attendance[i]) {
                    html += `<td>${attendance[i][dates[k].format2].last_check_out_time}</td>`;
                }else{
                    html += `<td>-</td>`;
                }
            }
            html += `</tr>
            <tr>
            <th>Duration</th>`;
            for(var k in dates) {
                if(dates[k].format2 in attendance[i]) {
                    html += '<td>'+calculateTime(attendance[i][dates[k].format2].duration)+'</td>';
                }else{
                    html += `<td>-</td>`;
                }
            }
            html += `</tr>
            <tr>
            <th>Late</th>`;
            for(var k in dates) {
                if(dates[k].format2 in attendance[i]) {
                    html += '<td>'+calculateTime(attendance[i][dates[k].format2].late_by)+'</td>';
                }else{
                    html += `<td>-</td>`;
                }
            }
            html += `</tr>
            <tr>
            <th>Early</th>`;
            for(var k in dates) {
                if(dates[k].format2 in attendance[i]) {
                    html += '<td>'+calculateTime(attendance[i][dates[k].format2].early_by)+'</td>';
                }else{
                    html += `<td>-</td>`;
                }
            }
            html += `</tr>
            </table>
        </div>
    </div>`;
    }

    // Add summary at the end for large datasets
    // if(attendance.length > 20) {
    //     html += `<div class="alert alert-success mt-4">
    //         <strong>Report Complete:</strong> Successfully loaded detailed attendance data for ${attendance.length} staff members.
    //     </div>`;
    // }

  return html;
}

function calculateTime(duration) {
	if(duration == 0 || duration < 0) return '';
    if(duration < 60) {
      return duration + ' m';
    } else {
      var hr = duration / 60;
      $result = parseInt(hr) + ' h ' + (duration % 60) + ' m';
      if($result < 0 ){
        return '';
      }
      return $result;
    }
  }

  function exportToExcel(){
          var htmls = "";
          var uri = 'data:application/vnd.ms-excel;base64,';
          var template = '<html xmlns:o="urn:schemas-microsoft-com:office:office" xmlns:x="urn:schemas-microsoft-com:office:excel" xmlns="http://www.w3.org/TR/REC-html40"><head><!--[if gte mso 9]><xml><x:ExcelWorkbook><x:ExcelWorksheets><x:ExcelWorksheet><x:Name>{worksheet}</x:Name><x:WorksheetOptions><x:DisplayGridlines/></x:WorksheetOptions></x:ExcelWorksheet></x:ExcelWorksheets></x:ExcelWorkbook></xml><![endif]--><meta http-equiv="content-type" content="text/plain; charset=UTF-8"/></head><body><table>{table}</table></body></html>';
          var base64 = function(s) {
              return window.btoa(unescape(encodeURIComponent(s)))
          };

          var format = function(s, c) {
              return s.replace(/{(\w+)}/g, function(m, p) {
                  return c[p];
              })
          };

          var mainTable = $("#attendance-data").html();

          htmls =mainTable;

          var ctx = {
            worksheet : 'Spreadsheet',
            table : htmls
          }

          var link = document.createElement("a");
          
          const reportName=`staff_attendance_detailed_report_${new Date().toLocaleString('default', { month: 'short' })+" "+new Date().getDate()+" "+new Date().getFullYear()}_${new Date().getHours()+""+new Date().getMinutes()}`;

          link.download = reportName;
          link.href = uri + base64(format(template, ctx));
          link.click();

        }

        function print_visitor(){
          var restorepage = document.body.innerHTML;
          var printcontent = document.getElementById('attendance-data').innerHTML;
          document.body.innerHTML = printcontent;
          window.print();
          document.body.innerHTML = restorepage;
        }
</script>

  <style type="text/css">
  @page {
    size: auto !important;
}

/* Progress bar animation for moving stripes */
@keyframes progress-bar-stripes {
  from {
    background-position: 1rem 0;
  }
  to {
    background-position: 0 0;
  }
}

.progress-bar-animated {
  animation: progress-bar-stripes 1s linear infinite;
}

.progress-bar-striped {
  background-image: linear-gradient(45deg, rgba(255, 255, 255, 0.15) 25%, transparent 25%, transparent 50%, rgba(255, 255, 255, 0.15) 50%, rgba(255, 255, 255, 0.15) 75%, transparent 75%, transparent);
  background-size: 1rem 1rem;
}

</style>
