<div class="col-md-12">
    <!-- Locked Order Notification Banner -->
    <div id="locked-order-banner" style="display: none;" class="locked-order-banner mb-4">
        <div class="locked-order-content">
           
            <div class="lock-message">
                <h5>Order Actions Locked</h5>
                <p id="lock-reason">This order's status prevents making any changes to ID cards.</p>
            </div>
        </div>
    </div>
    <?php 
        $stakeholderId = ($this->authorization->isAuthorized('ID_CARDS.MANAGE_ID_CARD_ORDERS')) 
            ? true 
            : (($this->authorization->getAvatarStakeHolderId() == 0) ? true : false);
        ?>
    <div id="entity-container" class="entity-container">
        <!-- Filter Bar -->
        <div class="filter-bar">
            <div class="status-tabs">
                <button class="status-tab active" data-status="all">All <span class="status-count"><?php if($stakeholderId){ echo isset($status_counts->total) ?  $status_counts->total : 0; } ?></span></button>
                <button class="status-tab" data-status="in review">In Review <span class="status-count"><?php if($stakeholderId){ echo isset($status_counts->in_review) ? $status_counts->in_review : 0; }?></span></button>
                <button class="status-tab" data-status="approved">Approved <span class="status-count"><?php if($stakeholderId){ echo isset($status_counts->approved) ? $status_counts->approved : 0; } ?></span></button>
                <button class="status-tab" data-status="removed">Removed <span class="status-count"><?php if($stakeholderId){ echo isset($status_counts->removed) ? $status_counts->removed : 0 ; }?></span></button>
                <button class="status-tab" data-status="modify">Modify <span class="status-count"><?php if($stakeholderId){ echo isset($status_counts->modify) ? $status_counts->modify : 0; } ?></span></button>
                <button class="status-tab" data-status="re_ordered">Re-Ordered <span class="status-count"><?php if($stakeholderId){ echo isset($status_counts->re_ordered) ? $status_counts->re_ordered : 0; } ?></span></button>
            </div>
            <div class="search-filters">
                <input type="text" class="search-input" id="search-entities" placeholder="<?= $order_details->id_card_for == 'Staff' ? 'Search Staff' : 'Search Students' ?>">
                <!-- <select class="filter-select" id="class-filter">
                    <option value=""><?php // $order_details->id_card_for == 'Staff' ? 'Department' : 'Class' ?></option>
                    <?php //if($order_details->id_card_for == 'Staff'): ?>
                    <option value="IT">IT</option>
                    <option value="HR">HR</option>
                    <option value="Admin">Admin</option>
                    <?php //else: ?>
                    <option value="1">Class 1</option>
                    <option value="2">Class 2</option>
                    <option value="3">Class 3</option>
                    <?php //endif; ?>
                </select>
                <select class="filter-select" id="section-filter">
                    <option value=""><?php //$order_details->id_card_for == 'Staff' ? 'Designation' : 'Section' ?></option>
                    <?php //if($order_details->id_card_for == 'Staff'): ?>
                    <option value="Manager">Manager</option>
                    <option value="Developer">Developer</option>
                    <option value="Assistant">Assistant</option>
                    <?php //else: ?>
                    <option value="A">A</option>
                    <option value="B">B</option>
                    <option value="C">C</option>
                    <?php //endif; ?>
                </select> -->
            </div>
        </div>

        <!-- Entity Table -->
        <div class="entity-table-container">
            <div class="entity-table-wrapper">
                <!-- Table will be inserted here by construct_entity_table function -->
            </div>
        </div>

        <div class="entity-count">
            Showing <span id="visible-count"></span> of <span id="total-count"></span> <?= $order_details->id_card_for == 'Staff' ? 'Staff' : 'Students' ?>
            <div id="loading-progress" class="loading-progress-inline" style="display: none;">
                <div class="progress-bar-container">
                    <div class="progress-bar" id="data-progress-bar"></div>
                </div>
                <span id="progress-text">Loading...</span>
            </div>

        </div>
    </div>
</div>

<!-- Enhanced Loading Overlay -->
<div id="enhanced-loading-overlay" style="display: none;">
    <div class="enhanced-loading-content">
        <div class="loading-animation">
            <div class="spinner-border" role="status">
                <span class="sr-only">Loading...</span>
            </div>
        </div>
        <h4 id="loading-title">Loading Data</h4>
        <p id="loading-message">Please wait while we fetch your data...</p>
        <div class="loading-progress-bar">
            <div class="progress">
                <div class="progress-bar progress-bar-striped progress-bar-animated"
                     id="main-progress-bar" role="progressbar" style="width: 0%"></div>
            </div>
            <small id="loading-stats" class="text-muted">Preparing...</small>
        </div>
    </div>
</div>

<!-- Notification Container -->
<div id="notification-container" class="notification-container"></div>

<!-- ID Card Preview Modal -->
<div id="idCardModal" class="id-card-modal">
    <div class="id-card-modal-content">
        <!-- Close button at the top right corner -->
        <span class="id-card-close ">&times;</span>
        <h3 id="idCardModalTitle" class="my-2" >ID Card Preview</h3>
        <!-- Navigation buttons -->
        <div class="id-card-navigation">
            <button id="prevStaffBtn" class="id-card-nav-btn icon-btn" title="Previous">
                <i class="fa fa-chevron-left"></i>
            </button>

            <button id="nextStaffBtn" class="id-card-nav-btn icon-btn" title="Next">
                <i class="fa fa-chevron-right"></i>
            </button>
        </div>

        <!-- Modal content loading indicator -->
        <div id="modalLoadingIndicator" style="display: none;">
            <div class="modal-loading-spinner">
                <i class="fa fa-spinner fa-spin fa-3x"></i>
                <p>Loading ID card data...</p>
            </div>
        </div>

        <div class="card">
            <div class="card-header" style="display: flex; align-items: center; gap: 12px; min-height: 32px;">
                <!-- Status badge will be injected here by JS -->
                <div id="entityStatusBadge"
                    style="font-size: 14px; font-weight: 500; padding: 4px 10px; border-radius: 20px; min-width: 100px; display: inline-block;" class="status-button"><div class="dot"></div>
                    <!-- Status will be set dynamically -->
                </div>
                <div id="entityPositionIndicator" style="font-size: 12px; color: #888; margin-left: 12px; display: none;"></div>
            </div>
            <div class="card-body">
                <div class="row">
                    <div class="col-md-6 text-center mb-4">
                        <h5 class="mb-3">Front</h5>
                        <div id="frontCardPreview" class="card-preview mx-auto">
                            <!-- Front preview will be rendered here -->
                        </div>
                    </div>
                    <div class="col-md-6 text-center mb-4">
                        <h5 class="mb-3">Back</h5>
                        <div id="backCardPreview" class="card-preview mx-auto">
                            <!-- Back preview will be rendered here -->
                        </div>
                    </div>
                </div>
            </div>
        </div>

        <!-- Progress bar for approval process -->
        <div id="approval-progress-container" style="display: none;">
            <div class="progress-status">Processing: <span id="progress-status-text">Preparing...</span></div>
            <div class="progress-bar-container">
                <div id="approval-progress-bar" class="progress-bar"></div>
            </div>
            <div class="progress-percentage">Progress: <span id="progress-percentage">0%</span></div>
        </div>

        <div class="id-card-actions">
            <button class="id-card-action-btn id-card-approve" disabled>
                <i class="fa fa-spinner fa-spin"></i> LOADING...</button>
            <button class="id-card-action-btn id-card-modify" disabled>
                <i class="fa fa-spinner fa-spin"></i> LOADING...</button>
            <button class="id-card-action-btn id-card-remove" disabled>
                <i class="fa fa-spinner fa-spin"></i> LOADING...</button>

        </div>

        <!-- New: Custom Loading Overlay for Approval Progress -->
        <div id="customApprovalLoadingOverlay" style="display:none;">
            <div class="custom-approval-loading-bg"></div>
            <div class="custom-approval-loading-content">
                <div class="custom-approval-title">Processing your ID card</div>
                <div class="custom-approval-desc">We're working on your ID card! Just hang tight for a bit...</div>
                <div class="custom-approval-progress-img">
                    <!-- Circular progress bar replaces the image -->
                    <div id="customApprovalProgressCircle" class="circular-progress">
                        <svg viewBox="0 0 100 100" width="120" height="120">
                            <circle class="progress-bg" cx="50" cy="50" r="45"/>
                            <circle class="progress-bar" cx="50" cy="50" r="45"/>
                        </svg>
                        <div class="custom-approval-progress-text" id="customApprovalProgressText">0%</div>
                    </div>
                </div>
            </div>
        </div>

    </div>
</div>

<style>


/* Enhanced entity table container */
.entity-table-container {
    height: 350px;
    overflow: scroll;
    border: 1px solid #dee2e6;
    border-radius: 4px;
    position: relative;
}

/* Ensure table takes full width */
.entity-table-container .entity-table {
    width: 100%;
    margin-bottom: 0;
}



/* Improved scroll indicators */
.entity-table-container::-webkit-scrollbar {
    width: 8px;
}

.entity-table-container::-webkit-scrollbar-track {
    background: #f1f1f1;
    border-radius: 4px;
}

.entity-table-container::-webkit-scrollbar-thumb {
    background: #007bff;
    border-radius: 4px;
}

.entity-table-container::-webkit-scrollbar-thumb:hover {
    background: #0056b3;
}



/* Loading progress indicator */
.loading-progress {
    position: fixed;
    top: 10px;
    right: 10px;
    background: rgba(0, 0, 0, 0.8);
    color: white;
    padding: 8px 12px;
    border-radius: 4px;
    font-size: 12px;
    z-index: 1000;
    display: none;
}

/* Smooth scrolling for better UX */
.entity-table-container {
    scroll-behavior: smooth;
}

/* Enhanced table styles for better performance */
.entity-table {
    margin-bottom: 0;
}

.entity-table tbody tr {
    transition: background-color 0.2s ease;
}

.entity-table tbody tr:hover {
    background-color: #f8f9fa;
}

/* Status badge improvements */
.status-badge {
    font-size: 11px;
    padding: 4px 8px;
    border-radius: 12px;
    font-weight: 500;
    text-transform: uppercase;
    letter-spacing: 0.5px;
}

/* Enhanced Loading Overlay */
#enhanced-loading-overlay {
    position: fixed;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    background: rgba(255, 255, 255, 0.95);
    display: flex;
    justify-content: center;
    align-items: center;
    z-index: 9999;
    backdrop-filter: blur(2px);
}

.enhanced-loading-content {
    text-align: center;
    background: white;
    padding: 40px;
    border-radius: 12px;
    box-shadow: 0 10px 30px rgba(0, 0, 0, 0.1);
    max-width: 400px;
    width: 90%;
}

.loading-animation {
    margin-bottom: 20px;
}

.spinner-border {
    width: 3rem;
    height: 3rem;
    color: #007bff;
}

#loading-title {
    color: #333;
    margin-bottom: 10px;
    font-weight: 600;
}

#loading-message {
    color: #666;
    margin-bottom: 20px;
    font-size: 14px;
}

.loading-progress-bar {
    margin-top: 20px;
}

.loading-progress-bar .progress {
    height: 8px;
    border-radius: 4px;
    background-color: #e9ecef;
}

.loading-progress-bar .progress-bar {
    background: linear-gradient(90deg, #007bff, #0056b3);
    transition: width 0.3s ease;
}

#loading-stats {
    display: block;
    margin-top: 8px;
    font-size: 12px;
}

/* Inline Loading Progress */
.loading-progress-inline {
    margin-top: 10px;
    padding: 10px;
    background: #f8f9fa;
    border-radius: 6px;
    border: 1px solid #dee2e6;
}

.progress-bar-container {
    width: 100%;
    height: 6px;
    background: #e9ecef;
    border-radius: 3px;
    overflow: hidden;
    margin-bottom: 5px;
}

.progress-bar {
    height: 100%;
    background: linear-gradient(90deg, #28a745, #20c997);
    transition: width 0.3s ease;
    border-radius: 3px;
}

#progress-text {
    font-size: 12px;
    color: #6c757d;
    font-weight: 500;
}

/* Notification Container */
.notification-container {
    position: fixed;
    top: 20px;
    right: 20px;
    z-index: 10000;
    max-width: 350px;
}

.notification {
    background: white;
    border-radius: 8px;
    box-shadow: 0 4px 12px rgba(0, 0, 0, 0.15);
    margin-bottom: 10px;
    padding: 16px;
    border-left: 4px solid #007bff;
    animation: slideInRight 0.3s ease;
    position: relative;
}

.notification.success {
    border-left-color: #28a745;
}

.notification.error {
    border-left-color: #dc3545;
}

.notification.warning {
    border-left-color: #ffc107;
}

.notification .notification-title {
    font-weight: 600;
    margin-bottom: 4px;
    color: #333;
}

.notification .notification-message {
    font-size: 14px;
    color: #666;
    margin: 0;
}

.notification .notification-close {
    position: absolute;
    top: 8px;
    right: 12px;
    background: none;
    border: none;
    font-size: 18px;
    color: #999;
    cursor: pointer;
    padding: 0;
    width: 20px;
    height: 20px;
    display: flex;
    align-items: center;
    justify-content: center;
}

.notification .notification-close:hover {
    color: #666;
}

@keyframes slideInRight {
    from {
        transform: translateX(100%);
        opacity: 0;
    }
    to {
        transform: translateX(0);
        opacity: 1;
    }
}

/* Responsive improvements */
@media (max-width: 768px) {
    .entity-table-container {
        max-height: 400px;
    }

    .chunk-loading-indicator {
        padding: 15px;
    }

    .loading-spinner {
        font-size: 12px;
    }

    .enhanced-loading-content {
        padding: 30px 20px;
    }

    .notification-container {
        right: 10px;
        left: 10px;
        max-width: none;
    }
}
</style>

<script>
    let order_id='<?php echo $order_id ?>';
    let template_id='<?php echo $template ?>';
    let id_card_for='<?= $order_details->id_card_for ?>';
    let order_status = '<?= $order_details->status ?>';
    let selectedStatus = 'all'; // Add this variable at the top level

    // Face API Models Loading
    let faceApiModelsLoaded = false;
    // Check if order is in a locked state
    const isOrderLocked = ['order_submitted', 'in_printing', 'delivered'].includes(order_status);

    // Show locked order banner if order is in a locked state
    if (isOrderLocked) {
        // Set the appropriate message based on order status
        let lockReason = '';
        let lockIcon = '';

        switch (order_status) {
            case 'order_submitted':
                lockReason = 'This order has been submitted for payment processing. No further changes can be made to ID cards at this stage.';
                lockIcon = 'fa-credit-card';
                break;
            case 'in_printing':
                lockReason = 'This order is currently in the printing process. ID cards cannot be modified once printing has begun.';
                lockIcon = 'fa-print';
                break;
            case 'delivered':
                lockReason = 'This order has been delivered. ID cards cannot be modified after delivery.';
                lockIcon = 'fa-check-circle';
                break;
            default:
                lockReason = 'This order\'s status prevents making any changes to ID cards.';
                lockIcon = 'fa-lock';
        }

        // Update the banner content
        $('#lock-reason').text(lockReason);
        $('.lock-icon i').removeClass('fa-lock').addClass(lockIcon);

        // Show the banner
        $('#locked-order-banner').show();
    }

    // Static field mappings
    const FIELD_MAPPINGS = [
        { field: '[[NAME]]', key: 'name' },
        { field: '[[ID]]', key: 'employee_code' },
        { field: '[[DEPARTMENT]]', key: 'department' },
        { field: '[[DESIGNATION]]', key: 'designation' },
        { field: '[[CONTACT]]', key: 'contact' },
        { field: '[[PARENT_NAME]]', key: 'parent_name' },
        { field: '[[BLOOD_GROUP]]', key: 'blood_group' },
        { field: '[[EMERGENCY_CONTACT]]', key: 'emergency_contact' },
        { field: '[[DATE_OF_BIRTH]]', key: 'dob' },
        { field: '[[STUDENT_ADDRESS]]', key: 'address' },
        { field: '[[FATHER_ADDRESS]]', key: 'father_address' },
        { field: '[[MOTHER_ADDRESS]]', key: 'mother_address' },
        { field: '[[FATHER_PHOTO]]', key: 'father_photo' },
        { field: '[[MOTHER_PHOTO]]', key: 'mother_photo' },
        { field: '[[GRADE]]', key: 'grade' },
        { field: '[[GRADE_SECTION]]', key: 'grade_section' },
        { field: '[[QR_CODE]]', key: 'qr_code' },
        { field: '[[BAR_CODE]]', key: 'bar_code' },
        { field: '[[SIGNATURE]]', key: 'signature' },
        { field: '[[LOGO]]', key: 'logo' },
        { field: '[[ADDRESS]]', key: 'address' },
        { field: '[[EMAIL]]', key: 'email' },
        { field: '[[PHONE]]', key: 'phone' },
        { field: '[[WEBSITE]]', key: 'website' },
        { field: '[[SOCIAL_MEDIA]]', key: 'social_media' },
        { field: '[[QR_CODE_URL]]', key: 'qr_code_url' },
        { field: '[[BAR_CODE_URL]]', key: 'bar_code_url' },
        { field: '[[SIGNATURE_URL]]', key: 'signature_url' },
        { field: '[[LOGO_URL]]', key: 'logo_url' },
        { field: '[[ADDRESS_URL]]', key: 'address_url' },
        { field: '[[EMAIL_URL]]', key: 'email_url' },
        { field: '[[PHONE_URL]]', key: 'phone_url' },
        { field: '[[WEBSITE_URL]]', key: 'website_url' },
        { field: '[[SOCIAL_MEDIA_URL]]', key: 'social_media_url' },
        { field: '[[COMBINATION]]', key: 'combination' },
        { field: '[[ENROLLMENT_NO]]', key: 'enrollment_no' },
        { field: '[[ALPHA_ROLL_NO]]', key: 'alpha_rollnum' },
        { field: '[[ADMISSION_NO]]', key: 'admission_no' },
        { field: '[[RELATION_TYPE]]', key: 'relation_type' },
        { field: '[[SIBLING_NAME]]', key: 'sibling_name' },
        { field: '[[NAME_CLASS]]', key: 'name_class' },
        { field: '[[STUDENT_PICKING_ROUTE]]', key: 'picking_route' },
        { field: '[[STUDENT_DROPPING_ROUTE]]', key: 'dropping_route' },
        { field: '[[SPOUSE_NAME]]', key: 'spouse_name' },
        { field: '[[STAFF_TYPE]]', key: 'staff_type' },
        { field: '[[QUALIFICATION]]', key: 'qualification' },
        { field: '[[CLASS_SECTION]]', key: 'class_section' }

    ];

    // Helper function to check if face detection is ready
    function isFaceDetectionReady() {
        return typeof faceapi !== 'undefined' &&
               faceApiModelsLoaded &&
               faceapi.nets.tinyFaceDetector.isLoaded;
    }

    // AI-powered face detection function
async function applyFaceDetection(photoId, photoUrl) {
    if (!photoUrl) {
        return;
    }

    const img = document.getElementById(photoId);
    if (!img) {
        console.warn('Face detection: Image element not found:', photoId);
        return;
    }

    // Set default positioning first
    $(img).css({
        'object-position': 'center top',
        'object-fit': 'cover'
    });

    // Skip face detection for external URLs or invalid URLs
    if (photoUrl.includes('wasabisys.com') || !isValidUrl(photoUrl)) {
        return;
    }

    // Check if Face API is ready for use
    if (!isFaceDetectionReady()) {
        // If models are still loading, retry after a delay
        if (typeof faceapi !== 'undefined' && !faceApiModelsLoaded) {
            setTimeout(() => applyFaceDetection(photoId, photoUrl), 2000);
            return;
        }
        // Use fallback positioning when Face API is not ready
        centerFaceInImage(img);
        return;
    }

    img.crossOrigin = "anonymous";

    // Wait for image to load before processing
    if (!img.complete) {
        img.onload = function() {
            performFaceDetection(img, photoUrl);
        };
        img.onerror = function() {
            // Image load error, using default positioning
            centerFaceInImage(img);
        };
    } else {
        performFaceDetection(img, photoUrl);
    }
}

// Perform actual face detection using Face API
async function performFaceDetection(img, photoUrl) {
    try {
        // Create a processing image for face detection
        const processImg = new Image();
        processImg.crossOrigin = "anonymous";

        // Wait for processing image to load
        await new Promise((resolve, reject) => {
            processImg.onload = resolve;
            processImg.onerror = () => {
                console.error('Error loading image for face detection');
                reject(new Error('Image load failed'));
            };
            processImg.src = photoUrl;
        });

        // Detect faces in the image
        const detections = await faceapi.detectAllFaces(
            processImg,
            new faceapi.TinyFaceDetectorOptions({ scoreThreshold: 0.3 })
        );

        if (detections && detections.length > 0) {
            // Get the first (most confident) face detected
            const face = detections[0];
            const box = face.box;

            // Face detected successfully

            // Calculate face center coordinates
            const faceX = box.x + (box.width / 2);
            const faceY = box.y + (box.height / 2);

            // Get image dimensions
            const imgWidth = processImg.naturalWidth || processImg.width;
            const imgHeight = processImg.naturalHeight || processImg.height;

            if (imgWidth === 0 || imgHeight === 0) {
                console.warn('Image has zero dimensions, using fallback positioning');
                centerFaceInImage(img);
                return;
            }

            // Calculate positioning percentages to center the face
            const faceXPercent = (faceX / imgWidth) * 100;
            const faceYPercent = (faceY / imgHeight) * 100;

            // Apply face-centered positioning with some adjustments for better framing
            const adjustedXPercent = Math.max(10, Math.min(90, faceXPercent));
            const adjustedYPercent = Math.max(15, Math.min(85, faceYPercent - 10)); // Slightly higher to show more of the face

            $(img).css({
                'object-position': `${adjustedXPercent}% ${adjustedYPercent}%`,
                'object-fit': 'cover'
            });

            // Face positioning applied successfully

        } else {
            // No face detected, using fallback positioning
            centerFaceInImage(img);
        }

    } catch (error) {
        console.error('Error in face detection:', error);
        // Fallback to basic positioning
        centerFaceInImage(img);
    }
}

// Fallback function for basic face positioning when AI detection fails
function centerFaceInImage(img) {
    try {
        if (!img.naturalWidth || !img.naturalHeight) {
            return;
        }

        // Improved fallback positioning based on image orientation
        if (img.naturalHeight > img.naturalWidth) {
            // Portrait image - position face higher up
            $(img).css({
                'object-position': 'center 20%',
                'object-fit': 'cover'
            });
        } else {
            // Landscape image - center horizontally, position face in upper third
            $(img).css({
                'object-position': 'center 30%',
                'object-fit': 'cover'
            });
        }

        // Applied fallback face positioning

    } catch (error) {
        // Error in centerFaceInImage, using basic center positioning
        $(img).css({
            'object-position': 'center center',
            'object-fit': 'cover'
        });
    }
}


// function applyFaceDetection(photoId, photoUrl) {
//     if (!photoUrl) {
//         return;
//     }

//     const img = document.getElementById(photoId);
//     if (!img) {
//         return;
//     }

//     $(img).css({
//         'object-position': 'center top'
//     });


//     if (photoUrl.includes('wasabisys.com') || !isValidUrl(photoUrl)) {
//         return;
//     }


//     img.crossOrigin = "anonymous";


//     if (!img.complete) {
//         img.onload = function() {
//             centerFaceInImage(img);
//         };
//         img.onerror = function() {
//             // console.log('Image load error, using default positioning');
//         };
//     } else {

//         centerFaceInImage(img);
//     }
// }


// function centerFaceInImage(img) {
//     try {

//         if (!img.naturalWidth || !img.naturalHeight) {
//             return;
//         }


//         if (img.naturalHeight > img.naturalWidth) {
//             $(img).css({
//                 'object-position': 'center 25%',
//                 'object-fit': 'fill'
//             });
//         } else {

//             $(img).css({
//                 'object-position': 'center 35%',
//                 'object-fit': 'fill'
//             });
//         }
//     } catch (error) {
//         // console.log('Error in centerFaceInImage:', error);
//     }
// }


function isValidUrl(string) {
    try {
        new URL(string);
        return true;
    } catch (_) {
        return false;
    }
}

// Load Face API models
async function loadFaceApiModels() {
    try {
        await Promise.all([
            faceapi.nets.tinyFaceDetector.loadFromUri('<?= base_url("assets/models/models") ?>'),
            faceapi.nets.faceLandmark68Net.loadFromUri('<?= base_url("assets/models/models") ?>'),
            faceapi.nets.faceRecognitionNet.loadFromUri('<?= base_url("assets/models/models") ?>'),
            faceapi.nets.faceExpressionNet.loadFromUri('<?= base_url("assets/models/models") ?>'),
        ]);
        faceApiModelsLoaded = true;
        console.log('Face API Models Loaded Successfully');
        return true;
    } catch (error) {
        console.error('Error loading Face API models:', error);
        faceApiModelsLoaded = false;
        return false;
    }
}

const cropFaceFromPhoto = async (url) => {
    try {
        const img = await faceapi.fetchImage(url);

        const detection = await faceapi
            .detectSingleFace(img, new faceapi.TinyFaceDetectorOptions())
            .withFaceLandmarks();

        if (!detection) {
            console.warn('No face detected.');
            return url;
        }

        const { x, y, width, height } = detection.detection.box;

        const imgWidth = img.width;
        const imgHeight = img.height;

        // Desired padding ratios
        const padRatioX = 0.4; // 40% of face width
        const padRatioYTop = 0.55; // 50% of face height (above head)
        const padRatioYBottom = 0.4; // 40% of face height (below chin)

        // Max padding in pixels
        const maxPadX = width * padRatioX;
        const maxPadTop = height * padRatioYTop;
        const maxPadBottom = height * padRatioYBottom;

        // Calculate how much space is actually available
        const padX = Math.min(x, imgWidth - (x + width), maxPadX);
        const padTop = Math.min(y, maxPadTop);
        const padBottom = Math.min(imgHeight - (y + height), maxPadBottom);

        // Define crop box
        const cropX = x - padX;
        const cropY = y - padTop;
        const cropWidth = width + padX * 2;
        const cropHeight = height + padTop + padBottom;

        // Prepare canvas
        const canvas = document.createElement('canvas');
        canvas.width = cropWidth * 2;
        canvas.height = cropHeight * 2;

        const ctx = canvas.getContext('2d');

        ctx.save();
        ctx.imageSmoothingEnabled = true;
        ctx.imageSmoothingQuality = 'high';
        ctx.filter = 'contrast(1.1) brightness(1.05)';

        // Optional: circular mask
        ctx.save();
        ctx.beginPath();
        ctx.arc(canvas.width / 2, canvas.height / 2, Math.min(canvas.width, canvas.height) / 2, 0, Math.PI * 2);
        ctx.closePath();
        ctx.clip();

        // Draw face portion
        ctx.drawImage(
            img,
            cropX, cropY,
            cropWidth, cropHeight,
            0, 0,
            canvas.width, canvas.height
        );

        ctx.restore();

        return canvas.toDataURL('image/jpeg', 0.95);
    } catch (err) {
        console.error('Face cropping error:', err);
        return url;
    }
};

// Initialize Face API when page loads
document.addEventListener('DOMContentLoaded', function() {
    // Check if face-api is available
    if (typeof faceapi !== 'undefined') {
        loadFaceApiModels();
    } else {
        console.warn('Face API library not available, face detection will be disabled');
    }
});

// Global variables for progress-based loading
let currentPage = 1;
let isLoading = false;
let hasMoreData = true;
let totalRecords = 0;
let loadedRecords = 0;
const recordsPerChunk = 100; // Larger chunks for faster loading
let loadingStartTime = null;
let maxLoadingTime = 300000; // 5 minutes maximum loading time

// Show loading with progress bar
showEnhancedLoading('Loading Data', 'Preparing to load all records...');

$(document).ready(function() {
    // Initialize progress-based data loading
    initializeProgressDataLoading();

    // Ensure status tabs are working
    console.log('Status tabs found:', $('.status-tab').length);
    if ($('.status-tab').length === 0) {
        console.error('No status tabs found! Check HTML structure.');
    }
});

/**
 * Initialize progress-based data loading system
 */
function initializeProgressDataLoading() {
    // Reset pagination variables
    currentPage = 1;
    isLoading = false;
    hasMoreData = true;
    totalRecords = 0;
    loadedRecords = 0;
    window.entityData = [];

    // Start loading all data with progress
    loadAllDataWithProgress();
}

/**
 * Load all data with progress indication
 */
function loadAllDataWithProgress() {
    if (isLoading) {
        return;
    }

    isLoading = true;
    loadingStartTime = Date.now(); // Track loading start time
    console.log('Starting progressive data loading...');

    // Load first chunk to get total count
    loadEntityChunk(1, true);
}

/**
 * Load a chunk of entity data for progress loading
 */
function loadEntityChunk(page = 1, isInitialLoad = false) {
    console.log('Loading chunk - Page:', page, 'Records:', loadedRecords + '/' + totalRecords);

    $.ajax({
        url: '<?php echo site_url('idcards/Idcards_controller/getEntitiesForOrderChunked'); ?>',
        type: 'POST',
        data: {
            order_id: order_id,
            id_card_for: id_card_for,
            status_filter: selectedStatus,
            page: page,
            limit: recordsPerChunk,
            search: $('#search-entities').val() || ''
        },
        dataType: 'json',
        success: function(response) {
            try {
                if (response.success && response.data) {
                    // Update pagination info
                    totalRecords = response.pagination.total_records;
                    hasMoreData = response.pagination.has_more;
                    currentPage = response.pagination.current_page;

                    if (isInitialLoad) {
                        // First load - initialize everything
                        window.entityData = response.data;
                        loadedRecords = response.data.length;

                        // Update status counts
                        updateStatusCountsFromResponse(response.status_counts);

                        // Build initial table
                        const tableHtml = construct_entity_table(window.entityData, id_card_for);
                        $('.entity-table-wrapper').html(tableHtml);

                        // Initialize event handlers
                        initializeEventHandlers();

                        // Update progress and continue loading
                        updateEnhancedLoadingProgress(
                            Math.round((loadedRecords / totalRecords) * 100),
                            `Loaded ${loadedRecords} of ${totalRecords} records...`
                        );

                        // Continue loading remaining data automatically
                        if (hasMoreData) {
                            setTimeout(() => {
                                continueProgressiveLoading();
                            }, 100);
                        } else {
                            // All data loaded in first chunk
                            isLoading = false; // Ensure loading state is reset
                            hideLoading();
                            hideEnhancedLoading(); // Explicitly hide enhanced loading overlay
                            $('#enhanced-loading-overlay').fadeOut(300); // Force hide overlay
                        }
                    } else {
                        // Subsequent loads - append data
                        window.entityData = window.entityData.concat(response.data);
                        loadedRecords += response.data.length;

                        // Append new rows to existing table
                        appendEntityRows(response.data);

                        // Update progress
                        updateEnhancedLoadingProgress(
                            Math.round((loadedRecords / totalRecords) * 100),
                            `Loaded ${loadedRecords} of ${totalRecords} records...`
                        );

                        // Continue loading if more data exists
                        if (hasMoreData) {
                            console.log('Scheduling next chunk load...');
                            setTimeout(() => {
                                continueProgressiveLoading();
                            }, 100); // Slightly slower for stability
                        } else {
                            // All data loaded
                            console.log('All data loading completed!');
                            isLoading = false; // Ensure loading state is reset
                            hideLoading();
                            hideEnhancedLoading(); // Explicitly hide enhanced loading overlay
                            $('#enhanced-loading-overlay').fadeOut(300); // Force hide overlay
                        }
                    }

                    // Update progress indicator
                    updateLoadingProgress();

                } else {
                    console.error('Error in chunked data response:', response);
                    handleChunkLoadError(response.message || 'Failed to load data');
                }
            } catch (e) {
                console.error('Error processing chunked data:', e);
                handleChunkLoadError('Error processing data: ' + e.message);
            }

            // Always reset loading state after each chunk
            isLoading = false;
        },
        error: function(xhr, status, error) {
            console.error('AJAX error in chunked loading:', error);
            console.error('XHR status:', xhr.status, 'Response:', xhr.responseText);
            handleChunkLoadError('Network error: ' + error);
            isLoading = false;
        }
    });
}

/**
 * Handle chunk loading errors
 */
function handleChunkLoadError(errorMessage) {
    if (currentPage === 1) {
        // First load failed - show error message
        hideLoading();
        $('.entity-table-wrapper').html('<div class="alert alert-danger">Error loading data: ' + errorMessage + '</div>');
    } else {
        // Subsequent load failed - show notification
        hideChunkLoadingIndicator();
        showNotification('error', 'Failed to load more data: ' + errorMessage);
    }
}

/**
 * Continue progressive loading of remaining data
 */
function continueProgressiveLoading() {
    console.log('continueProgressiveLoading called - hasMoreData:', hasMoreData, 'isLoading:', isLoading, 'currentPage:', currentPage);

    // Safety check for maximum loading time
    if (loadingStartTime && (Date.now() - loadingStartTime) > maxLoadingTime) {
        console.error('Loading timeout reached, stopping progressive loading');
        isLoading = false;
        hideLoading();
        showNotification('error', 'Loading Timeout', 'Loading took too long and was stopped. Please refresh the page.');
        return;
    }

    // Safety check for reasonable page limits
    if (currentPage > 50) { // Max 50 pages = 5000 records
        console.error('Too many pages loaded, stopping for safety');
        isLoading = false;
        hideLoading();
        showNotification('error', 'Loading Limit', 'Maximum loading limit reached. Please contact support.');
        return;
    }

    if (hasMoreData && !isLoading) {
        console.log('Loading next chunk:', currentPage + 1);
        loadEntityChunk(currentPage + 1, false);
    } else {
        console.log('Cannot continue loading:', { hasMoreData, isLoading, currentPage });

        // If we're stuck, reset and try once more
        if (hasMoreData && isLoading) {
            console.log('Detected stuck loading, resetting...');
            isLoading = false;
            setTimeout(() => {
                continueProgressiveLoading();
            }, 1000);
        }
    }
}





/**
 * Update loading progress indicator
 */
function updateLoadingProgress() {
    const progressPercentage = totalRecords > 0 ? Math.round((loadedRecords / totalRecords) * 100) : 0;
    const progressText = `Loaded ${loadedRecords} of ${totalRecords} records (${progressPercentage}%)`;

    // Update entity count display
    $('#visible-count').text(loadedRecords);
    $('#total-count').text(totalRecords);

    // Update inline progress bar
    if (totalRecords > recordsPerChunk) {
        $('#loading-progress').show();
        $('#data-progress-bar').css('width', progressPercentage + '%');
        $('#progress-text').text(progressText);
    } else {
        $('#loading-progress').hide();
    }

    // Update main progress bar if visible
    $('#main-progress-bar').css('width', progressPercentage + '%');
    $('#loading-stats').text(progressText);



    // Show completion notification
    if (!hasMoreData && loadedRecords > 0) {
        $('#loading-progress').hide();
        console.log('All data loaded successfully:', loadedRecords, 'records');

        // Force close loading overlay after completion
        setTimeout(() => {
            hideEnhancedLoading();
            $('#enhanced-loading-overlay').fadeOut(300);
            console.log('Forced loading overlay to close');
        }, 500);
    }
}

/**
 * Enhanced loading overlay functions
 */
function showEnhancedLoading(title = 'Loading Data', message = 'Please wait while we fetch your data...') {
    $('#loading-title').text(title);
    $('#loading-message').text(message);
    $('#main-progress-bar').css('width', '0%');
    $('#loading-stats').text('Preparing...');
    $('#enhanced-loading-overlay').fadeIn(300);
}

function hideEnhancedLoading() {
    $('#enhanced-loading-overlay').fadeOut(300);
    // Force hide with direct style change as backup
    setTimeout(() => {
        $('#enhanced-loading-overlay').hide().css('display', 'none');
    }, 400);
}

function updateEnhancedLoadingProgress(percentage, message) {
    $('#main-progress-bar').css('width', percentage + '%');
    $('#loading-stats').text(message);
}

/**
 * Notification system
 */
function showNotification(type = 'info', title = '', message = '', duration = 5000) {
    const notificationId = 'notification-' + Date.now();
    const iconClass = {
        'success': 'fa-check-circle',
        'error': 'fa-exclamation-circle',
        'warning': 'fa-exclamation-triangle',
        'info': 'fa-info-circle'
    }[type] || 'fa-info-circle';

    const notificationHtml = `
        <div id="${notificationId}" class="notification ${type}">
            <button class="notification-close" onclick="closeNotification('${notificationId}')">&times;</button>
            <div class="notification-title">
                <i class="fa ${iconClass}"></i> ${title}
            </div>
            <div class="notification-message">${message}</div>
        </div>
    `;

    $('#notification-container').append(notificationHtml);

    // Auto-remove notification after duration
    if (duration > 0) {
        setTimeout(() => {
            closeNotification(notificationId);
        }, duration);
    }
}

function closeNotification(notificationId) {
    $(`#${notificationId}`).fadeOut(300, function() {
        $(this).remove();
    });
}

/**
 * Override default loading functions to use enhanced loading
 */
function showLoading(message = 'Loading...') {
    showEnhancedLoading('Loading Data', message);
}

function hideLoading() {
    hideEnhancedLoading();
}

/**
 * Debug functions
 */




/**
 * Update status counts from server response
 */
function updateStatusCountsFromResponse(statusCounts) {
    if (statusCounts) {
        $('.status-tabs .status-tab[data-status="all"] .status-count').text(statusCounts.total || 0);
        $('.status-tabs .status-tab[data-status="in review"] .status-count').text(statusCounts.in_review || 0);
        $('.status-tabs .status-tab[data-status="approved"] .status-count').text(statusCounts.approved || 0);
        $('.status-tabs .status-tab[data-status="removed"] .status-count').text(statusCounts.removed || 0);
        $('.status-tabs .status-tab[data-status="modify"] .status-count').text(statusCounts.modify || 0);
        $('.status-tabs .status-tab[data-status="re_ordered"] .status-count').text(statusCounts.re_ordered || 0);
    }
}

/**
 * Append new entity rows to existing table
 */
function appendEntityRows(newData) {
    if (!newData || newData.length === 0) {
        return;
    }

    const tbody = $('.entity-table tbody');
    if (tbody.length === 0) {
        // No existing table, rebuild completely
        const tableHtml = construct_entity_table(window.entityData, id_card_for);
        $('.entity-table-wrapper').html(tableHtml);
        return;
    }

    // Append new rows
    newData.forEach(function(entity, index) {
        const rowHtml = constructEntityRow(entity, id_card_for);
        tbody.append(rowHtml);
    });

    // Reinitialize any necessary event handlers for new rows
    initializeNewRowEventHandlers();
}

/**
 * Construct a single entity row HTML
 */
function constructEntityRow(entity, id_card_for) {
    if (!entity || typeof entity !== 'object') {
        return '';
    }

    const status = (entity.status || 'in review').replace(/\s+/g, '-');
    const entityId = id_card_for.toLowerCase() === 'staff' ?
        (entity.sm_id || entity.id) :
        (id_card_for.toLowerCase() === 'parent' ?
            (entity.id) :
            (entity.sa_id));

    let html = '<tr data-entity-id="' + entityId + '" data-status="' + status + '">';

    if (id_card_for.toLowerCase() === 'staff') {
        html += '<td>' + (entity.staff_name || '') + '</td>';
        html += '<td class="status-cell" style="text-align:center">';
        html += '<span class="status-badge status-' + status + '">' + capitalizeFirstLetter(status) + '</span>';
        html += '</td>';
        html += '<td>' + (entity.employee_code || '') + '</td>';
        html += '<td style="white-space: nowrap;">' + (entity.dob || '') + '</td>';
        html += '<td>' + (entity.contact || '') + '</td>';
    } else if (id_card_for.toLowerCase() === 'parent') {
        // Handle parent rows based on avatar_type
        if (entity.avatar_type == 'Mother') {
            html += '<td>' + (entity.name || '') + '</td>';
            html += '<td class="status-cell" style="text-align:center">';
            html += '<span class="status-badge status-' + status + '">' + capitalizeFirstLetter(status) + '</span>';
            html += '</td>';
            html += '<td>Mother</td>';
            html += '<td>' + (entity.name || '') + '</td>';
            html += '<td>' + (entity.class_section || '') + '</td>';
            html += '<td>' + (entity.mother_contact || '') + '</td>';
        } else if (entity.avatar_type == 'Father') {
            html += '<td>' + (entity.father_name || '') + '</td>';
            html += '<td class="status-cell" style="text-align:center">';
            html += '<span class="status-badge status-' + status + '">' + capitalizeFirstLetter(status) + '</span>';
            html += '</td>';
            html += '<td>Father</td>';
            html += '<td>' + (entity.name || '') + '</td>';
            html += '<td>' + (entity.class_section || '') + '</td>';
            html += '<td>' + (entity.father_contact || '') + '</td>';
        }
        // Add other parent types as needed...
    } else {
        // Student rows
        html += '<td>' + (entity.name || '') + '</td>';
        html += '<td class="status-cell" style="text-align:center">';
        html += '<span class="status-badge status-' + status + '">' + capitalizeFirstLetter(status) + '</span>';
        html += '</td>';
        html += '<td>' + (entity.class_section || '') + '</td>';
        html += '<td style="white-space: nowrap;">' + (entity.dob || '') + '</td>';
        html += '<td>' + (entity.preferred_contact_no || '') + '</td>';
    }

    // Add action buttons
    html += '<td class="actions-cell">';
    html += '<div class="button-container">';

    const isOrderLocked = ['order_submitted', 'in_printing', 'delivered'].includes(order_status);
    const style = status.toLowerCase().trim() === 'modify' ? '' : 'display:none';
    const profileId = entityId;

    if (isOrderLocked) {
        html += '<button class="action-btn edit-btn locked-action" disabled ' +
                'title="Actions are locked - Order is in ' + capitalizeFirstLetter(order_status.replace('_', ' ')) + ' status">' +
                '<i class="fa fa-lock"></i></button>';
        html += '<button class="action-btn info-btn" ' +
                'title="View ID card (approval disabled)" ' +
                'data-entity="' + encodeURIComponent(JSON.stringify(entity)) + '">' +
                '<i class="fa fa-info-circle"></i></button>';
    } else {
        html += '<button style="' + style + '" class="action-btn edit-btn" ' +
                'onclick="navigateToProfile(\'' + id_card_for.toLowerCase() + '\', \'' + profileId + '\')" ' +
                'title="Edit"><i class="fa fa-pencil"></i></button>';
        html += '<button class="action-btn info-btn" ' +
                'title="View and approve ID card" ' +
                'data-entity="' + encodeURIComponent(JSON.stringify(entity)) + '">' +
                '<i class="fa fa-info-circle"></i></button>';
    }

    html += '</div></td></tr>';

    return html;
}

/**
 * Initialize event handlers for newly added rows
 */
function initializeNewRowEventHandlers() {
    // Re-bind info button click handlers for new rows
    $('.info-btn').off('click.newRows').on('click.newRows', async function() {
        // Show modal immediately
        $('#idCardModal').fadeIn(300);

        // Initialize visible entity IDs
        initializeVisibleEntityIds();

        // Get entity ID and find its index
        const entityRow = $(this).closest('tr');
        const entityId = entityRow.data('entity-id');
        window.currentEntityIndex = window.visibleEntityIds.indexOf(entityId);

        // Load entity data
        await window.loadEntityData(entityId);
    });
}
    
    function construct_entity_table(data,id_card_for) {
            if (!Array.isArray(data) || data.length === 0) {
                return '<div class="alert alert-info">No data available to display.</div>';
            }

            // Filter data based on status
            const filteredData = selectedStatus === 'all'
                ? data
                : data.filter(entity => {
                    const status = (entity.status || 'in review').toLowerCase();
                    const hyphenStatus = status.replace(/ /g, '-');
                    const underscoreStatus = status.replace(/-/g, '_');
                    return selectedStatus === hyphenStatus || selectedStatus === underscoreStatus;
                });

            var html = '';
            var count = filteredData.length; // Use filtered count

            $('#visible-count').text(count);

            html += '<table class="entity-table">';
            html += '<thead><tr>';

            if (id_card_for.toLowerCase() === 'staff') {
                html += '<th>Name</th>';
                html += '<th class="status-cell">Status</th>';
                html += '<th>Employee ID</th>';
                html += '<th style="white-space: nowrap;">DOB</th>';
                html += '<th>Mobile No.</th>';
            } else if (id_card_for.toLowerCase() === 'parent') {
                html += '<th>Parent Name</th>';
                html += '<th class="status-cell">Status</th>';
                html += '<th>Relation</th>';
                html += '<th>Student Name</th>';
                html += '<th>Class</th>';
                html += '<th>Mobile No.</th>';
            } else {
                html += '<th>Student Name</th>';
                html += '<th class="status-cell">Status</th>';
                html += '<th>Class</th>';
                html += '<th style="white-space: nowrap;">DOB</th>';
                html += '<th>Mobile No.</th>';
            }

            html += '<th class="actions-cell">Actions</th>';
            html += '</tr></thead>';
            html += '<tbody>';

            // Update the loop to use filteredData instead of data
            for (var i = 0; i < filteredData.length; i++) {
                var entity = filteredData[i];

                if (!entity || typeof entity !== 'object') {
                    continue;
                }

                var status = entity.status || 'in review';
                status = status.replace(/\s+/g, '-')
                var entityId = id_card_for.toLowerCase() === 'staff' ? 
                    (entity.sm_id || entity.id || i) : 
                    (id_card_for.toLowerCase() === 'parent' ? 
                        (entity.id || i) : 
                        (entity.sa_id || i));

                html += '<tr data-entity-id="' + entityId + '" data-status="' + status + '">';

                if (id_card_for.toLowerCase() === 'staff') {
                    html += '<td>' + (entity.staff_name || '') + '</td>';
                    html += '<td class="status-cell" style="text-align:center">';
                    html += '<span class="status-badge status-' + status + '">' + capitalizeFirstLetter(status) + '</span>';
                    html += '</td>';
                    html += '<td>' + (entity.employee_code || '') + '</td>';
                    html += '<td style="white-space: nowrap;">' + entity.dob + '</td>';
                    html += '<td>' + (entity.contact || '') + '</td>';
                } else if (id_card_for.toLowerCase() === 'parent') {
                    if(entity.avatar_type == 'Mother'){
                        html += '<td>' + (entity.name || '') + '</td>';
                        html += '<td class="status-cell" style="text-align:center">';
                        html += '<span class="status-badge status-' + status + '">' + capitalizeFirstLetter(status) + '</span>';
                        html += '</td>';
                        html += '<td>Mother</td>';
                        html += '<td>' + (entity.name || '') + '</td>';
                        html += '<td>' + (entity.class_section || '') + '</td>';
                        html += '<td>' + (entity.mother_contact || '') + '</td>';
                    }else if(entity.avatar_type == 'Father'){
                        html += '<td>' + (entity.father_name || '') + '</td>';
                        html += '<td class="status-cell" style="text-align:center">';
                        html += '<span class="status-badge status-' + status + '">' + capitalizeFirstLetter(status) + '</span>';
                        html += '</td>';
                        html += '<td>Father</td>';
                        html += '<td>' + (entity.name || '') + '</td>';
                        html += '<td>' + (entity.class_section || '') + '</td>';
                        html += '<td>' + (entity.father_contact || '') + '</td>';
                    }else if(entity.avatar_type == 'Guardian'){
                        html += '<td>' + (entity.guardian_name || '') + '</td>';
                        html += '<td class="status-cell" style="text-align:center">';
                        html += '<span class="status-badge status-' + status + '">' + capitalizeFirstLetter(status) + '</span>';
                        html += '</td>';
                        html += '<td>Guardian</td>';
                        html += '<td>' + (entity.name || '') + '</td>';
                        html += '<td>' + (entity.class_section || '') + '</td>';
                        html += '<td>' + (entity.guardian_contact || '') + '</td>';
                    }else if(entity.avatar_type == 'Guardian_2'){
                        html += '<td>' + (entity.guardian_2_name || '') + '</td>';
                        html += '<td class="status-cell" style="text-align:center">';
                        html += '<span class="status-badge status-' + status + '">' + capitalizeFirstLetter(status) + '</span>';
                        html += '</td>';
                        html += '<td>Guardian 2</td>';
                        html += '<td>' + (entity.name || '') + '</td>';
                        html += '<td>' + (entity.class_section || '') + '</td>';
                        html += '<td>' + (entity.guardian_2_contact || '') + '</td>';
                    }else if(entity.avatar_type == 'Driver'){
                        html += '<td>' + (entity.driver_name || '') + '</td>';
                        html += '<td class="status-cell" style="text-align:center">';
                        html += '<span class="status-badge status-' + status + '">' + capitalizeFirstLetter(status) + '</span>';
                        html += '</td>';
                        html += '<td>Guardian 2</td>';
                        html += '<td>' + (entity.name || '') + '</td>';
                        html += '<td>' + (entity.class_section || '') + '</td>';
                        html += '<td>' + (entity.driver_contact || '') + '</td>';
                    }else if(entity.avatar_type == 'Driver_2'){
                        html += '<td>' + (entity.driver_2_name || '') + '</td>';
                        html += '<td class="status-cell" style="text-align:center">';
                        html += '<span class="status-badge status-' + status + '">' + capitalizeFirstLetter(status) + '</span>';
                        html += '</td>';
                        html += '<td>Guardian 2</td>';
                        html += '<td>' + (entity.name || '') + '</td>';
                        html += '<td>' + (entity.class_section || '') + '</td>';
                        html += '<td>' + (entity.driver_2_contact || '') + '</td>';
                    }
                } else {
                    html += '<td>' + (entity.name || '') + '</td>';
                    html += '<td class="status-cell" style="text-align:center" >';
                    html += '<span class="status-badge status-' + status + '">' + capitalizeFirstLetter(status) + '</span>';
                    html += '</td>';
                    html += '<td>' + (entity.class_section || '') + '</td>';
                    html += '<td style="white-space: nowrap;">' + entity.dob + '</td>';
                    html += '<td>' + (entity.preferred_contact_no || '') + '</td>';
                }

                html += '<td class="actions-cell">';
                html += '<div class="button-container">';
                // Check if order is in a locked state (order_submitted, in_printing, or delivered)
                const isOrderLocked = ['order_submitted', 'in_printing', 'delivered'].includes(order_status);
                var style = "display:none";
                    if (status.toLowerCase().trim() === 'modify') {
                        style = "";
                    }
                const profileId = id_card_for.toLowerCase() === 'staff' ? 
                    (entity.sm_id || entity.id || i) : 
                    (id_card_for.toLowerCase() === 'parent' ? 
                        (entity.id || i) : 
                        (entity.id || i));
                if (isOrderLocked) {
                    html += '<button  class="action-btn edit-btn locked-action" disabled ' +
                            'title="Actions are locked - Order is in ' + capitalizeFirstLetter(order_status.replace('_', ' ')) + ' status">' +
                            '<i class="fa fa-lock"></i></button>';

                    html += '<button class="action-btn info-btn" ' +
                            'title="View ID card (approval disabled)" ' +
                            'data-entity="' + encodeURIComponent(JSON.stringify(entity)) + '">' +
                            '<i class="fa fa-info-circle"></i>' +
                            '</button>';
                } else {
                    html += '<button style="' + style + '" class="action-btn edit-btn" ' +
                        'onclick="navigateToProfile(\'' + id_card_for.toLowerCase() + '\', \'' + profileId + '\')" ' + 'title="Edit"><i class="fa fa-pencil"></i></button>';
                    html += '<button class="action-btn info-btn" ' +
                            'title="View and approve ID card" ' +
                            'data-entity="' + encodeURIComponent(JSON.stringify(entity)) + '">' +
                            '<i class="fa fa-info-circle"></i>' +
                            '</button>';
                }

                html += '</div>';
                html += '</td>';

                html += '</tr>';
            }

            html += '</tbody></table>';

            return html;
        }

        function capitalizeFirstLetter(string) {
            return string.charAt(0).toUpperCase() + string.slice(1);
        }

        function formatDate(dateString) {
            if (!dateString) return '';

            try {
                const date = new Date(dateString);
                if (isNaN(date.getTime())) return dateString;

                const day = date.getDate().toString().padStart(2, '0');
                const month = ['Jan', 'Feb', 'Mar', 'Apr', 'May', 'Jun', 'Jul', 'Aug', 'Sep', 'Oct', 'Nov', 'Dec'][date.getMonth()];
                const year = date.getFullYear();

                return `${day}-${month}-${year}`;
            } catch (e) {
                return dateString;
            }
        }

        function navigateToProfile(type, id) {
            const staffUrl = '<?php echo site_url("staff/Staff_controller/addMoreStaffInfo"); ?>';
            const studentUrl = '<?php echo site_url("student/Student_controller/addMoreStudentInfo"); ?>';

            if (type === 'staff') {
                window.open(`${staffUrl}/${id}`, '_blank');
            } else if (type === 'student') {
                window.open(`${studentUrl}/${id}`, '_blank');
            } else {
                console.error('Unknown type:', type);
            }
        }


        function updateStatusCounts(data) {
            let inReviewCount = 0;
            let approvedCount = 0;
            let rejectedCount = 0;
            let reOrderedCount = 0;

            // Use provided data or fall back to window.entityData if no data is provided
            const entityData = data || window.entityData || [];

            if (Array.isArray(entityData)) {
                entityData.forEach(entity => {
                    const status = entity.status || 'in review';
                    if (status === 'in review') inReviewCount++;
                    else if (status === 'approved') approvedCount++;
                    else if (status === 'removed') rejectedCount++;
                    else if (status === 're-ordered') reOrderedCount++;
                });
            }

            $('.status-tabs .status-tab[data-status="in review"] .status-count').text(inReviewCount);
            $('.status-tabs .status-tab[data-status="approved"] .status-count').text(approvedCount);
            $('.status-tabs .status-tab[data-status="removed"] .status-count').text(rejectedCount);
            $('.status-tabs .status-tab[data-status="re_ordered"] .status-count').text(reOrderedCount);
        }

        // Function to refresh entity data from the server
        function refreshEntityData(callback) {
            showLoading('Refreshing data...');

            $.ajax({
                url: '<?php echo site_url("idcards/Idcards_controller/get_entities_for_order"); ?>',
                type: 'POST',
                data: {
                    order_id: order_id,
                    id_card_for: id_card_for,
                    status_filter: 'all'
                },
                dataType: 'json',
                success: function(response) {
                    hideLoading();
                    if (response && response.success && response.data) {
                        // Update the global entity data
                        window.entityData = response.data;
                        // console.log('Entity data refreshed from server:', response.data.length, 'records');

                        if (typeof callback === 'function') {
                            callback(response.data);
                        }
                    } else {
                        console.error('Failed to refresh entity data:', response);
                        showNotification('error', 'Failed to refresh data from server');
                    }
                },
                error: function(xhr, status, error) {
                    hideLoading();
                    console.error('Error refreshing entity data:', error);
                    showNotification('error', 'Error connecting to server');
                }
            });
        }

        function initializeEventHandlers() {
            // Remove any existing click handlers to prevent duplicates
            $('.status-tab').off('click.statusFilter');

            // Add click handler with namespace
            $('.status-tab').on('click.statusFilter', function() {
                console.log('Status tab clicked:', $(this).data('status'));

                $('.status-tab').removeClass('active');
                $(this).addClass('active');
                selectedStatus = $(this).data('status');

                console.log('Selected status changed to:', selectedStatus);

                // Reload data with new status filter using progress loading
                showEnhancedLoading('Loading Data', 'Filtering records by status...');
                initializeProgressDataLoading();
            });

            // Debounced search functionality
            let searchTimeout;
            $('#search-entities').on('input', function() {
                const searchTerm = $(this).val();

                // Clear previous timeout
                clearTimeout(searchTimeout);

                // Set new timeout for debounced search
                searchTimeout = setTimeout(function() {
                    // Reload data with search filter using progress loading
                    showEnhancedLoading('Searching', 'Searching through all records...');
                    initializeProgressDataLoading();
                }, 500); // 500ms delay
            });

            $('#class-filter').change(function() {
                filterEntitiesByClass($(this).val());
            });

            $('#section-filter').change(function() {
                filterEntitiesBySection($(this).val());
            });

            // Store current entity data globally for navigation
            window.currentEntityIndex = -1;
            window.visibleEntityIds = [];

            // Function to navigate to previous staff
            function navigateToPreviousStaff() {
                if (window.currentEntityIndex > 0) {
                    window.currentEntityIndex--;
                    const entityId = window.visibleEntityIds[window.currentEntityIndex];

                    // Get the current status directly from the table row
                    const entityRow = $(`.entity-table tbody tr[data-entity-id="${entityId}"]`);
                    if (entityRow.length) {
                        const currentStatus = entityRow.attr('data-status') || 'in review';

                        // Load the entity data
                        window.loadEntityData(entityId);

                        // Update the status badge and buttons
                        updateEntityStatusBadge();
                        setModalActionButtonStates(currentStatus);
                    } else {
                        window.loadEntityData(entityId);
                    }
                }
            }

            // Function to navigate to next staff
            function navigateToNextStaff() {
                if (window.currentEntityIndex < window.visibleEntityIds.length - 1) {
                    window.currentEntityIndex++;
                    const entityId = window.visibleEntityIds[window.currentEntityIndex];

                    // Get the current status directly from the table row
                    const entityRow = $(`.entity-table tbody tr[data-entity-id="${entityId}"]`);
                    if (entityRow.length) {
                        const currentStatus = entityRow.attr('data-status') || 'in review';

                        // Load the entity data
                        window.loadEntityData(entityId);

                        // Update the status badge and buttons
                        updateEntityStatusBadge();
                        setModalActionButtonStates(currentStatus);
                    } else {
                        window.loadEntityData(entityId);
                    }
                }
            }

            // Function to update navigation buttons state
            function updateNavigationButtons() {
                // Disable/enable previous button
                $('#prevStaffBtn').prop('disabled', window.currentEntityIndex <= 0);

                // Disable/enable next button
                $('#nextStaffBtn').prop('disabled', window.currentEntityIndex >= window.visibleEntityIds.length - 1);
                updateEntityPositionIndicator();
                updateEntityStatusBadge();
            }

            // Initialize visible entity IDs
            function initializeVisibleEntityIds() {
                window.visibleEntityIds = [];
                $('.entity-table tbody tr:visible').each(function() {
                    window.visibleEntityIds.push($(this).data('entity-id'));
                });
            }

            // Set up navigation button click handlers
            $('#prevStaffBtn').on('click', navigateToPreviousStaff);
            $('#nextStaffBtn').on('click', navigateToNextStaff);

            // Info button click handler
            $(document).on('click', '.info-btn', async function() {
                // Show modal immediately
                $('#idCardModal').fadeIn(300);

                // Initialize visible entity IDs
                initializeVisibleEntityIds();

                // Get entity ID and find its index
                const entityRow = $(this).closest('tr');
                const entityId = entityRow.data('entity-id');
                window.currentEntityIndex = window.visibleEntityIds.indexOf(entityId);

                // Load entity data
                await window.loadEntityData(entityId);
            });

            $(document).ajaxComplete(function() {
                if (!$.fn.DataTable.isDataTable('.entity-table')) {
                    $('.entity-table').DataTable({
                        paging: false,
                        lengthChange: false,
                        searching: false,
                        ordering: true,
                        info: true,
                        autoWidth: false,
                        stateSave: true,
                        stateDuration: 60 * 60 * 24,
                    });
                }
            });
        }

        function filterEntitiesByStatus(status) {
            if ($.fn.DataTable.isDataTable('.entity-table')) {
                const dataTable = $('.entity-table').DataTable();

                const currentPage = dataTable.page.info().page;

                if (status === 'all') {
                    dataTable.search('').columns().search('').draw();
                } else {
                    $.fn.dataTable.ext.search.push(
                        function(settings, data, dataIndex) {
                            const rowStatus = $(dataTable.row(dataIndex).node()).attr('data-status');
                            return rowStatus === status;
                        }
                    );

                    dataTable.draw();

                    $.fn.dataTable.ext.search.pop();
                }

                if (dataTable.page.info().pages > currentPage) {
                    dataTable.page(currentPage).draw('page');
                }
            } else {
                if (status === 'all') {
                    $('.entity-table tbody tr').show();
                } else {
                    $('.entity-table tbody tr').hide();
                    $('.entity-table tbody tr[data-status="' + status + '"]').show();
                }
            }

            updateVisibleCount();
            updateStatusCounts();
        }

        function filterEntitiesBySearch(searchTerm) {
            $('.entity-table tbody tr').each(function() {
                const name = $(this).find('td:nth-child(1)').text().toLowerCase();
                const id = $(this).find('td:nth-child(2)').text().toLowerCase();

                if (name.includes(searchTerm) || id.includes(searchTerm)) {
                    $(this).show();
                } else {
                    $(this).hide();
                }
            });

            updateVisibleCount();
        }

        function filterEntitiesByClass(classValue) {
            if (!classValue) {
                $('.entity-table tbody tr').show();
            } else {
                $('.entity-table tbody tr').each(function() {
                    let classColumnIndex;
                    if (id_card_for.toLowerCase() === 'staff') {
                        classColumnIndex = 2;
                    } else {
                        classColumnIndex = 3;
                    }

                    const classText = $(this).find('td:nth-child(' + classColumnIndex + ')').text();
                    if (classText.includes(classValue)) {
                        $(this).show();
                    } else {
                        $(this).hide();
                    }
                });
            }

            updateVisibleCount();
        }

        function filterEntitiesBySection(sectionValue) {
            if (!sectionValue) {
                $('.entity-table tbody tr').show();
            } else {
                $('.entity-table tbody tr').each(function() {
                    let sectionColumnIndex;
                    if (id_card_for.toLowerCase() === 'staff') {

                        sectionColumnIndex = 3;
                    } else {
                        sectionColumnIndex = 3;
                    }

                    const sectionText = $(this).find('td:nth-child(' + sectionColumnIndex + ')').text();
                    if (sectionText.includes(sectionValue)) {
                        $(this).show();
                    } else {
                        $(this).hide();
                    }
                });
            }

            updateVisibleCount();
        }

        function updateVisibleCount() {
            const visibleCount = $('.entity-table tbody tr:visible').length;
            const entityType = id_card_for.toLowerCase() === 'staff' ? 'Staff' : 'Students';
            $('#visible-count').text(visibleCount);
            $('.entity-count').text(`Showing ${visibleCount} ${entityType}`);
        }

        // Function to disable action buttons during loading
        function disableActionButtons() {
            $('.id-card-action-btn').prop('disabled', true);
            $('.id-card-approve').html('<i class="fa fa-spinner fa-spin"></i> LOADING...');
            $('.id-card-modify').html('<i class="fa fa-spinner fa-spin"></i> LOADING...');
            $('.id-card-remove').html('<i class="fa fa-spinner fa-spin"></i> LOADING...');
        }

        // Function to enable action buttons after loading
        function enableActionButtons(status) {
            // Clear any pending timeout
            if (window.buttonEnableTimeout) {
                clearTimeout(window.buttonEnableTimeout);
                window.buttonEnableTimeout = null;
            }

            // Reset button text
            $('.id-card-approve').html('<i class="fa fa-check"></i> APPROVE');
            $('.id-card-modify').html('<i class="fa fa-pencil"></i> MODIFY');
            $('.id-card-remove').html('<i class="fa fa-trash"></i> REMOVE');

            // Set proper disabled state based on status
            setModalActionButtonStates(status || 'in review');
        }

        // Function to increment loaded shape elements counter
        function incrementShapeElementsLoaded() {
            window.shapeElementsLoaded++;

            // If all shape elements are loaded, enable the action buttons
            if (window.shapeElementsLoaded >= window.shapeElementsLoading) {
                // Get current status from the entity row
                const entityId = window.visibleEntityIds[window.currentEntityIndex];
                const entityRow = $(`.entity-table tbody tr[data-entity-id="${entityId}"]`);
                const currentStatus = entityRow.attr('data-status') || 'in review';

                // Enable buttons with proper state
                enableActionButtons(currentStatus);

                // Reset counters for next load
                window.shapeElementsLoading = 0;
                window.shapeElementsLoaded = 0;
            }
        }

        function renderPreview(entityData) {
            // Show loading indicator in the preview areas
            $('#frontCardPreview').html('<div class="preview-loading"><i class="fa fa-spinner fa-spin fa-2x"></i><p>Loading preview...</p></div>');
            $('#backCardPreview').html('<div class="preview-loading"><i class="fa fa-spinner fa-spin fa-2x"></i><p>Loading preview...</p></div>');

            // Reset shape element counters
            window.shapeElementsLoading = 0;
            window.shapeElementsLoaded = 0;

            // Disable action buttons during loading
            disableActionButtons();

            // Set a timeout to ensure buttons are enabled even if there's an issue with loading
            window.buttonEnableTimeout = setTimeout(function() {
                if ($('.id-card-approve').prop('disabled')) {
                    console.warn('Timeout reached for loading ID card elements, enabling buttons');
                    const entityId = window.visibleEntityIds[window.currentEntityIndex];
                    const entityRow = $(`.entity-table tbody tr[data-entity-id="${entityId}"]`);
                    const currentStatus = entityRow.attr('data-status') || 'in review';
                    enableActionButtons(currentStatus);
                }
            }, 5000); // 5 second timeout

            $.ajax({
                url: '<?php echo site_url("idcards/Idcards_controller/getIdCardTemplate"); ?>',
                type: 'post',
                data: { order_id: order_id },
                success: function(response) {
                    try {
                        const templateData = JSON.parse(response);

                        if (templateData && templateData.template) {
                            const template = templateData.template;

                            let frontDesign = JSON.parse(template.front_design);
                            let backDesign = JSON.parse(template.back_design);

                            $('#frontCardPreview').empty();
                            renderDesign('#frontCardPreview', frontDesign, entityData);

                            $('#backCardPreview').empty();
                            renderDesign('#backCardPreview', backDesign, entityData);

                            // If no shape elements were found, enable buttons immediately
                            if (window.shapeElementsLoading === 0) {
                                const entityId = window.visibleEntityIds[window.currentEntityIndex];
                                const entityRow = $(`.entity-table tbody tr[data-entity-id="${entityId}"]`);
                                const currentStatus = entityRow.attr('data-status') || 'in review';
                                enableActionButtons(currentStatus);
                            }
                        } else {
                            renderSimplePreview(entityData);
                        }
                    } catch (e) {
                        console.error('Error parsing template data:', e);
                        renderSimplePreview(entityData);

                        // Enable buttons on error
                        const entityId = window.visibleEntityIds[window.currentEntityIndex];
                        const entityRow = $(`.entity-table tbody tr[data-entity-id="${entityId}"]`);
                        const currentStatus = entityRow.attr('data-status') || 'in review';
                        enableActionButtons(currentStatus);
                    }
                },
                error: function(err) {
                    console.error('Error fetching template data:', err);
                    renderSimplePreview(entityData);

                    // Enable buttons on error
                    const entityId = window.visibleEntityIds[window.currentEntityIndex];
                    const entityRow = $(`.entity-table tbody tr[data-entity-id="${entityId}"]`);
                    const currentStatus = entityRow.attr('data-status') || 'in review';
                    enableActionButtons(currentStatus);
                }
            });
        }

        // Track loading state of shape elements
        window.shapeElementsLoading = 0;
        window.shapeElementsLoaded = 0;

        function renderDesign(container, design, entityData) {
            const $container = $(container);
            $container.addClass(`card-size-${design.styles?.size || 'portrait'}`);

            if (design.styles && design.styles.backgroundColor) {
                $container.css('background-color', design.styles.backgroundColor);
            }

            // Count shape elements that need to be loaded
            if (design.elements) {
                design.elements.forEach(element => {
                    if (element.type === 'shape') {
                        window.shapeElementsLoading++;
                    }
                });
            }

            // Disable action buttons until all shapes are loaded
            disableActionButtons();

            design.elements?.forEach(element => {
                // For address fields, use relative positioning and auto height
                let isAddressField =
                    element.type === 'field' &&
                    (
                        element.properties.fieldName === '[[ADDRESS]]' ||
                        element.properties.fieldName === '[[STUDENT_ADDRESS]]' ||
                        element.properties.fieldName === '[[FATHER_ADDRESS]]' ||
                        element.properties.fieldName === '[[MOTHER_ADDRESS]]'
                    );

                const $element = $('<div>').addClass('element').css({
                    position: isAddressField ? 'relative' : 'absolute',
                    left: element.x + 'px',
                    top: element.y + 'px',
                    width: element.width + 'px',
                    height: isAddressField ? 'auto' : (element.height + 'px'),
                    zIndex: element.zIndex || 0
                });
                switch (element.type) {
                    case 'text':
                        $element.html(`<div class="element-content">${element.properties.text || ''}</div>`);
                        $element.find('.element-content').css({
                            'font-family': element.properties.font,
                            'font-size': `${element.properties.size}px`,
                            'color': element.properties.color,
                            'font-weight': element.properties.bold ? 'bold' : 'normal',
                            'font-style': element.properties.italic ? 'italic' : 'normal',
                            'text-decoration': element.properties.underline ? 'underline' : 'none',
                            'text-align': element.properties.textAlign || 'left',
                            'background-color': element.properties.backgroundColor || 'transparent',
                            'border': element.properties.strokeWidth ?
                                `${element.properties.strokeWidth}px solid ${element.properties.strokeColor}` : 'none',
                            'padding': '2px 5px'
                        });
                        break;

                    case 'field':
                        const value = getFieldValue(element.properties.fieldName, entityData);
                        $element.html(`<div class="element-content">${value}</div>`);

                        const fixedHeight = element.height || 48;

                        if (
                            element.properties.fieldName === '[[ADDRESS]]' ||
                            element.properties.fieldName === '[[STUDENT_ADDRESS]]' ||
                            element.properties.fieldName === '[[FATHER_ADDRESS]]' ||
                            element.properties.fieldName === '[[MOTHER_ADDRESS]]'
                        ) {
                            $element.find('.element-content').css({
                                'font-family': element.properties.font,
                                'font-size': `${element.properties.size}px`,
                                'color': element.properties.color,
                                'font-weight': element.properties.bold ? 'bold' : 'normal',
                                'font-style': element.properties.italic ? 'italic' : 'normal',
                                'text-decoration': element.properties.underline ? 'underline' : 'none',
                                'text-align': element.properties.textAlign || 'left',
                                'background-color': element.properties.backgroundColor || 'transparent',
                                'border': element.properties.strokeWidth ?
                                    `${element.properties.strokeWidth}px solid ${element.properties.strokeColor}` : 'none',
                                'padding': '12px', 
                                'width': '100%',
                                'min-height': fixedHeight + 'px',
                                'height': 'auto',
                                'max-height': 'none',
                                'box-sizing': 'border-box',
                                'overflow': 'hidden',
                                'white-space': 'pre-line', 
                                'word-break': 'break-word',
                                'line-height': '1.2',
                                'display': 'block',
                                'text-transform': element.properties.textTransform || 'capitalize'
                            });
                        } else if (
                            element.properties.fieldName === '[[NAME]]'
                            
                        ) {
                            let fontSize = element.properties.size || 18;
                            let nameValue = value || '';
                            let textAlign = element.properties.textAlign || 'left';
                            let elementWidth = element.width || 215;
                            let maxFontSize = fontSize;
                            let minFontSize = 6;

                            $element.addClass('name-field');

                            // Start with maxFontSize
                            let appliedFontSize = maxFontSize;

                            // Create a temporary, invisible element to measure width
                            const $temp = $('<div>')
                                .text(nameValue)
                                .css({
                                    'position': 'absolute',
                                    'visibility': 'hidden',
                                    'white-space': 'nowrap',
                                    'font-family': element.properties.font,
                                    'font-weight': element.properties.bold ? 'bold' : 'normal',
                                    'font-style': element.properties.italic ? 'italic' : 'normal',
                                    'font-size': appliedFontSize + 'px',
                                    'text-transform': element.properties.textTransform || 'capitalize',
                                })
                                .appendTo('body');

                            // Shrink font until the text fits within 95% of the available width
                            while ($temp.outerWidth() > elementWidth * 0.95 && appliedFontSize > minFontSize) {
                                appliedFontSize--;
                                $temp.css('font-size', appliedFontSize + 'px');
                            }

                            $temp.remove(); // Cleanup temp element

                            fontSize = appliedFontSize;

                            // Optional y-shift for smaller fonts
                            if (fontSize < 10) {
                                element.y = (element.y || 0) + 1;
                            }

                            // Apply top position
                            $element.css({ 'top': element.y });

                            // Apply styles to content
                            $element.find('.element-content').css({
                                'font-family': element.properties.font,
                                'font-size': `${fontSize}px`,
                                'color': element.properties.color,
                                'font-weight': element.properties.bold ? 'bold' : 'normal',
                                'font-style': element.properties.italic ? 'italic' : 'normal',
                                'text-decoration': element.properties.underline ? 'underline' : 'none',
                                'text-align': textAlign,
                                'background-color': element.properties.backgroundColor || 'transparent',
                                'border': element.properties.strokeWidth ?
                                    `${element.properties.strokeWidth}px solid ${element.properties.strokeColor}` : 'none',
                                'padding': '6px 8px',
                                'width': '100%',
                                'height': 'auto',
                                'max-height': 'none',
                                'box-sizing': 'border-box',
                                'overflow': 'visible',
                                'white-space': 'normal',
                                'word-break': 'break-word',
                                'line-height': '1.2',
                                'display': 'block',
                                'text-transform': element.properties.textTransform || 'capitalize',
                            });

                        } else if (
                            element.properties.fieldName === '[[PARENT_NAME]]'
                        ) {
                            let parentNameValue = value || '';
                            let textAlign = element.properties.textAlign || 'left';
                            let elementWidth = element.width || 215;
                            let maxFontSize = element.properties.size;
                            let minFontSize = 6;
                            let fontSize = element.properties.size || maxFontSize;

                            $element.addClass('parent-name-field');
                            $element.css({ 'text-align': textAlign });

                            // Start with max font size
                            let appliedFontSize = maxFontSize;

                            // Temporary hidden div to measure width
                            const $temp = $('<div>')
                                .text(parentNameValue)
                                .css({
                                    'position': 'absolute',
                                    'visibility': 'hidden',
                                    'white-space': 'nowrap',
                                    'font-family': element.properties.font,
                                    'font-weight': element.properties.bold ? 'bold' : 'normal',
                                    'font-style': element.properties.italic ? 'italic' : 'normal',
                                    'font-size': appliedFontSize + 'px',
                                    'text-transform': element.properties.textTransform || 'capitalize'
                                })
                                .appendTo('body');

                            // Reduce font size until it fits within container width
                            while ($temp.outerWidth() > elementWidth * 0.95 && appliedFontSize > minFontSize) {
                                appliedFontSize--;
                                $temp.css('font-size', appliedFontSize + 'px');
                            }

                            $temp.remove(); // Cleanup

                            // Apply calculated font size
                            fontSize = appliedFontSize;

                            if (fontSize < 10) {
                                element.y = (element.y || 0) + 1;
                            }

                            // Apply styles
                            $element.find('.element-content').css({
                                'font-family': element.properties.font,
                                'font-size': `${fontSize}px`,
                                'color': element.properties.color,
                                'font-weight': element.properties.bold ? 'bold' : 'normal',
                                'font-style': element.properties.italic ? 'italic' : 'normal',
                                'text-decoration': element.properties.underline ? 'underline' : 'none',
                                'text-align': textAlign,
                                'background-color': element.properties.backgroundColor || 'transparent',
                                'border': element.properties.strokeWidth ?
                                    `${element.properties.strokeWidth}px solid ${element.properties.strokeColor}` : 'none',
                                'padding': '6px 8px',
                                'width': '100%',
                                'height': fixedHeight + 'px',
                                'max-height': fixedHeight + 'px',
                                'box-sizing': 'border-box',
                                'overflow': 'hidden',
                                'white-space': 'normal',
                                'word-break': 'break-word',
                                'line-height': '1.2',
                                'display': 'block',
                                'text-transform': element.properties.textTransform || 'capitalize',
                            });

                        }else if (element.properties.fieldName === '[[QR_CODE]]') {
                            if (entityData.qr_code) {
                                // Display QR code number below the QR code image
                                $element.find('.element-content').css({
                                    'flex-direction': 'column',
                                    'align-items': 'center',
                                    'justify-content': 'center'
                                });

                                // Use a QR code image if available, else generate using API
                                let qrCodeImg = '';
                                if (entityData.qr_code_url) {
                                    qrCodeImg = `<img src="${entityData.qr_code_url}" alt="QR Code" style="max-width:100%;max-height:100px;display:block;margin-bottom:4px;">`;
                                    $element.find('.element-content').html(qrCodeImg);
                                } else {
                                    $element.find('.element-content').html('<i class="fa fa-spinner fa-spin fa-2x"></i>');
                                    generate_qr_code('qr_code', entityData.qr_code, element.properties.qr_size, function(generatedQRCode) {
                                        qrCodeImg = `<img src="${generatedQRCode}" alt="QR Code">`;
                                        $element.find('.element-content').html(qrCodeImg);
                                    });
                                }
                            } else {
                                $element.find('.element-content').html('<span style="color:#aaa;">No QR Code</span>');
                            }
                        }else if (element.properties.fieldName === '[[FATHER_PHOTO]]') {
                            if (entityData.father_photo) {
                                const imageClass = element.properties.shapeType === 'circle' ?
                                'element-content photo-in-circle' : 'element-content';
                                let father_image = '';
                                if (entityData.father_photo) {
                                    $element.find('.element-content').html('<i class="fa fa-spinner fa-spin fa-2x"></i>');

                                    father_image = `<img src="${entityData.father_photo}" class="${imageClass}" alt="motherphoto" style="max-width:100%;max-height:100px;display:block;margin-bottom:4px;">`;
                                    $element.find('.element-content').html(father_image);
                                }

                            } else {
                                $element.find('.element-content').html('<span style="color:#aaa;"></span>');
                            }
                        }else if (element.properties.fieldName === '[[MOTHER_PHOTO]]') {
                            if (entityData.mother_photo) {
                                const imageClass = element.properties.shapeType === 'circle' ?
                                'element-content photo-in-circle' : 'element-content';
                                let mother_image = '';
                                if (entityData.mother_photo) {
                                    $element.find('.element-content').html('<i class="fa fa-spinner fa-spin fa-2x"></i>');

                                    mother_image = `<img src="${entityData.mother_photo}" class="${imageClass}" alt="motherphoto" style="max-width:100%;max-height:100px;display:block;margin-bottom:4px;">`;
                                    $element.find('.element-content').html(mother_image);
                                }

                            } else {
                                $element.find('.element-content').html('<span style="color:#aaa;"></span>');
                            }
                        }else if (element.properties.fieldName === '[[BAR_CODE]]') {
                            if (entityData.qr_code) {
                                // Display QR code number below the QR code image
                                $element.find('.element-content').css({
                                    'flex-direction': 'column',
                                    'align-items': 'center',
                                    'justify-content': 'center'
                                });

                                // Use a QR code image if available, else generate using API
                                let qrCodeImg = '';
                                if (entityData.qr_code_url) {
                                    qrCodeImg = `<img src="${entityData.qr_code_url}" alt="QR Code" style="max-width:100%;max-height:100px;display:block;margin-bottom:4px;">`;
                                    $element.find('.element-content').html(qrCodeImg);
                                } else {
                                    $element.find('.element-content').html('<i class="fa fa-spinner fa-spin fa-2x"></i>');
                                    generate_qr_code('bar_code', entityData.qr_code, element.properties.qr_size, function(generatedQRCode) {
                                        qrCodeImg = `<img src="${generatedQRCode}" alt="QR Code">`;
                                        $element.find('.element-content').html(qrCodeImg);
                                    });
                                }
                            } else {
                                $element.find('.element-content').html('<span style="color:#aaa;">No QR Code</span>');
                            }
                        }else {
                            let fontSize = element.properties.size || 11;
                            let NameValue = value || '';
                            let textAlign = element.properties.textAlign || 'left';
                            let elementWidth = element.width || 215;
                            
                            const charWidthFactor = 0.58;
                            const containerWidthInChars = Math.floor(elementWidth / (fontSize * charWidthFactor));
                            // Adjust font size based on both text length and available width
                            if (NameValue.length > containerWidthInChars * 0.8) {
                                // Text is approaching container width limit
                                const ratio = containerWidthInChars / NameValue.length;
                              
                               // More granular font size adjustments
                                if (ratio < 0.3) {
                                    // Extremely long text
                                    fontSize = Math.max(6, fontSize * 0.45);
                                } else if (ratio < 0.4) {
                                    // Very long text
                                    fontSize = Math.max(7, fontSize * 0.5);

                                } else if (ratio < 0.5) {
                                    // Long text
                                    fontSize = Math.max(8, fontSize * 0.6);

                                } else if (ratio < 0.6) {
                                    // Moderately long text
                                    fontSize = Math.max(9, fontSize * 0.5);
                                } else if (ratio < 0.7) {
                                    // Slightly long text
                                    fontSize = Math.max(9, fontSize * 0.5);
                                } else if (ratio < 0.8) {
                                    // Just a bit too long
                                    fontSize = Math.max(9, fontSize * 0.6);
                                } else if (ratio < 0.93) {
                                    fontSize = Math.max(8, fontSize * 0.7);
                                } else if (ratio < 0.95) {
                                    fontSize = Math.max(8, fontSize * 0.8);
                                } else if (ratio <= 1) {
                                    // Check for ratio less than 1.05
                                    fontSize = Math.max(8.5, fontSize * 0.8);
                                }else if (ratio < 1.04) {
                                    fontSize = Math.max(6, fontSize * 0.8);
                                }else if (ratio < 1.05) {
                                    // Check for ratio less than 1.05
                                    fontSize = Math.max(8, fontSize * 0.8);
                                } else if (ratio < 1.2) {
                                    fontSize = Math.max(9.5, fontSize * 0.8);
                                } else if (ratio < 1.5) {
                                    fontSize = Math.max(8, fontSize * 0.9);
                                }
                            }

                            // Apply base styling for all other fields
                            $element.find('.element-content').css({
                                'font-family': element.properties.font,
                                'font-size': `${fontSize}px`,
                                'color': element.properties.color,
                                'font-weight': element.properties.bold ? 'bold' : 'normal',
                                'font-style': element.properties.italic ? 'italic' : 'normal',
                                'text-decoration': element.properties.underline ? 'underline' : 'none',
                                'text-align': element.properties.textAlign || 'left',
                                'background-color': element.properties.backgroundColor || 'transparent',
                                'border': element.properties.strokeWidth ?
                                    `${element.properties.strokeWidth}px solid ${element.properties.strokeColor}` : 'none',
                                'padding': '6px 8px', // more vertical padding for spacing
                                'width': '100%',
                                'height': fixedHeight + 'px',
                                'max-height': fixedHeight + 'px',
                                'box-sizing': 'border-box',
                                'overflow': 'hidden',
                                'white-space': 'normal',
                                'word-break': 'break-word',
                                'line-height': '1.2',
                                'display': 'block',
                                'text-transform': 'uppercase',
                            });
                        }
                        break;

                    case 'shape':
                        $element.html('<div class="element-content shape-container"><i class="fa fa-spinner fa-spin fa-2x"></i></div>');
                        const $shapeContent = $element.find('.element-content');
                        $shapeContent.addClass(`shape-${element.properties.shapeType}`);
                        let photoUrl = entityData.photo || element.properties.defaultPhoto || '<?= base_url("assets/img/icons/profile.png") ?>';
                        if (photoUrl.includes('wasabisys.com')) {
                            $.ajax({
                                url: '<?php echo site_url("idcards/Idcards_controller/getImageAsBase64"); ?>',
                                type: 'POST',
                                data: {
                                    image_url: photoUrl
                                },
                                dataType: 'json',
                                success: function(response) {
                                    if (response.success && response.base64) {
                                        const imgId = 'photo-' + Date.now() + '-' + Math.floor(Math.random() * 1000);
                                        $shapeContent.html(`<img id="${imgId}" src="${response.base64}" class="photo-in-shape">`);
                                        // Add small delay to ensure DOM is updated
                                        setTimeout(() => applyFaceDetection(imgId, response.base64), 100);
                                        incrementShapeElementsLoaded();
                                    } else {
                                        console.warn('Base64 conversion failed, using original URL');
                                        const imgId = 'photo-' + Date.now() + '-' + Math.floor(Math.random() * 1000);
                                        $shapeContent.html(`<img id="${imgId}" src="${photoUrl}" class="photo-in-shape">`);
                                        // Add small delay to ensure DOM is updated
                                        setTimeout(() => applyFaceDetection(imgId, photoUrl), 100);
                                        incrementShapeElementsLoaded();
                                    }
                                },
                                error: function(xhr, status, error) {
                                    console.error('Error fetching base64 image:', error);
                                    const imgId = 'photo-' + Date.now() + '-' + Math.floor(Math.random() * 1000);
                                    $shapeContent.html(`<img id="${imgId}" src="${photoUrl}" class="photo-in-shape">`);
                                    // Add small delay to ensure DOM is updated
                                    setTimeout(() => applyFaceDetection(imgId, photoUrl), 100);
                                    incrementShapeElementsLoaded();
                                }
                            });
                        } else {
                            const imgId = 'photo-' + Date.now() + '-' + Math.floor(Math.random() * 1000);
                            $shapeContent.html(`<img id="${imgId}" src="${photoUrl}" class="photo-in-shape">`);
                            // Add small delay to ensure DOM is updated
                            setTimeout(() => applyFaceDetection(imgId, photoUrl), 100);
                            incrementShapeElementsLoaded();
                        }
                        $shapeContent.css({
                            'background-color': element.properties.fillColor || 'transparent',
                            'border': `${element.properties.borderWidth || 0}px solid ${element.properties.borderColor || '#000000'}`,
                            'overflow': 'hidden',
                            'border-radius': `${element.properties.borderRadius || ''}px`,
                        });
                        break;

                    case 'image':
                        if (element.properties.fieldName === '[[PHOTO]]') {
                            const photoUrl = entityData.photo || element.properties.src || '<?= base_url("assets/images/default-profile.jpg") ?>';
                            const imageClass = element.properties.shapeType === 'circle' ?
                                'element-content photo-in-circle' : 'element-content';
                            const imgId = 'photo-' + Date.now() + '-' + Math.floor(Math.random() * 1000);
                            $element.html(`<img id="${imgId}" src="${photoUrl}" class="${imageClass}">`);
                            // Add small delay to ensure DOM is updated
                            setTimeout(() => applyFaceDetection(imgId, photoUrl), 100);
                        } else {
                            $element.html(`<img src="${element.properties.src || ''}" class="element-content">`);
                        }
                        break;
                }

                $container.append($element);
            });
        }

        function getFieldValue(fieldName, entityData) {
            const FIELD_MAPPINGS = [
                { field: '[[NAME]]', key: 'name' },
                { field: '[[ID]]', key: 'employee_code' },
                { field: '[[DEPARTMENT]]', key: 'department' },
                { field: '[[DESIGNATION]]', key: 'designation' },
                { field: '[[CONTACT]]', key: 'contact' },
                { field: '[[PARENT_NAME]]', key: 'parent_name' },
                { field: '[[FATHER_NAME]]', key: 'father_name' },
                { field: '[[MOTHER_NAME]]', key: 'mother_name' },
                { field: '[[FATHER_CONTACT]]', key: 'father_contact' },
                { field: '[[MOTHER_CONTACT]]', key: 'mother_contact' },
                { field: '[[BLOOD_GROUP]]', key: 'blood_group' },
                { field: '[[EMERGENCY_CONTACT]]', key: 'emergency_contact' },
                { field: '[[DATE_OF_BIRTH]]', key: 'date_of_birth' },
                { field: '[[DOB]]', key: 'dob' },
                { field: '[[QR_CODE]]', key: 'qr_code' },
                { field: '[[BAR_CODE]]', key: 'bar_code' },
                { field: '[[SIGNATURE]]', key: 'signature' },
                { field: '[[LOGO]]', key: 'logo' },
                { field: '[[ADDRESS]]', key: 'address' },
                { field: '[[EMAIL]]', key: 'email' },
                { field: '[[PHONE]]', key: 'phone' },
                { field: '[[WEBSITE]]', key: 'website' },
                { field: '[[SOCIAL_MEDIA]]', key: 'social_media' },
                { field: '[[QR_CODE_URL]]', key: 'qr_code_url' },
                { field: '[[BAR_CODE_URL]]', key: 'bar_code_url' },
                { field: '[[SIGNATURE_URL]]', key: 'signature_url' },
                { field: '[[LOGO_URL]]', key: 'logo_url' },
                { field: '[[ADDRESS_URL]]', key: 'address_url' },
                { field: '[[EMAIL_URL]]', key: 'email_url' },
                { field: '[[PHONE_URL]]', key: 'phone_url' },
                { field: '[[WEBSITE_URL]]', key: 'website_url' },
                { field: '[[SOCIAL_MEDIA_URL]]', key: 'social_media_url' },
                { field: '[[PHOTO]]', key: 'photo' },
                { field: '[[GRADE_SECTION]]', key: 'grade_section' },
                { field: '[[STUDENT_ADDRESS]]', key: 'address' },
                { field: '[[FATHER_ADDRESS]]', key: 'father_address' },
                { field: '[[MOTHER_ADDRESS]]', key: 'mother_address' },
                { field: '[[FATHER_PHOTO]]', key: 'father_photo' },
                { field: '[[MOTHER_PHOTO]]', key: 'mother_photo' },
                { field: '[[GRADE]]', key: 'grade' },
                { field: '[[COMBINATION]]', key: 'combination' },
                { field: '[[ENROLLMENT_NO]]', key: 'enrollment_no' },
                { field: '[[ALPHA_ROLL_NO]]', key: 'alpha_rollnum' },
                { field: '[[ADMISSION_NO]]', key: 'admission_no' },
                { field: '[[RELATION_TYPE]]', key: 'relation_type' },
                { field: '[[SIBLING_NAME]]', key: 'sibling_name' },
                { field: '[[NAME_CLASS]]', key: 'name_class' },
                { field: '[[STUDENT_PICKING_ROUTE]]', key: 'picking_route' },
                { field: '[[STUDENT_DROPPING_ROUTE]]', key: 'dropping_route' },
                { field: '[[SPOUSE_NAME]]', key: 'spouse_name' },
                { field: '[[STAFF_TYPE]]', key: 'staff_type' },
                { field: '[[QUALIFICATION]]', key: 'qualification' },
                { field: '[[CLASS_SECTION]]', key: 'class_section' },
                
            ];
            const mapping = FIELD_MAPPINGS.find(m => m.field === fieldName);
            return mapping ? entityData[mapping.key] || '' : '';
        }

        function renderSimplePreview(entityData) {
            const frontHtml = `
                <div style="width: 100%; height: 100%; position: relative; background-color: #f8f9fa; border: 2px solid #ddd;">
                    <!-- X marks -->
                    <svg width="100%" height="100%" xmlns="http://www.w3.org/2000/svg" style="position: absolute; top: 0; left: 0;">
                        <line x1="0" y1="0" x2="100%" y2="100%" stroke="#ccc" stroke-width="3"/>
                        <line x1="100%" y1="0" x2="0" y2="100%" stroke="#ccc" stroke-width="3"/>
                    </svg>

                    <!-- Content overlay -->
                    <div style="position: absolute; top: 0; left: 0; width: 100%; height: 100%; display: flex; flex-direction: column; justify-content: center; align-items: center; z-index: 2;">
                        <div style="background-color: rgba(255,255,255,0.9); padding: 15px; border-radius: 5px; text-align: center; box-shadow: 0 2px 5px rgba(0,0,0,0.1);">
                            <div style="font-size: 16px; color: #333; font-weight: bold; margin-bottom: 10px;">
                                ID CARD PREVIEW
                            </div>
                            <div style="color: #666;">
                                <strong>${entityData.name}</strong><br>
                                ${entityData.employee_code || entityData.admission_no}
                            </div>
                        </div>
                    </div>
                </div>
            `;

            const backHtml = `
                <div style="width: 100%; height: 100%; position: relative; background-color: #f8f9fa; border: 2px solid #ddd;">
                    <!-- X marks -->
                    <svg width="100%" height="100%" xmlns="http://www.w3.org/2000/svg" style="position: absolute; top: 0; left: 0;">
                        <line x1="0" y1="0" x2="100%" y2="100%" stroke="#ccc" stroke-width="3"/>
                        <line x1="100%" y1="0" x2="0" y2="100%" stroke="#ccc" stroke-width="3"/>
                    </svg>

                    <!-- Content overlay -->
                    <div style="position: absolute; top: 0; left: 0; width: 100%; height: 100%; display: flex; flex-direction: column; justify-content: center; align-items: center; z-index: 2;">
                        <div style="background-color: rgba(255,255,255,0.9); padding: 15px; border-radius: 5px; text-align: center; box-shadow: 0 2px 5px rgba(0,0,0,0.1);">
                            <div style="font-size: 16px; color: #333; font-weight: bold;">
                                BACK SIDE
                            </div>
                        </div>
                    </div>
                </div>
            `;

            $('#frontCardPreview1').html(frontHtml);
            $('#backCardPreview1').html(backHtml);

            // Enable action buttons when using simple preview
            // Get current status from the entity row
            const entityId = window.visibleEntityIds[window.currentEntityIndex];
            const entityRow = $(`.entity-table tbody tr[data-entity-id="${entityId}"]`);
            const currentStatus = entityRow.attr('data-status') || 'in review';

            // Enable buttons with proper state
            enableActionButtons(currentStatus);
        }

        $('.id-card-close').click(function() {
            closeAndClearModal();
        });

        function closeAndClearModal() {
            $('#idCardModal').fadeOut(300, function() {
                clearModalContent();
            });
        }

        function clearModalContent() {
            $('#idCardModalTitle').text('ID Card Preview');

            $('#frontCardPreview').empty();
            $('#backCardPreview').empty();

            $('#entityPositionIndicator').hide();
            $('#entityStatusBadge').hide();
            resetApprovalUI();

            // Clear any pending timeout
            if (window.buttonEnableTimeout) {
                clearTimeout(window.buttonEnableTimeout);
                window.buttonEnableTimeout = null;
            }

            // Reset shape element counters
            window.shapeElementsLoading = 0;
            window.shapeElementsLoaded = 0;
        }





        function modifyIdCard(entityId, onSuccess) {
            const type=entityId.relation_type;
            const sm_id=entityId.id;
            let order_id='<?php echo $order_id ?>';
            let template_id='<?php echo $template ?>';
            Swal.fire({
                title: 'Modify ID Card?',
                text: "Are you sure you want to mark this ID card for modification?",
                icon: 'warning',
                showCancelButton: true,
                confirmButtonText: 'Yes, mark for modification',
                cancelButtonText: 'Cancel',
              
                reverseButtons: true,
                html: `
                    <textarea id="swal-textarea" class="swal2-textarea" placeholder="Enter reason" rows="3"></textarea>
                `,
                preConfirm: () => {
                    const remarks = document.getElementById('swal-textarea').value.trim();
                    if (!remarks) {
                        Swal.showValidationMessage('Remarks are required');
                    }
                    return { remarks };
                }
            }).then((result) => {
                if (result.isConfirmed) {
                    const remarks = result.value.remarks;
                    showLoading('Marking ID card for modification...');

                    $.ajax({
                        url: '<?php echo site_url("idcards/Idcards_controller/change_idcard_entity_status"); ?>',
                        type: 'post',
                        data: {
                            sm_id: sm_id,
                            order_id: order_id,
                            type: type,
                            status: 'modify',
                            remarks:remarks
                        },
                        dataType: 'json',
                        success: function(response) {
                            try {
                                if (response.success) {
                                    hideLoading();

                                    showNotification('success', 'ID card marked for modification');

                                    // Update the entity status in the table
                                    updateEntityStatus(sm_id, 'modify');

                                    // Update the button states in the modal
                                    setModalActionButtonStates('modify');

                                    // Update the status badge
                                    $('#entityStatusBadge')
                                        .text('Modify')
                                        .attr('class', 'status-badge status-modify')
                                        .show();

                                    // Update the info-btn data-entity attribute to ensure it has the latest status
                                    const $infoBtn = $(`.entity-table tbody tr[data-entity-id="${sm_id}"]`).find('.info-btn');
                                    if ($infoBtn.length) {
                                        try {
                                            let entityData = JSON.parse(decodeURIComponent($infoBtn.attr('data-entity')));
                                            entityData.status = 'modify';
                                            $infoBtn.attr('data-entity', encodeURIComponent(JSON.stringify(entityData)));
                                        } catch (e) {
                                            console.error('Error updating info-btn data-entity:', e);
                                        }
                                    }

                                    // Refresh the status counts from the server
                                    $.ajax({
                                        url: '<?php echo site_url("idcards/Idcards_controller/get_order_status_counts_ajax"); ?>',
                                        type: 'POST',
                                        data: { order_id: order_id },
                                        dataType: 'json',
                                        success: function(response) {
                                            if (response.success) {
                                                // Update the status counts in the UI
                                                $('.status-tabs .status-tab[data-status="all"] .status-count').text(response.counts.all);
                                                $('.status-tabs .status-tab[data-status="in review"] .status-count').text(response.counts.in_review);
                                                $('.status-tabs .status-tab[data-status="approved"] .status-count').text(response.counts.approved);
                                                $('.status-tabs .status-tab[data-status="removed"] .status-count').text(response.counts.removed);
                                                $('.status-tabs .status-tab[data-status="re_ordered"] .status-count').text(response.counts.re_ordered);
                                            }
                                        }
                                    });
                                    if (typeof onSuccess === 'function') onSuccess();

                                    // Auto-navigate to next entity after modification
                                    setTimeout(function() {
                                        if (
                                            typeof window.currentEntityIndex !== 'undefined' &&
                                            typeof window.visibleEntityIds !== 'undefined' &&
                                            window.currentEntityIndex < window.visibleEntityIds.length - 1
                                        ) {
                                            // Store the current entity ID before navigating
                                            const currentEntityId = window.visibleEntityIds[window.currentEntityIndex];

                                            window.currentEntityIndex++;
                                            const nextEntityId = window.visibleEntityIds[window.currentEntityIndex];
                                            if (typeof window.loadEntityData === 'function') {
                                                window.loadEntityData(nextEntityId);
                                            }
                                        }
                                    }, 800);
                                } else {
                                    hideLoading();

                                    showNotification('error', response.message || 'Failed to mark ID card for modification');
                                }
                            } catch (e) {
                                hideLoading();

                                showNotification('error', 'An error occurred while processing the response');
                                console.error('Error parsing response:', e);
                            }
                        },
                        error: function(err) {
                            hideLoading();

                            showNotification('error', 'Failed to mark ID card for modification');
                            console.error('Error modifying ID card:', err);
                        }
                    });
                }
            });
        }

        function removeIdCard(entityId, onSuccess) {
            const type=entityId.relation_type;
            const sm_id=entityId.id;
            let order_id='<?php echo $order_id ?>';
            let template_id='<?php echo $template ?>';
            Swal.fire({
                title: 'Remove ID Card?',
                text: "Are you sure you want to remove this ID card?",
                icon: 'warning',
                showCancelButton: true,
                confirmButtonText: 'Yes, remove it',
                cancelButtonText: 'Cancel',
                confirmButtonColor: '#d33',
                reverseButtons: true,
                html: `
                    <textarea id="swal-textarea" class="swal2-textarea" placeholder="Enter reason" rows="3"></textarea>
                `,
                preConfirm: () => {
                    const remarks = document.getElementById('swal-textarea').value.trim();
                    if (!remarks) {
                        Swal.showValidationMessage('Remarks are required');
                    }
                    return { remarks };
                }
            }).then((result) => {
                if (result.isConfirmed) {
                    const remarks = result.value.remarks;
                    showLoading('Removing ID card...');

                    $.ajax({
                        url: '<?php echo site_url("idcards/Idcards_controller/change_idcard_entity_status"); ?>',
                        type: 'post',
                        data: {
                            sm_id: sm_id,
                            order_id: order_id,
                            type: type,
                            status: 'removed',
                            remarks: remarks

                        },
                        dataType: 'json',
                        success: function(response) {
                            try {
                                if (response.success) {
                                    hideLoading();

                                    showNotification('success', 'ID card removed successfully');

                                    // Update the entity status in the table
                                    updateEntityStatus(sm_id, 'removed');

                                    // Update the button states in the modal
                                    setModalActionButtonStates('removed');

                                    // Update the status badge
                                    $('#entityStatusBadge')
                                        .text('Removed')
                                        .attr('class', 'status-badge status-removed')
                                        .show();

                                    // Update the info-btn data-entity attribute to ensure it has the latest status
                                    const $infoBtn = $(`.entity-table tbody tr[data-entity-id="${sm_id}"]`).find('.info-btn');
                                    if ($infoBtn.length) {
                                        try {
                                            let entityData = JSON.parse(decodeURIComponent($infoBtn.attr('data-entity')));
                                            entityData.status = 'removed';
                                            $infoBtn.attr('data-entity', encodeURIComponent(JSON.stringify(entityData)));
                                        } catch (e) {
                                            console.error('Error updating info-btn data-entity:', e);
                                        }
                                    }

                                    // Refresh the status counts from the server
                                    $.ajax({
                                        url: '<?php echo site_url("idcards/Idcards_controller/get_order_status_counts_ajax"); ?>',
                                        type: 'POST',
                                        data: { order_id: order_id },
                                        dataType: 'json',
                                        success: function(response) {
                                            if (response.success) {
                                                // Update the status counts in the UI
                                                $('.status-tabs .status-tab[data-status="all"] .status-count').text(response.counts.all);
                                                $('.status-tabs .status-tab[data-status="in review"] .status-count').text(response.counts.in_review);
                                                $('.status-tabs .status-tab[data-status="approved"] .status-count').text(response.counts.approved);
                                                $('.status-tabs .status-tab[data-status="removed"] .status-count').text(response.counts.removed);
                                                $('.status-tabs .status-tab[data-status="re_ordered"] .status-count').text(response.counts.re_ordered);
                                            }
                                        }
                                    });
                                    if (typeof onSuccess === 'function') onSuccess();

                                    // Auto-navigate to next entity after removal
                                    setTimeout(function() {
                                        if (
                                            typeof window.currentEntityIndex !== 'undefined' &&
                                            typeof window.visibleEntityIds !== 'undefined' &&
                                            window.currentEntityIndex < window.visibleEntityIds.length - 1
                                        ) {
                                            // Store the current entity ID before navigating
                                            const currentEntityId = window.visibleEntityIds[window.currentEntityIndex];

                                            window.currentEntityIndex++;
                                            const nextEntityId = window.visibleEntityIds[window.currentEntityIndex];
                                            if (typeof window.loadEntityData === 'function') {
                                                window.loadEntityData(nextEntityId);
                                            }
                                        }
                                    }, 800);
                                } else {
                                    hideLoading();

                                    showNotification('error', response.message || 'Failed to remove ID card');
                                }
                            } catch (e) {
                                hideLoading();

                                showNotification('error', 'An error occurred while processing the response');
                                console.error('Error parsing response:', e);
                            }
                        },
                        error: function(err) {
                            hideLoading();

                            showNotification('error', 'Failed to remove ID card');
                            console.error('Error removing ID card:', err);
                        }
                    });
                }
            });
        }

        function formatDate(dateString) {
            if (!dateString) return '';

            try {
                const date = new Date(dateString);
                if (isNaN(date.getTime())) return dateString;

                const day = date.getDate().toString().padStart(2, '0');
                const month = ['Jan', 'Feb', 'Mar', 'Apr', 'May', 'Jun', 'Jul', 'Aug', 'Sep', 'Oct', 'Nov', 'Dec'][date.getMonth()];
                const year = date.getFullYear();

                return day + '-' + month + '-' + year;
            } catch (e) {
                console.error('Error formatting date:', e);
                return dateString;
            }
        }

        function showLoading(message) {
            if ($('#loadingOverlay').length === 0) {
                $('body').append(`
                    <div id="loadingOverlay" style="display:none; position:fixed; top:0; left:0; width:100%; height:100%; background-color:rgba(0,0,0,0.5); z-index:9999;">
                        <div style="position:absolute; top:50%; left:50%; transform:translate(-50%,-50%); background-color:white; padding:20px; border-radius:5px; text-align:center;">
                            <div class="spinner-border text-primary" role="status">
                                <span class="sr-only">Loading...</span>
                            </div>
                            <div id="loadingMessage" class="mt-2">Loading...</div>
                        </div>
                    </div>
                `);
            }

            $('#loadingMessage').text(message || 'Loading...');

            $('#loadingOverlay').fadeIn(200);
        }

        function hideLoading() {
            $('#loadingOverlay').fadeOut(200);
        }

        function showNotification(type, message) {
            if ($('#notificationContainer').length === 0) {
                $('body').append('<div id="notificationContainer" style="position:fixed; top:20px; right:20px; z-index:9999;"></div>');
            }

            const notificationId = 'notification-' + Date.now();

            let bgColor, textColor;
            switch (type) {
                case 'success':
                    bgColor = '#28a745';
                    textColor = 'white';
                    break;
                case 'error':
                    bgColor = '#dc3545';
                    textColor = 'white';
                    break;
                case 'warning':
                    bgColor = '#ffc107';
                    textColor = '#212529';
                    break;
                default:
                    bgColor = '#17a2b8';
                    textColor = 'white';
            }

            const notification = $(`
                <div id="${notificationId}" style="margin-bottom:10px; padding:15px; border-radius:4px; background-color:${bgColor}; color:${textColor}; box-shadow:0 2px 5px rgba(0,0,0,0.2); opacity:0; transition:opacity 0.3s ease;">
                    <div style="display:flex; justify-content:space-between; align-items:center;">
                        <div>${message}</div>
                        <button type="button" style="background:none; border:none; color:${textColor}; font-size:20px; line-height:1; cursor:pointer;" onclick="$('#${notificationId}').fadeOut(300, function() { $(this).remove(); });">&times;</button>
                    </div>
                </div>
            `);

            $('#notificationContainer').append(notification);

            setTimeout(() => {
                $(`#${notificationId}`).css('opacity', '1');
            }, 10);

            setTimeout(() => {
                $(`#${notificationId}`).fadeOut(300, function() {
                    $(this).remove();
                });
            }, 5000);
        }

        // Update status badge after approve, modify, or remove actions
        function updateEntityStatus(entityId, status) {
            let dataTable = null;
            if ($.fn.DataTable.isDataTable('.entity-table')) {
                dataTable = $('.entity-table').DataTable();
            }

            const $row = $(`.entity-table tbody tr[data-entity-id="${entityId}"]`);
            if ($row.length) {
                // Update status text in DOM
                $row.attr('data-status', status);
                
                const $statusCell = $row.find('.status-cell .status-badge');
                $statusCell.text(capitalizeFirstLetter(status));
                
                // Change class for styling
                $statusCell.removeClass().addClass(`status-badge status-${status}`);
                
                // Check for 'modify' and toggle button visibility
                const $editButton = $row.find('.edit-btn');
                if (status.toLowerCase().trim() === 'modify') {
                    $editButton.show();
                } else {
                    $editButton.hide();
                }

                // Sync with DataTables if present
                if (dataTable) {
                    const rowIndex = dataTable.row($row).index();
                    if (rowIndex !== undefined) {
                        dataTable.row(rowIndex).invalidate().draw(false);
                    }
                }
            }
        }


        // Helper to set modal action button states based on status
        function setModalActionButtonStates(status) {
            // Check if order is in a locked state (order_submitted, in_printing, or delivered)
            const isOrderLocked = ['order_submitted', 'in_printing', 'delivered'].includes(order_status);

            // If order is locked, disable all action buttons regardless of entity status
            if (isOrderLocked) {
                // Disable all buttons
                $('.id-card-approve')
                    .prop('disabled', true)
                    .attr('title', 'Actions are locked - Order is in ' + capitalizeFirstLetter(order_status.replace('_', ' ')) + ' status')
                    .html('<i class="fa fa-lock"></i> APPROVE');
                $('.id-card-modify')
                    .prop('disabled', true)
                    .attr('title', 'Actions are locked - Order is in ' + capitalizeFirstLetter(order_status.replace('_', ' ')) + ' status')
                    .html('<i class="fa fa-lock"></i> MODIFY');
                $('.id-card-remove')
                    .prop('disabled', true)
                    .attr('title', 'Actions are locked - Order is in ' + capitalizeFirstLetter(order_status.replace('_', ' ')) + ' status')
                    .html('<i class="fa fa-lock"></i> REMOVE');

                // Add a locked class to the buttons for additional styling
                $('.id-card-action-btn').addClass('locked-action');

                // Show a notification to the user
                if (!window.lockNotificationShown) {
                    showNotification('info', 'This order is in ' + capitalizeFirstLetter(order_status.replace('_', ' ')) + ' status. All actions are locked.');
                    window.lockNotificationShown = true;
                }
            } else {
                // Normal behavior when order is not locked
                // status: 'approved', 'modify', 'removed', 'in review'
                $('.id-card-approve')
                    .prop('disabled', status === 'approved')
                    .attr('title', status === 'approved' ? 'This ID card is already approved' : 'Approve this ID card')
                    .html('<i class="fa fa-check"></i> APPROVE')
                    .removeClass('locked-action');
                $('.id-card-modify')
                    .prop('disabled', status === 'modify')
                    .attr('title', status === 'modify' ? 'This ID card is already marked for modification' : 'Mark this ID card for modification')
                    .html('<i class="fa fa-pencil"></i> MODIFY')
                    .removeClass('locked-action');
                $('.id-card-remove')
                    .prop('disabled', status === 'removed')
                    .attr('title', status === 'removed' ? 'This ID card is already removed' : 'Remove this ID card')
                    .html('<i class="fa fa-trash"></i> REMOVE')
                    .removeClass('locked-action');
            }
        }

        // Move this function to global scope so it can be called from anywhere
        window.loadEntityData = async function(entityId) {
            // Show loading indicator
            $('#modalLoadingIndicator').show();

            // Find the entity row
            const entityRow = $(`.entity-table tbody tr[data-entity-id="${entityId}"]`);
            if (!entityRow.length) {
                console.error('Entity row not found for ID:', entityId);
                $('#modalLoadingIndicator').hide();
                return;
            }

            // Get entity data
            let rawData = entityRow.find('.info-btn').attr('data-entity');
            if (!rawData) {
                console.error('Entity data not found for ID:', entityId);
                $('#modalLoadingIndicator').hide();
                return;
            }

            let entity = JSON.parse(decodeURIComponent(rawData));
            let entityData;

            // Process entity data based on type
            if (id_card_for.toLowerCase() === 'staff') {
                let faceImage = entity.picture_url;
                if(faceImage != ''){
                    faceImage = await cropFaceFromPhoto(entity.picture_url);
                }
                entityData = {
                    id: entity.avatar_id, // This is sm_id for staff
                    name: entity.staff_name,
                    employee_code: entity.employee_code,
                    dob: entity.dob,
                    date_of_birth: entity.dob,
                    contact: entity.contact,
                    emergency_contact: entity.emergency_contact,
                    blood_group: entity.blood_group,
                    address: entity.address,
                    department: entity.department,
                    designation: entity.designation,
                    phone: entity.contact,
                    photo:  entity.picture_url,
                    type: entity.type,
                    relation_type : id_card_for,
                    spouse_name:entity.spouse_name,
                    staff_type:entity.staff_type,
                    qualification : entity.qualification
                };
            } else {
                let parent_name_info = entity.father_name || '';
                let parent_contact_info = (entity.mother_contact || '') + ', ' + (entity.father_contact || '');

                if (entity.point_of_contact != null && entity.point_of_contact !== '') {
                    // override like original logic
                    parent_name_info = entity.parent_name;
                    parent_contact_info = entity.contact;
                } else {
                    // fallback to avatar_type logic
                    switch (entity.avatar_type) {
                        case 'Father':
                            parent_name_info = entity.father_name;
                            parent_contact_info = entity.father_contact;
                            break;
                        case 'Mother':
                            parent_name_info = entity.mother_name;
                            parent_contact_info = entity.mother_contact;
                            break;
                        case 'Guardian':
                            parent_name_info = entity.guardian_name;
                            parent_contact_info = entity.guardian_contact;
                            break;
                        case 'Guardian_2':
                            parent_name_info = entity.guardian_2_name;
                            parent_contact_info = entity.guardian_2_contact;
                            break;
                        case 'Driver':
                            parent_name_info = entity.driver_name;
                            parent_contact_info = entity.driver_contact;
                            break;
                        case 'Driver_2':
                            parent_name_info = entity.driver_2_name;
                            parent_contact_info = entity.driver_2_contact;
                            break;
                        default:
                            // If all else fails
                            parent_name_info = entity.parent_name || entity.father_name;
                            parent_contact_info = entity.contact || ((entity.mother_contact || '') + ', ' + (entity.father_contact || ''));
                    }
                }             
                entityData = {
                    id: entity.sa_id,
                    name: entity.name,
                    admission_no: entity.admission_no,
                    combination: entity.combination,
                    enrollment_no: entity.enrollment_no,
                    alpha_rollnum: entity.alpha_rollnum,
                    grade_section: entity.grade_section,
                    grade: entity.grade,
                    dob: entity.dob,
                    date_of_birth: entity.dob,
                    contact: parent_contact_info,
                    blood_group: entity.blood_group,
                    address: entity.address,
                    parent_name: parent_name_info,
                    father_name: entity.father_name,
                    mother_name: entity.mother_name,
                    father_contact: entity.father_contact,
                    mother_contact: entity.mother_contact,
                    email: entity.email,
                    phone: entity.preferred_contact_no,
                    photo: (entity.avatar_type == 'Father') ? entity.father_photo :
                            (entity.avatar_type == 'Mother') ? entity.mother_photo :
                            (entity.avatar_type == 'Guardian') ? entity.guardian_photo :
                            (entity.avatar_type == 'Guardian_2') ? entity.guardian_2_photo :
                            (entity.avatar_type == 'Driver') ? entity.driver_photo :
                            (entity.avatar_type == 'Driver_2') ? entity.driver_2_photo :
                            entity.picture_url,
                    type: entity.type,
                    father_address : entity.father_address,
                    mother_address : entity.mother_address,
                    qr_code : entity.qr_code,
                    relation_type : id_card_for,
                    sibling_name : entity.sibling_name,
                    name_class : entity.name_class,
                    picking_route : entity.picking_route,
                    dropping_route : entity.dropping_route,
                    mother_photo : entity.mother_photo,
                    father_photo : entity.father_photo,
                    class_section : entity.class_section
                };
            }
            // Reset approval UI for new staff details
            resetApprovalUI();

            // Update modal title
            $('#idCardModalTitle').text('ID Card Preview - ' + entityData.name);

            // Render preview (this will show another loading indicator)
            renderPreview(entityData);

            // Get the current status from the entity row
            const currentStatus = entityRow.attr('data-status') || 'in review';
            // Set up action buttons
            $('.id-card-approve')
                .data('staff-id', entityData.id)
                .data('type', entityData.relation_type)
                .off('click')
                .on('click', function () {
                    const $btn = $(this);
                    $btn.prop('disabled', true).html('<i class="fa fa-spinner fa-spin"></i> Please wait...');
                    // Only update status badge after approval is confirmed and successful
                    approveIdCard({
                        button: this,
                        onSuccess: function() {
                            $('#entityStatusBadge')
                                .text('Approved')
                                .attr('class', 'status-badge status-approved')
                                .show();
                        }
                    });
                });
            $('.id-card-modify')
                .off('click')
                .on('click', function() {
                    // Only update status badge after modification is confirmed and successful
                    modifyIdCard(entityData, function() {
                        $('#entityStatusBadge')
                            .text('Modify')
                            .attr('class', 'status-badge status-modify')
                            .show();
                    });
                });
            $('.id-card-remove')
                .off('click')
                .on('click', function() {
                    // Only update status badge after removal is confirmed and successful
                    removeIdCard(entityData, function() {
                        $('#entityStatusBadge')
                            .text('Removed')
                            .attr('class', 'status-badge status-removed')
                            .show();
                    });
                });

            // Set correct enabled/disabled state for all three buttons
            setModalActionButtonStates(currentStatus);

            // Update navigation buttons state
            if (typeof updateNavigationButtons === 'function') updateNavigationButtons();

            // Hide loading indicator after everything is set up
            $('#modalLoadingIndicator').hide();
        };

        // Helper to update the entity position indicator in the modal
        function updateEntityPositionIndicator() {
            // ...existing code...
            // Fix: Only count unique, visible entity IDs (avoid duplicates)
            if (
                typeof window.currentEntityIndex !== 'undefined' &&
                typeof window.visibleEntityIds !== 'undefined' &&
                Array.isArray(window.visibleEntityIds) &&
                window.visibleEntityIds.length > 0 &&
                window.currentEntityIndex >= 0
            ) {
                // Remove duplicates from visibleEntityIds
                const uniqueIds = [...new Set(window.visibleEntityIds)];
                $('#entityPositionIndicator')
                    .text((window.currentEntityIndex + 1) + ' of ' + uniqueIds.length)
                    .show();
            } else {
                $('#entityPositionIndicator').hide();
            }
        }

        // Add this function to update the status badge in the modal header
        function updateEntityStatusBadge() {
            if (
                typeof window.currentEntityIndex !== 'undefined' &&
                typeof window.visibleEntityIds !== 'undefined' &&
                Array.isArray(window.visibleEntityIds) &&
                window.visibleEntityIds.length > 0 &&
                window.currentEntityIndex >= 0
            ) {
                const entityId = window.visibleEntityIds[window.currentEntityIndex];
                // Always get the fresh row to ensure we have the latest status
                const $row = $(`.entity-table tbody tr[data-entity-id="${entityId}"]`);
                if ($row.length) {
                    const status = $row.attr('data-status') || 'in review';

                    // Set badge text and class with the correct styling
                    if (status === 'approved') {
                        $('#entityStatusBadge')
                            .text('Approved')
                            .attr('class', 'status-badge status-approved')
                            .show();
                    } else if (status === 'modify') {
                        $('#entityStatusBadge')
                            .text('Modify')
                            .attr('class', 'status-badge status-modify')
                            .show();
                    } else if (status === 'removed') {
                        $('#entityStatusBadge')
                            .text('Removed')
                            .attr('class', 'status-badge status-removed')
                            .show();
                    } else {
                        // Default to 'in review'
                        $('#entityStatusBadge')
                            .text('In Review')
                            .attr('class', 'status-badge status-in-review')
                            .show();
                    }

                    // Also update the button states to match the current status
                    setModalActionButtonStates(status);
                } else {
                    $('#entityStatusBadge').hide();
                }
            } else {
                $('#entityStatusBadge').hide();
            }
        }

        // Patch navigation and modal open to update the indicator and badge
        function updateNavigationButtons() {
            // ...existing code...
            $('#prevStaffBtn').prop('disabled', window.currentEntityIndex <= 0);
            $('#nextStaffBtn').prop('disabled', window.currentEntityIndex >= window.visibleEntityIds.length - 1);
            updateEntityPositionIndicator();
            updateEntityStatusBadge();
        }

        // Patch window.loadEntityData to update the indicator and badge after loading
        // Only patch if not already patched
        if (!window._loadEntityDataPatched) {
            const originalLoadEntityData = window.loadEntityData;
            window.loadEntityData = async function(entityId) {
                await originalLoadEntityData.call(this, entityId);
                updateEntityPositionIndicator();
                updateEntityStatusBadge();
            };
            window._loadEntityDataPatched = true;
        }

        // Hide indicator and badge when modal is cleared
        function clearModalContent() {
            $('#idCardModalTitle').text('ID Card Preview');
            $('#frontCardPreview').empty();
            $('#backCardPreview').empty();
            $('#entityPositionIndicator').hide();
            $('#entityStatusBadge').hide();
            resetApprovalUI();
        }

    function single_file_progress(percentage) {
        if (percentage == 100) {
            in_progress_promises--;
            if (in_progress_promises == 0) {
                current_percentage = percentage;
            }
        } else {
            if (current_percentage < percentage) {
                current_percentage = percentage;
            }
        }
        $("#percentage_doc_completed").html(`${current_percentage}%`);
        return false;
    }

function approveIdCard(opts) {
    // opts: {button, onSuccess}
    Swal.fire({
        title: 'Approve ID Card?',
        text: "Are you sure you want to approve this ID card?",
        icon: 'question',
        showCancelButton: true,
        confirmButtonText: 'Yes, approve it',
        cancelButtonText: 'Cancel',
        reverseButtons: true
    }).then((result) => {
        if (result.isConfirmed) {
            $('.id-card-actions').hide();
            // Hide old progress bar, show new overlay
            $('#approval-progress-container').hide();
            showCustomApprovalLoadingOverlay(0);
            const staff_id = $(opts.button).data('staff-id');
            const type = id_card_for;
            const template = <?= json_encode($template) ?>;
            const order_id='<?php echo $order_id ?>';
            in_progress_promises = 2;
            current_percentage = 0;
            setTimeout(() => {
                updateCustomApprovalProgress(10);
                processCardImages(staff_id, type, template, order_id);
                // Call onSuccess after approval process (simulate after processCardImages if needed)
                if (typeof opts.onSuccess === 'function') {
                    opts.onSuccess();
                }
            }, 500);
        } else {
            // Reset the button state if the user cancels
            $(opts.button).prop('disabled', false).html('<i class="fa fa-check"></i> APPROVE');

            // Make sure we update the status badge and buttons to reflect the current status
            if (typeof window.currentEntityIndex !== 'undefined' &&
                window.visibleEntityIds &&
                window.currentEntityIndex >= 0) {

                const entityId = window.visibleEntityIds[window.currentEntityIndex];
                const $row = $(`.entity-table tbody tr[data-entity-id="${entityId}"]`);
                if ($row.length) {
                    const currentStatus = $row.attr('data-status') || 'in review';
                    setModalActionButtonStates(currentStatus);
                    updateEntityStatusBadge();
                }
            }
        }
    });
}

function updateProgressBar(percentage, statusText) {
    updateCustomApprovalProgress(percentage);
    // Optionally update status text if you want to show it
    // $('#customApprovalProgressStatus').text(statusText || '');
}

function processCardImages(staff_id, type, template, order_id) {
    const frontEl = document.querySelector('#frontCardPreview');
    const backEl = document.querySelector('#backCardPreview');
    if (!frontEl || !backEl) {
        updateProgressBar(100, 'Error: Card preview not found');
        showNotification('error', 'Card preview not found.');
        resetApprovalUI();
        return;
    }

    // Update progress to show we're starting to process the front image
    updateProgressBar(15, 'Processing front side of ID card...');

    // Process front image first
    processImage(frontEl, 'front.png', staff_id, type, template, order_id)
        .then(frontUrl => {
            // Update progress to show we're starting to process the back image
            updateProgressBar(40, 'Processing back side of ID card...');

            // Then process back image
            return processImage(backEl, 'back.png', staff_id, type, template, order_id)
                .then(backUrl => {
                    // Both images processed successfully
                    updateProgressBar(70, 'Uploading ID card to server...');
                    generateCard(staff_id, type, template, frontUrl, backUrl,order_id);
                });
        })
        .catch(error => {
            updateProgressBar(100, 'Error: Failed to process images');
            showNotification('error', error.message || 'Failed to process images');
            resetApprovalUI();
        });
}

function processImage(element, filename, staff_id, type, template) {
    return new Promise((resolve, reject) => {
        // Use html2canvas with better options for more reliable rendering
        const options = {
            useCORS: true,           // Enable CORS for images
            allowTaint: true,        // Allow tainted canvas
            backgroundColor: null,    // Transparent background
            scale: 2                 // Higher quality
        };

        html2canvas(element, options).then(canvas => {
            canvas.toBlob(blob => {
                if (!blob) {
                    reject(new Error(`Failed to render ${filename}`));
                    return;
                }
                uploadToS3(blob, filename, staff_id, type, template)
                    .then(resolve)
                    .catch(reject);
            }, 'image/png');
        }).catch(err => {
            console.error('Error in html2canvas:', err);
            reject(err);
        });
    });
}

function uploadToS3(blob, filename, staff_id, type, template) {
    return new Promise((resolve, reject) => {
        // Update progress to show we're getting a signed URL
        if (filename === 'front.png') {
            updateProgressBar(20, 'Getting upload URL for front side...');
        } else {
            updateProgressBar(45, 'Getting upload URL for back side...');
        }

        $.ajax({
            url: '<?php echo site_url("S3_controller/getSignedUrl"); ?>',
            type: 'POST',
            data: {
                file_type: blob.type,
                filename: filename,
                folder: 'idcards'
            },
            success: function(response) {
                try {
                    const { path, signedUrl } = JSON.parse(response);

                    // Update progress to show we're uploading
                    if (filename === 'front.png') {
                        updateProgressBar(25, 'Uploading front side to server...');
                    } else {
                        updateProgressBar(50, 'Uploading back side to server...');
                    }

                    $.ajax({
                        url: signedUrl,
                        type: 'PUT',
                        headers: {
                            "Content-Type": blob.type,
                            "x-amz-acl": "public-read"
                        },
                        processData: false,
                        data: blob,
                        xhr: function() {
                            const xhr = $.ajaxSettings.xhr();
                            xhr.upload.onprogress = function(e) {
                                if (e.lengthComputable) {
                                    const progress = (e.loaded / e.total * 100) | 0;
                                    // Update progress based on which file we're uploading
                                    if (filename === 'front.png') {
                                        // Front image progress from 25% to 40%
                                        const adjustedProgress = 25 + (progress * 0.15);
                                        updateProgressBar(adjustedProgress, 'Uploading front side: ' + progress + '%');
                                    } else {
                                        // Back image progress from 50% to 65%
                                        const adjustedProgress = 50 + (progress * 0.15);
                                        updateProgressBar(adjustedProgress, 'Uploading back side: ' + progress + '%');
                                    }
                                }
                            };
                            return xhr;
                        },
                        success: () => {
                            if (filename === 'front.png') {
                                updateProgressBar(40, 'Front side uploaded successfully');
                            } else {
                                updateProgressBar(65, 'Back side uploaded successfully');
                            }
                            resolve(path);
                        },
                        error: (err) => {
                            updateProgressBar(100, 'Error: Failed to upload to server');
                            reject(new Error('Failed to upload to S3'));
                        }
                    });
                } catch (err) {
                    updateProgressBar(100, 'Error: Invalid server response');
                    reject(new Error('Invalid signed URL response'));
                }
            },
            error: () => {
                updateProgressBar(100, 'Error: Failed to get upload URL');
                reject(new Error('Failed to get signed URL'));
            }
        });
    });
}

function generateCard(staff_id, type, template, frontUrl, backUrl, order_id) {
    updateProgressBar(75, 'Finalizing ID card approval...');

    $.ajax({
        url: '<?php echo site_url("idcards/Idcards_controller/aprrove_idcards_templates") ?>',
        type: 'POST',
        data: {
            staffData: staff_id,
            type: type,
            template: template,
            frontUrl: frontUrl,
            backUrl: backUrl,
            order_id: order_id
        },
        success: function(response) {
            const result = JSON.parse(response);
            // console.log(result);
            if (result.success) {
                updateProgressBar(100, 'ID card approved successfully!');
                showNotification('success', 'ID card approved successfully');

                updateEntityStatus(staff_id, 'approved');

                // Update the info-btn data-entity attribute to ensure it has the latest status
                const $infoBtn = $(`.entity-table tbody tr[data-entity-id="${staff_id}"]`).find('.info-btn');
                if ($infoBtn.length) {
                    try {
                        let entityData = JSON.parse(decodeURIComponent($infoBtn.attr('data-entity')));
                        entityData.status = 'approved';
                        $infoBtn.attr('data-entity', encodeURIComponent(JSON.stringify(entityData)));
                    } catch (e) {
                        console.error('Error updating info-btn data-entity:', e);
                    }
                }

                // Update the button states in the modal
                setModalActionButtonStates('approved');

                // Update the status badge
                $('#entityStatusBadge')
                    .text('Approved')
                    .attr('class', 'status-badge status-approved')
                    .show();

                $.ajax({
                    url: '<?php echo site_url("idcards/Idcards_controller/get_order_status_counts_ajax"); ?>',
                    type: 'POST',
                    data: { order_id: order_id },
                    dataType: 'json',
                    success: function(response) {
                        if (response.success) {
                            $('.status-tabs .status-tab[data-status="all"] .status-count').text(response.counts.all);
                            $('.status-tabs .status-tab[data-status="in review"] .status-count').text(response.counts.in_review);
                            $('.status-tabs .status-tab[data-status="approved"] .status-count').text(response.counts.approved);
                            $('.status-tabs .status-tab[data-status="removed"] .status-count').text(response.counts.removed);
                            $('.status-tabs .status-tab[data-status="re_ordered"] .status-count').text(response.counts.re_ordered);
                        }
                    }
                });

                refreshEntityData(function(data) {
                    // console.log('Entity data refreshed after approval');
                });

                resetApprovalUI();

                // Auto-navigate to next entity after approval
                setTimeout(function() {
                    if (
                        typeof window.currentEntityIndex !== 'undefined' &&
                        typeof window.visibleEntityIds !== 'undefined' &&
                        window.currentEntityIndex < window.visibleEntityIds.length - 1
                    ) {
                        window.currentEntityIndex++;
                        const nextEntityId = window.visibleEntityIds[window.currentEntityIndex];
                        if (typeof window.loadEntityData === 'function') {
                            window.loadEntityData(nextEntityId);
                        }
                    }
                }, 800);
            } else {
                updateProgressBar(100, 'Error: ' + (result.message || 'Failed to approve ID card'));
                showNotification('error', result.message || 'Failed to approve ID card');
                resetApprovalUI();
            }
            Swal.close();
            updateVisibleCount();
        },
        error: function(xhr, status, error) {
            console.error('Card generation error:', error);
            updateProgressBar(100, 'Error: Failed to generate card');
            showNotification('error', 'Failed to generate card');
            resetApprovalUI();
        }
    });
}


function resetApprovalUI() {
    $('#approval-progress-bar').css('width', '0%');
    $('#progress-percentage').text('0%');
    $('#progress-status-text').text('Preparing...');

    $('#approval-progress-container').hide();
    $('.id-card-actions').show();

    // Get the current entity row and status if available
    let currentStatus = 'in review';

    if (window.currentEntityIndex >= 0) {
        const visibleRows = $('.entity-table tbody tr:visible');
        if (visibleRows.length > 0 && window.currentEntityIndex < visibleRows.length) {
            const entityId = window.visibleEntityIds[window.currentEntityIndex];
            if (entityId) {
                const entityRow = $(`.entity-table tbody tr[data-entity-id="${entityId}"]`);
                if (entityRow.length) {
                    currentStatus = entityRow.attr('data-status') || 'in review';
                }
            }
        }
    }

    // Set correct enabled/disabled state for all three buttons
    setModalActionButtonStates(currentStatus);

    hideLoading();
    hideCustomApprovalLoadingOverlay();
}

function showCustomApprovalLoadingOverlay(percent) {
    // Show overlay only inside modal content, no blur
    $('#customApprovalLoadingOverlay').css({
        display: 'flex',
        position: 'absolute', // Overlay only modal content
        top: 0,
        left: 0,
        width: '100%',
        height: '100%'
    });
    updateCustomApprovalProgress(percent || 0);

    // Auto-navigate to next staff when progress completes
    if (percent === 100) {
        setTimeout(function() {
            // Try to move to next staff if possible
            if (
                typeof window.currentEntityIndex !== 'undefined' &&
                typeof window.visibleEntityIds !== 'undefined' &&
                window.currentEntityIndex < window.visibleEntityIds.length - 1
            ) {
                // Hide overlay before navigating
                hideCustomApprovalLoadingOverlay();
                // Move to next staff
                window.currentEntityIndex++;
                // Use the same loader as info-btn click
                const nextEntityId = window.visibleEntityIds[window.currentEntityIndex];
                // Load next entity data
                $('.id-card-actions').show(); // Ensure actions are visible for next
                // Use the global loadEntityData function
                if (typeof window.loadEntityData === 'function') {
                    window.loadEntityData(nextEntityId);
                }
            } else {
                // If no more staff, just hide overlay
                hideCustomApprovalLoadingOverlay();
            }
        }, 800); // Small delay for user to see 100%
    }
}
function hideCustomApprovalLoadingOverlay() {
    $('#customApprovalLoadingOverlay').hide();
}

function updateCustomApprovalProgress(percent) {
    var roundedPercent = Math.round(percent);
    $('#customApprovalProgressText').text(roundedPercent + '%');

    // Update circular progress bar
    var circle = document.querySelector('#customApprovalProgressCircle .progress-bar');
    if (circle) {
        var radius = circle.r.baseVal.value;
        var circumference = 2 * Math.PI * radius;
        var offset = circumference - (roundedPercent / 100) * circumference;
        circle.style.strokeDasharray = circumference;
        circle.style.strokeDashoffset = offset;
    }
}

// Move this function to global scope so it can be called from anywhere
window.loadEntityData = async function(entityId) {
    // Show loading indicator
    $('#modalLoadingIndicator').show();

    // Find the entity row - always get the fresh row to ensure we have the latest status
    const entityRow = $(`.entity-table tbody tr[data-entity-id="${entityId}"]`);
    if (!entityRow.length) {
        console.error('Entity row not found for ID:', entityId);
        $('#modalLoadingIndicator').hide();
        return;
    }

    // Get the current status directly from the table row
    const currentStatus = entityRow.attr('data-status') || 'in review';

    // Get entity data
    let rawData = entityRow.find('.info-btn').attr('data-entity');
    if (!rawData) {
        console.error('Entity data not found for ID:', entityId);
        $('#modalLoadingIndicator').hide();
        return;
    }

    let entity;
    try {
        entity = JSON.parse(decodeURIComponent(rawData));
        // Update the entity status in the data-entity attribute if it doesn't match the row status
        if (entity.status !== currentStatus) {
            entity.status = currentStatus;
            entityRow.find('.info-btn').attr('data-entity', encodeURIComponent(JSON.stringify(entity)));
        }
    } catch (e) {
        console.error('Error parsing entity data:', e);
        $('#modalLoadingIndicator').hide();
        return;
    }

    let entityData;
    // Process entity data based on type
    if (id_card_for.toLowerCase() === 'staff') {
        let faceImage = entity.picture_url;
        if(faceImage != ''){
            faceImage = await cropFaceFromPhoto(entity.picture_url);
        }
        entityData = {
            id: entity.avatar_id, // This is sm_id for staff
            name: entity.staff_name,
            employee_code: entity.employee_code,
            dob: entity.dob,
            date_of_birth: entity.dob,
            contact: entity.contact,
            blood_group: entity.blood_group,
            emergency_contact: entity.emergency_contact,
            address: entity.address,
            department: entity.department,
            designation: entity.designation,
            phone: entity.contact,
            photo: entity.picture_url,
            type: entity.type,
            status: currentStatus, // Add status to entityData
            relation_type : id_card_for,
            spouse_name:entity.spouse_name,
            staff_type:entity.staff_type,
            qualification : entity.qualification
        };
    } else {
        let parent_name_info = entity.father_name || '';
        let parent_contact_info = (entity.mother_contact || '') + ', ' + (entity.father_contact || '');

        if (entity.point_of_contact != null && entity.point_of_contact !== '') {
            // override like original logic
            parent_name_info = entity.parent_name;
            parent_contact_info = entity.contact;
        } else {
            // fallback to avatar_type logic
            switch (entity.avatar_type) {
                case 'Father':
                    parent_name_info = entity.father_name;
                    parent_contact_info = entity.father_contact;
                    break;
                case 'Mother':
                    parent_name_info = entity.mother_name;
                    parent_contact_info = entity.mother_contact;
                    break;
                case 'Guardian':
                    parent_name_info = entity.guardian_name;
                    parent_contact_info = entity.guardian_contact;
                    break;
                case 'Guardian_2':
                    parent_name_info = entity.guardian_2_name;
                    parent_contact_info = entity.guardian_2_contact;
                    break;
                case 'Driver':
                    parent_name_info = entity.driver_name;
                    parent_contact_info = entity.driver_contact;
                    break;
                case 'Driver_2':
                    parent_name_info = entity.driver_2_name;
                    parent_contact_info = entity.driver_2_contact;
                    break;
                default:
                    // If all else fails
                    parent_name_info = entity.parent_name || entity.father_name;
                    parent_contact_info = entity.contact || ((entity.mother_contact || '') + ', ' + (entity.father_contact || ''));
            }
        }
        entityData = {
            id: entity.sa_id,
            name: entity.name,
            admission_no: entity.admission_no,
            combination: entity.combination,
            alpha_rollnum: entity.alpha_rollnum,
            enrollment_no: entity.enrollment_no,
            grade_section: entity.grade_section,
            grade: entity.grade,
            dob: entity.dob,
            date_of_birth: entity.dob,
            contact: parent_contact_info,
            blood_group: entity.blood_group,
            address: entity.address,
            parent_name: parent_name_info,
            father_name: entity.father_name,
            mother_name: entity.mother_name,
            father_contact: entity.father_contact,
            mother_contact: entity.mother_contact,
            email: entity.email,
            phone: entity.preferred_contact_no,
            photo:  (entity.avatar_type == 'Father') ? entity.father_photo :
                    (entity.avatar_type == 'Mother') ? entity.mother_photo :
                    (entity.avatar_type == 'Guardian') ? entity.guardian_photo :
                    (entity.avatar_type == 'Guardian_2') ? entity.guardian_2_photo :
                    (entity.avatar_type == 'Driver') ? entity.driver_photo :
                    (entity.avatar_type == 'Driver_2') ? entity.driver_2_photo :
                    entity.picture_url,
            type: entity.type,
            father_address : entity.father_address,
            mother_address : entity.mother_address,
            qr_code : entity.qr_code,
            relation_type : id_card_for,
            sibling_name : entity.sibling_name,
            name_class : entity.name_class,
            picking_route : entity.picking_route,
            dropping_route : entity.dropping_route,
            status: currentStatus, // Add status to entityData
            mother_photo : entity.mother_photo,
            father_photo : entity.father_photo,
            class_section : entity.class_section
        };
    }

    // Reset approval UI for new staff details
    resetApprovalUI();

    // Update modal title
    $('#idCardModalTitle').text('ID Card Preview - ' + entityData.name);

    // Render preview (this will show another loading indicator)
    renderPreview(entityData);

    // Set up action buttons
    $('.id-card-approve')
        .data('staff-id', entityData.id)
        .data('type', entityData.relation_type)
        .off('click')
        .on('click', function () {
            const $btn = $(this);
            $btn.prop('disabled', true).html('<i class="fa fa-spinner fa-spin"></i> Please wait...');
            // Only update status badge after approval is confirmed and successful
            approveIdCard({
                button: this,
                onSuccess: function() {
                    $('#entityStatusBadge')
                        .text('Approved')
                        .attr('class', 'status-badge status-approved')
                        .show();
                }
            });
        });
    $('.id-card-modify')
        .off('click')
        .on('click', function() {
            // Only update status badge after modification is confirmed and successful
            modifyIdCard(entityData, function() {
                $('#entityStatusBadge')
                    .text('Modify')
                    .attr('class', 'status-badge status-modify')
                    .show();
            });
        });
    $('.id-card-remove')
        .off('click')
        .on('click', function() {
            // Only update status badge after removal is confirmed and successful
            removeIdCard(entityData, function() {
                $('#entityStatusBadge')
                    .text('Removed')
                    .attr('class', 'status-badge status-removed')
                    .show();
            });
        });

    // Set correct enabled/disabled state for all three buttons
    setModalActionButtonStates(currentStatus);

    // Update navigation buttons state
    if (typeof updateNavigationButtons === 'function') updateNavigationButtons();

    // Hide loading indicator after everything is set up
    $('#modalLoadingIndicator').hide();
};

// Helper to update the entity position indicator in the modal
function updateEntityPositionIndicator() {
    // ...existing code...
    // Fix: Only count unique, visible entity IDs (avoid duplicates)
    if (
        typeof window.currentEntityIndex !== 'undefined' &&
        typeof window.visibleEntityIds !== 'undefined' &&
        Array.isArray(window.visibleEntityIds) &&
        window.visibleEntityIds.length > 0 &&
        window.currentEntityIndex >= 0
    ) {
        // Remove duplicates from visibleEntityIds
        const uniqueIds = [...new Set(window.visibleEntityIds)];
        $('#entityPositionIndicator')
            .text((window.currentEntityIndex + 1) + ' of ' + uniqueIds.length)
            .show();
    } else {
        $('#entityPositionIndicator').hide();
    }
}

// Add this function to update the status badge in the modal header
function updateEntityStatusBadge() {
    if (
        typeof window.currentEntityIndex !== 'undefined' &&
        typeof window.visibleEntityIds !== 'undefined' &&
        Array.isArray(window.visibleEntityIds) &&
        window.visibleEntityIds.length > 0 &&
        window.currentEntityIndex >= 0
    ) {
        const entityId = window.visibleEntityIds[window.currentEntityIndex];
        // Always get the fresh row to ensure we have the latest status
        const $row = $(`.entity-table tbody tr[data-entity-id="${entityId}"]`);
        if ($row.length) {
            const status = $row.attr('data-status') || 'in review';

            // Set badge text and class with the correct styling
            if (status === 'approved') {
                $('#entityStatusBadge')
                    .text('Approved')
                    .attr('class', 'status-badge status-approved')
                    .show();
            } else if (status === 'modify') {
                $('#entityStatusBadge')
                    .text('Modify')
                    .attr('class', 'status-badge status-modify')
                    .show();
            } else if (status === 'removed') {
                $('#entityStatusBadge')
                    .text('Removed')
                    .attr('class', 'status-badge status-removed')
                    .show();
            } else {
                // Default to 'in review'
                $('#entityStatusBadge')
                    .text('In Review')
                    .attr('class', 'status-badge status-in-review')
                    .show();
            }

            // Also update the button states to match the current status
            setModalActionButtonStates(status);

            // Update the info-btn data-entity attribute to ensure it has the latest status
            const $infoBtn = $row.find('.info-btn');
            if ($infoBtn.length) {
                try {
                    let entityData = JSON.parse(decodeURIComponent($infoBtn.attr('data-entity')));
                    if (entityData.status !== status) {
                        entityData.status = status;
                        $infoBtn.attr('data-entity', encodeURIComponent(JSON.stringify(entityData)));
                    }
                } catch (e) {
                    console.error('Error updating info-btn data-entity:', e);
                }
            }
        } else {
            $('#entityStatusBadge').hide();
        }
    } else {
        $('#entityStatusBadge').hide();
    }
}

// Patch navigation and modal open to update the indicator and badge
function updateNavigationButtons() {
    // ...existing code...
    $('#prevStaffBtn').prop('disabled', window.currentEntityIndex <= 0);
    $('#nextStaffBtn').prop('disabled', window.currentEntityIndex >= window.visibleEntityIds.length - 1);
    updateEntityPositionIndicator();
    updateEntityStatusBadge();
}

// Patch window.loadEntityData to update the indicator and badge after loading
// Only patch if not already patched
if (!window._loadEntityDataPatched) {
    const originalLoadEntityData = window.loadEntityData;
    window.loadEntityData = async function(entityId) {
        await originalLoadEntityData.call(this, entityId);

        // Get the current status directly from the table row
        const entityRow = $(`.entity-table tbody tr[data-entity-id="${entityId}"]`);
        if (entityRow.length) {
            const currentStatus = entityRow.attr('data-status') || 'in review';

            // Update the status badge
            updateEntityStatusBadge();

            // Update the button states
            setModalActionButtonStates(currentStatus);
        }

        updateEntityPositionIndicator();
    };
    window._loadEntityDataPatched = true;
}

// Hide indicator and badge when modal is cleared
function clearModalContent() {
    $('#idCardModalTitle').text('ID Card Preview');
    $('#frontCardPreview').empty();
    $('#backCardPreview').empty();
    $('#entityPositionIndicator').hide();
    $('#entityStatusBadge').hide();
    resetApprovalUI();
}

 function generate_qr_code(dataType, qrdata, qr_size, callback){
    $.ajax({
        url: "<?php echo site_url('idcards/Idcards_controller/generate_qr_code_for_idcards') ?>",
        data: {dataType:dataType, qrdata: qrdata, qr_size:qr_size},
        success: function (base64img) {
            callback(base64img);
        }
    });
}

</script>

<script src="https://cdnjs.cloudflare.com/ajax/libs/html2canvas/1.4.1/html2canvas.min.js"></script>
<script src="https://cdn.jsdelivr.net/npm/sweetalert2@11"></script>


<style>
    /* Entity Table Styles */
    .entity-container {
        background-color: #f8f9fa;
        border-radius: 8px;
        padding: 20px;
        margin-bottom: 20px;
    }

    .filter-bar {
        display: flex;
        justify-content: space-between;
        align-items: center;
        margin-bottom: 15px;
    }

    .status-tabs {
        display: flex;
        gap: 5px;
    }

    .status-tab {
        background-color: #6c757d;
        color: white;
        border: none;
        border-radius: 4px;
        padding: 6px 12px;
        font-size: 14px;
        cursor: pointer;
        display: flex;
        align-items: center;
    }

    .status-tab.active {
        background-color: #343a40;
    }

    .status-count {
        background-color: white;
        color: #343a40;
        border-radius: 50%;
        padding: 2px 6px;
        font-size: 12px;
        margin-left: 5px;
    }

    .search-filters {
        display: flex;
        gap: 10px;
    }

    .search-input {
        border-radius: 4px;
        border: 1px solid #ced4da;
        padding: 6px 12px;
        width: 250px;
    }

    .filter-select {
        border-radius: 4px;
        border: 1px solid #ced4da;
        padding: 6px 12px;
        width: 120px;
    }

    .view-toggle {
        background-color: #6c757d;
        color: white;
        border: none;
        border-radius: 4px;
        padding: 6px 12px;
        cursor: pointer;
    }

    .entity-table-container {
        background-color: white;
        border-radius: 4px;
        overflow: hidden;
        height: 350px;
        overflow: scroll;
    }

    .entity-table {
        width: 100%;
        border-collapse: collapse;
    }

    .entity-table th {
        background-color: #f8f9fa;
        padding: 12px 15px;
        text-align: left;
        font-weight: 500;
        color: #495057;
        border-bottom: 1px solid #dee2e6;
    }

    .entity-table td {
        padding: 12px 15px;
        border-bottom: 1px solid #dee2e6;
        color: #212529;
    }

    .entity-table tr:hover {
        background-color: #f8f9fa;
    }

    .actions-cell {
        width: 100px;
        text-align: center;
    }

    .action-btn {
        background: none;
        border: none;
        color: #6c757d;
        cursor: pointer;
        padding: 4px;
        margin: 0 2px;
    }

    .action-btn:hover {
        color: #343a40;
    }

    .entity-count {
        color: #6c757d;
        font-size: 14px;
        margin-top: 10px;
    }

    /* Add styles for the status badge */
    .status-cell {
        text-align: center;
        width: 120px;
    }
    .status-badge {
        display: inline-block;
        padding: 4px 10px;
        border-radius: 20px;
        font-size: 14px;
        font-weight: 500;
        text-transform: capitalize;
        text-align: center;
        min-width: 100px;
        box-shadow: 0 1px 3px rgba(0,0,0,0.1);
    }

    /* In Review */

    .status-in-review {
        border-radius: 4px;
        border: 1px solid #B0DEFF;
        background: #E6F4FF;
        color:#0088E8;
    }

    /* Approved (using Delivered style) */
    .status-approved {
        border-radius: 4px;
        border: 1px solid #C5E9CD;
        background: #ECF8EF;
        color: #3DA755;
    }

    /* Modify (using Order Placed style) */
    .status-modify {
        border-radius: 4px;
        border: 1px solid #FFE5B0;
        background: #FFF7E6;
        color: #E89B00;
    }

    /* Removed (using Cancelled style) */
    .status-removed {
        border-radius: 4px;
        border: 1px solid #FAC5C3;
        background: #FDECEC;
        color:#D93E39;
    }


    /* ID Card Preview Modal Styles */
    .id-card-modal {
        display: none;
        position: fixed;
        top: 0;
        left: 0;
        width: 100%;
        height: 100%;
        background-color: rgba(0, 0, 0, 0.7);
        z-index: 1000;
        overflow: auto;
    }

    .id-card-modal-content {
        background-color: #fff;
        margin: 2% auto;
        padding: 20px;
        border-radius: 8px;
        width: 90%;
        max-width: 900px;
        position: relative;
        box-shadow: 0 4px 8px rgba(0, 0, 0, 0.1);
        overflow-y: auto;
        max-height: 90vh;
        display: flex;
        flex-direction: column;
    }

    .id-card-close {
        position: absolute;
        top: 10px;
        right: 15px;
        font-size: 24px;
        font-weight: bold;
        cursor: pointer;
        z-index: 20; /* Ensure it's above other elements */
        width: 30px;
        height: 30px;
        text-align: center;
        line-height: 30px;
        background-color: #f8f9fa;
        border-radius: 50%;
        border: 1px solid #dee2e6;
        transition: all 0.2s ease;
    }

    .id-card-close:hover {
        background-color: #e9ecef;
        border-color: #ced4da;
        color: #dc3545; /* Red color on hover */
    }

    .id-card-preview-container {
        display: flex;
        justify-content: center;
        gap: 30px;
        margin: 20px 0;
        flex-wrap: wrap;
    }

    .id-card-preview-wrapper {
        display: flex;
        flex-direction: column;
        align-items: center;
        margin-bottom: 20px;
    }
 .status-button {
      display: inline-flex;
      align-items: center;
      background-color: #f3f3f3;
      border: 1px solid #d0d0d0;
      border-radius: 16px;
      padding: 4px 12px;
      font-family: Arial, sans-serif;
      font-size: 14px;
      color: #666;
    }

    .status-button .dot {
      width: 8px;
      height: 8px;
      background-color: #666;
      border-radius: 50%;
      margin-right: 8px;
    }
    .card-preview {
        border: 1px solid #ccc;
        position: relative;
        overflow: hidden;
        margin: 0 auto;
        background: #fff;
        width: 215px;
        height: 335px;
        display: block;
    }

    .card-size-portrait {
        width: 215px;
        height: 335px;
    }

    .card-size-landscape {
        width: 335px;
        height: 215px;
    }

    .element {
        position: absolute;
    }

    .element-content {
        width: 100%;
        height: 100%;
        display: flex;
        align-items: center;
        justify-content: center;
        box-sizing: border-box;
        white-space: nowrap;
        overflow: visible;
    }

    .shape-circle {
        border-radius: 50%;
    }

    img.element-content {
        object-fit: cover;
        max-width: 100%;
        max-height: 100%;
    }

    .shape-container {
        position: relative;
        overflow: hidden;
    }

    .photo-in-shape,
    .photo-in-circle {
        object-fit: fill;
        width: 100%;
        height: 100%;
        /* Default object-position will be set by face detection */
        transition: object-position 0.3s ease;
    }

    .photo-in-circle {
        border-radius: 50%;
    }

    .card-size-portrait {
        width: 215px;
        height: 335px;
    }

    /* .card-size-landscape {
        width: 86mm;
        height: 54mm;
    } */

    .element {
        position: absolute;
    }

    /* This is a duplicate rule that has been updated above */

    .shape-circle {
        border-radius: 50%;
    }

    .shape-rectangle {
        border-radius: 0;
    }

    img.element-content {
        object-fit: cover;
    }

    .shape-container {
        position: relative;
        overflow: hidden;
    }

    .photo-in-shape,
    .photo-in-circle {
        object-fit: fill;
        width: 100%;
        height: 100%;
        /* Default object-position will be set by face detection */
        transition: object-position 0.3s ease;
    }

    .photo-in-circle {
        border-radius: 50%;
    }

    .id-card-actions {
        display: flex;
        justify-content: space-between;
        margin-top: 20px;
        padding: 15px 0;
        border-top: 1px solid #eee;
        width: 100%;
    }

    .id-card-action-btn {
        flex: 1;
        padding: 10px 0;
        border: none;
        cursor: pointer;
        display: flex;
        align-items: center;
        justify-content: center;
        gap: 8px;
        font-weight: 500;
        font-size: 14px;
        letter-spacing: 0.5px;
        transition: all 0.2s ease;
        box-shadow: none;
        margin: 0;
        border-radius: 0;
    }

    .id-card-approve {
        background-color: transparent;
        border-top: 1px solid #495057;
        border-bottom: 1px solid #495057;
        border-left: 1px solid #495057;
        border-right: none;
        color: #495057;
    }

    .id-card-approve:hover {
        background-color: #495057;
        color: white;
    }

    .id-card-modify {
        background-color: transparent;
        border-top: 1px solid #495057;
        border-bottom: 1px solid #495057;
        border-left: none;
        border-right: none;
        color: #495057;
    }

    .id-card-modify:hover {
        background-color: #495057;
        color: white;
    }

    .id-card-remove {
        background-color: transparent;
        border-top: 1px solid #495057;
        border-bottom: 1px solid #495057;
        border-right: 1px solid #495057;
        border-left: none;
        color: #495057;
    }

    .id-card-remove:hover {
        background-color: #495057;
        color: white;
    }

    /* Active state for all buttons */
    .id-card-action-btn:active {
        background-color: #343a40 !important;
        color: white !important;
    }

    /* Disabled state for all buttons */
    .id-card-action-btn:disabled {
        opacity: 0.6;
        cursor: not-allowed;
        background-color: #f8f9fa !important;
        color: #6c757d !important;
        position: relative;
    }

    /* Add a diagonal line through disabled buttons */
    .id-card-action-btn:disabled::after {
        content: '';
        position: absolute;
        top: 0;
        left: 0;
        width: 100%;
        height: 100%;
        background: repeating-linear-gradient(
            45deg,
            rgba(0, 0, 0, 0.05),
            rgba(0, 0, 0, 0.05) 5px,
            transparent 5px,
            transparent 10px
        );
        border-radius: 4px;
    }

    .id-card-photo {
        width: 100%;
        height: 100%;
        object-fit: cover;
    }

    /* Status Badge Styles - Duplicate styles removed to avoid conflicts */
    /* Progress Bar Styles */
    #approval-progress-container {
        margin: 20px 0;
        padding: 15px;
        background-color: #f8f9fa;
        border-radius: 8px;
        border: 1px solid #dee2e6;
    }

    /* Improved Action Button Styles */
    .action-btn {
        width: 40px;          /* Increased button width */
        height: 40px;         /* Increased button height */
        border-radius: 4px;   /* Rounded corners */
        margin: 0 5px;        /* Add spacing between buttons */
        border: 1px solid #ddd;
        background-color: #f8f9fa;
        transition: all 0.3s ease;
        position: relative;   /* For tooltip positioning */
        cursor: pointer;
        display: inline-block; /* Ensure buttons are side by side */
        vertical-align: middle; /* Align buttons vertically */
    }

    /* Button container styling */
    .button-container {
        display: inline-flex;
        gap: 5px;
    }

    .action-btn i {
        font-size: 18px;      /* Larger icons */
        color: #495057;       /* Darker icon color */
    }

    .action-btn:hover {
        background-color: #e9ecef;
        border-color: #ced4da;
    }

    .action-btn:active {
        background-color: #dee2e6;
    }

    /* Edit button specific styling */
    .edit-btn {
        background-color: #e8f4f8;
        border-color: #bee5eb;
    }

    .edit-btn i {
        color: #495057;
    }

    .edit-btn:hover {
        background-color: #d1ecf1;
    }

    /* Info button specific styling */
    .info-btn {
        background-color: transparent;
        border-color: rgba(184, 218, 255, 0.5);
    }

    .info-btn i {
        color: #495057;
    }

    .info-btn:hover {
        background-color: rgba(204, 229, 255, 0.3);
    }

    .info-btn:active {
        background-color: rgba(204, 229, 255, 0.5);
    }

    /* Disabled button styling */
    .action-btn:disabled,
    .action-btn[disabled] {
        background-color: #f2f2f2 !important;
        border-color: #e0e0e0 !important;
        cursor: not-allowed;
        opacity: 0.8;
        position: relative;
    }

    /* Special styling for approved buttons */
    .approved-btn {
        background-color: transparent !important; /* Transparent background to match uploaded image */
        border-color: rgba(200, 230, 201, 0.5) !important;
    }

    .approved-btn i {
        color: #4caf50 !important; /* Green check icon */
    }

    /* Regular disabled button icon styling */
    .action-btn:disabled:not(.approved-btn) i,
    .action-btn[disabled]:not(.approved-btn) i {
        color: #adb5bd !important;
    }

    /* Add a subtle border for disabled buttons */
    .action-btn:disabled::after,
    .action-btn[disabled]::after {
        content: '';
        position: absolute;
        top: 0;
        left: 0;
        width: 100%;
        height: 100%;
        border-radius: 4px;
        box-shadow: inset 0 0 0 2px rgba(0, 0, 0, 0.1);
    }

    /* Actions cell styling */
    .actions-cell {
        min-width: 120px;
        position: relative;
        padding: 10px 5px !important;
        white-space: nowrap;  /* Prevent buttons from wrapping */
        text-align: center;   /* Center the buttons */
    }

    .progress-status {
        margin-bottom: 8px;
        font-weight: 500;
        color: #495057;
    }

    .progress-bar-container {
        height: 20px;
        background-color: #e9ecef;
        border-radius: 4px;
        overflow: hidden;
        margin-bottom: 8px;
    }

    .progress-bar {
        height: 100%;
        background-color: #28a745;
        width: 0%;
        transition: width 0.3s ease;
    }

    .progress-percentage {
        text-align: right;
        font-size: 14px;
        color: #6c757d;
    }

    /* Navigation buttons styles */
    .id-card-navigation {
        position: absolute;
        top: 0;
        left: 0;
        width: 100%;
        height: 100%;
        pointer-events: none; /* Prevents blocking modal content, see below */
        z-index: 10;
    }
    .id-card-navigation h3 {
        display: none; /* Hide in navigation bar, but keep for modal header */
    }
    .id-card-nav-btn {
        position: absolute;
        top: 50%;
        transform: translateY(-50%);
        pointer-events: auto;
        background: #f8f9fa;
        border: 1px solid #dee2e6;
        border-radius: 50%;
        width: 44px;
        height: 44px;
        display: flex;
        align-items: center;
        justify-content: center;
        font-size: 20px;
        color: #495057;
        transition: background 0.2s, border 0.2s;
        box-shadow: 0 2px 6px rgba(0,0,0,0.08);
    }
    .id-card-nav-btn:disabled {
        opacity: 0.5;
        cursor: not-allowed;
    }
    #prevStaffBtn {
        left: 0;
    }
    #nextStaffBtn {
        right: 0;
    }
    .id-card-nav-btn:hover:not(:disabled) {
        background: #e9ecef;
        border-color: #adb5bd;
        color: #007bff;
    }

    /* Modal loading indicator styles */
    #modalLoadingIndicator {
        position: absolute;
        top: 0;
        left: 0;
        width: 100%;
        height: 100%;
        background-color: rgba(255, 255, 255, 0.8);
        z-index: 10;
        display: flex;
        justify-content: center;
        align-items: center;
    }

    .modal-loading-spinner {
        text-align: center;
        padding: 30px;
        background-color: white;
        border-radius: 8px;
        box-shadow: 0 4px 8px rgba(0, 0, 0, 0.1);
    }

    .modal-loading-spinner i {
        color: #007bff;
        margin-bottom: 15px;
    }

    .modal-loading-spinner p {
        margin: 0;
        color: #495057;
        font-weight: 500;
    }

    /* Preview loading indicator styles */
    .preview-loading {
        display: flex;
        flex-direction: column;
        align-items: center;
        justify-content: center;
        height: 100%;
        width: 100%;
        padding: 20px;
        background-color: #f8f9fa;
        border-radius: 4px;
        border: 1px dashed #dee2e6;
    }

    .preview-loading i {
        color: #007bff;
        margin-bottom: 10px;
    }

    .preview-loading p {
        margin: 0;
        color: #6c757d;
        font-size: 14px;
    }

    /* Custom Approval Loading Overlay Styles */
    #customApprovalLoadingOverlay {
        position: fixed;
        z-index: 2000;
        top: 0; left: 0; right: 0; bottom: 0;
        background: rgba(180,180,180,0.7);
        display: flex;
        align-items: center;
        justify-content: center;
    }
    .custom-approval-loading-bg {
        position: absolute;
        top:0; left:0; right:0; bottom:0;
        background: rgba(180,180,180,0.7);
        z-index: 1;
    }
    .custom-approval-loading-content {
        position: relative;
        z-index: 2;
        background: transparent;
        text-align: center;
        padding: 40px 30px 30px 30px;
        border-radius: 12px;
        min-width: 320px;
    }
    .custom-approval-title {
        font-size: 1.5rem;
        font-weight: bold;
        color: #444;
        margin-bottom: 8px;
    }
    .custom-approval-desc {
        color: #555;
        margin-bottom: 24px;
    }
    .custom-approval-progress-img {
        position: relative;
        display: inline-block;
    }
    .custom-approval-progress-text {
        position: absolute;
        top: 50%;
        left: 50%;
        transform: translate(-50%, -50%);
        font-size: 2rem;
        font-weight: bold;
        color: #444;
        pointer-events: none;
    }
    .blurred-bg {
        filter: blur(3px);
        pointer-events: none;
        user-select: none;
    }
    .circular-progress {
        position: relative;
        width: 120px;
        height: 120px;
        display: block;
        margin: auto;
    }
    .circular-progress svg {
        transform: rotate(-90deg);
    }
    .circular-progress .progress-bg {
        fill: none;
        stroke: #e6e6e6;
        stroke-width: 10;
    }
    .circular-progress .progress-bar {
        fill: none;
        stroke: #007bff;
        stroke-width: 10;
        stroke-linecap: round;
        stroke-dasharray: 283;
        stroke-dashoffset: 283;
        transition: stroke-dashoffset 0.4s ease;
    }
    .circular-progress .custom-approval-progress-text {
        position: absolute;
        top: 50%;
        left: 50%;
        transform: translate(-50%, -50%);
        font-size: 2rem;
        font-weight: bold;
        color: #444;
        pointer-events: none;
    }

    /* Modal header title styling (keep visible at top) */
    .card-header h6,
    #idCardModalTitle {
        display: block !important;
        text-align: center;
        font-size: 1.2rem;
        font-weight: 600;
        margin: 0;
        padding: 0;
    }
    /* Special styling for locked action buttons */
    .id-card-action-btn.locked-action:disabled {
        background-color: #f1f1f1 !important;
        color: #888 !important;
        border-color: #ddd !important;
    }

    .id-card-action-btn.locked-action:disabled::after {
        content: '';
        position: absolute;
        top: 0;
        left: 0;
        width: 100%;
        height: 100%;
        background: repeating-linear-gradient(
            45deg,
            rgba(0, 0, 0, 0.03),
            rgba(0, 0, 0, 0.03) 5px,
            transparent 5px,
            transparent 10px
        );
        border-radius: 4px;
    }

    /* Lock icon styling */
    .id-card-action-btn.locked-action:disabled i.fa-lock {
        color: #999 !important;
        font-size: 14px;
    }
    /* Locked Order Banner Styling */
    .locked-order-banner {
        background-color: #fff8e1;
        border: 1px solid #ffe082;
        border-left: 4px solid #ffa000;
        border-radius: 4px;
        padding: 15px;
        margin-bottom: 20px;
        box-shadow: 0 2px 5px rgba(0, 0, 0, 0.05);
        animation: fadeIn 0.5s ease-in-out;
    }

    @keyframes fadeIn {
        from { opacity: 0; transform: translateY(-10px); }
        to { opacity: 1; transform: translateY(0); }
    }

    .locked-order-content {
        display: flex;
        align-items: center;
    }

    .lock-icon {
        background-color: #ffa000;
        color: white;
        width: 40px;
        height: 40px;
        border-radius: 50%;
        display: flex;
        align-items: center;
        justify-content: center;
        margin-right: 15px;
        flex-shrink: 0;
    }

    .lock-icon i {
        font-size: 18px;
    }

    .lock-message {
        flex-grow: 1;
    }

    .lock-message h5 {
        margin: 0 0 5px 0;
        color: #f57c00;
        font-weight: 600;
    }

    .lock-message p {
        margin: 0;
        color: #5d4037;
        font-size: 14px;
    }

    /* Locked action button styling in table */
    .action-btn.locked-action:disabled {
        background-color: #f5f5f5 !important;
        border-color: #e0e0e0 !important;
    }

    .action-btn.locked-action:disabled i.fa-lock {
        color: #999 !important;
    }

    /* Add a tooltip to explain why buttons are locked */
    .action-btn.locked-action:disabled:hover::before {
        content: attr(title);
        position: absolute;
        bottom: 100%;
        left: 50%;
        transform: translateX(-50%);
        background-color: rgba(0, 0, 0, 0.8);
        color: white;
        padding: 5px 10px;
        border-radius: 4px;
        font-size: 12px;
        white-space: nowrap;
        z-index: 1000;
        pointer-events: none;
        margin-bottom: 5px;
    }
    /* Base styling for name and parent name fields */
    .name-field .element-content,
    .parent-name-field .element-content {
        line-height: 1.2 !important; /* Adjust line height for better readability */
        white-space: nowrap !important; /* Allow text to wrap if needed */
        overflow: visible !important;
        text-overflow: ellipsis !important;
       /* Enable hyphenation for very long words */
        text-align: center /* Center text by default (will be overridden for long names) */
    }

    /* Additional styling for name field */
    .name-field .element-content {
        font-weight: 600 !important; /* Make name slightly bolder */
    }

    /* Additional styling for parent name field */
    .parent-name-field .element-content {
        font-style: normal !important; /* Ensure parent name is not italic by default */
    }
</style>
