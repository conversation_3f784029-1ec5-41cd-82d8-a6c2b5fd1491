<?php
    // Data is already decoded by the model - no need to json_decode again!
    // Model decodes: answer_data, hints, attachments, blooms_taxonomy, skills, custom_tags
    $answer_data = $question->answer_data ?: [];
    $hints_data = $question->hints ?: [];
    $attachments_data = $question->attachments ?: [];
    $blooms_data = $question->blooms_taxonomy ?: [];
    $skills_data = $question->skills ?: [];
    $tags_data = $question->custom_tags ?: [];
?>

<ul class="breadcrumb">
    <li><a href="<?php echo site_url('dashboard');?>">Dashboard</a></li>
    <li><a href="<?php echo site_url('academics/academics_menu/index');?>">Academics</a></li>
    <li><a href="<?php echo site_url('academics/question_bank/index');?>">Question Bank</a></li>
    <li>View Question</li>
</ul>

<div class="col-md-12">
    <div class="panel panel-default new-panel-style_3">
        <div class="card-header panel_heading_new_style_staff_border">
            <div class="row" style="margin: 0px;">
                <div class="col-md-8 pl-0">
                    <h3 class="card-title panel_title_new_style_staff">
                        <a class="back_anchor" href="<?php echo site_url('academics/question_bank/index'); ?>">
                            <span class="fa fa-arrow-left"></span>
                        </a>
                        View Question
                    </h3>
                </div>
                <div class="col-md-4 text-right">
                    <span class="badge <?= getStatusBadgeClass($question->status) ?> badge-lg">
                        <?= ucwords(str_replace('_', ' ', $question->status)) ?>
                    </span>
                </div>
            </div>
        </div>

        <div class="card-body pt-0">
            <!-- Action Buttons -->
            <div class="row mb-3 d-none">
                <div class="col-12">
                    <div class="btn-group" role="group">
                        <a href="<?= site_url('academics/question_bank/index') ?>" class="btn btn-secondary">
                            <i class="fa fa-arrow-left"></i> Back to List
                        </a>
                        <a href="<?= site_url('academics/question_bank/edit/' . $question->id) ?>" class="btn btn-primary">
                            <i class="fa fa-edit"></i> Edit Question
                        </a>
                        <a href="<?= site_url('academics/question_bank/duplicate/' . $question->id) ?>" class="btn btn-info">
                            <i class="fa fa-copy"></i> Duplicate
                        </a>
                        <button type="button" class="btn btn-success" onclick="printQuestion()">
                            <i class="fa fa-print"></i> Print
                        </button>
                        <button type="button" class="btn btn-warning" onclick="exportQuestion()">
                            <i class="fa fa-download"></i> Export
                        </button>
                    </div>
                </div>
            </div>

            <div class="row">
                <!-- Question Details -->
                <div class="col-md-8">
                    <div class="card">
                        <div class="card-header">
                            <h3 class="card-title">
                                <i class="fa fa-question-circle"></i> Question Details
                            </h3>
                        </div>
                        <div class="card-body">
                            <!-- Question Text -->
                            <div class="question-content mb-4">
                                <h5 class="text-primary">Question:</h5>
                                <div class="question-text p-3 bg-light rounded">
                                    <?= nl2br(htmlspecialchars($question->question_text)) ?>
                                </div>
                                
                                <?php if (!empty($question->question_image)): ?>
                                <div class="question-image mt-3">
                                    <img src="<?= base_url($question->question_image) ?>" alt="Question Image" class="img-fluid rounded">
                                </div>
                                <?php endif; ?>
                            </div>

                            <!-- Passage for Comprehension -->
                            <?php if ($question->question_type === 'Comprehension' && !empty($question->passage_text)): ?>
                            <div class="passage-content mb-4">
                                <h5 class="text-primary">Passage:</h5>
                                <div class="passage-text p-3 bg-info bg-opacity-10 rounded border-start border-info border-4">
                                    <?= nl2br(htmlspecialchars($question->passage_text)) ?>
                                </div>
                            </div>
                            <?php endif; ?>

                            <!-- Answer Options -->
                            <div class="answer-options mb-4">
                                <h5 class="text-primary">Answer Options:</h5>
                                <?php if ($question->question_type === 'MCQ'): ?>
                                    <?php if (!empty($answer_data['options'])): ?>
                                    <div class="mcq-options">
                                        <?php foreach ($answer_data['options'] as $index => $option): ?>
                                        <div class="option-item mb-2 p-3 rounded <?= isset($option['is_correct']) && $option['is_correct'] ? 'bg-success bg-opacity-10 border border-success' : 'bg-light' ?>">
                                            <div class="d-flex align-items-center">
                                                <span class="option-label me-3 fw-bold"><?= chr(65 + $index) ?>.</span>
                                                <span class="option-text flex-grow-1"><?= htmlspecialchars($option['text']) ?></span>
                                                <?php if (isset($option['is_correct']) && $option['is_correct']): ?>
                                                <span class="badge bg-success ms-2">
                                                    <i class="fa fa-check"></i> Correct
                                                </span>
                                                <?php endif; ?>
                                            </div>
                                        </div>
                                        <?php endforeach; ?>
                                    </div>
                                    <?php else: ?>
                                    <div class="alert alert-warning">
                                        <i class="fa fa-exclamation-triangle"></i>
                                        No options found for this MCQ question.
                                    </div>
                                    <?php endif; ?>

                                <?php elseif ($question->question_type === 'True/False'): ?>
                                    <div class="tf-answer">
                                        <?php
                                        $correct_answer = $answer_data['correct_answer'] ? $answer_data['correct_answer'] : '';
                                        if (!empty($correct_answer)):
                                        ?>
                                        <div class="answer-item p-3 bg-success bg-opacity-10 rounded border border-success">
                                            <strong>Correct Answer:</strong>
                                            <span class="badge bg-success ms-2"><?= htmlspecialchars($correct_answer) ?></span>
                                        </div>
                                        <?php if (!empty($answer_data['explanation'])): ?>
                                        <div class="explanation mt-2 p-3 bg-info bg-opacity-10 rounded">
                                            <strong>Explanation:</strong> <?= nl2br(htmlspecialchars($answer_data['explanation'])) ?>
                                        </div>
                                        <?php endif; ?>
                                        <?php else: ?>
                                        <div class="alert alert-warning">
                                            <i class="fa fa-exclamation-triangle"></i>
                                            No answer found for this True/False question.
                                        </div>
                                        <?php endif; ?>
                                    </div>

                                <?php elseif ($question->question_type === 'Short Answer'): ?>
                                    <div class="short-answers">
                                        <?php
                                        $answers = $answer_data['answers'] ? $answer_data['answers'] : [];
                                        if (!empty($answers)):
                                        ?>
                                        <div class="answers-list">
                                            <?php foreach ($answers as $index => $answer): ?>
                                            <div class="answer-item mb-2 p-3 bg-success bg-opacity-10 rounded border border-success">
                                                <i class="fa fa-check text-success me-2"></i>
                                                <strong>Answer <?= $index + 1 ?>:</strong> <?= htmlspecialchars($answer) ?>
                                            </div>
                                            <?php endforeach; ?>
                                        </div>
                                        <div class="answer-settings mt-2">
                                            <small class="text-muted">
                                                <strong>Case Sensitive:</strong> <?= ($answer_data['case_sensitive'] ? $answer_data['case_sensitive'] : false) ? 'Yes' : 'No' ?> |
                                                <strong>Exact Match:</strong> <?= ($answer_data['exact_match'] ? $answer_data['exact_match'] : false) ? 'Yes' : 'No' ?>
                                            </small>
                                        </div>
                                        <?php else: ?>
                                        <div class="alert alert-warning">
                                            <i class="fa fa-exclamation-triangle"></i>
                                            No answers found for this Short Answer question.
                                        </div>
                                        <?php endif; ?>
                                    </div>

                                <?php elseif ($question->question_type === 'Long Answer'): ?>
                                    <div class="long-answers">
                                        <?php
                                        $sample_answers = $answer_data['sample_answers'] ? $answer_data['sample_answers'] : [];
                                        if (!empty($sample_answers)):
                                        ?>
                                        <div class="answers-list">
                                            <?php foreach ($sample_answers as $index => $answer): ?>
                                            <div class="answer-item mb-3 p-3 bg-info bg-opacity-10 rounded border border-info">
                                                <h6 class="text-primary"><span style="color: white;">Sample Answer <?= $index + 1 ?>:</span></h6>
                                                <p class="mb-0"><?= nl2br(htmlspecialchars($answer)) ?></p>
                                            </div>
                                            <?php endforeach; ?>
                                        </div>
                                        <?php if (!empty($answer_data['rubric'])): ?>
                                        <div class="rubric mt-3 p-3 bg-warning bg-opacity-10 rounded">
                                            <h6 class="text-warning">Rubric:</h6>
                                            <p class="mb-0"><?= nl2br(htmlspecialchars($answer_data['rubric'])) ?></p>
                                        </div>
                                        <?php endif; ?>
                                        <?php if (!empty($answer_data['max_words'])): ?>
                                        <div class="word-limit mt-2">
                                            <small class="text-muted">
                                                <strong>Maximum Words:</strong> <?= $answer_data['max_words'] ?>
                                            </small>
                                        </div>
                                        <?php endif; ?>
                                        <?php else: ?>
                                        <div class="alert alert-info">
                                            <i class="fa fa-info-circle"></i>
                                            This is a Long Answer question. Sample answers may be provided during evaluation.
                                        </div>
                                        <?php endif; ?>
                                    </div>

                                <?php elseif ($question->question_type === 'Fill in the Blanks'): ?>
                                    <div class="fill-blanks">
                                        <?php
                                        $answers = $answer_data['answers'] ? $answer_data['answers'] : [];
                                        if (!empty($answers)):
                                        ?>
                                        <div class="blanks-list">
                                            <?php foreach ($answers as $index => $answer): ?>
                                            <div class="blank-item mb-2 p-3 bg-warning bg-opacity-10 rounded border border-warning">
                                                <strong>Blank <?= $index + 1 ?>:</strong>
                                                <span class="badge bg-warning text-dark ms-2"><?= htmlspecialchars($answer) ?></span>
                                            </div>
                                            <?php endforeach; ?>
                                        </div>
                                        <?php else: ?>
                                        <div class="alert alert-warning">
                                            <i class="fa fa-exclamation-triangle"></i>
                                            No answers found for this Fill in the Blanks question.
                                        </div>
                                        <?php endif; ?>
                                    </div>

                                <?php elseif ($question->question_type === 'Match'): ?>
                                    <?php $matchAnsersData = !empty($question->answer_data['pairs']) ? $question->answer_data['pairs'] : [] ; ?>
                                    <?php if (!empty($matchAnsersData)): ?>
                                    <?php $lefts = array_column($matchAnsersData, 'left'); ?>
                                    <?php $rights = array_column($matchAnsersData, 'right'); ?>
                                    <?php $shuffledRights = $rights; ?>
                                    <?php shuffle($shuffledRights); ?>
                                        <div class="match-pairs">

                                            <!-- Section 1: Question with Shuffled Right -->
                                            <div class="match-pairs-view mb-5">
                                                <h4 class="text-center text-primary mb-4">Match the Following</h4>

                                                <div class="row">
                                                    <!-- Left Column -->
                                                    <div class="col-md-5">
                                                        <h6 class="text-center">Left</h6>
                                                        <?php foreach ($lefts as $left): ?>
                                                            <div class="p-2 border mb-2 rounded bg-light text-center">
                                                                <?= htmlspecialchars($left) ?>
                                                            </div>
                                                        <?php endforeach; ?>
                                                    </div>

                                                    <!-- Arrows -->
                                                    <div class="col-md-2 d-flex flex-column align-items-center justify-content-center">
                                                        <?php foreach ($lefts as $_): ?>
                                                            <div class="my-3 text-secondary fs-4">➡</div>
                                                        <?php endforeach; ?>
                                                    </div>

                                                    <!-- Shuffled Right Column -->
                                                    <div class="col-md-5">
                                                        <h6 class="text-center">Right</h6>
                                                        <?php foreach ($shuffledRights as $right): ?>
                                                            <div class="p-2 border mb-2 rounded bg-light text-center">
                                                                <?= htmlspecialchars($right) ?>
                                                            </div>
                                                        <?php endforeach; ?>
                                                    </div>
                                                </div>
                                            </div>

                                            <!-- Section 2: Correct Matching Answer -->
                                            <div class="correct-matching">
                                                <h4 class="text-center text-success mb-4">Correct Matching</h4>

                                                <div class="row">
                                                    <!-- Left Column -->
                                                    <div class="col-md-5">
                                                        <h6 class="text-center">Left</h6>
                                                        <?php foreach ($matchAnsersData as $pair): ?>
                                                            <div class="p-2 border mb-2 rounded bg-white text-center">
                                                                <?= htmlspecialchars($pair['left']) ?>
                                                            </div>
                                                        <?php endforeach; ?>
                                                    </div>

                                                    <!-- Arrows -->
                                                    <div class="col-md-2 d-flex flex-column align-items-center justify-content-center">
                                                        <?php foreach ($matchAnsersData as $_): ?>
                                                            <div class="my-3 text-secondary fs-4">➡</div>
                                                        <?php endforeach; ?>
                                                    </div>

                                                    <!-- Right Column -->
                                                    <div class="col-md-5">
                                                        <h6 class="text-center">Right</h6>
                                                        <?php foreach ($matchAnsersData as $pair): ?>
                                                            <div class="p-2 border mb-2 rounded bg-white text-center">
                                                                <?= htmlspecialchars($pair['right']) ?>
                                                            </div>
                                                        <?php endforeach; ?>
                                                    </div>
                                                </div>
                                            </div>

                                        </div>
                                    <?php endif; ?>

                                <?php elseif ($question->question_type === 'Comprehension'): ?>
                                    <?php $subQuestions = $question->answer_data['sub_questions']; ?>
                                    <div class="sub-questions">
                                        <?php if (!empty($subQuestions)): ?>
                                            <?php foreach ($subQuestions as $index => $sub_question): ?>
                                                <div class="sub-question-item mb-4 p-3 border rounded">
                                                    <h6 class="text-primary">Sub-Question <?= $index + 1 ?>:</h6>
                                                    <p class="mb-3"><?= nl2br(htmlspecialchars(isset($sub_question['question']) ? $sub_question['question'] : '')) ?></p>

                                                    <div class="sub-question-details">
                                                        <div class="row">
                                                            <div class="col-md-6">
                                                                <small class="text-muted">
                                                                    <strong>Type:</strong> <?= isset($sub_question['type']) ? $sub_question['type'] : '' ?><br>
                                                                    <strong>Marks:</strong> <?= isset($sub_question['marks']) ? $sub_question['marks'] : 0 ?>
                                                                    <?php if (!empty($sub_question['negative_marks'])): ?>
                                                                        <span class="text-danger">(Negative: <?= $sub_question['negative_marks'] ?>)</span>
                                                                    <?php endif; ?>
                                                                </small>
                                                            </div>
                                                        </div>
                                                    </div>

                                                    <?php
                                                        $type = isset($sub_question['type']) ? $sub_question['type'] : '';
                                                        $answer_data = isset($sub_question['answer_data']) ? $sub_question['answer_data'] : [];
                                                        switch ($type) {
                                                            case 'MCQ':
                                                                if (!empty($answer_data['options'])): ?>
                                                                    <div class="mcq-options mt-3">
                                                                        <?php foreach ($answer_data['options'] as $opt_index => $option): ?>
                                                                            <div class="option-item mb-1 p-2 rounded <?= !empty($option['is_correct']) ? 'bg-success bg-opacity-10 border border-success' : 'bg-light' ?>">
                                                                                <span class="option-label me-2"><?= chr(65 + $opt_index) ?>.</span>
                                                                                <span class="option-text"><?= htmlspecialchars($option['text']) ?></span>
                                                                                <?php if (!empty($option['is_correct'])): ?>
                                                                                    <span class="badge bg-success ms-2">✓</span>
                                                                                <?php endif; ?>
                                                                            </div>
                                                                        <?php endforeach; ?>
                                                                    </div>
                                                                <?php else: ?>
                                                                    <div class="alert alert-warning mt-2">No options found for this MCQ sub-question.</div>
                                                                <?php endif;
                                                                break;

                                                            case 'Match':
                                                                if (!empty($answer_data['pairs'])): ?>
                                                                    <?php $lefts = array_column($answer_data['pairs'], 'left');?>
                                                                    <?php $rights = array_column($answer_data['pairs'], 'right');?>
                                                                    <?php $shuffledRights = $rights;?>
                                                                    <?php shuffle($shuffledRights);?>
                                                                    <div class="match-pairs-view mb-4">
                                                                        <h5 class="text-primary text-center">Match the Following</h5>
                                                                        <div class="row">
                                                                            <!-- Left Column -->
                                                                            <div class="col-md-5">
                                                                                <h6 class="text-center">Left</h6>
                                                                                <?php foreach ($lefts as $left): ?>
                                                                                    <div class="p-2 border mb-2 rounded bg-light text-center">
                                                                                        <?= htmlspecialchars($left) ?>
                                                                                    </div>
                                                                                <?php endforeach; ?>
                                                                            </div>

                                                                            <!-- Arrows -->
                                                                            <div class="col-md-2 d-flex flex-column align-items-center justify-content-center">
                                                                                <?php foreach ($lefts as $_): ?>
                                                                                    <div class="my-3 text-secondary fs-4">➡</div>
                                                                                <?php endforeach; ?>
                                                                            </div>

                                                                            <!-- Shuffled Right Column -->
                                                                            <div class="col-md-5">
                                                                                <h6 class="text-center">Right (Shuffled)</h6>
                                                                                <?php foreach ($shuffledRights as $right): ?>
                                                                                    <div class="p-2 border mb-2 rounded bg-light text-center">
                                                                                        <?= htmlspecialchars($right) ?>
                                                                                    </div>
                                                                                <?php endforeach; ?>
                                                                            </div>
                                                                        </div>
                                                                    </div>

                                                                    <!-- Correct Matching -->
                                                                    <div class="correct-matching">
                                                                        <h5 class="text-success text-center">Correct Matching</h5>
                                                                        <div class="row">
                                                                            <!-- Left Column -->
                                                                            <div class="col-md-5">
                                                                                <h6 class="text-center">Left</h6>
                                                                                <?php foreach ($answer_data['pairs'] as $pair): ?>
                                                                                    <div class="p-2 border mb-2 rounded bg-white text-center">
                                                                                        <?= htmlspecialchars($pair['left']) ?>
                                                                                    </div>
                                                                                <?php endforeach; ?>
                                                                            </div>

                                                                            <!-- Arrows -->
                                                                            <div class="col-md-2 d-flex flex-column align-items-center justify-content-center">
                                                                                <?php foreach ($answer_data['pairs'] as $_): ?>
                                                                                    <div class="my-3 text-secondary fs-4">➡</div>
                                                                                <?php endforeach; ?>
                                                                            </div>

                                                                            <!-- Right Column -->
                                                                            <div class="col-md-5">
                                                                                <h6 class="text-center">Right</h6>
                                                                                <?php foreach ($answer_data['pairs'] as $pair): ?>
                                                                                    <div class="p-2 border mb-2 rounded bg-white text-center">
                                                                                        <?= htmlspecialchars($pair['right']) ?>
                                                                                    </div>
                                                                                <?php endforeach; ?>
                                                                            </div>
                                                                        </div>
                                                                    </div>
                                                                <?php else: ?>
                                                                    <div class="alert alert-warning mt-2">No pairs found for this Match sub-question.</div>
                                                                <?php endif;
                                                                break;

                                                            case 'True/False':
                                                                if (!empty($answer_data['correct_answer'])): ?>
                                                                    <div class="option-item mt-3 p-2 bg-light rounded">
                                                                        <i class="fa fa-check text-success me-2"></i>
                                                                        <?= htmlspecialchars($answer_data['correct_answer']) ?>
                                                                    </div>
                                                                <?php else: ?>
                                                                    <div class="alert alert-warning mt-2">No answer found for this True/False sub-question.</div>
                                                                <?php endif;
                                                                break;

                                                            case 'Short Answer':
                                                                $answers = isset($answer_data['answers']) ? $answer_data['answers'] : [];
                                                                if (!empty($answers)): ?>
                                                                    <div class="answers-list mt-3">
                                                                        <?php foreach ($answers as $i => $ans): ?>
                                                                            <div class="answer-item mb-2 p-3 bg-success bg-opacity-10 rounded border border-success">
                                                                                <strong>Answer <?= $i + 1 ?>:</strong> <?= htmlspecialchars($ans) ?>
                                                                            </div>
                                                                        <?php endforeach; ?>
                                                                        <small class="text-muted">
                                                                            <strong>Case Sensitive:</strong> <?= (!empty($answer_data['case_sensitive']) && $answer_data['case_sensitive']) ? 'Yes' : 'No' ?> |
                                                                            <strong>Exact Match:</strong> <?= (!empty($answer_data['exact_match']) && $answer_data['exact_match']) ? 'Yes' : 'No' ?>
                                                                        </small>
                                                                    </div>
                                                                <?php else: ?>
                                                                    <div class="alert alert-warning mt-2">No answers found for this Short Answer sub-question.</div>
                                                                <?php endif;
                                                                break;

                                                            case 'Long Answer':
                                                                $samples = isset($answer_data['sample_answers']) ? $answer_data['sample_answers'] : [];
                                                                if (!empty($samples)): ?>
                                                                    <div class="answers-list mt-3">
                                                                        <?php foreach ($samples as $i => $ans): ?>
                                                                            <div class="answer-item mb-2 p-3 bg-info bg-opacity-10 rounded border border-info">
                                                                                <h6 class="text-primary">Sample Answer <?= $i + 1 ?>:</h6>
                                                                                <p class="mb-0"><?= nl2br(htmlspecialchars($ans)) ?></p>
                                                                            </div>
                                                                        <?php endforeach; ?>
                                                                    </div>
                                                                    <?php if (!empty($answer_data['rubric'])): ?>
                                                                        <div class="rubric mt-2 p-3 bg-warning bg-opacity-10 rounded">
                                                                            <strong>Rubric:</strong> <?= nl2br(htmlspecialchars($answer_data['rubric'])) ?>
                                                                        </div>
                                                                    <?php endif; ?>
                                                                    <?php if (!empty($answer_data['max_words'])): ?>
                                                                        <div class="word-limit mt-2">
                                                                            <small class="text-muted">
                                                                                <strong>Maximum Words:</strong> <?= $answer_data['max_words'] ?>
                                                                            </small>
                                                                        </div>
                                                                    <?php endif; ?>
                                                                <?php else: ?>
                                                                    <div class="alert alert-info mt-2">No sample answers provided for this Long Answer sub-question.</div>
                                                                <?php endif;
                                                                break;

                                                            case 'Fill in the Blanks':
                                                                $blanks = isset($answer_data['answers']) ? $answer_data['answers'] : [];
                                                                if (!empty($blanks)): ?>
                                                                    <div class="blanks-list mt-3">
                                                                        <?php foreach ($blanks as $i => $ans): ?>
                                                                            <div class="blank-item mb-2 p-3 bg-warning bg-opacity-10 rounded border border-warning">
                                                                                <strong>Blank <?= $i + 1 ?>:</strong>
                                                                                <span class="badge bg-warning text-dark ms-2"><?= htmlspecialchars($ans) ?></span>
                                                                            </div>
                                                                        <?php endforeach; ?>
                                                                    </div>
                                                                <?php else: ?>
                                                                    <div class="alert alert-warning mt-2">No blanks found for this sub-question.</div>
                                                                <?php endif;
                                                                break;

                                                            case 'Custom':
                                                                $custom_answer = '';
                                                                if (!empty($answer_data['answer'])) {
                                                                    $custom_answer = $answer_data['answer'];
                                                                } elseif (!empty($answer_data['custom_answer'])) {
                                                                    $custom_answer = $answer_data['custom_answer'];
                                                                }
                                                                if (!empty($custom_answer)): ?>
                                                                    <div class="answer-item mt-3 p-3 bg-secondary bg-opacity-10 rounded border border-secondary">
                                                                        <strong>Custom Answer:</strong>
                                                                        <p class="mt-2 mb-0"><?= nl2br(htmlspecialchars($custom_answer)) ?></p>
                                                                    </div>
                                                                    <?php if (!empty($answer_data['instructions'])): ?>
                                                                        <div class="instructions mt-2 p-3 bg-info bg-opacity-10 rounded">
                                                                            <strong>Instructions:</strong> <?= nl2br(htmlspecialchars($answer_data['instructions'])) ?>
                                                                        </div>
                                                                    <?php endif; ?>
                                                                <?php else: ?>
                                                                    <div class="alert alert-info mt-2">This is a Custom type sub-question. No answer available.</div>
                                                                <?php endif;
                                                                break;

                                                            default: ?>
                                                                <div class="alert alert-secondary mt-2">Unknown sub-question type: <?= htmlspecialchars($type) ?></div>
                                                        <?php } ?>
                                                </div>
                                            <?php endforeach; ?>
                                        <?php endif; ?>
                                    </div>

                                <?php elseif ($question->question_type === 'Custom'): ?>
                                    <div class="custom-answer">
                                        <?php
                                        $custom_answer = isset($answer_data['answer']) ? $answer_data['answer'] : (isset($answer_data['custom_answer']) ? $answer_data['custom_answer'] : '');
                                        if (!empty($custom_answer)):
                                        ?>
                                        <div class="answer-item p-3 bg-secondary bg-opacity-10 rounded border border-secondary">
                                            <strong>Custom Answer:</strong>
                                            <p class="mt-2 mb-0"><?= nl2br(htmlspecialchars($custom_answer)) ?></p>
                                        </div>
                                        <?php if (!empty($answer_data['instructions'])): ?>
                                        <div class="instructions mt-2 p-3 bg-info bg-opacity-10 rounded">
                                            <strong>Instructions:</strong> <?= nl2br(htmlspecialchars($answer_data['instructions'])) ?>
                                        </div>
                                        <?php endif; ?>
                                        <?php else: ?>
                                        <div class="alert alert-info">
                                            <i class="fa fa-info-circle"></i>
                                            This is a Custom type question. Answer details may be provided during evaluation.
                                        </div>
                                        <?php endif; ?>
                                    </div>

                                <?php else: ?>
                                    <div class="other-answer">
                                        <div class="alert alert-info">
                                            <i class="fa fa-info-circle"></i>
                                            This is a <?= $question->question_type ?> type question.
                                            Answer details may be stored in a custom format.
                                        </div>
                                    </div>
                                <?php endif; ?>
                            </div>

                            <!-- Hints -->
                            <?php if (!empty($hints_data)): ?>
                            <div class="hints-section mb-4">
                                <h5 class="text-primary">Hint:</h5>
                                <!-- <?php //foreach ($hints_data as $index => $hint): ?>
                                <div class="hint-item mb-2 p-3 bg-warning bg-opacity-10 rounded border-start border-warning border-4">
                                    <strong>Hint <?php // echo $index + 1 ?>:</strong> <?php // echo nl2br(htmlspecialchars($hint)) ?>
                                </div>
                                <?php //endforeach; ?> -->
                                <?php echo $hints_data ?>
                            </div>
                            <?php endif; ?>

                            <!-- Attachments -->
                            <?php if (!empty($attachments_data)): ?>
                            <div class="attachments-section mb-4">
                                <h5 class="text-primary">Attachments:</h5>
                                <div class="row">
                                    <?php foreach ($attachments_data as $attachment): ?>
                                    <div class="col-md-6 mb-2">
                                        <div class="attachment-item p-2 border rounded">
                                            <i class="fa fa-file me-2"></i>
                                            <a href="<?= base_url($attachment['file_path']) ?>" target="_blank">
                                                <?= htmlspecialchars($attachment['description'] ?: $attachment['file_name']) ?>
                                            </a>
                                            <small class="text-muted d-block">
                                                Type: <?= ucfirst($attachment['file_type']) ?> |
                                                Size: <?= formatFileSize($attachment['file_size']) ?>
                                            </small>
                                        </div>
                                    </div>
                                    <?php endforeach; ?>
                                </div>
                            </div>
                            <?php endif; ?>
                        </div>
                    </div>
                </div>

                <!-- Question Metadata -->
                <div class="col-md-4">
                    <div class="card">
                        <div class="card-header">
                            <h3 class="card-title">
                                <i class="fa fa-info-circle"></i> Question Information
                            </h3>
                        </div>
                        <div class="card-body">
                            <table class="table table-sm">
                                <tr>
                                    <th>Question Type:</th>
                                    <td><span class="badge badge-secondary"><?= $question->question_type ?></span></td>
                                </tr>
                                <tr>
                                    <th>Class:</th>
                                    <td><?= $question->class_name ?></td>
                                </tr>
                                <tr>
                                    <th>Subject:</th>
                                    <td><?= $question->subject_name ?></td>
                                </tr>
                                <?php if ($question->lesson_name): ?>
                                <tr>
                                    <th>Lesson:</th>
                                    <td style="line-break: anywhere;"><?= $question->lesson_name ?></td>
                                </tr>
                                <?php endif; ?>
                                <?php if ($question->topic_name): ?>
                                <tr>
                                    <th>Topic:</th>
                                    <td style="line-break: anywhere;"><?= $question->topic_name ?></td>
                                </tr>
                                <?php endif; ?>
                                <?php if ($question->learning_outcome_name): ?>
                                <tr>
                                    <th>Learning Outcome:</th>
                                    <td><?= $question->learning_outcome_name ?></td>
                                </tr>
                                <?php endif; ?>
                                <?php if ($question->difficulty_level): ?>
                                <tr>
                                    <th>Difficulty:</th>
                                    <td><span class="badge <?= getDifficultyBadgeClass($question->difficulty_level) ?>"><?= $question->difficulty_level ?></span></td>
                                </tr>
                                <?php endif; ?>
                                <tr>
                                    <th>Max Marks:</th>
                                    <td><strong><?= $question->max_marks ?></strong></td>
                                </tr>
                                <?php if ($question->negative_marks > 0): ?>
                                <tr>
                                    <th>Negative Marks:</th>
                                    <td><span class="text-danger"><?= $question->negative_marks ?></span></td>
                                </tr>
                                <?php endif; ?>
                                <tr>
                                    <th>Created By:</th>
                                    <td><?= $question->created_by_name ?></td>
                                </tr>
                                <tr>
                                    <th>Created On:</th>
                                    <td><?= date('M d, Y H:i', strtotime($question->created_at)) ?></td>
                                </tr>
                                <?php if ($question->updated_at !== $question->created_at): ?>
                                <tr>
                                    <th>Last Updated:</th>
                                    <td><?= date('M d, Y H:i', strtotime($question->updated_at)) ?></td>
                                </tr>
                                <?php endif; ?>
                                <?php if ($question->usage_count > 0): ?>
                                <tr>
                                    <th>Usage Count:</th>
                                    <td><span class="badge badge-info"><?= $question->usage_count ?></span></td>
                                </tr>
                                <?php endif; ?>
                                <?php if ($question->average_score): ?>
                                <tr>
                                    <th>Avg. Score:</th>
                                    <td><span class="badge badge-success"><?= number_format($question->average_score, 1) ?>%</span></td>
                                </tr>
                                <?php endif; ?>
                            </table>
                        </div>
                    </div>

                    <!-- Bloom's Taxonomy -->
                    <?php if (!empty($blooms_data)): ?>
                    <div class="card mt-3">
                        <div class="card-header">
                            <h3 class="card-title">
                                <i class="fa fa-brain"></i> Bloom's Taxonomy
                            </h3>
                        </div>
                        <div class="card-body">
                            <?php foreach ($blooms_data as $bloom): ?>
                            <span class="badge badge-primary me-1 mb-1"><?= htmlspecialchars($bloom) ?></span>
                            <?php endforeach; ?>
                        </div>
                    </div>
                    <?php endif; ?>

                    <!-- Skills -->
                    <?php if (!empty($skills_data)): ?>
                    <div class="card mt-3">
                        <div class="card-header">
                            <h3 class="card-title">
                                <i class="fa fa-cogs"></i> Skills
                            </h3>
                        </div>
                        <div class="card-body">
                            <?php foreach ($skills_data as $skill): ?>
                            <span class="badge badge-info me-1 mb-1"><?= htmlspecialchars($skill) ?></span>
                            <?php endforeach; ?>
                        </div>
                    </div>
                    <?php endif; ?>

                    <!-- Reusable Classes -->
                    <?php if (!empty($question->reusable_classes)): ?>
                    <div class="card mt-3">
                        <div class="card-header">
                            <h3 class="card-title">
                                <i class="fa fa-share-alt"></i> Reusable for Classes
                            </h3>
                        </div>
                        <div class="card-body">
                            <?php foreach ($question->reusable_classes as $class): ?>
                            <span class="badge badge-secondary me-1 mb-1"><?= $class->class_name ?></span>
                            <?php endforeach; ?>
                        </div>
                    </div>
                    <?php endif; ?>

                    <!-- Custom Tags -->
                    <?php if (!empty($tags_data)): ?>
                    <div class="card mt-3">
                        <div class="card-header">
                            <h3 class="card-title">
                                <i class="fa fa-tags"></i> Custom Tags
                            </h3>
                        </div>
                        <div class="card-body">
                            <?php foreach ($tags_data as $tag): ?>
                            <span class="badge badge-warning me-1 mb-1"><?= htmlspecialchars($tag) ?></span>
                            <?php endforeach; ?>
                        </div>
                    </div>
                    <?php elseif (!empty($question->tags)): ?>
                    <div class="card mt-3">
                        <div class="card-header">
                            <h3 class="card-title">
                                <i class="fa fa-tags"></i> Custom Tags
                            </h3>
                        </div>
                        <div class="card-body">
                            <?php
                            $tags = is_string($question->tags) ? explode(',', $question->tags) : [];
                            foreach ($tags as $tag):
                                $tag = trim($tag);
                                if (!empty($tag)):
                            ?>
                            <span class="badge badge-warning me-1 mb-1"><?= htmlspecialchars($tag) ?></span>
                            <?php
                                endif;
                            endforeach;
                            ?>
                        </div>
                    </div>
                    <?php endif; ?>
                </div>
            </div>
        </div>
    </div>
</div>

<style>
.question-text {
    font-size: 1.1rem;
    line-height: 1.6;
}

.option-item {
    transition: all 0.3s ease;
}

.option-item:hover {
    transform: translateX(5px);
}

.sub-question-item {
    background-color: #f8f9fa;
}

.hint-item {
    position: relative;
}

.attachment-item {
    transition: all 0.3s ease;
}

.attachment-item:hover {
    background-color: #f8f9fa;
}

@media print {
    .btn-group,
    .card-tools,
    .breadcrumb {
        display: none !important;
    }
    
    .card {
        border: none !important;
        box-shadow: none !important;
    }
}

.match-pairs h6 {
    font-weight: 600;
    margin-bottom: 10px;
}
.match-pairs .border {
    background-color: #f8f9fa;
}
</style>

<script>
function printQuestion() {
    window.print();
}

function exportQuestion() {
    // Implement export functionality
    window.location.href = '<?= site_url('academics/question_bank/export/' . $question->id) ?>';
}

function deleteQuestion() {
    if (confirm('Are you sure you want to delete this question? This action cannot be undone.')) {
        window.location.href = '<?= site_url('academics/question_bank/delete/' . $question->id) ?>';
    }
}
</script>

<?php
// Helper functions
function getStatusBadgeClass($status) {
    $classes = [
        'draft' => 'badge-secondary',
        'pending_review' => 'badge-warning',
        'approved' => 'badge-success',
        'rejected' => 'badge-danger',
        'archived' => 'badge-dark'
    ];
    return $classes[$status] ? $classes[$status] : 'badge-secondary';
}

function getDifficultyBadgeClass($difficulty) {
    $classes = [
        'Easy' => 'badge-success',
        'Medium' => 'badge-warning',
        'Hard' => 'badge-danger'
    ];
    return $classes[$difficulty] ? $classes[$difficulty] : 'badge-secondary';
}

function formatFileSize($bytes) {
    if ($bytes >= **********) {
        return number_format($bytes / **********, 2) . ' GB';
    } elseif ($bytes >= 1048576) {
        return number_format($bytes / 1048576, 2) . ' MB';
    } elseif ($bytes >= 1024) {
        return number_format($bytes / 1024, 2) . ' KB';
    } else {
        return $bytes . ' bytes';
    }
}
?>
