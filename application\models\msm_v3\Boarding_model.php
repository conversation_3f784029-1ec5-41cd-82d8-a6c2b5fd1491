<?php
defined('BASEPATH') OR exit('No direct script access allowed');          
class Boarding_model extends CI_Model {

    private $yearId;
	public function __construct() {
		parent::__construct();
		$this->yearId = $this->acad_year->getAcadYearId();
	}

    public function get_student_boarding_count_data($acad_year) {
		$sql = "select sa.gender, count(sy.boarding) as boarding_count
		FROM student_year sy
		join student_admission sa on sa.id = sy.student_admission_id
		where acad_year_id = '$acad_year'
		and sy.promotion_status!='JOINED'
		and sy.promotion_status!='4'
		and sy.promotion_status!='5'
		and sa.admission_status = '2'
		group by sa.gender";
		$result = $this->db_readonly->query($sql)->result();
		$boarding_type = $this->settings->getSetting('boarding');
		foreach ($result as &$row) {
			$boarding_number = $row->boarding;
			if (isset($boarding_type[$boarding_number])) {
				$row->boarding = $boarding_type[$boarding_number];
			}
		}
		$boarding_label = isset($boarding_type[1]) ? $boarding_type[1] : '';

		$responseData = array(
			'data' => $result,
			'boarding_type' => $boarding_label
		);
		return $responseData;
	}

    public function get_boarding_statistics_data($acad_year) {
		$sql = "select sy.boarding, count(sy.boarding) as boarding_count
		FROM student_year sy
		join student_admission sa on sa.id = sy.student_admission_id
		where acad_year_id = '$acad_year'
		and sy.promotion_status!='JOINED'
		and sy.promotion_status!='4'
		and sy.promotion_status!='5'
		and sa.admission_status = '2'
		group by sy.boarding";
		$result = $this->db_readonly->query($sql)->result();
		$boarding_type = $this->settings->getSetting('boarding');
		foreach ($result as &$row) {
			$boarding_number = $row->boarding;
			if (isset($boarding_type[$boarding_number])) {
				$row->boarding = $boarding_type[$boarding_number];
			}
		}
		return $result;
	}

    public function get_boarding_nationalitywise_data($acad_year){
		$sql = "select sy.boarding, count(sy.boarding) as boarding_count, sa.nationality 
		from student_year sy
		join student_admission sa on sa.id = sy.student_admission_id
		where acad_year_id = '$acad_year'
		and sy.promotion_status!='JOINED'
		and sy.promotion_status!='4'
		and sy.promotion_status!='5'
		and sa.admission_status = '2'
		group by sy.boarding, sa.nationality
		order by count(sy.boarding) desc";

		$result = $this->db_readonly->query($sql)->result();

		$boarding_type = $this->settings->getSetting('boarding');
		foreach ($result as &$row) {
			$boarding_number = $row->boarding;
			if (isset($boarding_type[$boarding_number])) {
				$row->boarding = $boarding_type[$boarding_number];
			}
		}
		return $result;
	}

}