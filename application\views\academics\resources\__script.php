<div class="modal fade" id="resource-uploader" tabindex="-1" role="dialog" aria-labelledby="resource-uploader-label" aria-hidden="true">
  <div class="modal-dialog" role="document">
    <?php if($this->mobile_detect->isMobile()) { ?>
      <div class="modal-content" style="margin: auto;margin-top: 8%;border-radius: .75rem;">
    <?php } else { ?>
      <div class="modal-content" style="width: 52%;margin: auto;margin-top: 8%;border-radius: .75rem;">
    <?php } ?>
      <div class="modal-header" style="border-top-left-radius: .75rem; border-top-right-radius: .75rem;">
        <h4 class="modal-title" id="resource-uploader-label">Add Resources</h4>
        <button type="button" class="close" data-dismiss="modal" aria-label="Close">
          <i class="fa fa-times" aria-hidden="true" style="color: #d80403;font-size: 21px;"></i>
        </button>
      </div>
      <div class="modal-body">
        <form enctype="multipart/form-data" method="post" id="resources-form"  data-parsley-validate="" class="form-horizontal">
          <div class="card-body p-0" id="resource-data"> 
            <div class="col-md-12 pb-3">
              <div class="form-group">
                  <label for="name" class="col-md-3"> Name <font color="red">*</font></label>
                  <div class="col-md-9">
                      <input class="form-control" placeholder="Enter Resource Name" name="resource_name" class="form-control" id="resource_name" type="text" required="" maxlength="750" data-parsley-error-message="Name cannot be empty"/>
                      <span id="resource_name_error" style="color: red;"></span>
                  </div>
              </div>

              <div class="form-group">
                  <label class="col-md-3">Description</label>
                  <div class="col-md-9">
                  <textarea name="resource_description" id="resource_description" class="form-control" placeholder="Enter description"></textarea>
                                          </div>
              </div>

              <div class="form-group" >
                <label class="col-md-3">Class <font color="red">*</font></label>
                <div class="col-md-9">
                  <select id="resource_grade" name="resource_grade" required="" class="form-control input-md" onchange="getSubjectsOfGrade()" data-parsley-error-message="Class cannot be empty">
                    <option value="">Select Class</option>
                      <?php
                        foreach($grades as $cs => $cl){
                          echo "<option value='$cl->id'>$cl->class_name</option>";
                        }
                      ?>
                  </select>    
                  <span id="resource_grade_error" style="color: red;"></span>
                </div>
              </div>

              <?php if ($is_semester_scheme == '1') : ?>
                <div class="form-group" >
                  <label class="col-md-3" for="semester_id_add" >Semester <font color="red">*</font></label>
                  <div class="col-md-9">
                      <select  class="form-control" id="semester_id_add" name="semester_id_add" onchange="getSubjectsOfGrade();"> 
                        <?php foreach($semester_list as $sem){ ?>
                          <option value ="<?= $sem->semester_id ?>"><?= $sem->semester_name ?></option>
                        <?php } ?>
                      </select>
                  </div>
                </div>
              <?php endif ?>

              <div class="form-group" >
                <label class="col-md-3">Subject <font color="red">*</font></label>
                  <div class="col-md-9">
                    <select onchange="document.getElementById('resource_subject_error').innerHTML=''" id="resource_subject" name="resource_subject" required="" class="form-control input-md" data-parsley-error-message="Subject cannot be empty">
                      <option value="">Select Subject</option>
                    </select>    
                    <span id="resource_subject_error" style="color: red;"></span>
                  </div>
              </div>

              <div class="form-group" >
                  <label class="col-md-3" for="resource_type" data-parsley-error-message="Resource type cannot be empty.">Resource Type <font color="red">*</font></label>
                  <div class="col-md-9">
                  <select type="text" data-type="" name="resource_type"  onchange="changeResourceType()" class="form-control input-md" data-parsley-error-message="Cannot be empty." required="" id="resourceType">
                      <option value=''>Select Resource Type</option>
                      <option value='Image'>Image</option>
                      <option value='Video'>Video</option>
                      <option value='Audio'>Audio</option>
                      <option value='PDF'>PDF</option>
                      <option value='PPT'>PPT</option>
                      <option value='Text File'>Text File</option>
                      <option value='Video Link'>Video Link (Only Youtube Link)</option>
                      <option value='Hyper Link'>Hyper Link</option>
                      <option value='Vimeo'>Vimeo Video Id</option>
                      <option value='Other'>Other</option>
                  </select> 
                  <span id="resourceType_error" style="color: red;"></span>   
                  </div>
              </div>
              <div id="upload-block">
                <div class="form-group">
                  <label class="col-md-3">Upload Files <font color="red">*</font></label>
                    <div class="col-md-9">
                        <input type="file" class="form-control verify-size" name="resource_file" id="resource-file">
                        <span class="help-block" id="file-size">Max file size allowed is <?php echo $resource_size ?>.</span>
                        <span id="file-size-error" style="color: red;"></span>
                        <span id="resource-file_error" style="color: red;"></span>
                    </div>

                </div>
                <div class="loader-background">
                  <div style="color:white;text-align:center;height: 100%;">
                    <i style="color:black;font-size: 50px; margin-top: 100px;" class="fa fa-spinner fa-spin"></i>
                  </div>
                </div>
              </div>

              <div id="resource-links"></div>
              <div class="mt-3">
                <center>
                  <button type="button" onclick="saveResource()" style="width: 9rem; border-radius: .45rem;" class="btn btn-primary">Submit</button>     
                  <button type="button" class="btn btn-danger" style="width: 9rem; border-radius: .45rem;" data-dismiss="modal">Close</button>
                </center>
              </div>
            </div>
          </div>
        </form>
      </div>
    </div>
  </div>
</div>


<script src="https://cdn.jsdelivr.net/npm/sweetalert2@11"></script>
<script type="text/javascript">
  var isNameAlphanmeric=true;

  var link_types = ['Video Link', 'Hyper Link'];
  $("#resource_name").change(function(){
      document.getElementById('resource_name_error').innerHTML = ''; 
    if($(this).val() == '') {
      document.getElementById('resource_name_error').innerHTML = 'Name is required'; 
    }
  });

  $('#resource-file').on('click', function (e) {
      var resource_type = $('#resourceType').val();

      if (!resource_type) {
          e.preventDefault(); // 🛑 Stop the file dialog
          $('#resource-file_error').html('Please select a resource type first.');
          return false;
      }

      $('#resource-file_error').html(''); // Clear message if type is selected
  });

  $("#resource-file").change(function(){
    var resource_type = $('#resourceType').val();
    if(link_types.includes(resource_type)) {
      return false;
    }
    var file = document.getElementById('resource-file');
    if(!file.files[0]) {
      document.getElementById('resource-file_error').innerHTML = 'Resource is required'; 
      return false;
    }
    document.getElementById('resource-file_error').innerHTML = '';
    var file_size = parseFloat(file.files[0].size/1024/1024);
    var max_size_string = '<?php echo $resource_size ?>';
    var max_file_size = parseInt(max_size_string.replace('MB',''));
    if(file_size > max_file_size) {
      $("#file-size-error").html('File size exceeded.');
      $("#resource-file").val('');
    } else {
      $("#file-size-error").html('');
    }
  });

  function changeResourceType(){
    $("#file-size-error").html('');
    $("#resource-file").val('');
    document.getElementById('resourceType_error').innerHTML = '';
    var resource_type = $('#resourceType').val();
    var uploadBlock = document.getElementById('upload-block');
    var html = '';
    if(link_types.includes(resource_type)) {
      uploadBlock.style.display = 'none';
      html += '<div class="form-group"><label for="name" class="col-md-3"> Link <font color="red">*</font></label><div class="col-md-9"><input class="form-control" placeholder="Enter '+resource_type+'" name="resource_file" class="form-control" id="resource-link" type="text" required="" maxlength="750" data-parsley-error-message="Field cannot be empty"/><span id="resource-link_error" style="color: red;"></span></div>';
    } else if(resource_type === 'Vimeo') {
      uploadBlock.style.display = 'none';
      html += `<div class="form-group">
                <label for="name" class="col-md-3"> Vimeo Video Id <font color="red">*</font>
                </label>
                <div class="col-md-9">
                  <input class="form-control" placeholder="Enter Vimeo Video Id" name="resource_file" class="form-control" id="vimeo-id" autocomplete="off" type="text" required="" maxlength="750" data-parsley-error-message="Field cannot be empty"/>
                  <span class="help-block">Provide the Vimeo Video ID here. Eg: 6786890008</span>
                  <span id="vimeo-id_error" style="color: red;"></span>
                </div>`;
    } else {
      uploadBlock.style.display = 'block';
    }
    document.getElementById('resource-links').innerHTML = html;
    var acceptMap = {
        'Image': 'image/*',
        'Video': 'video/*',
        'Audio': 'audio/*',
        'PDF': 'application/pdf',
        'PPT': '.ppt, .pptx',
        'Text File': '.txt',
        'Other': '*'
    };
    var acceptValue = acceptMap[resource_type] || '*';
    $('#resource-file').attr('accept', acceptValue);
  }

  function resetForm(){
    document.getElementById('resources-form').reset();
    document.getElementById('resource-links').innerHTML = '';
    document.getElementById('upload-block').style.display = 'block';
    document.getElementById('resource_name_error').innerHTML = '';
    document.getElementById('resource_grade_error').innerHTML = '';
    document.getElementById('resource_subject_error').innerHTML = '';
    document.getElementById('resourceType_error').innerHTML = '';
    if(document.getElementById('resource-link_error')){
      document.getElementById('resource-link_error').innerHTML = '';
    } 
    if(document.getElementById('vimeo-id_error')){
      document.getElementById('vimeo-id_error').innerHTML = '';
    }
    document.getElementById('resource-file_error').innerHTML = '';
    document.getElementById('file-size-error').innerHTML = '';
    $('#resource-file').attr('accept', '*');
  }

  function saveResource(){
    validateCharacters();
    if(!isNameAlphanmeric) return;

    var formData = new FormData();
    var resource_name = document.getElementById('resource_name').value;
    var resource_description = document.getElementById('resource_description').value;
    var resource_grade = document.getElementById('resource_grade').value;
    var resource_subject = document.getElementById('resource_subject').value;
    var resourceType = document.getElementById('resourceType').value;

    var name_error = document.getElementById('resource_name_error');
    var grade_error = document.getElementById('resource_grade_error');
    var subject_error = document.getElementById('resource_subject_error');
    var type_error = document.getElementById('resourceType_error');
    var valid = 1;
    name_error.innerHTML = '';
    grade_error.innerHTML = '';
    subject_error.innerHTML = '';
    type_error.innerHTML = '';
    if(resource_name) {
      formData.append('name', resource_name);
    } else {
      valid = 0;
      name_error.innerHTML = 'Name is required';
    }
    formData.append('description', resource_description);
    if(resource_grade) {
      formData.append('grade', resource_grade);
    } else {
      valid = 0;
      grade_error.innerHTML = 'Grade is required';
    }
    if(resource_subject) {
      formData.append('subject', resource_subject);
    } else {
      valid = 0;
      subject_error.innerHTML = 'Subject is required';
    }
    if(resourceType) {
      formData.append('resource_type', resourceType);
    } else {
      valid = 0;
      type_error.innerHTML = 'Resource type is required';
    }
    if(link_types.includes(resourceType)) {
      var link_error = document.getElementById('resource-link_error');
      link_error.innerHTML = '';
      var resource_link = document.getElementById('resource-link').value;
      resource_link = resource_link.trim();
      if(resource_link) {
        formData.append('resource_link', resource_link);
      } else {
        valid = 0;
        link_error.innerHTML = 'Resource is required';
      }
    } else if(resourceType === 'Vimeo') {
      var link_error = document.getElementById('vimeo-id_error');
      link_error.innerHTML = '';
      var vimeo_id = document.getElementById('vimeo-id').value;
      vimeo_id = vimeo_id.trim();
      if(vimeo_id) {
        formData.append('vimeo_id', vimeo_id);
      } else {
        valid = 0;
        link_error.innerHTML = 'Vimeo video id is required';
      }
    } else {
      var file_error = document.getElementById('resource-file_error');
      var resource_file = document.getElementById('resource-file');
      file_error.innerHTML = '';
      var file = resource_file.files[0];
      if(file) {
        formData.append('resource_file', file);
      } else {
        valid = 0;
        file_error.innerHTML = 'Resource is required';
      }
    }
    if (resource_file != undefined) {
      var file = resource_file.files[0];
    }
    switch (resourceType) {
      case 'Image':
        if (file.type.toUpperCase().indexOf('IMAGE') === -1) {
          alert('Image file-type specified but selected file is of type ' + file.type);
          return 0;
        }
        break;
        case 'PDF':
        if (file.type.toUpperCase().indexOf('PDF') === -1) {
          alert('PDF file-type specified but selected file is of type ' + file.type);
          return 0;
        }
        break;
        case 'Video':
        if (file.type.toUpperCase().indexOf('VIDEO') === -1) {
          alert('Video file-type specified but selected file is of type ' + file.type);
          return 0;
        }
        break;
        case 'PPT':
        if (file.type.toUpperCase().indexOf('POWERPOINT') === -1 && file.type.toUpperCase().indexOf('PRESENTATION') === -1) {
          alert('PPT file-type specified but selected file is of type ' + file.type);
          return 0;
        }
        break;
    }
    if(!valid) return false;
    sendResourceData(formData);
  }

  async function sendResourceData(formData) {
    $("#resource-data .loader-background").show();
    const notAllowedTypes = ['Video Link', 'Hyper Link', 'Vimeo'];
    const resourceType = formData.get('resource_type');
    if (!notAllowedTypes.includes(resourceType)) {
        const file = formData.get('resource_file');
        if (file) {
          try {
            const fileData = await getSignedUrl(file);
            formData.delete('resource_file');
            formData.append('resource_file', fileData.path);
          } catch (err) {
            console.error("Upload Error:", err);
            Swal.fire({
              icon: 'error',
              title: 'Upload Failed',
              text: 'Failed to upload the file. Please try again.',
              timer: 3000,
              showConfirmButton: false
            });
            $("#resource-data .loader-background").hide();
            return;
          }
        }
    }
    $.ajax({
      url: '<?php echo site_url('academics/resources/save_resource_data'); ?>',
      type: 'post',
      data: formData,
      processData: false,
      contentType: false,
      success: function(data) {
        $("#resource-data .loader-background").hide();
        $("#resource-uploader").modal('hide');
        if (parseInt(data.trim())) {
          document.getElementById("resources-form").reset();
          Swal.fire({
            icon: "success",
            title: "Added resource",
            text: "Resources added successfully!",
          });
        } else {
          Swal.fire({
              icon: "error",
              title: "Oops...",
              text: "Something went wrong!",
            });
        }
      }
    });
  }

  function getSignedUrl(file) {
      return new Promise((resolve, reject) => {
          // Step 1: Get signed URL
          $.ajax({
              url: '<?php echo site_url("S3_controller/getSignedUrl"); ?>',
              type: 'POST',
              data: {
                  filename: file.name,
                  file_type: file.type,
                  folder: 'resources'
              },
              success: function (response) {
                  let data = JSON.parse(response)
                  if (!data.signedUrl || !data.path) {
                      return reject('Invalid signed URL response');
                  }

                  $.ajax({
                      url: data.signedUrl,
                      type: 'PUT',
                      headers: {
                          "Content-Type": file.type,
                          'x-amz-acl': 'public-read'
                      },
                      processData: false,
                      data: file,
                      success: function (_, status, xhr) {
                          if (xhr.status == 200 || xhr.status == 201) {
                              resolve({ name: file.name, path: data.path });
                          } else {
                              reject('Unexpected status');
                          }
                      },
                      error: function (xhr, status, err) {
                          console.error('Upload Error:', err);
                          reject(err);
                      }
                  });
              },
              error: function () {
                  reject('Request failed');
              }
          });
      });
  }

  function getSubjectsOfGrade(){
    var grade = $("#resource_grade").val();
    var semester_field = $("#semester_id_add");

    var semester_id_add = 'all';
    if (semester_field.length)
      semester_id_add = semester_field.val();

    if(grade) {
      document.getElementById('resource_grade_error').innerHTML = '';
    }
    $.ajax({
      url: '<?php echo site_url('academics/resources/getSubjects'); ?>',
      data: {'grade': grade, 'semester_id':semester_id_add},
      type: "post",
      success: function (data) {
        var data = $.parseJSON(data);
        var subjectsList = data.subjectsList;
        var subjects_options='<option value="">Select Subject</option>';
        for(var i=0;i<subjectsList.length;i++){
          subjects_options+='<option value="'+subjectsList[i].subject_id+'">'+subjectsList[i].subject_name+'</option>';
        }
        $("#resource_subject").html(subjects_options);
      },
      error: function (err) {
        console.log(err);
      }
    });
  }

  function validateCharacters(){
    // Regex to match the allowed characters
    const name=$("#resource_name").val();
    const regex = /^[a-zA-Z0-9 _-]+$/;
    isNameAlphanmeric=regex.test(name);

    if(isNameAlphanmeric){
      document.getElementById('resource_name_error').innerHTML =""; 
    }else{
      document.getElementById('resource_name_error').innerHTML = 'Only Alphanumeric characters allowed'; 
    }
  }

  $("#resource_name").keydown(validateCharacters);
</script>