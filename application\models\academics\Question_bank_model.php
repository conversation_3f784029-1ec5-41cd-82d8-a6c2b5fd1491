<?php
class Question_bank_model extends CI_Model {

    public function insert_question($data) {
        $this->db->insert('lp_qb_questions', $data);
        return $this->db->insert_id();
    }

    public function getLearningOutcomes(){
        return $this->db->select('id, objective_name, ifnull(objective_description, "") as objective_description')->from('lp_objectives')->get()->result();
    }

    public function getLearningOutcomesByClassSubject($class_id, $subject_master_id = null) {
        $this->db->select('DISTINCT lo.id, lo.objective_name, lo.objective_description');
        $this->db->from('lp_objectives lo');
        $this->db->join('lp_session_objectives lso', 'lso.manage_objective_id = lo.id', 'inner');
        $this->db->join('lp_session ls', 'ls.id = lso.lp_session_id', 'inner');
        $this->db->join('lp_program_weeks_session lpws', 'lpws.lp_session_id = ls.id', 'inner');
        $this->db->where('lpws.class_master_id', $class_id);

        if ($subject_master_id) {
            $this->db->where('lpws.lp_subject_id', $subject_master_id);
        }

        $this->db->order_by('lo.objective_name', 'ASC');
        return $this->db->get()->result();
    }

    public function getClassesForLms(){
        // $is_staff_access_control_enabled=$this->settings->getSetting("enable_staff_class_subject_access_control_for_lms");
        // $is_lms_admin=$this->authorization->isAuthorized('LESSON_PLAN.ADMIN');
        
        // if($is_staff_access_control_enabled==1 && !$is_lms_admin){
        //     $class_section_and_subject_lists=$this->get_class_section_and_subject_ids_for_lms_access_control();
        //     $class_section_ids=$class_section_and_subject_lists["class_section_list"];

        //     return $this->db->select('c.class_name, c.class_master_id, c.id as class_id')
        //     ->from("class c")
        //     ->join("class_section cs","cs.class_id=c.id")
        //     ->where("c.acad_year_id", $this->acad_year->getAcadYearId())
        //     ->where_in("cs.id", $class_section_ids)
        //     ->group_by("c.id")
        //     ->get()->result();
        // }else{
        //     return $this->db->select('c.class_name, c.class_master_id, c.id as class_id')
        //     ->from("class c")
        //     ->join("class_section cs","cs.class_id=c.id")
        //     ->where("c.acad_year_id", $this->acad_year->getAcadYearId())
        //     ->group_by("c.id")
        //     ->get()->result();
        // }

        $classes = $this->db->select('id as class_master_id, class_name')
        ->from('class_master')
        ->get()->result();
        if(!empty($classes))
            return $classes;
        else 
            return [];
    }

    public function get_class_section_and_subject_ids_for_lms_access_control(){
        $class_sections_and_subject_ids=$this->db->select("lp_subjects_id, class_section_id, access_level")
        ->from("lp_subjects_section_staff")
        ->where("staff_id",$this->authorization->getAvatarStakeHolderId())
        ->get()->result();

        $subject_list=[];
        $class_section_list = [];
        foreach($class_sections_and_subject_ids as $key => $val){
        if(!array_key_exists($val->lp_subjects_id,$subject_list)){
            $subject_list[$val->lp_subjects_id]=$val->lp_subjects_id;
        }

        if (!array_key_exists($val->class_section_id, $class_section_list)) {
            $class_section_list[$val->class_section_id] = $val->class_section_id;
        }
        }

        return ["class_section_list"=>$class_section_list,"subject_list"=>$subject_list];
    }

    public function get_subjects_list(){
        $is_staff_access_control_enabled = $this->settings->getSetting("enable_staff_class_subject_access_control_for_lms");
        $is_lms_admin = $this->authorization->isAuthorized('LESSON_PLAN.ADMIN');
        $class_section_and_subject_lists = $this->get_class_section_and_subject_ids_for_lms_access_control();
        $subject_list = $class_section_and_subject_lists["subject_list"];

        $class_master_id = $_POST['class_master_id'];

        $this->db->select('ls.*')
        ->from('lp_subjects ls')
        ->where("ls.acad_year_id",$this->acad_year->getAcadYearId())
        ->where('ls.class_master_id', $class_master_id);
        
        if($is_staff_access_control_enabled==1 && !$is_lms_admin){
            $this->db->where_in("ls.id",$subject_list);
        }

        if(isset($is_semester_scheme) && $is_semester_scheme==1 && $semester_id!=0){
            $this->db->where('ls.semester_id', $semester_id);
        }

        $result = $this->db->get()->result();

        if($result)
            return $result;
        else [];
    }

    public function get_subject_lessons($data) {
        $subjectMasterId = $data['subject_master_id'];
        $classMasterId = $data['class_master_id'];
        // Get only the IDs from the result
        $lpSubjectResults = $this->db->select('id')
            ->from('lp_subjects')
            ->where('class_master_id', $classMasterId)
            ->where('subject_master_id', $subjectMasterId)
            ->get()->result();

        // Extract IDs into a flat array
        if (empty($lpSubjectResults)) {
            return [];
        }
        $lpSubjectIds = array_column($lpSubjectResults, 'id');
        // Get lessons that match the subject IDs
        $lpLessons = $this->db
            ->distinct()
            ->select("*")
            ->from("lp_lessons")
            ->where_in("lp_subject_id", $lpSubjectIds)
            ->get()->result();
        return $lpLessons;
    }

    public function get_lesson_topics(){
        $result = $this->db
        ->distinct()
        ->select('*')
        ->from('lp_sub_topics')
        ->where('lp_lesson_id', $_POST['lessonId'])
        ->get()->result();
        return $result;
    }

    public function insert_attachments($question_id, $files) {
        foreach ($files['name'] as $index => $filename) {
            if ($files['error'][$index] === 0) {
                $tmpPath = $files['tmp_name'][$index];
                $ext = pathinfo($filename, PATHINFO_EXTENSION);
                $path = 'uploads/questions/' . uniqid() . '.' . $ext;
                move_uploaded_file($tmpPath, $path);

                $this->db->insert('question_attachments', [
                    'question_id' => $question_id,
                    'file_path' => $path,
                    'file_type' => in_array($ext, ['pdf']) ? 'pdf' : 'image',
                    'description' => $filename
                ]);
            }
        }
    }

    public function getTeachers() {
        return $this->db->select('id, concat(first_name, " ", ifnull(last_name, "")) as staff_name')
            ->from('staff_master')
            // ->where('is_active', 1)
            ->order_by('first_name', 'ASC')
            ->get()->result();
    }

    public function get_questions_list($page = 1, $per_page = 25, $filters = []) {
        $offset = ($page - 1) * $per_page;

        // Build the base query
        $this->db->select('
            q.id,
            q.uuid,
            q.question_text,
            q.question_type,
            q.difficulty_level,
            q.max_marks,
            q.negative_marks,
            q.status,
            q.created_at,
            q.usage_count,
            q.average_score,
            q.learning_outcome_id,
            c.class_name,
            s.subject_name,
            q.lesson_name,
            q.topic_name,
            CONCAT(st.first_name, " ", st.last_name) as created_by_name,
            CASE WHEN q.answer_data IS NOT NULL THEN JSON_LENGTH(q.answer_data) ELSE 0 END as answer_count,
            CASE WHEN q.attachments IS NOT NULL THEN JSON_LENGTH(q.attachments) ELSE 0 END as attachment_count
        ');

        $this->db->from('lp_qb_questions q');
        $this->db->join('class c', 'c.class_master_id = q.class_master_id', 'left');
        $this->db->join('lp_subjects s', 's.id = q.subject_master_id', 'left');
        // $this->db->join('lp_lessons l', 'l.id = q.lesson_id', 'left');
        // $this->db->join('lp_sub_topics t', 't.id = q.topic_id', 'left');
        $this->db->join('staff_master st', 'st.id = q.created_by', 'left');

        // Apply filters
        $this->apply_question_filters($filters);

        // Get total count for pagination
        $total_query = clone $this->db;
        $total_query->group_by('q.id');
        $total = $total_query->count_all_results('', false);

        // Apply sorting
        $this->apply_question_sorting($filters);

        // Group by question ID to prevent duplicates from JOINs
        $this->db->group_by('q.id');

        // Apply pagination
        $this->db->limit($per_page, $offset);

        $questions = $this->db->get()->result();

        // Process JSON fields and add learning outcome names
        foreach ($questions as $question) {
            $question->learning_outcome_names = [];
            if (!empty($question->learning_outcome_ids)) {
                $outcome_ids = json_decode($question->learning_outcome_ids, true);
                if (is_array($outcome_ids)) {
                    $this->db->select('objective_name');
                    $this->db->from('lp_objectives');
                    $this->db->where_in('id', $outcome_ids);
                    $outcomes = $this->db->get()->result();
                    $question->learning_outcome_names = array_column($outcomes, 'objective_name');
                }
            }
        }

        // Calculate pagination info
        $total_pages = ceil($total / $per_page);

        return [
            'success' => true,
            'questions' => $questions,
            'pagination' => [
                'current_page' => $page,
                'per_page' => $per_page,
                'total' => $total,
                'total_pages' => $total_pages,
                'has_next' => $page < $total_pages,
                'has_prev' => $page > 1
            ]
        ];
    }

    private function apply_question_filters($filters) {
        // Class filter
        if (!empty($filters['class_id'])) {
            $this->db->where('q.class_master_id', $filters['class_id']);
        }

        // Subject filter
        if (!empty($filters['subject_master_id'])) {
            $this->db->where('q.subject_master_id', $filters['subject_master_id']);
        }

        // Question type filter
        if (!empty($filters['question_type'])) {
            $this->db->where('q.question_type', $filters['question_type']);
        }

        // Difficulty level filter
        if (!empty($filters['difficulty_level'])) {
            $this->db->where('q.difficulty_level', $filters['difficulty_level']);
        }

        // Status filter
        if (!empty($filters['status'])) {
            $this->db->where('q.status', $filters['status']);
        }

        // Created by filter
        if (!empty($filters['created_by'])) {
            if ($filters['created_by'] === 'me') {
                $this->db->where('q.created_by', $this->authorization->getAvatarStakeHolderId());
            } else {
                $this->db->where('q.created_by', $filters['created_by']);
            }
        }

        // Date range filter
        if (!empty($filters['date_from'])) {
            $this->db->where('DATE(q.created_at) >=', $filters['date_from']);
        }
        if (!empty($filters['date_to'])) {
            $this->db->where('DATE(q.created_at) <=', $filters['date_to']);
        }

        // Marks range filter
        if (!empty($filters['marks_min'])) {
            $this->db->where('q.max_marks >=', $filters['marks_min']);
        }
        if (!empty($filters['marks_max'])) {
            $this->db->where('q.max_marks <=', $filters['marks_max']);
        }

        // Learning outcome filter
        if (!empty($filters['learning_outcome_id'])) {
            $this->db->where("JSON_CONTAINS(q.learning_outcome_ids, '\"" . $filters['learning_outcome_id'] . "\"')");
        }

        // Session filter
        if (!empty($filters['session_id'])) {
            $this->db->where('q.session_id', $filters['session_id']);
        }

        // Search filter - enhanced to include JSON fields
        if (!empty($filters['search'])) {
            $search = $this->db->escape_like_str($filters['search']);
            $this->db->group_start();
            $this->db->like('q.question_text', $search);
            $this->db->or_like('q.passage_text', $search);
            $this->db->or_like('JSON_EXTRACT(q.custom_tags, "$")', $search);
            $this->db->or_like('JSON_EXTRACT(q.answer_data, "$")', $search);
            $this->db->group_end();
        }

        // Combined filters for advanced searches
        if (!empty($filters['outcome_and_topic']) && !empty($filters['topic_name']) && !empty($filters['learning_outcome_id'])) {
            $this->db->where('q.topic_name', $filters['topic_name']);
            $this->db->where("JSON_CONTAINS(q.learning_outcome_ids, '\"" . $filters['learning_outcome_id'] . "\"')");
        }

        // Only show active questions by default
        $this->db->where('q.is_active', 1);
    }

    private function apply_question_sorting($filters) {
        $sort_by = $filters['sort_by'] ? $filters['sort_by'] : 'created_at_desc';

        switch ($sort_by) {
            case 'created_at_asc':
                $this->db->order_by('q.created_at', 'ASC');
                break;
            case 'question_text_asc':
                $this->db->order_by('q.question_text', 'ASC');
                break;
            case 'question_text_desc':
                $this->db->order_by('q.question_text', 'DESC');
                break;
            case 'difficulty_asc':
                $this->db->order_by('FIELD(q.difficulty_level, "Easy", "Medium", "Hard")', 'ASC');
                break;
            case 'difficulty_desc':
                $this->db->order_by('FIELD(q.difficulty_level, "Hard", "Medium", "Easy")', 'ASC');
                break;
            case 'usage_count_desc':
                $this->db->order_by('q.usage_count', 'DESC');
                break;
            case 'marks_desc':
                $this->db->order_by('q.max_marks', 'DESC');
                break;
            default: // created_at_desc
                $this->db->order_by('q.created_at', 'DESC');
                break;
        }
    }

    public function get_question_details($question_id) {
        $this->db->select('
            q.*,
            c.class_name,
            s.subject_name,
            q.lesson_name,
            q.topic_name,
            concat(st.first_name, " ", ifnull(st.last_name, "")) as created_by_name,
            lo.objective_name as learning_outcome_name
        ');

        $this->db->from('lp_qb_questions q');
        $this->db->join('class c', 'c.class_master_id = q.class_master_id', 'left');
        $this->db->join('lp_subjects s', 's.id = q.subject_master_id', 'left');
        // $this->db->join('lp_lessons l', 'l.id = q.lesson_id', 'left');
        // $this->db->join('lp_sub_topics t', 't.id = q.topic_id', 'left');
        $this->db->join('lp_objectives lo', 'lo.id = q.learning_outcome_id', 'left');
        $this->db->join('staff_master st', 'st.id = q.created_by', 'left');

        $this->db->where('q.id', $question_id);
        $question = $this->db->get()->row();

        if ($question) {
            // Decode JSON fields
            $question->answer_data = json_decode($question->answer_data, true) ?: [];
            $question->attachments = json_decode($question->attachments, true) ?: [];
            $question->blooms_taxonomy = json_decode($question->blooms_taxonomy, true) ?: [];
            $question->skills = json_decode($question->skills, true) ?: [];
            $question->custom_tags = json_decode($question->custom_tags, true) ?: [];

            // Get learning outcome names
            $question->learning_outcome_names = [];
            if (!empty($question->learning_outcome_ids)) {
                $outcome_ids = json_decode($question->learning_outcome_ids, true);
                if (is_array($outcome_ids)) {
                    $this->db->select('id, objective_name, objective_description');
                    $this->db->from('lp_objectives');
                    $this->db->where_in('id', $outcome_ids);
                    $outcomes = $this->db->get()->result();
                    $question->learning_outcomes = $outcomes;
                    $question->learning_outcome_names = array_column($outcomes, 'objective_name');
                }
            }

            // Get session information if available
            if (!empty($question->session_id)) {
                $this->db->select('session_name, session_description');
                $this->db->from('lp_session');
                $this->db->where('id', $question->session_id);
                $session = $this->db->get()->row();
                $question->session_info = $session;
            }
        }

        return $question;
    }

    public function get_question_options($question_id) {
        return $this->db->select('*')
            ->from('question_options')
            ->where('question_id', $question_id)
            ->order_by('option_order', 'ASC')
            ->get()->result();
    }

    public function get_question_hints($question_id) {
        return $this->db->select('*')
            ->from('question_hints')
            ->where('question_id', $question_id)
            ->where('is_active', 1)
            ->order_by('hint_order', 'ASC')
            ->get()->result();
    }

    public function get_question_attachments($question_id) {
        return $this->db->select('*')
            ->from('question_attachments')
            ->where('question_id', $question_id)
            ->where('is_active', 1)
            ->order_by('attachment_order', 'ASC')
            ->get()->result();
    }

    public function get_sub_questions($question_id) {
        $sub_questions = $this->db->select('*')
            ->from('sub_questions')
            ->where('parent_question_id', $question_id)
            ->order_by('sub_question_order', 'ASC')
            ->get()->result();

        foreach ($sub_questions as $sub_question) {
            $sub_question->options = $this->get_sub_question_options($sub_question->id);
        }

        return $sub_questions;
    }

    public function get_sub_question_options($sub_question_id) {
        return $this->db->select('*')
            ->from('sub_question_options')
            ->where('sub_question_id', $sub_question_id)
            ->order_by('option_order', 'ASC')
            ->get()->result();
    }

    public function get_question_blooms($question_id) {
        return $this->db->select('blooms_level')
            ->from('question_blooms')
            ->where('question_id', $question_id)
            ->get()->result_array();
    }

    public function get_question_skills($question_id) {
        return $this->db->select('skill_name')
            ->from('question_skills')
            ->where('question_id', $question_id)
            ->get()->result_array();
    }

    public function get_question_reusable_classes($question_id) {
        return $this->db->select('qrc.class_master_id, c.class_name')
            ->from('question_reusable_classes qrc')
            ->join('class c', 'c.class_master_id = qrc.class_master_id')
            ->where('qrc.question_id', $question_id)
            ->where('qrc.is_active', 1)
            ->get()->result();
    }

    public function delete_question($question_id) {
        try {
            $this->db->trans_start();

            // Soft delete the question
            $this->db->where('id', $question_id);
            $this->db->update('lp_qb_questions', [
                'is_active' => 0,
                'status' => 'archived',
                'updated_at' => date('Y-m-d H:i:s')
            ]);

            $this->db->trans_complete();

            if ($this->db->trans_status() === FALSE) {
                return [
                    'success' => false,
                    'message' => 'Failed to delete question'
                ];
            }

            return [
                'success' => true,
                'message' => 'Question deleted successfully'
            ];
        } catch (Exception $e) {
            return [
                'success' => false,
                'message' => 'Error: ' . $e->getMessage()
            ];
        }
    }

    public function save_question($data) {
        try {
            $this->db->trans_start();

            // Generate UUID and hash code
            $uuid = $this->generate_uuid();
            $hash_code = $this->generate_hash_code($data);

            // Check for duplicates using hash code
            $existing = $this->check_duplicate_question($hash_code);
            if ($existing) {
                throw new Exception('A similar question already exists. Question ID: ' . $existing->id);
            }

            // Prepare answer data based on question type
            $answer_data = $this->prepare_answer_data($data);

            // Prepare other JSON fields
            $attachments = null;
            // $attachments = $this->prepare_attachments_data($_FILES['attachments'] ? $_FILES['attachments'] : []);
            $blooms = $data['blooms'] ? $data['blooms'] : [];
            $skills = $data['skills'] ? $data['skills'] : [];
            $tags = !empty($data['tags']) ? (is_array($data['tags']) ? $data['tags'] : explode(',', $data['tags'])) : [];

            // Extract search keywords
            $search_keywords = $this->extract_search_keywords($data);

            $question_data = [
                'uuid' => $uuid,
                'hash_code' => $hash_code,
                'question_text' => $data['question_text'],
                'question_type' => $data['question_type'],
                'question_image' => $data['question_image'] ? $data['question_image'] : null,
                'passage_text' => $data['passage_text'] ? $data['passage_text'] : null,
                'class_master_id' => $data['class_master_id'],
                'subject_master_id' => $data['subject_master_id'],
                'lesson_name' => $data['lesson_name'] ? $data['lesson_name'] : null,
                'topic_name' => $data['topic_name'] ? $data['topic_name'] : null,
                'learning_outcome_id' => $data['learning_outcome_id'] ? $data['learning_outcome_id'] : null,
                'difficulty_level' => $data['difficulty_level'] ? $data['difficulty_level'] : null,
                'max_marks' => $data['max_marks'] ? $data['max_marks'] : 1,
                'negative_marks' => $data['negative_marks'] ? $data['negative_marks'] : 0,
                'answer_data' => $this->safe_json_encode($answer_data),
                // 'hints' => !empty($hints) ? $this->safe_json_encode($hints) : null,
                'hints' => !empty($data['hints']) ? $data['hints'] : null,
                'attachments' => !empty($attachments) ? $this->safe_json_encode($attachments) : null,
                'blooms_taxonomy' => !empty($blooms) ? $this->safe_json_encode($blooms) : null,
                'skills' => !empty($skills) ? $this->safe_json_encode($skills) : null,
                'custom_tags' => !empty($tags) ? $this->safe_json_encode($tags) : null,
                // 'reusable_classes' => !empty($reusable_classes) ? $this->safe_json_encode($reusable_classes) : null,
                'status' => $data['status'] ? $data['status'] : 'draft',
                'search_keywords' => $search_keywords,
                'created_by' => $this->authorization->getAvatarStakeHolderId(),
                'created_at' => date('Y-m-d H:i:s'),
                'updated_at' => date('Y-m-d H:i:s')
            ];
            
            // Insert main question
            $this->db->insert('lp_qb_questions', $question_data);
            $question_id = $this->db->insert_id();

            $this->db->trans_complete();

            if ($this->db->trans_status() === FALSE) {
                throw new Exception('Transaction failed');
            }

            return $question_id;
        } catch (Exception $e) {
            $this->db->trans_rollback();
            throw $e;
        }
    }

    private function save_question_type_data($question_id, $data) {
        switch ($data['question_type']) {
            case 'MCQ':
                if (!empty($data['mcq_data'])) {
                    $mcq_data = json_decode($data['mcq_data'], true);
                    $this->save_mcq_options($question_id, $mcq_data);
                }
                break;

            case 'Comprehension':
                if (!empty($data['comprehension_data'])) {
                    $comprehension_data = json_decode($data['comprehension_data'], true);
                    $this->save_comprehension_data($question_id, $comprehension_data);
                }
                break;

            case 'Match':
                if (!empty($data['match_data'])) {
                    $match_data = json_decode($data['match_data'], true);
                    $this->save_match_data($question_id, $match_data);
                }
                break;

            case 'Fill in the Blanks':
                if (!empty($data['fill_blanks_data'])) {
                    $fill_blanks_data = json_decode($data['fill_blanks_data'], true);
                    $this->save_fill_blanks_data($question_id, $fill_blanks_data);
                }
                break;

            case 'True/False':
                if (!empty($data['tf_answer'])) {
                    $this->save_tf_answer($question_id, $data['tf_answer']);
                }
                break;

            case 'Short Answer':
                if (!empty($data['short_answers'])) {
                    $this->save_short_answers($question_id, $data['short_answers']);
                }
                break;

            case 'Long Answer':
                if (!empty($data['long_answers'])) {
                    $this->save_long_answers($question_id, $data['long_answers']);
                }
                break;

            case 'Custom':
                if (!empty($data['custom_answer'])) {
                    $this->save_custom_answer($question_id, $data['custom_answer']);
                }
                break;
        }
    }

    private function save_mcq_options($question_id, $mcq_data) {
        foreach ($mcq_data['options'] as $index => $option) {
            $option_data = [
                'question_id' => $question_id,
                'option_type' => 'mcq',
                'option_text' => $option['text'],
                'is_correct' => $option['is_correct'] ? 1 : 0,
                'option_order' => $index + 1
            ];
            $this->db->insert('question_options', $option_data);
        }
    }

    private function save_question_blooms($question_id, $blooms) {
        foreach ($blooms as $bloom) {
            $this->db->insert('question_blooms', [
                'question_id' => $question_id,
                'blooms_level' => $bloom
            ]);
        }
    }

    private function save_question_skills($question_id, $skills) {
        foreach ($skills as $skill) {
            $this->db->insert('question_skills', [
                'question_id' => $question_id,
                'skill_name' => $skill
            ]);
        }
    }

    private function save_question_reusable_classes($question_id, $classes) {
        foreach ($classes as $class_id) {
            $this->db->insert('question_reusable_classes', [
                'question_id' => $question_id,
                'class_master_id' => $class_id,
                'is_active' => 1
            ]);
        }
    }

    // Helper methods for JSON-based single table approach

    private function generate_uuid() {
        return sprintf('%04x%04x-%04x-%04x-%04x-%04x%04x%04x',
            mt_rand(0, 0xffff), mt_rand(0, 0xffff),
            mt_rand(0, 0xffff),
            mt_rand(0, 0x0fff) | 0x4000,
            mt_rand(0, 0x3fff) | 0x8000,
            mt_rand(0, 0xffff), mt_rand(0, 0xffff), mt_rand(0, 0xffff)
        );
    }

    private function generate_hash_code($data) {
        // Create a hash based on question content for duplicate detection
        $hash_content = [
            'question_text' => trim(strtolower($data['question_text'])),
            'question_type' => $data['question_type'],
            'class_master_id' => $data['class_master_id'],
            'subject_master_id' => $data['subject_master_id']
        ];

        // Add answer-specific content to hash
        if (!empty($data['mcq_data'])) {
            $mcq_data = is_string($data['mcq_data']) ? $this->safe_json_decode($data['mcq_data']) : $data['mcq_data'];
            $hash_content['mcq_options'] = $mcq_data['options'] ? $mcq_data['options'] : [];
        }

        if (!empty($data['tf_answer'])) {
            $hash_content['tf_answer'] = $data['tf_answer'];
        }

        if (!empty($data['short_answers'])) {
            $hash_content['short_answers'] = $data['short_answers'];
        }

        // Use JSON_SORT_KEYS if available, otherwise use 0
        $json_flags = defined('JSON_SORT_KEYS') ? JSON_SORT_KEYS : 0;
        return hash('sha256', $this->safe_json_encode($hash_content, $json_flags));
    }

    private function check_duplicate_question($hash_code) {
        return $this->db->select('id, question_text')
            ->from('lp_qb_questions')
            ->where('hash_code', $hash_code)
            ->where('is_active', 1)
            ->get()->row();
    }

    private function prepare_answer_data($data) {
        $answer_data = ['type' => $data['question_type']];

        // Debug logging
        log_message('debug', 'Preparing answer data for question type: ' . $data['question_type']);
        log_message('debug', 'Available data keys: ' . implode(', ', array_keys($data)));

        switch ($data['question_type']) {
            case 'MCQ':
                log_message('debug', 'Processing MCQ data. mcq_data present: ' . (!empty($data['mcq_data']) ? 'Yes' : 'No'));
                if (!empty($data['mcq_data'])) {
                    log_message('debug', 'MCQ data content: ' . $data['mcq_data']);
                    $mcq_data = is_string($data['mcq_data']) ? $this->safe_json_decode($data['mcq_data']) : $data['mcq_data'];
                    log_message('debug', 'Decoded MCQ data: ' . print_r($mcq_data, true));
                    $answer_data = array_merge($answer_data, $mcq_data);
                } else {
                    log_message('debug', 'No MCQ data found, using fallback structure');
                    // Fallback to empty structure
                    $answer_data['options'] = [];
                    $answer_data['correct_answers'] = [];
                    $answer_data['allow_multiple'] = false;
                    $answer_data['randomize_options'] = true;
                }
                break;

            case 'True/False':
                if (!empty($data['tf_data'])) {
                    $tf_data = is_string($data['tf_data']) ? $this->safe_json_decode($data['tf_data']) : $data['tf_data'];
                    $answer_data = array_merge($answer_data, $tf_data);
                } else {
                    // Fallback to legacy field
                    $answer_data['correct_answer'] = $data['tf_answer'] ? $data['tf_answer'] : '';
                    $answer_data['explanation'] = $data['tf_explanation'] ? $data['tf_explanation'] : '';
                }
                break;

            case 'Short Answer':
                if (!empty($data['short_answer_data'])) {
                    $short_data = is_string($data['short_answer_data']) ? $this->safe_json_decode($data['short_answer_data']) : $data['short_answer_data'];
                    $answer_data = array_merge($answer_data, $short_data);
                } else {
                    // Fallback to legacy fields
                    $answer_data['answers'] = $data['short_answers'] ? $data['short_answers'] : [];
                    $answer_data['case_sensitive'] = $data['case_sensitive'] ? $data['case_sensitive'] : false;
                    $answer_data['exact_match'] = false;
                }
                break;

            case 'Fill in the Blanks':
                log_message('debug', 'Processing Fill in the Blanks data. fill_blanks_data present: ' . (!empty($data['fill_blanks_data']) ? 'Yes' : 'No'));
                if (!empty($data['fill_blanks_data'])) {
                    log_message('debug', 'Fill blanks data content: ' . $data['fill_blanks_data']);
                    $fill_data = is_string($data['fill_blanks_data']) ? $this->safe_json_decode($data['fill_blanks_data']) : $data['fill_blanks_data'];
                    log_message('debug', 'Decoded fill blanks data: ' . print_r($fill_data, true));
                    $answer_data = array_merge($answer_data, $fill_data);
                } else {
                    log_message('debug', 'No fill blanks data found, using fallback structure');
                    // Fallback to empty structure
                    $answer_data['answers'] = [];
                    $answer_data['blanks'] = [];
                }
                break;

            case 'Match':
                log_message('debug', 'Processing Match data. match_data present: ' . (!empty($data['match_data']) ? 'Yes' : 'No'));
                if (!empty($data['match_data'])) {
                    log_message('debug', 'Match data content: ' . $data['match_data']);
                    $match_data = is_string($data['match_data']) ? $this->safe_json_decode($data['match_data']) : $data['match_data'];
                    log_message('debug', 'Decoded match data: ' . print_r($match_data, true));
                    $answer_data = array_merge($answer_data, $match_data);
                } else {
                    log_message('debug', 'No match data found, using fallback structure');
                    // Fallback to empty structure
                    $answer_data['pairs'] = [];
                    $answer_data['randomize'] = true;
                }
                break;

            case 'Comprehension':
                if (!empty($data['comprehension_data'])) {
                    $comp_data = is_string($data['comprehension_data']) ? $this->safe_json_decode($data['comprehension_data']) : $data['comprehension_data'];
                    $answer_data = array_merge($answer_data, $comp_data);
                } else {
                    // Fallback to empty structure
                    $answer_data['sub_questions'] = [];
                    $answer_data['passage'] = $data['passage_text'] ? $data['passage_text'] : '';
                }
                break;

            case 'Long Answer':
                if (!empty($data['long_answer_data'])) {
                    $long_data = is_string($data['long_answer_data']) ? $this->safe_json_decode($data['long_answer_data']) : $data['long_answer_data'];
                    $answer_data = array_merge($answer_data, $long_data);
                } else {
                    // Fallback to legacy fields
                    $answer_data['sample_answers'] = $data['long_answers'] ? $data['long_answers'] : [];
                    $answer_data['rubric'] = $data['evaluation_criteria'] ? $data['evaluation_criteria'] :  '';
                    $answer_data['max_words'] = null;
                }
                break;

            case 'Custom':
                if (!empty($data['custom_answer_data'])) {
                    $custom_data = is_string($data['custom_answer_data']) ? $this->safe_json_decode($data['custom_answer_data']) : $data['custom_answer_data'];
                    $answer_data = array_merge($answer_data, $custom_data);
                } else {
                    // Fallback to legacy fields
                    $answer_data['answer'] = $data['custom_answer'] ? $data['custom_answer'] : '';
                    $answer_data['instructions'] = '';
                    $answer_data['evaluation_method'] = $data['evaluation_method'] ? $data['evaluation_method'] : 'manual';
                }
                break;
        }

        log_message('debug', 'Final answer_data prepared: ' . print_r($answer_data, true));
        return $answer_data;
    }

    private function prepare_attachments_data($files) {
        $attachments = [];
        if (!empty($files['name'])) {
            foreach ($files['name'] as $index => $filename) {
                if ($files['error'][$index] === 0) {
                    $tmpPath = $files['tmp_name'][$index];
                    $ext = pathinfo($filename, PATHINFO_EXTENSION);
                    $path = 'uploads/questions/' . uniqid() . '.' . $ext;

                    if (move_uploaded_file($tmpPath, $path)) {
                        $attachments[] = [
                            'id' => $index + 1,
                            'file_name' => $filename,
                            'file_path' => $path,
                            'file_type' => $this->get_file_type($ext),
                            'file_size' => filesize($path),
                            'mime_type' => mime_content_type($path),
                            'description' => $filename,
                            'order' => $index + 1,
                            'is_active' => true,
                            'uploaded_at' => date('Y-m-d H:i:s')
                        ];
                    }
                }
            }
        }
        return $attachments;
    }

    private function get_file_type($extension) {
        $image_exts = ['jpg', 'jpeg', 'png', 'gif', 'bmp', 'svg'];
        $video_exts = ['mp4', 'avi', 'mov', 'wmv', 'flv'];
        $audio_exts = ['mp3', 'wav', 'ogg', 'aac'];
        $doc_exts = ['doc', 'docx', 'txt', 'rtf'];

        $ext = strtolower($extension);

        if (in_array($ext, $image_exts)) return 'image';
        if (in_array($ext, $video_exts)) return 'video';
        if (in_array($ext, $audio_exts)) return 'audio';
        if (in_array($ext, $doc_exts)) return 'document';
        if ($ext === 'pdf') return 'pdf';

        return 'document';
    }

    private function extract_search_keywords($data) {
        $keywords = [];

        // Extract from question text
        $question_words = str_word_count(strtolower($data['question_text']), 1);
        $keywords = array_merge($keywords, $question_words);

        // Extract from passage text
        if (!empty($data['passage_text'])) {
            $passage_words = str_word_count(strtolower($data['passage_text']), 1);
            $keywords = array_merge($keywords, $passage_words);
        }

        // Add custom tags
        if (!empty($data['tags'])) {
            $tags = is_array($data['tags']) ? $data['tags'] : explode(',', $data['tags']);
            $keywords = array_merge($keywords, array_map('strtolower', $tags));
        }

        // Remove common stop words and short words
        $stop_words = ['the', 'a', 'an', 'and', 'or', 'but', 'in', 'on', 'at', 'to', 'for', 'of', 'with', 'by', 'is', 'are', 'was', 'were', 'be', 'been', 'have', 'has', 'had', 'do', 'does', 'did', 'will', 'would', 'could', 'should'];
        $keywords = array_filter($keywords, function($word) use ($stop_words) {
            return strlen($word) > 2 && !in_array($word, $stop_words);
        });

        // Remove duplicates and return as space-separated string
        return implode(' ', array_unique($keywords));
    }

    // Method to check for duplicates during question creation (AJAX endpoint)
    public function check_question_duplicate() {
        $question_text = $this->input->post('question_text');
        $question_type = $this->input->post('question_type');
        $class_master_id = $this->input->post('class_master_id');
        $subject_master_id = $this->input->post('subject_master_id');

        if (empty($question_text)) {
            return ['exists' => false];
        }

        $data = [
            'question_text' => $question_text,
            'question_type' => $question_type,
            'class_master_id' => $class_master_id,
            'subject_master_id' => $subject_master_id
        ];

        $hash_code = $this->generate_hash_code($data);
        $existing = $this->check_duplicate_question($hash_code);

        if ($existing) {
            return [
                'exists' => true,
                'question_id' => $existing->id,
                'question_text' => $existing->question_text,
                'similarity' => 'exact'
            ];
        }

        return ['exists' => false];
    }

    // Helper method for safe JSON encoding
    private function safe_json_encode($data, $flags = 0) {
        $json = json_encode($data, $flags);
        if (json_last_error() !== JSON_ERROR_NONE) {
            // Log the error and return a safe fallback
            log_message('error', 'JSON encoding error: ' . json_last_error_msg());
            return json_encode(['error' => 'JSON encoding failed']);
        }
        return $json;
    }

    // Helper method for safe JSON decoding
    private function safe_json_decode($json, $assoc = true) {
        if (empty($json)) {
            return $assoc ? [] : null;
        }

        $data = json_decode($json, $assoc);
        if (json_last_error() !== JSON_ERROR_NONE) {
            // Log the error and return a safe fallback
            log_message('error', 'JSON decoding error: ' . json_last_error_msg());
            return $assoc ? [] : null;
        }
        return $data;
    }
}