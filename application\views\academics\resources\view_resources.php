<ul class="breadcrumb">
    <li><a href="<?php echo base_url('avatars');?>">Dashboard</a></li>
    <li><a href="<?php echo base_url('academics/academics_menu');?>">Academics</a></li>
    <li class="active">Resources</li>
</ul>

<div class="col-md-12 col_new_padding">
  <div class="card cd_border">
    <div class="card-header panel_heading_new_style_staff_border">
      <div class="row" style="margin: 0px">
        <div class="col-md-9 pl-0">
          <h3 class="card-title panel_title_new_style_staff">
            <a class="back_anchor" href="<?php echo site_url('academics/academics_menu') ?>" class="control-primary">
              <span class="fa fa-arrow-left"></span>
            </a> 
            Add & View Resources
          </h3>
        </div>
        <div class="col-md-3 pr-0">
          <ul class="panel-controls">
            <li>
              <a href="" class="new_circleShape_res" style="background-color: #fe970a;" data-toggle="modal" data-target="#resource-uploader" onclick="resetForm()">
                <span class="fa fa-plus" style="font-size: 19px;"></span>
              </a>
            </li>
          </ul>
        </div>
      </div>
    </div>
    <div class="card-body pt-1">
      <div class="row">
        <div class="col-md-2">
          <label>Created Date</label>
          <div id="reportrange" class="dtrange" style="width: 100%">
              <span></span>
              <input type="hidden" id="from_date">
              <input type="hidden" id="to_date">
          </div>
        </div>

        <div class="col-md-2">
          <label for="gradeView">Class</label>
            <select  class="form-control" id="gradeView" onchange="getSubjects(); getStaff();"  name="grade"> 
              <option value=''>Select Class</option>
              <?php
                foreach($grades as $cs => $cl){ ?>
                    <option value ="<?= $cl->id ?>"><?= $cl->class_name ?></option>
                <?php } 
              ?>
            </select>
        </div>

        <?php if ($is_semester_scheme == '1') : ?>
          <div class="col-md-2">
            <label for="semester_id">Semester</label>
              <select  class="form-control" id="semester_id" name="semester_id" onchange="getSubjects();"> 
                <option value="all">All</option>
                <?php foreach($semester_list as $sem){ ?>
                      <option value ="<?= $sem->semester_id?>"><?= $sem->semester_name ?></option>
                  <?php } ?>
              </select>
          </div>
        <?php endif ?>

        <div class="col-md-2">
          <label for="subjectView">Subjects</label>
            <select  class="form-control" id="subjectView" name="subject" > 
              <option value="all">All</option>
            </select>
        </div>

        <div class="col-md-2">
          <label for="created_by">Created By</label>
            <select  class="form-control" id="created_by" name="created_by"> 
              <option value='all'>All</option>
            </select>
        </div>

        <div class="col-md-2">
          <label for="resourceView">Resource Types</label>
            <select  class="form-control" id="resourceView" name="resource_type" > 
              <option value='all'>All</option>
              <?php foreach($resourceTypes as $row){ ?>
                  <option value ="<?= $row->resource_type?>"><?php echo $row->resource_type ?></option>
              <?php } ?>
            </select>
        </div>
        <div class="col-md-2">
          <label for="resource_status">Status</label>
            <select  class="form-control" id="resource_status" name="resource_status" > 
              <option value='Active'>Active</option>
              <option value='Inactive'>Inactive</option>
              <option value='all'>All</option>
            </select>
        </div>
      </div>

      <div class="row mt-3">
        <div class="col-md-2 d-flex align-items-end">
          <button type="submit" id="get" class="btn btn-primary" style="border-radius: 1.2rem;width: 220px;" onclick="getResources()" >Get Resources</button>
        </div>
      </div>
      <div class="mt-3" id="displayResourcePanel">
      </div>      
    </div>
</div>

<div id="video-data" data-backdrop="static" data-keyboard="false" class="modal fade" role="dialog">
    <div class="modal-dialog" role="document">
      <div class="modal-content" style="border-radius: 8px; margin-top: 2% !important; margin: auto; width: 70%;">
        <div class="modal-header">
            <h4 class="modal-title" id="modalHeader">Video</h4>
            <button style="font-size: 32px;font-weight: bold;color: #e04b4a;opacity: 1;padding-top: .5rem;" type="button" class="close" onclick="pauseVideo()" data-dismiss="modal">&times;</button>
        </div>
        <div class="modal-body">
            <div class="col-12 col-md-12" id="uploaded">
            </div>
        </div>
      </div>
    </div>
</div>

<div id="youtube-data" data-backdrop="static" data-keyboard="false" class="modal fade" role="dialog">
    <div class="modal-dialog" role="document">
      <div class="modal-content" style="border-radius: 8px; margin-top: 2% !important; margin: auto; width: 70%;">
        <div class="modal-header">
            <h4 class="modal-title" id="modalHeader">Video</h4>
            <button style="font-size: 32px;font-weight: bold;color: #e04b4a;opacity: 1;padding-top: .5rem;" type="button" class="close" onclick="pauseYouTubeVideo()" data-dismiss="modal">&times;</button>
        </div>
        <div class="modal-body">
            <div class="col-12 col-md-12" id="uploadedYouTube">
              <div class="video-wrapper">
                <iframe id="resourceVideo" frameborder="0" allowfullscreen></iframe>
              </div>
            </div>
        </div>
      </div>
    </div>
</div>

<div id="audio-data" data-backdrop="static" data-keyboard="false" class="modal fade" role="dialog">
    <div class="modal-dialog" role="document">
      <div class="modal-content" style="border-radius: 8px; margin-top: 2% !important; margin: auto; width: 70%;">
        <div class="modal-header">
            <h4 class="modal-title" id="modalHeader">Audio</h4>
            <button style="font-size: 32px;font-weight: bold;color: #e04b4a;opacity: 1;padding-top: .5rem;" type="button" class="close" onclick="pauseAudio()" data-dismiss="modal">&times;</button>
        </div>
        <div class="modal-body">
            <div class="col-12 col-md-12" id="audio1">
            </div>
        </div>
      </div>
    </div>
</div>

<div class="modal fade" id="exampleModal" tabindex="-1" role="dialog" aria-labelledby="exampleModalLabel" aria-hidden="true">
  <div class="modal-dialog" role="document">
    <div class="modal-content" style="width: 52%;margin: auto;margin-top: 8%;border-radius: .75rem;">
      <div class="modal-header" style="border-top-left-radius: .75rem; border-top-right-radius: .75rem;">
        <h4 class="modal-title" id="exampleModalLabel">Add Resources</h4>
        <button type="button" class="close" data-dismiss="modal" aria-label="Close">
          <i class="fa fa-times" aria-hidden="true" style="color: #d80403;font-size: 21px;"></i>
        </button>
      </div>
      <div class="modal-body">
        <form enctype="multipart/form-data" method="post" id="FileSaver"  data-parsley-validate="" class="form-horizontal">
          <div class="card-body" id="recording-data"> 
            <div class="col-md-12 pb-3">
              <div class="form-group">
                  <label for="name" class="col-md-2"> Name<font color="red">*</font></label>
                  <div class="col-md-10">
                      <input class="form-control" placeholder="Enter Resource Name" name="name" class="form-control" id="name1" type="text" required="" maxlength="750" data-parsley-error-message="Name cannot be empty"/>
                  </div>
              </div>

              <div class="form-group">
                  <label class="col-md-2">Description</label>
                  <div class="col-md-10">
                  <textarea name="description" id="description" class="form-control"></textarea>
                  </div>
              </div>

              <div class="form-group" >
                <label class="col-md-2">Class<font color="red">*</font></label>
                <div class="col-md-10">
                  <select id="grade" name="grade" required="" class="form-control input-md" onchange="getSubjectsToAdd()" data-parsley-error-message="Class cannot be empty">
                    <option value="">Select Class</option>
                      <?php
                        foreach($grades as $cs => $cl){
                          echo "<option value='$cl->class_name'>$cl->class_name</option>";
                        }
                      ?>
                  </select>    
                </div>
              </div>

              <div class="form-group" >
                <label class="col-md-2">Subject<font color="red">*</font></label>
                <div class="col-md-10">
                  <select id="subject" name="subject" required="" class="form-control input-md" data-parsley-error-message="Subject cannot be empty">
                    <option value="">Select Subject</option>
                  </select>    
                </div>
              </div>

              <div class="form-group" >
                  <label class="col-md-2" for="resource_type" data-parsley-error-message="Resource type cannot be empty.">Resource Type<font color="red">*</font></label>
                  <div class="col-md-10">
                  <select type="text" data-type="" name="resource_type"  onchange="showFields()" class="form-control input-md" data-parsley-error-message="Cannot be empty." required="" id="resource_type1">
                      <option value=''>Select Resource Type</option>
                      <option value='Image'>Image</option>
                      <option value='Video'>Video</option>
                      <option value='Audio'>Audio</option>
                      <option value='PDF'>PDF</option>
                      <option value='PPT'>PPT</option>
                      <option value='Text File'>Text File</option>
                      <option value='Video Link'>Video Link (Only Youtube Link)</option>
                      <option value='Hyper Link'>Hyper Link</option>
                      <option value='Other'>Other</option>
                  </select>    
                  </div>
              </div>
              <div id="upload">

              <div class="form-group">
                <label class="col-md-2">Upload Files</label>
                  <div class="col-md-10 d-flex"> 
                    <div id="uploader"></div>                    
                      <input id="fileName" type="text" class="form-control" readonly>
                      <label class="input-group-btn" style="width: 17%;">
                          <span class="btn btn-primary" style="width: 7rem; margin-left: 1rem; border-radius: 0.45rem;">
                              <input type="file" name="selectFiles" id="selectFiles" style="display: none;" data-parsley-id="32">
                              Browse                          
                          </span>
                      </label>
                      <span id="fileuploadError" style="color: red;"></span>
                  </div>
                  <span style="margin-left:130px;" id="file-size">Max file size allowed is <?php echo $resource_size ?>.</span>

                </div>
                  <div class="loader-background">
                  <div style="color:white;text-align:center;height: 100%;">
                      <i style="color:black;font-size: 50px; margin-top: 100px;" class="fa fa-spinner fa-spin"></i>
                      <br>
                      <span id="percent-span" style="color:black;font-size: 25px; margin-top: 100px;">0</span><span style="color:black;font-size: 25px; margin-top: 100px;">&nbsp;of 100%</span>
                      <br>
                      <button id="cancel-btn" class="btn btn-sm btn-danger">Cancel</button>

                      <br>
                  </div>
              </div>
              <input type="hidden" name="location" id="location">
              <center>
                <button type="button" id="start-upload" style="width: 9rem; border-radius: .45rem;" class="btn btn-primary" disabled>Submit</button>     
                <button type="button" class="btn btn-danger" style="width: 9rem; border-radius: .45rem;" data-dismiss="modal">Close</button>
              </center>
              </div>

              <div id="append_field">
                
              </div>


            </div>
          </div>
        </form>
      </div>
    </div>
  </div>
</div>




<style type="text/css">
  .new_circleShape_res {
    padding: 8px;
    border-radius: 50% !important;
    color: white !important;
    font-size: 22px;
    height: 3.2rem !important;
    width: 3.2rem !important;
    text-align: center;
    vertical-align: middle;
    border: none !important;
    box-shadow: 0px 3px 7px #ccc;
    line-height: 1.7rem !important;
  }
  .new_circleShape_res1 {
    padding: 5px 8px;
    border-radius: 50% !important;
    font-size: 16px;
    height: 3.2rem !important;
    width: 3.2rem !important;
    text-align: center;
    vertical-align: middle;
    border: none !important;
    box-shadow: 0px 3px 7px #ccc;
    line-height: 0rem !important;
  }
  .loader-background {
    width: 100%;
    height: 100%;            
    position: absolute;
    display: none;
    top: 0;
    left: 0;
    opacity: 0.8;
    z-index: 10;
    background-color: #fff;
    border-radius: 8px;
  }
  
  .editable, .select-editable, .select-editable1 {
    cursor: pointer;
    position: relative;
    margin-left: 5px;
  }
  .editable:hover, .select-editable:hover, .select-editable1:hover {
    font-weight: 700;
  }
  .editable::before, .select-editable::before, .select-editable1::before {
    font-family: "FontAwesome";
    content: "\f040";
    display: inline-block;
    padding-right: 5px;
    vertical-align: middle;
    font-weight: 900;
    position: absolute;
    right: 5px;
  }

  #video-player{
    object-fit: cover;
    width: 100%;
    height: 500px;
}

.video-wrapper {
  position: relative;
  padding-bottom: 56.25%; /* 16:9 aspect ratio */
  height: 0;
  overflow: hidden;
  max-width: 100%;
}

.video-wrapper iframe {
  position: absolute;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
}
</style>

<?php $this->load->view('academics/resources/__script.php'); ?>
<script type="text/javascript" src="<?php echo base_url();?>assets/js/plugins/jeditor/editable.js"></script>
<script>
var is_resource_admin = 0;
$(document).ready(function(){
  is_resource_admin = <?php echo $is_resource_admin; ?>;
});

function enableButton(){
  $("#get").prop('disabled', false);
}

function showFields(){
  var resource_type = $('#resource_type1').val();
  var resource_size = '<?php echo $resource_size?>';
  var html = '';
  if(resource_type=='Video Link'){
    $('#upload').hide();
    
    html += `<div class="form-group">
                <label for="name" class="col-md-2"> Link<font color="red">*</font></label>
                  <div class="col-md-10">
                      <input class="form-control" placeholder="Enter Video Link" name="resource_file" class="form-control" id="resource_file" type="text" required="" maxlength="750" data-parsley-error-message="Field cannot be empty"/>
                  </div>
                </div>
                  <center>
                    <button type="button" id="submit_link" onclick="submitResource()" style="width: 9rem; border-radius: .45rem;" class="btn btn-primary">Submit</button>     
                    <button type="button" class="btn btn-danger" style="width: 9rem; border-radius: .45rem;" data-dismiss="modal">Close</button>
                  </center>`;

  }else if(resource_type=='Hyper Link'){

    $('#upload').hide();

    html += `<div class="form-group">
              <label for="name" class="col-md-2"> Link<font color="red">*</font></label>
                <div class="col-md-10">
                    <input class="form-control" placeholder="Enter Hyper Link" name="resource_file" class="form-control" id="resource_file" type="text" required="" maxlength="750" data-parsley-error-message="Field cannot be empty"/>
                </div>
              </div>
                <center>
                  <button type="button" id="submit_link" onclick="submitResource()" style="width: 9rem; border-radius: .45rem;" class="btn btn-primary">Submit</button>
                  <button type="button" class="btn btn-danger" style="width: 9rem; border-radius: .45rem;" data-dismiss="modal">Close</button>
                </center>`;
  }else{
    $('#upload').show();

  }
  $('#append_field').html(html);
}
     
function submitResource(){
  var $form = $('#FileSaver');
      if ($form.parsley().validate()){
        $("#exampleModal").modal('hide');
          var form = $('#FileSaver')[0];
          var formData = new FormData(form);
          // console.log(formData);
          $.ajax({
                url: '<?php echo site_url('academics/resources/submit_resource'); ?>',
                type: 'post',
                data: formData,
                processData: false,
                contentType: false,
                success: function(data) {
					if(data){
						$(function(){
				          new PNotify({
				              title: 'Success',
				              text: 'Resources added successfully',
				              type: 'success',
				          });
                });
                
				        $("#name1").val('');
				        $("#grade").val('');
				        $("#subject").val('');
				        $("#resource_type1").val('');
				        $("#resource_file").val('');
                $("#description").val('');
                $("#submit_link").hide();

                
                
					}
					else{
						$(function(){
				          new PNotify({
				              title: 'Warning',
				              text: 'Something Went Wrong',
				              type: 'warning',
				          });
				        });
					}
                }
            });
		}
}

function getSubjects(){
  var grade = $("#gradeView").val();
  var semester_field = $("#semester_id");

  var semester_id = 'all';
  if (semester_field.length)
    semester_id = semester_field.val();

  $.ajax({
    url: '<?php echo site_url('academics/resources/getSubjects'); ?>',
    data: {'grade': grade, 'semester_id':semester_id},
    type: "post",
    success: function (data) {
      var data = $.parseJSON(data);
      var subjectsList = data.subjectsList;
      // var subjects_options='<option value="">Select Subject</option>';
      var subjects_options = '<option value="all">All</option>';
      for(var i=0;i<subjectsList.length;i++){
        subjects_options+='<option value="'+subjectsList[i].subject_id+'">'+subjectsList[i].subject_name+'</option>';
      }
      $("#subjectView").html(subjects_options);
    },
    error: function (err) {
      console.log(err);
    }
  });
}

function getStaff(){
  var grade = $("#gradeView").val();
  $.ajax({
    url: '<?php echo site_url('academics/resources/getStaff'); ?>',
    data: {'grade': grade},
    type: "post",
    success: function (data) {
      var data = $.parseJSON(data);
      var staff = data.staff;
      // var staff_options='<option value="">Select Created By</option>';
      staff_options = '<option value="all">All</option>';
      for(var i=0;i<staff.length;i++){
        staff_options+='<option value="'+staff[i].created_by+'">'+staff[i].friendly_name+'</option>';
      }
      $("#created_by").html(staff_options);
    },
    error: function (err) {
      console.log(err);
    }
  });
}

function getSubjectsToAdd(){
  var grade = $("#grade").val();
  $.ajax({
    url: '<?php echo site_url('academics/resources/getSubjects'); ?>',
    data: {'grade': grade},
    type: "post",
    success: function (data) {
      var data = $.parseJSON(data);
      var subjectsList = data.subjectsList;
      var subjects_options='<option value="">Select Subject</option>';
      for(var i=0;i<subjectsList.length;i++){
        subjects_options+='<option value="'+subjectsList[i].subject_id+'">'+subjectsList[i].subject_name+'</option>';
      }
      $("#subject").html(subjects_options);
    },
    error: function (err) {
      console.log(err);
    }
  });
}
  

  function showRecording(resource_id) {
		$.ajax({
            url: '<?php echo site_url('academics/resources/getResourceToPlay'); ?>',
            type: 'post',
            data: {'resource_id':resource_id},
            beforeSend: function() {
            	$('#opacity').css('opacity','0.5');
		        $('#loader').show();
		    },
        success: function(data) {
          var data = $.parseJSON(data);
				  var resource = data.resource;
				  if(resource != '' && resource != undefined && resource != null) {
            var video = '<video id="video-player" controls controlsList="nodownload">';
            video += '<source src="'+resource+'" type="video/mpeg">';
            video += '<source src="'+resource+'" type="video/mp4">';
            video += 'Your browser does not support the video tag.';
            video += '</video>';
            $("#uploaded").html(video);
				  }
        },
        complete: function() {
          $('#loader').hide();
          $('#opacity').css('opacity','');
		    }
      });
	  	$("#video-data").modal('show');
  }

  function showYouTubeVideo(resource_id) {
		$.ajax({
        url: '<?php echo site_url('academics/resources/getYouTubeVideo'); ?>',
        type: 'post',
        data: {'resource_id':resource_id},
        beforeSend: function() {
          $('#opacity').css('opacity','0.5');
          $('#loader').show();
        },
        success: function(data) {
          var data = $.parseJSON(data);
				  var resource = data.resource;
          var path = resource.trim();
				  if(path != '' && path != undefined && path != null) {
              var video_id = getVideoId(path);
              var embed_path = "https://www.youtube.com/embed/"+video_id;
              document.getElementById('resourceVideo').src= embed_path;
              
				  }
        },
        complete: function() {
          $('#loader').hide();
          $('#opacity').css('opacity','');
		    }
      });
	  	$("#youtube-data").modal('show');
  }

function getVideoId(url) {
    const regExp = /^.*(youtu.be\/|v\/|u\/\w\/|embed\/|watch\?v=|&v=)([^#&?]*).*/;
    const match = url?.match(regExp);

    return (match && match[2].length === 11)
      ? match[2]
      : null;
}
  
  function showAudio(resource_id) {
		$.ajax({
        url: '<?php echo site_url('academics/resources/getResourceToPlay'); ?>',
        type: 'post',
        data: {'resource_id':resource_id},
        beforeSend: function() {
          $('#opacity').css('opacity','0.5');
          $('#loader').show();
		    },
        success: function(data) {
          var data = $.parseJSON(data);
				  var resource = data.resource;
				  if(resource != '' && resource != undefined && resource != null) {
            var audio = '<audio id="audio-player" controls controlsList="nodownload">';
            audio += '<source src="'+resource+'" type="audio/ogg">';
            audio += '<source src="'+resource+'" type="audio/mpeg">';
            audio += '<source src="'+resource+'" type="audio/mp3">';
            audio += 'Your browser does not support the audio tag.';
            audio += '</audio>';
            $("#audio1").html(audio);
          }
        },
        complete: function() {
        $('#loader').hide();
        $('#opacity').css('opacity','');
		    }
      });
		  $("#audio-data").modal('show');

  }
  
 
  function pauseVideo() {
    var vid = document.getElementById("video-player");
    if(vid != null || vid != undefined)
      vid.pause();
    $("#video-data").modal('hide');
  }

  function pauseYouTubeVideo(){
   $('#resourceVideo').attr('src', '');
  }

  function pauseAudio() {
    var audio = document.getElementById("audio-player");
    if(audio != null || audio != undefined)
      audio.pause();
    $("#audio-data").modal('hide');
  }

  

  function fillSubjects(){
    var grade = $("#grade option:selected").val();
      $.ajax({
        url: '<?php echo site_url('academics/resources/getSubjectsByGrade'); ?>',
        data: {'grade': grade},
        type: "post",
        success: function (data) {
          data = $.parseJSON(data);
          subjectData = data.subjectData;

          var output='';
          output += '<option value="">Select Subject</option>';

          output += '<option value="all">All</option>';
          for (var i=0;i<subjectData.length; i++) {
              output += '<option   value="' + subjectData[i].subject + '">' + subjectData[i].subject +  '</option>';
          };
          $('#subject').html(output);
        },
        error: function (err) {
          console.log(err);
        }
    });
  };

  function getData(){
    var grade = $('#gradeView').val();
    var subject = $('#subjectView').val();
    var resourceType = $('#resourceView').val();
    var created_by = $('#created_by').val();

    if(subject == 'all' && resourceType == 'all' && created_by == 'all'){
      viewAllResourcesByGrade(grade);
    }
    else if(subject == 'all' && resourceType == 'all'){
      viewResourcesByGradeCreatedBy(grade, created_by);
    }else if(subject == 'all' && created_by == 'all'){
      viewResourcesByGradeResourceType(grade, resourceType);
    }
    else if(resourceType == 'all' && created_by == 'all'){
      viewResourcesByGradeSubject(grade, subject);
    }
    else if(subject == 'all'){
      viewResourcesByGradeResourceTypeCreatedBy(grade, resourceType, created_by);
    }else if(resourceType == 'all'){
      viewResourcesByGradeSubjectCreatedBy(grade, subject, created_by);
    }else if(created_by == 'all'){
      viewResourcesByGradeSubjectResourceType(grade, subject, resourceType);
    }else{
      viewResourcesByGradeSubjectTypeCreatedBy(grade, subject, created_by, resourceType);
    }
  }

  function getResources() {
    var grade = $('#gradeView').val();
    if(!grade) {
      Swal.fire({
        icon: 'error',
        title: 'Oops...',
        text: 'Please select Class!',
      });
      return false;
    };

    $("#get").text("Please wait...").prop("disabled", true);


    let loading = `<div class="no-data-display">Loading...</div>`;
    $('#displayResourcePanel').html(loading);
    if(grade == '') { 
      return false;
    }
    var subject = $('#subjectView').val();
    var resourceType = $('#resourceView').val();
    var created_by = $('#created_by').val();
    var status = $('#resource_status').val();
    var from_date = $('#from_date').val();
    var to_date = $('#to_date').val();
    $.ajax({
      url: '<?php echo site_url('academics/resources/getResources') ?>',
      type: 'post',
      data: {
        'from_date': from_date,
        'to_date': to_date,
        'grade': grade,
        'subject': subject,
        'created_by': created_by,
        'resource_type': resourceType,
        'resource_status': status
      },
      success: function(data) {
        $("#get").text("Get Resources").prop("disabled", false);

        data = $.parseJSON(data); 
        viewResources = data.viewResources;
        base_url = data.base_url;
        $('#displayResourcePanel').html(makeResourceTable(viewResources, base_url));
        after_call(viewResources);
      },
      error: function (err) {
        console.log(err);
      }
    });
  }

  function viewAllResourcesByGrade(grade) {
    $.ajax({
      url: '<?php echo site_url('academics/resources/viewAllResourcesByGrade') ?>',
      type: 'post',
      data: {
        'grade': grade,
      },
      success: function(data) {
        data = $.parseJSON(data); 
        viewResources = data.viewResources;
        base_url = data.base_url;
        $('#displayResourcePanel').html(makeResourceTable(viewResources, base_url));
        after_call(viewResources);

      },
      error: function (err) {
              console.log(err);
            }
    });
  }

  function viewResourcesByGradeCreatedBy(grade, created_by){
    $.ajax({
      url: '<?php echo site_url('academics/resources/viewResourcesByGradeCreatedBy') ?>',
      type: 'post',
      data: {
        'grade': grade,
        'created_by': created_by
      },
      success: function(data) {
        data = $.parseJSON(data);        
        viewResources = data.viewResources;
        base_url = data.base_url;
        $('#displayResourcePanel').html(makeResourceTable(viewResources, base_url));
        after_call(viewResources);

      },
      error: function (err) {
        console.log(err);
      }
    });
  }

  function viewResourcesByGradeResourceType(grade, resourceType){
    $.ajax({
      url: '<?php echo site_url('academics/resources/viewResourcesByGradeResourceType') ?>',
      type: 'post',
      data: {
        'grade': grade,
        'resourceType': resourceType
      },
      success: function(data) {
        data = $.parseJSON(data);        
        viewResources = data.viewResources;
        base_url = data.base_url;
        $('#displayResourcePanel').html(makeResourceTable(viewResources, base_url));
        after_call(viewResources);

      },
      error: function (err) {
        console.log(err);
      }
    });
  }

  function viewResourcesByGradeSubject(grade, subject) {
    $.ajax({
      url: '<?php echo site_url('academics/resources/viewResourcesByGradeSubject') ?>',
      type: 'post',
      data: {
        'grade': grade,
        'subject': subject
      },
      success: function(data) {
        data = $.parseJSON(data);        
        viewResources = data.viewResources;
        base_url = data.base_url;
        $('#displayResourcePanel').html(makeResourceTable(viewResources, base_url));
        after_call(viewResources);

      },
      error: function (err) {
        console.log(err);
      }
    });
  }

  function viewResourcesByGradeResourceTypeCreatedBy(grade, resourceType, created_by){
    $.ajax({
      url: '<?php echo site_url('academics/resources/viewResourcesByGradeResourceTypeCreatedBy') ?>',
      type: 'post',
      data: {
        'grade': grade,
        'resourceType': resourceType,
        'created_by': created_by
      },
      success: function(data) {
        data = $.parseJSON(data);        
        viewResources = data.viewResources;
        base_url = data.base_url;
        $('#displayResourcePanel').html(makeResourceTable(viewResources, base_url));
        after_call(viewResources);

      },
      error: function (err) {
        console.log(err);
      }
    });
  }

  function viewResourcesByGradeSubjectCreatedBy(grade, subject, created_by){
    $.ajax({
      url: '<?php echo site_url('academics/resources/viewResourcesByGradeSubjectCreatedBy') ?>',
      type: 'post',
      data: {
        'grade': grade,
        'subject': subject,
        'created_by': created_by
      },
      success: function(data) {
        data = $.parseJSON(data);        
        viewResources = data.viewResources;
        base_url = data.base_url;
        $('#displayResourcePanel').html(makeResourceTable(viewResources, base_url));
        after_call(viewResources);

      },
      error: function (err) {
        console.log(err);
      }
    });
  }

  function viewResourcesByGradeSubjectResourceType(grade, subject, resourceType){
    $.ajax({
      url: '<?php echo site_url('academics/resources/viewResourcesByGradeSubjectResourceType') ?>',
      type: 'post',
      data: {
        'grade': grade,
        'subject': subject,
        'resourceType': resourceType
      },
      success: function(data) {
        data = $.parseJSON(data);        
        viewResources = data.viewResources;
        base_url = data.base_url;
        $('#displayResourcePanel').html(makeResourceTable(viewResources, base_url));
        after_call(viewResources);

      },
      error: function (err) {
        console.log(err);
      }
    });
  }

  function viewResourcesByGradeSubjectTypeCreatedBy(grade, subject, created_by, resourceType){
    $.ajax({
      url: '<?php echo site_url('academics/resources/viewResourcesByGradeSubjectTypeCreatedBy') ?>',
      type: 'post',
      data: {
        'grade': grade,
        'subject': subject,
        'created_by': created_by,
        'resourceType': resourceType
      },
      success: function(data) {
        data = $.parseJSON(data);        
        viewResources = data.viewResources;
        base_url = data.base_url;
        $('#displayResourcePanel').html(makeResourceTable(viewResources, base_url));
        after_call(viewResources);

      },
      error: function (err) {
        console.log(err);
      }
    });
  }

  function makeResourceTable(resourceData, base_url) {
    // console.log(resourceData);
    var output = '';
    if (resourceData.length == 0) {
      let msg = `<div class="no-data-display">No Resources Added</div>`;
      output += msg;
    }
    else{

     output += '<table id="resourceTable" class="table table-bordered datatable">';


    output += '<thead>';
    output += '<tr>';
    output += '<th style="width:5%;">#</th>';
    output += '<th style="width:5%;">Class</th>';
    output += '<th style="width:10%;">Subject</th>';
    output += '<th style="width:15%;" >Name</th>';
    output += '<th style="width:10%;" >Description</th>';
    output += '<th style="width:10%;">Type</th>';
    output += '<th style="width:10%;">Created By</th>';
    output += '<th style="width:10%;">Created On</th>';
    output += '<th style="width:15%;">Status</th>';
    output += '<th style="width:10%;">Action</th>';
    output += '</tr>';
    output += '</thead>';
    output += '<tbody>';

   
    for (i = 0; i < resourceData.length; i++) {

      output += "<tr>";
      output += '<td>' + (i + 1) + '</td>';
      output += '<td>' + resourceData[i].grade + '</td>';
      output += '<td>' + resourceData[i].subject_name + '</td>';
      output += '<td id="'+resourceData[i].id+'" class="editable">'+ resourceData[i].name + '</td>';
      output += '<td id="'+resourceData[i].id+'" class="select-editable">'+resourceData[i].description+'</td>';
      output += '<td >' + resourceData[i].resource_type + '</td>';
      output += '<td>' + resourceData[i].created_by + '</td>';
      output += '<td>' + resourceData[i].created_on + '</td>';

      if(resourceData[i].status == 'Active'){
        output += '<td><strong style="color:green">ACTIVE</strong>';
        if(is_resource_admin) {
          output += '<button class="btn btn-sm btn-danger pull-right" onclick=updateResourceStatusToInactive('+resourceData[i].id+') style="width:6rem;border-radius:.45rem">Deactivate</button>';
        }
        output += '</td>';
      }
      else{
        output += '<td><strong style="color:red">INACTIVE</strong>';
        if(is_resource_admin) {
          output += '<button class="btn btn-sm btn-warning pull-right" onclick=updateResourceStatusToActive('+resourceData[i].id+') style="width:6rem;border-radius:.45rem">Activate</button>';
        }
        output += '</td>'
      }
      var file_name = '';
      file_name = resourceData[i].resource_file;
      var newString = '';
      newString = file_name.slice(-3);
      downloadUrl = "<?php echo site_url('academics/resources/downloadSignedURLResource/') ?>" + resourceData[i].id;
      if(resourceData[i].status == 'Active'){
        output+= '<td>';
       if(resourceData[i].resource_type=='Audio'){
          output+='<a onclick="showAudio('+resourceData[i].id+')" class="new_circleShape_res1"><span class="fa fa-eye" style="color:#428bca;"></span></a>&nbsp;';
          output += '<a  href="' + downloadUrl+ '" class="new_circleShape_res1" data-placement="top" data-toggle="tooltip" data-original-title="Download Resource"><span class="fa fa-download" style="color:#fe970a;"></span></a></td>'; 
        }
        else if(resourceData[i].resource_type=='Video'){
          output+='<a onclick="showRecording('+resourceData[i].id+')" class="new_circleShape_res1"><span class="fa fa-eye" style="color:#428bca;"></span></a>&nbsp;';
          output += '<a  href="' + downloadUrl+ '" class="new_circleShape_res1" data-placement="top" data-toggle="tooltip" data-original-title="Download Resource"><span class="fa fa-download" style="color:#fe970a;"></span></a></td>'; 
        }
        else if(resourceData[i].resource_type == 'PDF' || resourceData[i].resource_type=='Image' || resourceData[i].resource_type == 'Text File'){
          url = resourceData[i].signed_url;
          output += '<a target="_blank" href="' + url + '" class="new_circleShape_res1" data-placement="top" data-toggle="tooltip" data-original-title="View Resource"><span class="fa fa-eye" style="color:#428bca;"></span></a>&nbsp;';
          output += '<a  href="' + downloadUrl+ '" class="new_circleShape_res1" data-placement="top" data-toggle="tooltip" data-original-title="Download Resource"><span class="fa fa-download" style="color:#fe970a;"></span></a></td>'; 
        }else if( resourceData[i].resource_type=='Video Link'){
          url = resourceData[i].signed_url;
          output += '<a onclick="showYouTubeVideo('+resourceData[i].id+')" class="new_circleShape_res1" data-placement="top" data-toggle="tooltip" data-original-title="View Resource"><span class="fa fa-eye" style="color:#428bca;"></span></a>&nbsp;';
        }
        else if (resourceData[i].resource_type=='Hyper Link'){
          url = resourceData[i].resource_file;

          output += '<a href=' + resourceData[i].original_link + ' target = "new" class="new_circleShape_res1" data-placement="top" data-toggle="tooltip" data-original-title="View Resource"><span class="fa fa-eye" style="color:#428bca;"></span></a>&nbsp;';
        } else if(resourceData[i].resource_type === 'Vimeo') {
          output+='<a onclick="showVimeoVideo('+resourceData[i].resource_file+')" class="new_circleShape_res1" data-toggle="tooltip" data-original-title="View Resource"><span class="fa fa-eye" style="color:#428bca;"></span></a>&nbsp;';
        } else {
          output += '<a  href="' + downloadUrl+ '" class="new_circleShape_res1" data-placement="top" data-toggle="tooltip" data-original-title="Download Resource"><span class="fa fa-download" style="color:#fe970a;"></span></a></td>'; 
        }
         
      }
        
      else{
        // url = base_url+''+resourceData[i].resource_file;
        output += '<td><a class="new_circleShape_res1" data-placement="top" data-toggle="tooltip" data-original-title="View Resource" disabled><span class="fa fa-eye" style="color:#8ca2b5;"></span></a>&nbsp;<a  class="new_circleShape_res1" data-placement="top" data-toggle="tooltip" data-original-title="Download Resource" disabled><span class="fa fa-download" style="color:#ccb79b;"></span></a></td>'
      }

      output += '</tr>';
    }
    output += '</tbody>';
    output += '</table>';
    }
    return output;
  }

function showVimeoVideo(vimeo_id) {
  document.getElementById('resourceVideo').src= "https://player.vimeo.com/video/"+vimeo_id;
  $("#youtube-data").modal('show');
}

  function after_call(classes) {
        $(".editable").editable("<?php echo site_url('academics/resources/updateResourceData'); ?>", {
            tooltip : "Click to edit...",
            indicator : '<i class="fa fa-spinner fa-spin" style="font-size:20px"></i>',
            inputcssclass: 'form-control',
            // submitdata : {'resource_id': resource_id},
            width:'100%'
        });

        // $(".editable1").editable("<?php echo site_url('academics/resources/updateDescription'); ?>", {
        //     tooltip : "Click to edit...",
        //     indicator : '<i class="fa fa-spinner fa-spin" style="font-size:20px"></i>',
        //     inputcssclass: 'form-control',
        //     // submitdata : {'resource_id': resource_id},
        //     width:'100%'
        // });
        $(".select-editable").editable("<?php echo site_url('academics/resources/updateDescription'); ?>", {
            tooltip : "Click to edit...",
            // type   : "select",
            // data : {
            //   'default' : 'Select Type',
            //   'Image' : 'Image',
            //   'Video' : 'Video',
            //   'Audio' : 'Audio',
            //   'PDF' : 'PDF',
            //   'PPT' : 'PPT',
            //   'Text File' : 'Text File',
            //   'Other' : 'Other'
            // },
            indicator : '<i class="fa fa-spinner fa-spin" style="font-size:20px"></i>',
            inputcssclass: 'form-control',
           
            // submitdata : {'resource_id': resource_id},
            width:'100%'
    });
    // $(".select-editable1").editable("<?php echo site_url('enquiry/updateEnquiryData'); ?>", {
    //         tooltip : "Click to edit...",
    //         type   : "select",
    //         data : departments,
    //         indicator : '<i class="fa fa-spinner fa-spin" style="font-size:20px"></i>',
    //         inputcssclass: 'form-control',
    //         intercept: function(result, status) {
    //         return departments[result];
    //         },
    //         submitdata : {'enquiry_id': enquiry_id},
    //         width:'100%'
    // });

}

  function updateResourceStatusToInactive(resourceId){
    $.ajax({
      url: '<?php echo site_url('academics/resources/updateResourceStatusToInactive') ?>',
      type: 'post',
      data: {
        'resourceId': resourceId
      },
      success : function(data){
        // var url = '<?php // echo site_url('academics/resources/viewResourcesPage') ?>';
        getResources();
      }
    });
  }

  function updateResourceStatusToActive(resourceId){
    $.ajax({
      url: '<?php echo site_url('academics/resources/updateResourceStatusToActive') ?>',
      type: 'post',
      data: {
        'resourceId': resourceId
      },
      success : function(data){
        // var url = '<?php // echo site_url('academics/resources/viewResourcesPage') ?>';
        getResources();
      }
    });
  }
</script>

<script type="text/javascript" src="<?php echo base_url('assets/js/plugins/moment.min.js') ?>"></script>
<script type="text/javascript" src="<?php echo base_url('assets/js/plugins/daterangepicker/daterangepicker.js') ?>"></script>
<script type="text/javascript">
  $("#reportrange").daterangepicker({
        ranges: {
            'Today': [moment(), moment()],
            'Yesterday': [moment().subtract(1, 'days'), moment().subtract(1, 'days')],
            'Last 7 Days': [moment().subtract(6, 'days'), moment()],
            'Last 30 Days': [moment().subtract(29, 'days'), moment()],
            'This Month': [moment().startOf('month'), moment().endOf('month')],
        },
        opens: 'right',
        buttonClasses: ['btn btn-default'],
        applyClass: 'btn-small btn-primary',
        cancelClass: 'btn-small',
        format: 'DD-MM-YYYY',
        separator: ' to ',
        startDate: moment().subtract(29, 'days'),
        endDate: moment()            
    },function(start, end) {
        $('#reportrange span').html(start.format('MMM D, YYYY') + ' - ' + end.format('MMM D, YYYY'));
        $('#from_date').val(start.format('DD-MM-YYYY'));
        $('#to_date').val(end.format('DD-MM-YYYY'));
    });
  
    $("#reportrange span").html(moment().subtract(29, 'days').format('MMM D, YYYY') + ' - ' + moment().format('MMM D, YYYY'));
    $('#from_date').val(moment().subtract(29, 'days').format('DD-MM-YYYY'));
    $('#to_date').val(moment().format('DD-MM-YYYY'));

    function saveFileLocation(location) {
      $("#percent-span").html('Completing');
      $("#location").val(location);
      saveResources();
    }

    function saveResources(){
        var $form = $('#FileSaver');
        if ($form.parsley().validate()){
            $("#exampleModal").modal('hide');
            var form = $('#FileSaver')[0];
            var formData = new FormData(form);
            // console.log(formData);
            $.ajax({
                url: '<?php echo site_url('academics/resources/submit_resource'); ?>',
                type: 'post',
                data: formData,
                processData: false,
                contentType: false,
                success: function(data) {
                    if(data){
                      $(function(){
                          new PNotify({
                              title: 'Success',
                              text: 'Resources added successfully',
                              type: 'success',
                          });
                      });
                      // uploader.stop();
                      $("#recording-data .loader-background").hide();
                
                      $("#name1").val('');
                      $("#grade").val('');
                      $("#subject").val('');
                      $("#resource_type1").val('');
                      $("#fileName").val('');
                      $("#description").val('');
                      $("#start-upload").val('');
                  } else{
                      $(function(){
                          new PNotify({
                              title: 'Warning',
                              text: 'Something Went Wrong',
                              type: 'warning',
                          });
                      });
                  }
                }
            });
        }
    }
</script>
