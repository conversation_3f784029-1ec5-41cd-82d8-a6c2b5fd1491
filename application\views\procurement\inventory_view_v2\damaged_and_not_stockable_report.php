<ul class="breadcrumb">
    <li><a href="<?php echo site_url('dashboard') ?>">Dashboard</a></li>
    <li><a href="<?php echo site_url('procurement/requisition_controller_v2');?>">Procurement</a></li>
    <li><a href="<?php echo site_url('procurement/inventory_controller_v2/item_master_widgets');?>">Item Master</a></li>
    <li class="active">Item Threshold Report</li>
</ul>

<div class="col-md-12">
    <div class="card cd_border">
        <div class="card-header panel_heading_new_style_staff_border">
            <div class="panel-header" style="margin: 0px; background: none; border-bottom: 1px solid lightgray; height: 3.7rem;">
                <h3>
                    <a style="" class="back_anchor" href="<?php echo site_url('procurement/inventory_controller_v2/item_master_widgets') ?>" class="control-primary">
                        <span class="fa fa-arrow-left"></span>
                    </a> 
                    Threshold Report
                    <span class="btn btn-dark pull-right" onclick="display_info()"><span class="fa fa-info"></span></span>
                </h3>

            </div>
        </div>
        <div class="panel-body">
            <div id="filter_div" style="width: 100%;">

            <!-- <div class="col-md-2">
              <div class="input-group">
                <input type="checkbox" id="below_threshold_only" checked style="height: 19px; width: 19px;  margin-top: 5px;">
                <div class="input-group-addon" style="background: #fff; width: 100px; border: none; color: black;">
                  <label for="below_threshold_only" style="margin-left: -5px; cursor: pointer;">Below Threshold Only</label>
                </div>
              </div>
            </div> -->

                <div class="col-md-3 form-group">
                    <div class="input-group">
                      <select class="form-control" name="sales_year_id" id="sales_year_id">
                        <option value="All">Select Sales Year</option>
                        <?php
                          if(!empty($salesYear)) {
                            foreach($salesYear as $key => $val) {
                                $selected= '';
                                if($val->is_active == 1) {
                                    $selected = 'selected';
                                }
                              echo "<option $selected value='$val->id'>$val->year_name</option>";
                            }
                          }

                        ?>
                      </select>
                      <span class="input-group-addon"><span class="fa fa-caret-down"></span></span>
                    </div>
                </div>

                <div class="col-md-3 form-group">
                    <div class="input-group">
                      <select class="form-control" name="item_type" id="item_type">
                        <option value="3">Not Stockable Only</option>
                        <option value="2">Damaged Only</option>
                        <!-- <option value="All">All</option> -->
                      </select>
                      <span class="input-group-addon"><span class="fa fa-caret-down"></span></span>
                    </div>
                </div>

                <div class="col-md-3 form-group">
                    <div class="input-group">
                      <select class="form-control" name="category_id" id="category_id">
                        <option value="All">Select Category...</option>
                        <?php if(!empty($categories)) {foreach($categories as $key => $val) {
                          echo "<option value='$val->id'>$val->category_name</option>";
                        }} ?>
                        </select>
                        <span class="input-group-addon"><span class="fa fa-caret-down"></span></span>
                    </div>
                </div>
                
                <div class="col-md-2 form-group">
                    <button class="btn btn-dark" id="get_button_id" onclick="get_not_stockable_report()">Get Report</button>
                </div>
            </div>

            <div id="report_div" class="" style="overflow: auto; width: 100%;">     </div>

        </div>
    </div>
</div>

<script src="//cdn.jsdelivr.net/npm/sweetalert2@11"></script>
<script>

  function display_info() {
    Swal.fire({
      title: 'Stockable vs Non-Stockable Items',
      html: 'Only <b>non-stockable</b> items are those that cannot be used for student sales and staff allocation.<br><br>' +
            'However, <b>damaged items are stockable</b> - meaning after repairs they can be used in sales and allocation.',
      icon: 'info',
      confirmButtonText: 'OK'
    });
  }

  $(document).ready(function() {
    setTimeout(() => {
      // $("#get_button_id").click();
    }, 700);
  });
    // function get_subcategories() {
    //     var category_id= $("#category_id").val();
    //     console.log(category_id);
    //     if(category_id) {
    //         $.ajax({
    //             url: '<?php // echo site_url('procurement/inventory_controller_v2/get_subcategories') ?>',
    //             type: 'POST',
    //             data: {category_id},
    //             success: function(data) {
    //                 var p_data= JSON.parse(data);
                    
    //                 if(Object.keys(p_data)?.length !== 0) {
    //                     var opt= '<option value="">Select Sub Category...</option>';
    //                     for(var v of p_data) {
    //                         opt += `<option value="${v.id}">${v.subcategory_name}</option>`;
    //                     }
    //                     $("#sub_category_id").html(opt);
    //                 }
    //             },
    //             error: function(e) {
    //                 console.log(e);
    //             }
    //         });
    //     }
    // }

    function get_not_stockable_report() {
        var category_id= $("#category_id").val() || 0;
        var item_type= $("#item_type").val() || 'Both';
        var sales_year_id= $("#sales_year_id").val();
        if(!sales_year_id) {
            Swal.fire('Sales year not selected', '', 'warning');
        }
        $("#category_id").prop('disabled', true);
        $("#item_type").prop('disabled', true);
        $("#get_button_id").prop('disabled', true).html('Please Wait....');
        $("#report_div").html(`<center><div class="spinner-border text-primary" role="status" style="font-size: large; height: 120px; width: 120px;">
                                    <span class="sr-only">Loading...</span>
                                </div></center>`);
        
        $.ajax({
            url: '<?php echo site_url('procurement/inventory_controller_v2/get_not_stockable_report'); ?>',
            type: 'POST',
            data: {category_id, sales_year_id, item_type},
            success: function(data) {
                var resdata = JSON.parse(data);
                $("#category_id").prop('disabled', false);
                $("#item_type").prop('disabled', false);
                $("#get_button_id").prop('disabled', false).html('Get Report');
                __construct_table(resdata);
            },
            error: function() {

            }
        });
    }

    function __construct_table(data) {

        var table= `  <div class="col-md-12" style="height: 30px;"></div>
                      <table class="table table-bordered" id="table_id">
                        <thead class="thead-dark">
                            <tr>
                                <th style="min-width: 50px;">#</th>
                                <th style="min-width: 200px;">Category</th>
                                <th style="min-width: 200px;">Sub Category</th>
                                <th style="min-width: 200px;">Item</th>
                                <th style="min-width: 200px;">Item Type</th>
                                <th style="min-width: 170px;">Quantity</th>
                            </tr>
                        </thead>
                        <tbody>`;
        var k = 1;
        for(var i in data){
            var bg_color= '';
            if(Number(data[i].threshold_quantity) >= Number(data[i].current)) {
                bg_color = `color: #ff0606; font-weight: bold;`;
            }
            table +=`<tr>
                    <td>${k}</td>
                    <td>${data[i].category_name}</td>
                    <td>${data[i].subcategory_name}</td>
                    <td>${data[i].item_name}</td>
                    <td>${data[i].return_type && data[i].return_type == '2' ? 'Damaged' : 'Not Stockable'}</td>
                    <td>${data[i].returned_quantity}</td>
                </tr>`;   
            k++;
        }          
        table += `</tbody>
                </table>`;
        $("#report_div").html(table);
        __data_table();
    }

    function __data_table() {
        $('#table_id').DataTable( {
            "language": {
                "search": "",
                "searchPlaceholder": "Enter Search..."
            },
            "lengthMenu": [ [10, 25, 50, -1], [10, 25, 50, "All"] ],
            "pageLength": 10,
            dom: 'lBfrtip',
            buttons: [
                {
                    extend: 'excelHtml5',
                    text: 'Excel',
                    filename: 'item_stock',
                    className: 'btn btn-outline-dark'
                },
                {
                    extend: 'csvHtml5',
                    text: 'CSV',
                    filename: 'item_stock',
                    className: 'btn btn-outline-dark'
                },
                {
                    extend: 'pdfHtml5',
                    text: 'PDF',
                    filename: 'item_stock',
                    className: 'btn btn-outline-dark'
                }
            ]
        } );
    }

</script>

<style>
    body {
      font-family: Arial, sans-serif;
      margin: 0;
      padding: 0;
      display: flex;
      justify-content: center;
      align-items: center;
      height: 100vh;
      background-color: #f4f4f4;
    }

    .tabs {
      display: inline-flex;
      border: 1px solid #ccc;
      border-radius: 8px;
      overflow: hidden;
      box-shadow: 0 4px 6px rgba(0, 0, 0, 0.1);
    }

    .tab-button {
      padding: 10px 20px;
      border: none;
      background-color: #fff;
      cursor: pointer;
      transition: background-color 0.3s, color 0.3s;
      flex: 1;
      text-align: center;
    }

    .tab-button:hover {
      background-color: #007bff;
      color: white;
    }

    .tab-button.active {
      background-color: #007bff;
      color: white;
    }

    .tab-content {
      margin-top: 20px;
      text-align: center;
    }
  </style>
<style>
    .dataTables_wrapper .dt-buttons {
		float: right;
	}

	.dataTables_filter input {
		background-color: #f2f2f2;
		border: 1px solid #ccc;
		border-radius: 4px;
		margin-right: 5px;
	}
  
	.dataTables_wrapper .dataTables_filter {
		float: right;
		text-align: left;
		width: unset;
	}

    table {
    width: 100%;
    border-collapse: collapse;
  }


div#summary_div1::-webkit-scrollbar {
  width: 12px; /* Adjust as needed */
}

/* Style the scrollbar track */
div#summary_div1::-webkit-scrollbar-track {
  -webkit-box-shadow: inset 0 0 6px rgba(0, 0, 0, 0.3);
  border-radius: 10px;
}

/* Customize the scrollbar thumb appearance */
div#summary_div1::-webkit-scrollbar-thumb {
  background: #eee; /* Adjust the color as desired */
}

div#summary_div1 {
  scrollbar-width: thin;
}
/* 
.color_class_blue {
  background: lightblue;
}

.color_class_red {
  background: #ff9191;
}

.color_class_green {
  background: #9acc9c;
} */
</style>