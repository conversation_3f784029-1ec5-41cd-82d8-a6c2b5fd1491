<ul class="breadcrumb">
    <li><a href="<?php echo site_url('dashboard');?>">Dashboard</a></li>
    <li><a href="<?php echo site_url('examination/Assessments/index');?>">Examination</a></li>
    <li>Assessment Marks</li>
</ul>

<hr>

<?php $is_mobile = ($this->mobile_detect->isMobile())?1:0; ?>
<div class="col-md-12">
    <div class="card cd_border">
        <div class="card-header panel_heading_new_style_staff_border">
          <div class="row" style="margin: 0px;">
            <div class="col-md-6 d-flex justify-content-between" style="width:100%;">
              <h3 class="card-title panel_title_new_style_staff">
                <a class="back_anchor" href="<?php echo site_url('examination/Assessments/index'); ?>">
                  <span class="fa fa-arrow-left"></span>
                </a> 
                Assessment Marks
              </h3>   
            </div>
            <div class="float-right col-md-6">
                <ul class="list-group list-group-horizontal-md float-right" style="margin-left:20px;margin-right:15px;">
                    <li class="list-group-item" >Short-cuts</li>
                    <?php if($this->authorization->isAuthorized('EXAMINATION.CREATE')) { ?><li class="list-group-item" style="background-color: aliceblue"><a id="marks_entry_sc" href="<?php echo site_url() . "/examination/Assessments/showAssessments/$classSelected"; ?>">Goto Assessments</a></li><?php } ?>
                    <?php if($this->authorization->isAuthorized('EXAMINATION.CONSOLIDATION')) { ?><li class="list-group-item" style="background-color: aliceblue"><a id="derived_ass_sc" href="<?php echo site_url() . "/examination/Assessments/consolidationIndex/$classSelected"; ?>">Goto Derived Assessments</a></li><?php } ?>
                    <?php if($this->authorization->isAuthorized('EXAMINATION.CONSOLIDATION')) { ?><li class="list-group-item" style="background-color: aliceblue"><a id="report_card_sc" href="<?php echo site_url() . "/examination/assessment_marks/marksCards/index/$classSelected"; ?>">Goto Report Cards</a></li><?php } ?>
                    <li> <!--- Leaving this empty to workaround a bug ---></li>
                </ul>
            </div>


        </div>
        </div>

        <style>
                .radio-container {
                    display: flex;
                    align-items: center;
                    gap: 20px;
                    font-family: Arial, sans-serif;
                }
                .radio-option {
                    display: flex;
                    align-items: center;
                    margin-top: 26px;
                }
                input[type="radio"] {
                    margin-right: 8px;
                    position: relative;
                }
            </style>

        <div class="card-body pt-1">
            <?php if (!empty($classList)) { ?>
                <div class="row">
                    <div class="col-md-3">
                        <label class="control-label">Search By Class</label>
                        <select id="classSectionId" name="classSectionId" onchange="getAssessments()" class="form-control input-md">
                            <option value="">Select Class</option>
                            <?php
                            foreach ($classList as $cl) {
                                $option = '<option value="' . $cl->id . '"';
                                if($cl->id == $classSelected)
                                    $option .= ' selected';
                                $option .= '>' . $cl->class_name .'</option>';
                                echo $option;
                            }
                            $selected = '';
                            if($classSelected == 0)
                            $selected = ' Selected';
                            ?>
                        </select>
                    </div>
                    <div class="radio-container col-md-9">
                        <label for="" style="opacity: 0;">l</label>
                        <div class="radio-option">
                            <input checked type="radio" id="manual" name="assessment_type" value="manual" style="margin: -5px 5px 0 10px;" onclick="getAssessments()">
                            <label for="manual">Manual Only</label>
                        </div>
                        <div class="radio-option">
                            <input type="radio" id="auto" name="assessment_type" value="auto" style="margin: -5px 5px 0 10px;" onclick="getAssessments()">
                            <label for="auto">Include Auto with Manual</label>
                        </div>
                    </div>
                </div>


                


            <?php } else { ?>
                <div class="row col-md-12">
                    <h4 class='no-data-display'>No Assessments released for Marks entry</h4>
                </div>
            <?php } ?>


            <div class="row px-3 mt-3" id="content">
                
            </div>
        </div>
    </div>
</div>

<div class="visible-xs visible-sm">
  <a href="<?php echo site_url('examination/Assessments/index');?>" id="backBtn" onclick="loader()"><span class="fa fa-mail-reply"></span></a>
</div>

<script type="text/javascript">
    var is_mobile = 0;
    $(document).ready(function(){
        is_mobile = <?php echo $is_mobile; ?>;
        // alert(is_mobile)
        var classId = $('#classSectionId').val();
        if(classId) {
            getAssessments();
        }
    });
    function getAssessments() {
        $('#classSectionId').css({
            'pointer-events' : 'none',
            'background' : 'lightgray',
            'opacity' : '0.4'
        });
        var site_url = "<?php echo site_url(); ?>";
        var classId = $('#classSectionId').val();
        let selectedRadio = document.querySelector('input[name="assessment_type"]:checked');
        if (!classId) {
            $('#classSectionId').css({
                'pointer-events' : 'auto',
                'background' : 'white',
                'opacity' : '1'
            });
            return;
        }

      //Update Shortcuts
      $('#marks_entry_sc').attr("href", site_url + "/examination/Assessments/showAssessments/" + classId);
      $('#derived_ass_sc').attr("href", site_url + "examination/Assessments/consolidationIndex/" + classId);
      $('#report_card_sc').attr("href", site_url + "/examination/assessment_marks/marksCards/index/" + classId);

        $('#content').html('<div style="height: 200px;font-size: 40px;width: 100%;" class="d-flex justify-content-center align-items-center"><i class="fa fa-spinner fa-spin"></i></div>');

        $.ajax({
          url: '<?php echo site_url('examination/assessment_marks_v2/getAssessmentDataForMarksEntry'); ?>',
          data: {'classId':classId, 'selectedRadio': selectedRadio.value},
          type: "post",
          success: function (data) {
            $('#classSectionId').css({
                'pointer-events' : 'auto',
                'background' : 'white',
                'opacity' : '1'
            });
            var assessments = $.parseJSON(data);
            var output = '';
            if(assessments.length == 0){
                $('#content').html('<h4>Assessments not released for marks entry</h4>');
            } else {
                output += '<table class="table table-bordered">';
                output += '<thead>';
                output += '<th width="5%">#</th>';
                output += '<th width="15%">Short Name</th>';
                output += '<th width="15%">Long Name</th>';
                output += '<th width="15%">Generation Type</th>';
                output += '<th width="25%">Subjects Added</th>';
                output += '<th width="35%">Actions</th>';
                output += '</thead>';
                output += '<tbody>';
                for (i=0; i < assessments.length; i ++) {
                    var marks_entry_url_new = '<?php echo site_url("examination/assessment_marks_v2/marks_entry_subjects/") ?>' + assessments[i].id + "/" + classId;
                    var remarks_entry_url_new = '<?php echo site_url("examination/assessment_marks_v2/subject_remarks_entry_asssubjects_view/") ?>' + assessments[i].id + "/" + classId;
                    var enable_subject_remarks = assessments[i].enable_subject_remarks == 1 ? 'enabled' : 'disabled';

                    var action_button_html = '';
                    if (assessments[i].subAdded != '') {
                        action_button_html = `
                            <a ${assessments[i].add_marks_enability == '-1' ? 'disabled' : ''} style="margin:2px;" class="btn  btn-info" data-placement="top"  data-toggle="tooltip" data-original-title="Add Marks Group Wise" href="${marks_entry_url_new}">Add Marks</a>
                            <a style="margin:2px;" class="btn  btn-info" data-placement="top"  data-toggle="tooltip" data-original-title="Add Marks Group Wise" href="${remarks_entry_url_new}" ${enable_subject_remarks}>Add Subject Remarks</a>
                            <button id="ass-${assessments[i].id}" data-assessment_name="${assessments[i].short_name}" data-assessment_long_name="${assessments[i].long_name}" class="btn btn-primary" data-toggle="modal" style="margin:2px;" data-target="#portions-modal" onclick="view_portions('${assessments[i].id}')">View Portions</button>

                        `;
                    }
                    output += `
                        <tr>
                            <td>${i+1}</td>
                            <td>${assessments[i].short_name}</td>
                            <td>${assessments[i].long_name}</td>
                            <td>${assessments[i].generation_type}</td>
                            <td>${assessments[i].subAdded || 'No Subjects Added'}</td>
                            <td>${action_button_html}</td>
                        </tr>`;
                }
                output += '</tbody>';
                output += '</table>';
                $('#content').html(output);
            }
        }
      });
    }


function view_portions(assessment_id) {
    $("#portions-body").html(`<div class="d-flex justify-content-center align-items-center" style="height:70px;font-size: 40px;"><i class="fa fa-spinner fa-spin"></i></div>`);
    var assessment_name = $("#ass-"+assessment_id).data('assessment_name');
    $("#portions-title").html(`Portions for <b>${assessment_name}</b>`);
    $.ajax({
        url: '<?php echo site_url('examination/assessment_marks_v2/get_portions'); ?>',
        type: 'post',
        data: {'assessment_id':assessment_id},
        success: function(data) {
            var portions = JSON.parse(data);
            var html = '';
            if(portions == null) {
                html += `<div class="d-flex justify-content-center align-items-center" style="height:70px;"><h3>Portions not added.</h3></div>`;
            } else {
                html += `<div><p><b>Publish Status: </b>${portions.publish_status==1?'<span class="text-success">PUBLISHED</span>':'<span class="text-danger">NOT PUBLISHED</span>'}</p></div>`;
                html += portions.portions;
            }
            $("#portions-body").html(html);
            // console.log(portions)
        }
    });
}
</script>


<div id="portions-modal" class="modal fade" tabindex="-1" role="dialog" aria-labelledby="myModalLabel" aria-hidden="true" style="display: none;">
    <?php if($is_mobile) { ?>
        <div class="modal-dialog"> 
    <?php } else { ?>
        <div class="modal-dialog" style="width: 60%;top: 2%;margin: auto;"> 
    <?php } ?>
        <div class="modal-content">  
            <div class="modal-header"> 
               <h4 class="modal-title" id="portions-title">Portions For </h4>
               <button type="button" class="close" data-dismiss="modal" aria-hidden="true">×</button> 
            </div> 
            <div class="modal-body mobile" id="portions-body" style="max-height: 80vh; overflow-y: auto;">

            </div>
            <div class="modal-footer"> 
                <button type="button" class="btn btn-secondary" data-dismiss="modal">Close</button>  
            </div>         
        </div> 
    </div>
</div>