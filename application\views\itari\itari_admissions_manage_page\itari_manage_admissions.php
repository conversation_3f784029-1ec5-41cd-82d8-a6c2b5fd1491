    <ul class="breadcrumb">
    <li><a href="<?php echo site_url('dashboard');?>">Dashboard</a></li>
    <li><a  href="<?php echo site_url('itari/itari_menu'); ?>"> ITARI </a></li>
    <li>Itari Application Form</li>
    </ul>

<div class="col-md-12">
    <div class="card cd_border">
        <div class="card-header panel_heading_new_style_staff_border">
            <div class="row" style="margin: 0px">
                <div class="col-md-10">
                <h3 class="card-title panel_title_new_style_staff">
                    <a class="back_anchor" href="<?php echo site_url('itari/itari_menu'); ?>">
                    <span class="fa fa-arrow-left"></span>
                    </a> 
                    ITARI Application Form
                </h3>
                </div>
            </div>
        </div>
        <div class="card-body">
            <div class="row" style="margin: 0px">
                <div class="col-lg-2">
                    <p>Created Date</p>
                    <select name="daterange" id="daterange" class="form-control" onchange="changeDateRange()">
                        <option value="_">All</option>
                        <option value="<?= date('d-m-Y',strtotime('today')).'_'.date('d-m-Y',strtotime('today')) ?>">Today </option>
                        <option value="<?= date('d-m-Y',strtotime('today - 7 days')).'_'.date('d-m-Y',strtotime('today')) ?>">Last 7 Days </option>
                        <option value="<?= date('d-m-Y',strtotime('today - 30 days')).'_'.date('d-m-Y',strtotime('today')) ?>">Last 30 Days </option>
                        <option value="<?= date('d-m-Y',strtotime('today - 60 days')).'_'.date('d-m-Y',strtotime('today')) ?>">Last 60 Days </option>
                        <option value="<?= date('d-m-Y',strtotime('today - 90 days')).'_'.date('d-m-Y',strtotime('today')) ?>">Last 90 Days </option>
                        <option value="7">Custom Range </option>
                    </select>
                </div>

                <div id="custom_range" class="col-lg-4 form-group" style="display: none;">
                    <div class="col-lg-6">
                        <p>From</p>
                        <div class="input-group date" id="datePicker"> 
                        <input class="form-control" autocomplete="off" type="text" id="created_from_date" name="created_from_date" placeholder="Select Date">
                        <span class="input-group-addon"> <span class="glyphicon glyphicon-calendar"></span>
                        </span>
                        </div>
                    </div>
                    <div class="col-lg-6">
                        <p>To</p>
                        <div class="input-group date" id="datePicker"> 
                        <input class="form-control" autocomplete="off" type="text" id="created_to_date" name="created_to_date" placeholder="Select Date">
                        <span class="input-group-addon"> <span class="glyphicon glyphicon-calendar"></span>
                        </span>  
                        </div>
                    </div>
                </div>

                <div class="col-md-2">
                    <p>Admission Status</p>
                    <select class="form-control select"  name="follow_up_status" multiple="" title="All" id="follow_up_status">
                        <option value="Moved To ERP">Moved To ERP</option> 
                        <option value="Application Fees Paid">Application Fees Paid</option>    
                        <option value="admit">Admit</option>
                        <option value="submitted">Application Submitted</option>
                        <option value="closed">Closed-not-interested</option>
                        <option value="draft">Draft</option>
                        <option value="pending">Follow-up required</option>
                    </select>
                </div>

                <div class="col-md-2">
                    <p>Fees Status</p>
                    <select class="form-control select"  name="fee_status" title="All" id="fee_status">
                        <option value="">Select</option>
                        <option value="online">Fees Paid Online</option>
                        <option value="offline">Fees Paid Offline</option>
                        <option value="no">Fees not Paid</option>
                    </select>
                </div>

                <div class="col-lg-2">
                    <p>Next Follow-up Date</p>
                    <select name="daterange" id="daterange1" class="form-control" onchange="changeFollowDateRange()">
                        <option value="_">All</option>
                        <option value="<?= date('d-m-Y',strtotime('today')).'_'.date('d-m-Y',strtotime('today')) ?>">Today </option>
                        <option value="<?= date('d-m-Y',strtotime('today - 7 days')).'_'.date('d-m-Y',strtotime('today')) ?>">Last 7 Days </option>
                        <option value="<?= date('d-m-Y',strtotime('today - 30 days')).'_'.date('d-m-Y',strtotime('today')) ?>">Last 30 Days </option>
                        <option value="<?= date('d-m-Y',strtotime('today - 60 days')).'_'.date('d-m-Y',strtotime('today')) ?>">Last 60 Days </option>
                        <option value="<?= date('d-m-Y',strtotime('today - 90 days')).'_'.date('d-m-Y',strtotime('today')) ?>">Last 90 Days </option>
                        <option value="7">Custom Range </option>
                    </select>
                </div>

                <div id="custom_range1" class="col-lg-4 form-group" style="display: none;">
                    <div class="col-lg-6">
                        <p>From</p>
                        <div class="input-group date" id="datePicker"> 
                        <input class="form-control" autocomplete="off" type="text" id="followup_from_date" name="followup_from_date" placeholder="Select Date">
                        <span class="input-group-addon"> <span class="glyphicon glyphicon-calendar"></span>
                        </span>
                        </div>
                    </div>
                    <div class="col-lg-6">
                        <p>To</p>
                        <div class="input-group date" id="datePicker"> 
                        <input class="form-control" autocomplete="off" type="text" id="followup_to_date" name="followup_to_date" placeholder="Select Date">
                        <span class="input-group-addon"> <span class="glyphicon glyphicon-calendar"></span>
                        </span>  
                        </div>
                    </div>
                </div>
                <div class="col-lg-2">
                    <p>Program</p>
                    <select class="form-control select" multiple="" title="All"  name="grade" id="grade">
                        <option value="pgde">PGDE(I)</option>
                            <option value="ma-rpl">MA Ed.</option>
                            <option value="pgpece">PGP in ECE</option>
                            <option value="ibtl">IB in Learning & Training</option>
                            <option value="School Workshops">School Workshops</option>
                            <option value="IBEC">IB EC</option>
                    </select>
                </div>

                <div class="col-sm-2 col-md-2 d-flex align-items-end pl-0" style="height: 4.7rem;">
                    <input type="button" name="search" id="generate" class="btn btn-primary" value="Get Report">
                </div>

            </div>
        </div>
        <div class="card-body">
            <div id="message" class="no-data-display">Click on "Get Report" to view data.</div>
            <div id="loader" class="loaderclass" style="display:none;"></div>
            <input type="hidden" id="date_check" value="<?php echo date('Y-m-d')?>">
            <input type="hidden" id="todayDate" value="<?php echo date('Y-m-d')?>">
            <div class="col-md-12 message" style="height: 650px; overflow-y: scroll;"></div>
            <div class="col-md-5" style="padding: 0;">
                <div  id="pipelinebar"  class="pendingdates" style="padding: 10px;">
                    <div class="form-group" style="width: 110px; float: right;"></div>
                </div>
                <div class="list-group-enquiry" style="height: 650px; overflow-y: scroll;"></div>
            </div>
            <div class="col-md-7 details-tab">
                <div class="row">
                    <div class="card-body" id="action-btn" style="display: none;">
                        <span onclick="actionTabs(0)" id="detailsForm" class="label label-default label-form active"><i id="faAction1" class="fa fa-angle-down"></i> Details</span>
                        <span onclick="actionTabs(1)" style="display: none;" id="followForm" class="label label-default label-form"><i id="faAction2" class="fa fa-angle-up"></i> Follow-up</span>
                        <span onclick="actionTabs(2)" id="feesForm" class="label label-default label-form"><i id="faAction3" class="fa fa-angle-up"></i> Fees Details</span>
                        <span onclick="open_erp_modal()" id="move_to_erp_button" style="float: right; margin-left:3px; display:none" class="label label-default label-form move_to_erp">Move to ERP</span>
                        <span onclick="download_application_form()" id="download_button" style="float: right; margin-left:3px; display:none" class="label label-default label-form download_details">Generate Details</span>
                        <span onclick="download_essay()" style="float: right; display:none" class="label label-default label-form download_essay">Download Essay</span>
                    </div>
                </div>
                <div class="row">
                    <div class="card-body">
                        <div id="studentEnquiryDetails" style="height: 650px; overflow-y: scroll;"></div>
                        <div style="display: none; height: 650px; overflow-y: scroll;" id="followEnquiryDetails">
                            <div id="follow_up"></div>
                            <form method="post" class="form-horizontal" id="follow-up-action" data-parsley-validate="">
                            <input type="hidden" name="itari_admission_id" id="itari_admission_id">
                            <input type="hidden" name="follow_up_type" value="Itari-Enquiry">
                            <div class="row">
                                <div class="form-group col-md-12 follow_up_action">
                                    <div class="row">
                                        <label class="control-label col-md-3">Follow-up Action: <font color="red">*</font></label>
                                        <div class="col-md-8">
                                            <select class="form-control" id="followup_action" required="" name="followup_action">
                                            <option value="">Select</option>
                                            <option value="In-person">In-person</option>
                                            <option value="Phone-call">Phone-call</option>
                                            </select>
                                        </div>                  
                                    </div>
                                </div>
                                <div class="form-group col-md-12 remarks">
                                    <div class="row">
                                    <label class="control-label col-md-3">Remarks: </label>
                                    <div class="col-md-8">
                                        <input type="hidden" value="" id="remarks">
                                        <textarea class="form-control" id="remarks" rows="3" name="remarks"></textarea>
                                    </div>                  
                                    </div>
                                </div>
                                <div class="form-group col-md-12 status">
                                    <div class="row">
                                    <label class="control-label col-md-3">Status: <font color="red">*</font></label>
                                        <div class="col-md-8">
                                        <select class="form-control" required="" name="status" id="follow_status">
                                            <option value="">Select</option>
                                            <option value="pending">Follow-up pending</option>
                                            <option value="admit">Admit</option>
                                            <option value="closed">Closed-not-interested</option>
                                        </select>
                                        </div>
                                    </div>
                                    </div>

                                <div class="form-group col-md-12 follow_up_date">
                                    <div class="row">
                                    <label class="control-label col-md-3">Next follow up date: <font color="red">*</font></label>
                                    <div class="col-md-8">
                                        <input type="date" autocomplete="off" class="form-control date_pick" id="dob_dtpicker" name="next_follow_date"  placeholder="Next follow up date" required="">
                                    </div>
                                    </div>
                                </div>
                            </div>
                            <div style="margin-top: 30px;">
                                <center>
                                    <button type="button" id="submitbutton" style="margin-left: 270px;" onclick="submit_followUp()" class="col-md-3 btn btn-primary">Submit</button>
                                    <div id="success" style="color: green; font-weight: bold; font-size: 14px; display:none;">Enquiry Follow-up successfully Inserted </div>
                                    <div id="error" style="color: red; font-weight: bold; font-size: 14px; display:none;">Something went wrong </div>
                                </center>
                            </div>
                            <br><br>
                            <div id="follow-history" style="height: 200px; overflow-y: scroll;"></div>
                            </form>
                        </div>
                        <div id="feesEnquiryDetails" style="height: 650px; overflow-y: scroll;"></div>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>

<div id="offline_fees_collection_modal" class="modal" tabindex="-1">
    <div class="modal-dialog" style="width:40%; margin:auto;">
        <div class="modal-content" style="border-radius: 8px; overflow-x:auto;">
            <div class="modal-header">
                <h4 class="modal-title">Collect Fees Offline</h4>
            </div>
            <div class="modal-body">
                <form id="offline_fees_form" class="form-horizontal" method="post">
                    <input type="hidden" id="enquiry_id">
                    <div class="form-floating mb-3">
                        <label for="fees_amount">Amount:</label>
                        <input type="text" class="form-control" id="fees_amount" placeholder="Amount" readonly>
                    </div>
                    <div class="form-floating mb-3">
                        <label for="remarks">Remarks:</label>
                        <textarea id="offline_remarks" class="form-control" placeholder="Remarks" style="resize: none;"></textarea>
                    </div>
                </form>
            </div>
            <div class="modal-footer">
                <button type="button" class="btn btn-default" data-dismiss="modal" id="offline_fees_close">Close</button>
                <button type="submit" class="btn btn-primary" id="offline_fees_submit">Submit</button>
            </div>
        </div>
    </div>
</div>

<div id="move_to_erp_modal" class="modal" tabindex="-1">
    <div class="modal-dialog" style="width:40%; margin:auto;">
        <div class="modal-content" style="border-radius: 8px; overflow-x:auto;">
            <div class="modal-header">
                <h4 class="modal-title">Move to ERP</h4>
            </div>
            <div id="move_to_erp_error" class="alert alert-danger d-none" role="alert"></div>
            <div class="modal-body">
                <div class="form-floating mb-3">
                    <label for="fees_amount">Admission No:</label>
                    <input type="text" class="form-control" id="admission_no" placeholder="Admission Number">
                </div>
                <div class="form-floating mb-3" id="classes">
                </div>
                <div class="form-floating mb-3" id="sections">
                </div>
            </div>
            <div class="modal-footer">
                <button type="button" class="btn btn-default" data-dismiss="modal" id="move_to_erp_close">Close</button>
                <button type="submit" class="btn btn-primary" id="move_to_erp_submit">Submit</button>
            </div>
        </div>
    </div>
</div>

<?php $this->load->view('itari/itari_admissions_manage_page/css.php'); ?>
<?php $this->load->view('itari/itari_admissions_manage_page/script.php'); ?>
